package com.cdz360.biz.model.cus.auth.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class CardDto {

    private Long commId;

    @Schema(description = "卡面号码")
    private String printNo;

    @Schema(description = "卡鉴权号码")
    private String authNo;

    private Integer cardType;

    private String cardName;

    private String phone;

    private Long cusId;

    private String cusName;


    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车辆自编号
     */
    private String carNum;

    /**
     * 车队名称
     */
    private String carDepart;

    /**
     * 线路
     */
    private String lineNum;
}
