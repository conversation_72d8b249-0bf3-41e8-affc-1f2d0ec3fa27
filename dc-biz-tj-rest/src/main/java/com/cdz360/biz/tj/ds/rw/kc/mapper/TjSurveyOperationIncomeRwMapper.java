package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyOperationIncomeRwMapper {

    TjSurveyOperationIncomePo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveyOperationIncome(TjSurveyOperationIncomePo tjSurveyOperationIncomePo);

    int updateTjSurveyOperationIncome(TjSurveyOperationIncomePo tjSurveyOperationIncomePo);

    int disableTjSurveyOperationIncome(String surveyNo);

}

