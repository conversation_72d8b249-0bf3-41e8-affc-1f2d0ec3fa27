package com.cdz360.biz.device.business.entity.result;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Classname BsBoxSetting
 * @Description 设备配置主信息
 * @Date 2019/6/6 9:33
 * @Created by <PERSON><PERSON><PERSON>
 * @Email <EMAIL>
 */
@Data
@Accessors(chain = true)
public class BsBoxSettingVo {
    // 主键ID
    private Long id;
    // 桩设备ID
    private String boxCode;
    // 设备序列号/桩号
    private String boxOutFactoryCode;
    // 计费模板id
    private Long chargeId;
    // 1下发成功2失败3下发中
    private Integer status;
    // 管理员密码
    private String adminPassword;
    // 二级管理员密码
    private String level2Password;
    // 白天音量
    private Integer dayVolume;
    // 夜晚音量
    private Integer nightVolume;
    // 二维码url
    private String url;
    // 是否支持充电记录查询 （1是0否）
    private Integer isQueryChargeRecord;
    // 是否支持定时充电 （1是0否）
    private Integer isTimedCharge;
    // 是否支持无卡充电 （1是0否）
    private Integer isNoCardCharge;
    // 是否支持扫码充电 （1是0否）
    private Integer isScanCharge;
    // 是否支持Vin码充电 （1是0否）
    private Integer isVinCharge;
    // 是否支持刷卡充电 （1是0否）
    private Integer isCardCharge;
    // 是否支持定额电量充电 （1是0否）
    private Integer isQuotaEleCharge;
    // 是否支持固定金额充电 （1是0否）
    private Integer isQuotaMoneyCharge;
    // 是否支持固定时长充电 （1是0否）
    private Integer isQuotaTimeCharge;
    // 国际协议
    private String internationalAgreement;
    // 自动停充 （1是0否）
    private Integer isAutoStopCharge;
    // 均/轮充设置 0均充 1轮充
    private Integer avgOrTurnCharge;
    // 合充开关 （1开0关）
    private Integer isCombineCharge;
    // 是否支持辅电手动切换(1是0否)
    private Integer heating;
    // 辅电电压设置
    private Integer heatingVoltage;
    // 是否支持电池反接检测(1是0否)
    private Integer batteryCheck;
    // 是否支持主动安全检测(1是0否)
    private Integer securityCheck;
    // 是否支持不拔枪二次充电
    private Integer constantCharge;
    // 是否支持插枪获取二维码
    private Integer vinDiscover;
    // 订单隐私设置开关
    private Integer orderPrivacySetting;
    // 订单账号显示类型
    private Integer accountDisplayType;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 操作人id
    private Long updateByUserid;
    //主模板编号**唯一标识**
    private String templateCode;

}
