package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "采样点数据")
@Data
@Accessors(chain = true)
public class KwhSummaryPointVo {

    @Schema(description = "UNIX时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long unixTime;

    @Schema(description = "充电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal charge;

    @Schema(description = "放电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal discharge;
}
