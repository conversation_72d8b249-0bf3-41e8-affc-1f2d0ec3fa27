package com.cdz360.biz.trading.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.trading.service.BoxService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
public class BoxRest {

    @Autowired
    private BoxService boxService;

    @GetMapping("/api/box/getBoxSetting")
    public ObjectResponse<BsBoxSettingPo> getBoxSetting(@RequestParam("evseNo") String evseNo) {
        log.info("getBoxSetting evseNo = {}", evseNo);
        Assert.isTrue(StringUtils.isNotBlank(evseNo), "evseNo不能为空");

        BsBoxSettingPo vo = boxService.getOneByEvseNo(evseNo);
        return RestUtils.buildObjectResponse(vo);
    }

}
