package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjAreaAnalysisRoMapper {

    TjAreaAnalysisPo getById(@Param("id") Long id);

    List<TjAreaAnalysisPo> findTjAnalysis(ListTjAreaAnalysisParam param);

    long countTjAnalysis(ListTjAreaAnalysisParam param);

    TjAreaAnalysisWithPointVo getWithPointById(@Param("id") Long id);
}

