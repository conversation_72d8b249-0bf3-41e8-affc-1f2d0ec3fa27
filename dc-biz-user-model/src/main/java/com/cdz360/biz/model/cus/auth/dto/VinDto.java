package com.cdz360.biz.model.cus.auth.dto;

import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class VinDto {

    private Long commId;


    private String phone;

    private Long cusId;

    private String cusName;


    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车辆自编号
     */
    private String carNum;

    /**
     * 车队名称
     */
    private String carDepart;

    /**
     * 线路
     */
    private String lineNum;

    /**
     * 品牌
     */
    private String brand;

    /**
     * 型号
     */
    private String model;
}
