package com.cdz360.biz.model.cus.post.dto;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.cus.post.type.CusPostLevel;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户评论信息传输")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CusPostDto extends BaseObject {

    @Schema(description = "主键ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "订单号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "站点编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "用户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "评论状态: SUBMITTED(待回复), REPLIED(已回复)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CusPostStatus status;

    @Schema(description = "评论等级,, 范围(1~5)", format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CusPostLevel level;

    @Schema(description = "评论内容")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusContent;

    @Schema(description = "true公开，false不公开")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean open;

    @Schema(description = "回复内容")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String replyContent;

    @Schema(description = "标签")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> tags;

    @Schema(description = "操作人ID. sys_user.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    @Schema(description = "创建时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @Schema(description = "更新时间")
    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;
}
