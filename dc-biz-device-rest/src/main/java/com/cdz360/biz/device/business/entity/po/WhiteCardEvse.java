package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@TableName("t_r_white_card_evse")
public class WhiteCardEvse {
    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 物理卡号
     */
    @TableField("whiteCardNo")
    private String whiteCardNo;
    /**
     * 桩号
     */
    @TableField("evseId")
    private String evseId;
    /**
     * 紧急充电卡密码
     */
    @TableField("passWord")
    private String passWord;

    /**
     * 拟下发密码
     */
    @TableField("passWordTmp")
    private String passWordTmp;
    /**
     * 逻辑卡号
     */
    @TableField("card_no")
    private String cardNo;
    /**
     * 发送状态
     * 1-下发成功
     * 2-失败
     * 3-下发中
     * 4-弃用
     * 5-弃用失败
     * 6-弃用中
     */
    @TableField("sendStatus")
    private Integer sendStatus;

}