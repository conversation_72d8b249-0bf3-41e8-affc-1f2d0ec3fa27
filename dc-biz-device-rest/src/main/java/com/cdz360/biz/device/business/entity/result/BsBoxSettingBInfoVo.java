package com.cdz360.biz.device.business.entity.result;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Classname BsBoxSetting
 * @Description 设备配置 + 设备信息
 * @Date 2019/6/6 9:33
 * @Created by JLei
 * @Email <EMAIL>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BsBoxSettingBInfoVo extends BsBoxSettingVo {

    List<ChargerBarCodeUrlVo> chargerList;
    private String productName;
    private String boxName;
    private Integer currentType;

    private Integer sendStatus;
    private String urgencyPassword;
    private Integer evseStatus;

    /**
     * 桩协议版本
     */
    private Integer protocolVer;

    private Integer power; // 额定功率

}
