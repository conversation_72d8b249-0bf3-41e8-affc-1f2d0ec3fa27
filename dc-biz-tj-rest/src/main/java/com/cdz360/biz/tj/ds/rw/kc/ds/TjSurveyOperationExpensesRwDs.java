package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjMaterialCostRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationExpensesRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyOperationExpensesRwDs {


    @Autowired

    private TjSurveyOperationExpensesRwMapper tjSurveyOperationExpensesRwMapper;


    public TjSurveyOperationExpensesPo getById(Long id, boolean lock) {

        return this.tjSurveyOperationExpensesRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveyOperationExpenses(TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        return this.tjSurveyOperationExpensesRwMapper.insertTjSurveyOperationExpenses(tjSurveyOperationExpensesPo) > 0;
    }


    public boolean updateTjSurveyOperationExpenses(TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        return this.tjSurveyOperationExpensesRwMapper.updateTjSurveyOperationExpenses(tjSurveyOperationExpensesPo) > 0;
    }

    public boolean disableTjSurveyOperationExpensesBySurveyNo(String surveyNo) {
        return this.tjSurveyOperationExpensesRwMapper.disableTjSurveyOperationExpensesBySurveyNo(surveyNo) > 0;
    }

}

