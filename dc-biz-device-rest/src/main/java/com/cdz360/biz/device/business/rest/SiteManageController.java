package com.cdz360.biz.device.business.rest;

//
//import com.cdz360.base.model.base.dto.BaseResponse;
//import com.cdz360.biz.device.business.rest.base.BaseController;
//import com.cdz360.biz.device.business.service.SiteManageServiceImpl;
//import com.cdz360.biz.device.common.utils.LoggerHelper;
//import jakarta.servlet.http.HttpServletRequest;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.http.MediaType;
//import org.springframework.web.bind.annotation.PostMapping;
//import org.springframework.web.bind.annotation.RequestMapping;
//import org.springframework.web.bind.annotation.RestController;
//
///**
// * <AUTHOR>
// * @since 2018年8月30日
// */
//
//@Slf4j
//@RequestMapping("/api/site")
//@RestController
//@Deprecated
//public class SiteManageController extends BaseController {
//
//    @Autowired
//    private SiteManageServiceImpl siteManageService;
//
//
//    @PostMapping(value = "/publishAllSiteInfo", produces = MediaType.APPLICATION_JSON_UTF8_VALUE)
//    public BaseResponse publishAllSiteInfo(HttpServletRequest request) {
//        log.warn(LoggerHelper.formatEnterLog(request) + "同步所有场站信息开始");
//        siteManageService.publishAllSiteInfo();
//        log.warn("同步所有场站信息完成");
//        return BaseResponse.success();
//    }
//
//}
