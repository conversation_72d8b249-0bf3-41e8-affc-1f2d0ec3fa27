package com.cdz360.biz.model.cus.site.po;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SiteWhitelistPo
 * 
 * @since 12/24/2020 9:09 AM
 * <AUTHOR>
 */
@Schema(description = "场站白名单")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteWhitelistPo extends BaseObject {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "站点编号")
    private String siteId;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "true,在黑名单; false,不在黑名单")
    private Boolean enable;

    @Schema(description = "企业id")
    private Long corpId;

    @Schema(description = "普通客户手机号")
    private String phone;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}