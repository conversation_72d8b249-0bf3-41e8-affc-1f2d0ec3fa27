package com.cdz360.biz.ess.model.data.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "储能ESS下PCS每日数据")
public class PcsDailyPo {

    @Schema(description = "数据日期")
    @NotNull(message = "date 不能为 null")
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate date;

    @Schema(description = "ESS设备序号")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 32, message = "dno 长度不能超过 32")
    private String essDno;

    @Schema(description = "设备序号")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 32, message = "dno 长度不能超过 32")
    private String dno;

    @Schema(description = "总充电电量，单位kwh")
    private BigDecimal totalInKwh;

    @Schema(description = "总放电电量，单位kwh")
    private BigDecimal totalOutKwh;

    @Schema(description = "当日充电电量, 单位kwh")
    private BigDecimal todayInKwh;

    @Schema(description = "当日放电电量, 单位kwh")
    private BigDecimal todayOutKwh;

    @Schema(description = "当天储能收益,单位: 元")
    private BigDecimal todayProfit;

    @Schema(description = "当天充电支出,单位: 元")
    private BigDecimal todayInFee;

    @Schema(description = "当天放电收益,单位: 元")
    private BigDecimal todayOutFee;

    @Schema(description = "计算充电模板ID(t_template.id)")
    private Long inPriceId;

    @Schema(description = "计算放电模板ID(t_template.id)")
    private Long outPriceId;

    @Schema(description = "是否有效")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

    @Schema(description = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "记录最后修改时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

}
