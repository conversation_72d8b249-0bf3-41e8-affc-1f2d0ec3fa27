<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjCashOutflowRwMapper">

  <resultMap id="RESULT_TJ_CASH_OUTFLOW_PO" type="com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="stationType" jdbcType="INTEGER" property="stationType"/>
    <result column="stationArea" jdbcType="VARCHAR" property="stationArea"/>
    <result column="channelAreaName" jdbcType="VARCHAR" property="channelAreaName"/>
    <result column="channelArea" jdbcType="VARCHAR" property="channelArea"/>
    <result column="commonRate" jdbcType="DECIMAL" property="commonRate"/>
    <result column="firstYearRate" jdbcType="DECIMAL" property="firstYearRate"/>
    <result column="secondYearRate" jdbcType="DECIMAL" property="secondYearRate"/>
    <result column="thirdYearRate" jdbcType="DECIMAL" property="thirdYearRate"/>
    <result column="fourthYearRate" jdbcType="DECIMAL" property="fourthYearRate"/>
    <result column="fifthYearRate" jdbcType="DECIMAL" property="fifthYearRate"/>
    <result column="sixthYearRate" jdbcType="DECIMAL" property="sixthYearRate"/>
    <result column="seventhYearRate" jdbcType="DECIMAL" property="seventhYearRate"/>
    <result column="eighthYearRate" jdbcType="DECIMAL" property="eighthYearRate"/>
    <result column="ninthYearRate" jdbcType="DECIMAL" property="ninthYearRate"/>
    <result column="tenthYearRate" jdbcType="DECIMAL" property="tenthYearRate"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_CASH_OUTFLOW_PO">
    select * from t_tj_cash_outflow where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjCashOutflow"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo">
    insert into t_tj_cash_outflow (`type`,
    <if test="null != stationType">
      `stationType`,
    </if>
    <if test="null != stationArea">
      `stationArea`,
    </if>
    <if test="null != channelAreaName">
      `channelAreaName`,
    </if>
    <if test="null != channelArea">
      `channelArea`,
    </if>
    <if test="null != commonRate">
      `commonRate`,
    </if>
    <if test="null != firstYearRate">
      `firstYearRate`,
    </if>
    <if test="null != secondYearRate">
      `secondYearRate`,
    </if>
    <if test="null != thirdYearRate">
      `thirdYearRate`,
    </if>
    <if test="null != fourthYearRate">
      `fourthYearRate`,
    </if>
    <if test="null != fifthYearRate">
      `fifthYearRate`,
    </if>
    <if test="null != sixthYearRate">
      `sixthYearRate`,
    </if>
    <if test="null != seventhYearRate">
      `seventhYearRate`,
    </if>
    <if test="null != eighthYearRate">
      `eighthYearRate`,
    </if>
    <if test="null != ninthYearRate">
      `ninthYearRate`,
    </if>
    <if test="null != tenthYearRate">
      `tenthYearRate`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{type},
    <if test="null != stationType">
      #{stationType},
    </if>
    <if test="null != stationArea">
      #{stationArea},
    </if>
    <if test="null != channelArea">
      #{channelArea},
    </if>
    <if test="null != commonRate">
      #{commonRate},
    </if>
    <if test="null != firstYearRate">
      #{firstYearRate},
    </if>
    <if test="null != secondYearRate">
      #{secondYearRate},
    </if>
    <if test="null != thirdYearRate">
      #{thirdYearRate},
    </if>
    <if test="null != fourthYearRate">
      #{fourthYearRate},
    </if>
    <if test="null != fifthYearRate">
      #{fifthYearRate},
    </if>
    <if test="null != sixthYearRate">
      #{sixthYearRate},
    </if>
    <if test="null != seventhYearRate">
      #{seventhYearRate},
    </if>
    <if test="null != eighthYearRate">
      #{eighthYearRate},
    </if>
    <if test="null != ninthYearRate">
      #{ninthYearRate},
    </if>
    <if test="null != tenthYearRate">
      #{tenthYearRate},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjCashOutflow" parameterType="com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo">
    update t_tj_cash_outflow set
    <if test="stationType != null">
      stationType = #{stationType},
    </if>
    <if test="stationArea != null">
      stationArea = #{stationArea},
    </if>
    <if test="channelAreaName != null">
      channelAreaName = #{channelAreaName},
    </if>
    <if test="channelArea != null">
      channelArea = #{channelArea},
    </if>
    <if test="commonRate != null">
      commonRate = #{commonRate},
    </if>
    <if test="firstYearRate != null">
      firstYearRate = #{firstYearRate},
    </if>
    <if test="secondYearRate != null">
      secondYearRate = #{secondYearRate},
    </if>
    <if test="thirdYearRate != null">
      thirdYearRate = #{thirdYearRate},
    </if>
    <if test="fourthYearRate != null">
      fourthYearRate = #{fourthYearRate},
    </if>
    <if test="fifthYearRate != null">
      fifthYearRate = #{fifthYearRate},
    </if>
    <if test="sixthYearRate != null">
      sixthYearRate = #{sixthYearRate},
    </if>
    <if test="seventhYearRate != null">
      seventhYearRate = #{seventhYearRate},
    </if>
    <if test="eighthYearRate != null">
      eighthYearRate = #{eighthYearRate},
    </if>
    <if test="ninthYearRate != null">
      ninthYearRate = #{ninthYearRate},
    </if>
    <if test="tenthYearRate != null">
      tenthYearRate = #{tenthYearRate},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjCashOutflow">
    update t_tj_cash_outflow set
    enable = false, updateTime = now()
    where id = #{id}
  </update>


</mapper>

