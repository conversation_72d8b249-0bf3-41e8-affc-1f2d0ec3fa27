package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * @Classname SiteDefultSetting
 * @Description TODO
 * @Date 2019/6/5
 * @Created by wangzheng
 */
@Data
@Accessors(chain = true)
@TableName("t_site_defult_setting")
public class SiteDefultSetting {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 场站编号
     */
    @TableField("siteId")
    private String siteId;
    /**
     * 计费模板id
     */
    @TableField("chargeId")
    private Long chargeId;
    /**
     * 管理员密码
     */
    @TableField("adminPassword")
    private String adminPassword;
    /**
     * 二级管理员密码
     */
    @TableField("level2Password")
    private String level2Password;
    /**
     * 白天音量
     */
    @TableField("dayVolume")
    private Integer dayVolume;
    /**
     * 夜晚音量
     */
    @TableField("nightVolume")
    private Integer nightVolume;
    /**
     * 二维码url
     */
    @TableField("url")
    private String url;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    @TableField("isQueryChargeRecord")
    private Boolean isQueryChargeRecord;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    @TableField("isTimedCharge")
    private Boolean isTimedCharge;
    /**
     * 是否支持无卡充电 （1是0否）
     */
    @TableField("isNoCardCharge")
    private Boolean isNoCardCharge;
    /**
     * 是否支持扫码充电 （1是0否）
     */
    @TableField("isScanCharge")
    private Boolean isScanCharge;
    /**
     * 是否支持Vin码充电 （1是0否）
     */
    @TableField("isVinCharge")
    private Boolean isVinCharge;
    /**
     * 是否支持刷卡充电 （1是0否）
     */
    @TableField("isCardCharge")
    private Boolean isCardCharge;
    /**
     * 是否支持定额电量充电 （1是0否）
     */
    @TableField("isQuotaEleCharge")
    private Boolean isQuotaEleCharge;
    /**
     * 是否支持固定金额充电 （1是0否）
     */
    @TableField("isQuotaMoneyCharge")
    private Boolean isQuotaMoneyCharge;
    /**
     * 是否支持固定时长充电 （1是0否）
     */
    @TableField("isQuotaTimeCharge")
    private Boolean isQuotaTimeCharge;
    /**
     * 国际协议
     */
    @TableField("internationalAgreement")
    private String internationalAgreement;
    /**
     * 自动停充 （1是0否）
     */
    @TableField("isAutoStopCharge")
    private Integer isAutoStopCharge;
    /**
     * 均/轮充设置 0均充 1轮充
     */
    @TableField("avgOrTurnCharge")
    private Integer avgOrTurnCharge;
    /**
     * 合充开关 （1开0关）
     */
    @TableField("isCombineCharge")
    private Integer isCombineCharge;
    /**
     * 是否支持辅电手动切换
     */
    @TableField("heating")
    private Boolean heating;
    /**
     * 辅电电压设置
     */
    @TableField("heatingVoltage")
    private Integer heatingVoltage;
    /**
     * 是否支持电池反接检测
     */
    @TableField("batteryCheck")
    private Boolean batteryCheck;
    /**
     * 是否支持主动安全检测
     */
    @TableField("securityCheck")
    private Boolean securityCheck;
    /**
     * 不拔枪充电开关(二次充电)
     */
    @TableField("constantCharge")
    private Boolean constantCharge;
    /**
     * 插枪获取VIN开关
     */
    @TableField("vinDiscover")
    private Boolean vinDiscover;
    /**
     * 订单信息隐私设置开关（null不做更改 true开启 false关闭）
     */
    @TableField("orderPrivacySetting")
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    @TableField("accountDisplayType")
    private Integer accountDisplayType;
//    /**
//     * 1是0否
//     */
//    @TableField("isUseDefaultSetting")
//    private Integer isUseDefaultSetting;
    /**
     * 创建时间
     */
    @TableField("createTime")
    private Date createTime;
    /**
     * 更新时间
     */
    @TableField("updateTime")
    private Date updateTime;
    /**
     * 操作人id
     */
    @TableField("updateByUserid")
    private Long updateByUserid;

}