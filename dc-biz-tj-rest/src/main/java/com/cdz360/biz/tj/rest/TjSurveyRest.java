package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.cdz360.biz.tj.service.kc.TjSurveyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建勘察相关操作接口", description = "投建勘察相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/survey")
public class TjSurveyRest {

    @Autowired
    private TjSurveyService tjSurveyService;

    @Operation(summary = "获取勘察场站列表")
    @PostMapping(value = "/findTjSurvey")
    public Mono<ListResponse<TjSurveyPo>> findTjSurvey(@RequestBody ListTjSurveyParam param) {
        log.debug("获取勘察场站列表: param = {}", JsonUtils.toJsonString(param));
        return tjSurveyService.findTjSurvey(param);
    }

    @Operation(summary = "获取投建场站勘察汇总信息")
    @PostMapping(value = "/tjSurveyBi")
    public Mono<ObjectResponse<TjSurveyBiVo>> tjSurveyBi(@RequestBody TjSurveyBiParam param) {
        log.debug("获取投建场站勘察汇总信息: param = {}", JsonUtils.toJsonString(param));
        return tjSurveyService.tjSurveyBi(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "重复勘察场站判断")
    @PostMapping(value = "/repeatSurvey")
    public Mono<ListResponse<TjSurveyVo>> repeatSurvey(@RequestBody RepeatSurveyParam param) {
        log.debug("重复勘察场站判断: param = {}", JsonUtils.toJsonString(param));
        return tjSurveyService.repeatSurvey(param)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "新增或编辑场站勘察记录")
    @PostMapping(value = "/saveTjSurvey")
    public Mono<ObjectResponse<TjSurveyVo>> saveTjSurvey(@RequestBody TjSurveyVo survey) {
        log.debug("新增或编辑场站勘察记录: param = {}", JsonUtils.toJsonString(survey));
        return tjSurveyService.saveTjSurvey(survey)
            .map(RestUtils::buildObjectResponse);
    }
}
