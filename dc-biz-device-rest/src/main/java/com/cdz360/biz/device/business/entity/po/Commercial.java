package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 * 商户表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-28
 */
@Data
@TableName("`auth_center`.t_commercial")
public class Commercial {

    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("pid")
    private Long pid;
    /**
     * 商户名称
     */
    @TableField("comm_name")
    private String commName;
    /**
     * 简称
     */
    @TableField("short_name")
    private String shortName;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系电话
     */
    private String phone;
    /**
     * 联系邮箱
     */
    private String email;
    /**
     * 商户二维码
     */
    @TableField("url")
    private String url;
}
