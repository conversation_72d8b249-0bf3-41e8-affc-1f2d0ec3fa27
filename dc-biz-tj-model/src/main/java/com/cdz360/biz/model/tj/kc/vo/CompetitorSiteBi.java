package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "竞争对手运营场站信息")
public class CompetitorSiteBi {

    @NotNull(message = "竞争对手记录ID")
    private Long id;

    @Schema(description = "竞争对手名称")
    private String name;

    @Schema(description = "场站数量")
    private Integer siteNum;
}
