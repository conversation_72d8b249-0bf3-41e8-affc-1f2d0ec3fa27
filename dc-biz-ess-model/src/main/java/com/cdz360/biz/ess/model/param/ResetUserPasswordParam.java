package com.cdz360.biz.ess.model.param;

import com.cdz360.biz.ess.model.dto.BaseUserAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户密码充值请求参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ResetUserPasswordParam extends BaseUserAccount {

    @Schema(description = "验证码", required = true)
    @NotBlank
    private String verificationCode;

//    @NotBlank
//    @Schema(description = "确认密码", required = true)
//    private String confirmPassw;

}
