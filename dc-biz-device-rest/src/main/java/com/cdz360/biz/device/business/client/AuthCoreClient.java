package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.request.TokenRequest;
import com.cdz360.biz.device.business.entity.result.SysUserVo;
import com.fasterxml.jackson.databind.JsonNode;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

/**
 * @Interfacename AuthCoreClient
 * @Description 用于调用auth-core服务
 * @Date 2019/6/5
 * @Created by wangzheng
 */
@FeignClient(value = DcConstants.KEY_FEIGN_DC_BIZ_AUTH)
public interface AuthCoreClient {


    @GetMapping("data/commercials/user/getMaxCommercial/{commId}")
    ObjectResponse<JsonNode> getMaxCommercialByCommId(@PathVariable("commId") Long commId);

    @RequestMapping(value = "/api/info/token", method = RequestMethod.POST)
    ObjectResponse<SysUserVo> getLoginUserByToken(@RequestBody TokenRequest tokenRequest);
}
