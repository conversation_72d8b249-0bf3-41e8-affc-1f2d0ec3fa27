<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjDailyChargingDurationRoMapper">

  <resultMap id="RESULT_TJ_DAILY_CHARGING_DURATION_PO" type="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="province" jdbcType="VARCHAR" property="province"/>
    <result column="city" jdbcType="VARCHAR" property="city"/>
    <result column="area" jdbcType="VARCHAR" property="area"/>
    <result column="provinceName" jdbcType="VARCHAR" property="provinceName"/>
    <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
    <result column="areaName" jdbcType="VARCHAR" property="areaName"/>
    <result column="provinceFullName" jdbcType="VARCHAR" property="provinceFullName"/>
    <result column="cityFullName" jdbcType="VARCHAR" property="cityFullName"/>
    <result column="areaFullName" jdbcType="VARCHAR" property="areaFullName"/>
    <result column="firstYearThirdMonth" jdbcType="DECIMAL" property="firstYearThirdMonth"/>
    <result column="firstYearLastNinthMonth" jdbcType="DECIMAL" property="firstYearLastNinthMonth"/>
    <result column="secondYear" jdbcType="DECIMAL" property="secondYear"/>
    <result column="thirdYear" jdbcType="DECIMAL" property="thirdYear"/>
    <result column="fourthYear" jdbcType="DECIMAL" property="fourthYear"/>
    <result column="fifthYear" jdbcType="DECIMAL" property="fifthYear"/>
    <result column="sixthYear" jdbcType="DECIMAL" property="sixthYear"/>
    <result column="seventhYear" jdbcType="DECIMAL" property="seventhYear"/>
    <result column="eighthYear" jdbcType="DECIMAL" property="eighthYear"/>
    <result column="ninthYear" jdbcType="DECIMAL" property="ninthYear"/>
    <result column="tenthYear" jdbcType="DECIMAL" property="tenthYear"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_DAILY_CHARGING_DURATION_PO">
    select * from t_tj_daily_charging_duration where id = #{id} and enable = true
  </select>

  <select id="findTjDailyChargingDuration"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam"
    resultMap="RESULT_TJ_DAILY_CHARGING_DURATION_PO">
    select * from t_tj_daily_charging_duration where 1=1 and enable = true
    <if test="null != provinceName">
      and provinceName = #{provinceName}
    </if>
    <if test="null != cityName">
      and cityName = #{cityName}
    </if>
    <if test="null != areaName">
      and areaName = #{areaName}
    </if>
    <if test="null != provinceFullName">
      and provinceFullName = #{provinceFullName}
    </if>
    <if test="null != cityFullName">
      and cityFullName = #{cityFullName}
    </if>
    <if test="null != areaFullName">
      and areaFullName = #{areaFullName}
    </if>
    order by id desc
  </select>

  <select id="countTjDailyChargingDuration"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam"
    resultType="java.lang.Long">
    select count(*) from t_tj_daily_charging_duration where 1=1 and enable = true
    <if test="null != provinceName">
      and provinceName = #{provinceName}
    </if>
    <if test="null != cityName">
      and cityName = #{cityName}
    </if>
    <if test="null != areaName">
      and areaName = #{areaName}
    </if>
    <if test="null != provinceFullName">
      and provinceFullName = #{provinceFullName}
    </if>
    <if test="null != cityFullName">
      and cityFullName = #{cityFullName}
    </if>
    <if test="null != areaFullName">
      and areaFullName = #{areaFullName}
    </if>
  </select>


</mapper>

