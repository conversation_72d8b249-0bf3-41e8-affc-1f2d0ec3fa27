package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 基于位置的站点列表查询请求
 *
 * <AUTHOR>
 * @since 2018年7月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SiteGeoListRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    private String siteId;
    /**
     * 运营商ID
     */
    private Long commercialId;
    /**
     * 省份区划编码
     */
    private Integer provinceCode;
    /**
     * 城市区划编码
     */
    private Integer cityCode;
    /**
     * 纬度
     */
    private Double latitude;
    /**
     * 经度
     */
    private Double longitude;
    /**
     * 距离-单位米,最小为1000，默认为50000
     */
    private Integer distance;
    /**
     * 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
     */
    private Integer type;

    /**
     * 商户ID 集合
     */
    @Deprecated
    private List<Long> commercialIds;

    /**
     * 0-已删除 1-待上线 2-已上线 4-维护中
     */
    private Integer status;
}
