package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveySupportingFacilitiesOtherRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveySupportingFacilitiesOtherRwDs {


    @Autowired

    private TjSurveySupportingFacilitiesOtherRwMapper tjSurveySupportingFacilitiesOtherRwMapper;


    public TjSurveySupportingFacilitiesOtherPo getById(Long id, boolean lock) {

        return this.tjSurveySupportingFacilitiesOtherRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveySupportingFacilitiesOther(TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        return this.tjSurveySupportingFacilitiesOtherRwMapper.insertTjSurveySupportingFacilitiesOther(tjSurveySupportingFacilitiesOtherPo) > 0;
    }


    public boolean updateTjSurveySupportingFacilitiesOther(TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        return this.tjSurveySupportingFacilitiesOtherRwMapper.updateTjSurveySupportingFacilitiesOther(tjSurveySupportingFacilitiesOtherPo) > 0;
    }

    public boolean disableTjSurveySupportingFacilitiesOtherBySurveyNo(String surveyNo) {
        return this.tjSurveySupportingFacilitiesOtherRwMapper.disableTjSurveySupportingFacilitiesOtherBySurveyNo(surveyNo) > 0;
    }

}

