package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "充放电数据汇总")
@Data
@Accessors(chain = true)
public class EssDataSummary {

    @Schema(name = "场站数量")
    @JsonInclude(Include.NON_NULL)
    private Long siteNum;

    @Schema(name = "总功率,单位kw")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

    @Schema(name = "总容量,单位kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal capacity;

    @Schema(description = "设备充电量(kW·h)", required = true)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(description = "设备放电量(kW·h)", required = true)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(description = "收益,单位 元", required = true)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal profit;

    @Schema(description = "减碳")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal co2 = new BigDecimal("856.78");   // TODO: 需要计算公式
}
