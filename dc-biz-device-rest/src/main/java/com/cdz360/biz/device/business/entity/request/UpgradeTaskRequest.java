package com.cdz360.biz.device.business.entity.request;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @Classname UpgradeTaskRequest
 * @Description TODO
 * @Date 9/21/2019 9:53 AM
 * @Created by Rafael
 */
@Data
@ApiModel("请求桩升级参数")
public class UpgradeTaskRequest {
    @ApiModelProperty("场站id")
    String siteId;
    @ApiModelProperty("桩编号列表")
    List<String> evseIds;
    @ApiModelProperty("升级包id")
    Long bundleId;
    @ApiModelProperty(value = "操作者id(不要传，传了也没用)", hidden = true)
    Long opId;
    @ApiModelProperty(value = "操作者名字(不要传，传了也没用)", hidden = true)
    String OpName;
    @ApiModelProperty(value = "这个参数用于再升级失败的桩")
    Long taskId;
}