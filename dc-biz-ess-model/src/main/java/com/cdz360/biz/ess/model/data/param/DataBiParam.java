package com.cdz360.biz.ess.model.data.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.biz.model.iot.type.SiteBiSampleType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "数据汇总查询参数")
@Data
@Accessors(chain = true)
public class DataBiParam {

    @Schema(description = "商户链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "数据汇总单位 数据汇总粒度")
    @NotNull(message = "数据汇总单位不能为空(sampleType)")
    private SiteBiSampleType sampleType;

    @Schema(description = "快捷查询参数",
        example = "1.shortcut = 7 and sampleType = 'DAY' 表示近七天数据\n" +
            "2.shortcut = 24 and sampleType = 'HOUR' 表示近24小时数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer shortcut;

    @Schema(description = "查询某一天数据 这结合(sampleType='HOUR')使用，仅查询这一天数据")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime date;

    @Schema(description = "查询开始时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime fromDate;

    @Schema(description = "查询结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime toDate;

    public static void rangeDate(DataBiParam param) {
        if (null == param.getFromDate() && null == param.getToDate()) {
            if (null == param.getShortcut() &&
                !SiteBiSampleType.HOUR.equals(param.getSampleType())) {
                throw new DcArgumentException("时间范围不明确");
            }

            param.setToDate(LocalDateTime.now());
            switch (param.getSampleType()) {
                case HOUR:
                    if (null != param.getShortcut()) {
                        param.setFromDate(LocalDateTime.now().minusHours(param.getShortcut() + 1));
                    } else if (null != param.getDate()) {
                        param.setFromDate(LocalDate.now().atStartOfDay());
                    } else {
                        throw new DcArgumentException("时间范围不明确");
                    }
                    break;
                case DAY:
                    param.setFromDate(LocalDateTime.now().minusDays(param.getShortcut() + 1));
                    break;
                case MONTH:
                    param.setFromDate(LocalDateTime.now().minusMonths(param.getShortcut() + 1));
                    break;
            }
        }
    }
}
