package com.cdz360.biz.model.cus.post.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.cus.post.type.CusPostLevel;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户评论参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class CommentParam extends BaseObject {

    @Schema(description = "订单号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty(message = "充电订单号不能为空")
    private String orderNo;

    @Schema(description = "站点编号", required = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty(message = "站点编号不能为空")
    private String siteId;

    @Schema(description = "用户ID", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "评论等级, 范围(1~5)", required = true, format = "java.lang.Integer")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @NotNull(message = "评论等级不能为空")
    private CusPostLevel level;

    @Schema(description = "评论内容")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @NotEmpty(message = "评论内容不能为空")
    private String cusContent;

    public static void check(CommentParam param) {
        if (StringUtils.isBlank(param.getOrderNo())) {
            throw new DcArgumentException("充电订单号无效");
        }

//        if (StringUtils.isBlank(param.getSiteId())) {
//            throw new DcArgumentException("场站ID无效");
//        }

//        if (StringUtils.isBlank(param.getCusContent())) {
//            throw new DcArgumentException("请填写有效评论内容");
//        }

        if (null == param.getLevel()) {
            throw new DcArgumentException("请选择满意程度");
        }
    }
}
