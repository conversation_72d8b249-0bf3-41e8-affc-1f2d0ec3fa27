package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取日充电时长列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjDailyChargingDurationParam extends BaseListParam {

    @Schema(description = "省份名称")
    @JsonInclude(Include.NON_NULL)
    private String provinceName;

    @Schema(description = "城市名称")
    @JsonInclude(Include.NON_NULL)
    private String cityName;

    @Schema(description = "区域名称")
    @JsonInclude(Include.NON_NULL)
    private String areaName;

    @Schema(description = "省份全称")
    @JsonInclude(Include.NON_NULL)
    private String provinceFullName;

    @Schema(description = "城市全称")
    @JsonInclude(Include.NON_NULL)
    private String cityFullName;

    @Schema(description = "区域全称")
    @JsonInclude(Include.NON_NULL)
    private String areaFullName;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
