package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.List;

/**
 * @ClassName： EvseBundleListRequest
 * @Description: 升级包查询请求
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/17 15:19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EvseBundleListRequest extends BaseRequest {

    private List<String> evseIds;

    @ApiModelProperty(value = "排序方式 1：按新增时间倒序 2：按目标版本号顺序")
    private Integer sortType;


}
