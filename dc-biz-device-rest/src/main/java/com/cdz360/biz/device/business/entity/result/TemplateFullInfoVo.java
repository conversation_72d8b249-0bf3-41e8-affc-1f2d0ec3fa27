package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.business.constant.TemplateCalculateTypeEnum;
import com.cdz360.biz.device.business.constant.TemplateCalculateUnit;
import com.cdz360.biz.device.business.constant.TemplateChargingTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * 计费模板信息（包括当前时间的价格）
 *
 * <AUTHOR>
 * @date Create on 2019/05/30 19:02
 */
@Data
public class TemplateFullInfoVo implements Serializable {

    /**
     * 计费模板ID
     */
    private Long id;
    /**
     * 计费模板编号**唯一标识**
     */
    private String code;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 模板名称
     */
    private String name;
    /**
     * 代理商ID
     */
    private Long commercialId;
    /**
     * 充电计费单位{@link TemplateCalculateUnit}
     */
    private Integer calculateUnit;
    /**
     * 充电计费方式{@link TemplateCalculateTypeEnum}
     */
    private Integer calculateType;
    /**
     * 充电收费方式{@link TemplateChargingTypeEnum}
     */
    private Integer chargingType;
    /**
     * 当前价格：当前充电费价格+当前服务费价格
     */
    private BigDecimal curChargePrice;

    /**
     * 当前价格：当前充电费价格+当前服务费价格,描述加上单位
     */
    private String curChargePriceDesc;

    /**
     * 创建时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 免费充电标识
     */
    private Integer freeChargeFlag;
    /**
     * 计费子模板列表
     */
    private List<SubTemplateInfoVo> subTemplateList;

    /**
     * 计费模版是否上一级下发
     */
    private Boolean isSuperior;
}
