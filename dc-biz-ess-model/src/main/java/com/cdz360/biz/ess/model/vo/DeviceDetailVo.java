package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.es.type.EquipCfgStatus;
import com.cdz360.base.model.es.type.EssCfgStrategy;
import com.cdz360.base.model.es.vo.EssInOutStrategyItem;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "设备详细信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeviceDetailVo extends DeviceBase {

    @Schema(description = "DTU设备序列号")
    @JsonInclude(Include.NON_NULL)
    private String dtuSerialNo;

    @Schema(description = "配置信息状态: 0,未知;1,已下发;2,下发中;3,下发失败")
    private EquipCfgStatus cfgStatus;

    // monitor
    // (电池1SOC * 电池1容量 + 电池2SOC * 电池2容量) / (电池1容量 + 电池2容量)
    @Schema(description = "当前SOC(%)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 电池
    // 1: 逆变器 <-- 电池
    // 2. 逆变器 --> 电池
    @Schema(description = "充放电状态: 0(不充不放);1(放电);2(充电)")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 光伏
    // 1: 逆变器 <-- 光伏
    // 2. 逆变器 --> 光伏
    @Schema(description = "光伏状态: 0(待机);1(发电)")
    @JsonInclude(Include.NON_NULL)
    private Integer pvStatus;

    @Schema(description = "逆变器状态: 0(待机);1(就绪);2(离网);3(并网)")
    @JsonInclude(Include.NON_NULL)
    private Integer invStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 负载
    // 1: 逆变器 <-- 负载
    // 2. 逆变器 --> 负载
    @Schema(description = "负载状态: 0(不耗不供);1(供电);2(耗电)")
    @JsonInclude(Include.NON_NULL)
    private Integer loadStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 电网
    // 1: 逆变器 <-- 电网
    // 2. 逆变器 --> 电网
    @Schema(description = "电网状态: 0(不购不馈);1(购电);2(馈电)")
    @JsonInclude(Include.NON_NULL)
    private Integer gridStatus;

    // data summary
    @Schema(description = "今天充放电信息")
    @JsonInclude(Include.NON_NULL)
    private EssDataSummary todayDataSummary;

    // total summary
    @Schema(description = "总充放电信息")
    @JsonInclude(Include.NON_NULL)
    private EssDataSummary totalDataSummary;

    @Schema(description = "软件版本", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String softVersion;

    // 电网信息
    @Schema(description = "电网有功功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal gridActivePower;

    // 负载信息
    @Schema(description = "负载有功功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadActivePower;

    // 逆变器信息
    @Schema(description = "逆变器有功功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal invActivePower;

    // 光伏信息
    @Schema(description = "光伏功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pvPower;

    // 电池信息
    @Schema(description = "电池功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batPower;

//    @Schema(description = "电池主动充放电功率,单位: kW")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal batActivePower;

    // 是否可升级
//    @Schema(description = "是否可升级: true(可升级); false(不可升级)")
//    @JsonInclude(Include.NON_NULL)
//    private Boolean canUpgrade;

    // basic setting
//    @Schema(description = "设备组网类型")
//    @JsonInclude(Include.NON_NULL)
//    private Integer plantType;

//    @Schema(description = "平台授权码")
//    @JsonInclude(Include.NON_EMPTY)
//    private String authCode;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); "
        + "PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电); DISASTER_SPARE(灾备)")
    @NotNull(message = "strategy 不能为 null")
    private EssCfgStrategy strategy;

    @Schema(description = "其他时段工作模式")
    private EssCfgStrategy otherStrategy;

    @Schema(description = "重复周期: 当前协议支持周天~周六，使用位来存在对应信息")
    private Integer repeatCycle;

    @Schema(description = "生效开始时间(Unix时间戳)")
    private Long effectiveStartTime;

    @Schema(description = "生效结束时间(Unix时间戳)")
    private Long effectiveEndTime;

    @Schema(description = "最大功率值是否支持时段设置")
    private Boolean supportDivision;

    @Schema(description = "充放电时段")
    private List<EssInOutStrategyItem> inOutItems = new ArrayList<>();

    @Schema(description = "下发中的配置ID")
    @JsonInclude(Include.NON_NULL)
    private Long deliverCfgId;

    @Schema(description = "策略类型: SELF_USE(自发自用); PEAK_VALLEY_ARBITRAGE(峰谷套利); PEAK_SHARE(削峰填谷); TIMING_CHARGING_DISCHARGING(定时充放电); DISASTER_SPARE(灾备)")
    private EssCfgStrategy deliverStrategy;

    @Schema(description = "(下发)其他时段工作模式")
    private EssCfgStrategy deliverOtherStrategy;

    @Schema(description = "(下发)重复周期: 当前协议支持周天~周六，使用位来存在对应信息")
    private Integer deliverRepeatCycle;

    @Schema(description = "(下发)生效开始时间(Unix时间戳)")
    private Long deliverEffectiveStartTime;

    @Schema(description = "(下发)生效结束时间(Unix时间戳)")
    private Long deliverEffectiveEndTime;

    @Schema(description = "下发中的充放电时段")
    @JsonInclude(Include.NON_NULL)
    private List<EssInOutStrategyItem> deliverInOutItems = new ArrayList<>();

    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "下发时间")
    private LocalDateTime deliverCreateTime;
}
