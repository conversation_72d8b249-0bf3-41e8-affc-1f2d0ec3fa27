package com.cdz360.biz.tj.ds.rw.kc.ds;

import com.cdz360.biz.tj.ds.rw.kc.mapper.TjCompetitorSiteRwMapper;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjCompetitorSiteRwDs {

    @Autowired
    private TjCompetitorSiteRwMapper tjCompetitorSiteRwMapper;

    public TjCompetitorSitePo getById(Long id, boolean lock) {
        return this.tjCompetitorSiteRwMapper.getById(id, lock);
    }

    public boolean insertTjCompetitorSite(TjCompetitorSitePo tjCompetitorSitePo) {
        return this.tjCompetitorSiteRwMapper.insertTjCompetitorSite(tjCompetitorSitePo) > 0;
    }

    public boolean updateTjCompetitorSite(TjCompetitorSitePo tjCompetitorSitePo) {
        return this.tjCompetitorSiteRwMapper.updateTjCompetitorSite(tjCompetitorSitePo) > 0;
    }

    public int batchUpset(List<TjCompetitorSitePo> sitePoList) {
        return this.tjCompetitorSiteRwMapper.batchUpset(sitePoList);
    }

}

