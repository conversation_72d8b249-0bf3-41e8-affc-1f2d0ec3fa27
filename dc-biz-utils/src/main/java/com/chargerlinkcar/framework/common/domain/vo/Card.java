package com.chargerlinkcar.framework.common.domain.vo;

import com.cdz360.base.model.charge.type.SettlementType;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import lombok.Data;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Card implements Serializable {
    private static final long serialVersionUID = -2846586772761888157L;
    /**
     * 自动增长列,卡片ID
     */
    private Long id;

    /**
     * 卡片号码(逻辑卡号)
     */
    private String cardNo;

    /**
     * 卡片类型0在线卡1离线卡
     */
    private Integer cardType;

    /**
     * 卡片芯片号(物理卡号)
     **/
    private String cardChipNo;

    /**
     * 卡片渠道-发卡方
     **/
    private String cardChannel;

    /**
     * 卡片面值（分）
     */
    private Long cardDenomination;

    /**
     * 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     */
    private String cardStatus;

    /**
     * 卡片激活日期
     */
    private Date cardActivationDate;

    /**
     * 创建时间
     */
    private Date cardCreateDate;

    /**
     * 修改时间
     */
    private Date cardUpdateDate;

    /**
     * 企业ID
     */
    private Long corpId;

    /**
     * 商户Id
     */
    private Long commId;

    /**
     * 商户idChain
     */
    private String idChain;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户名
     */
    private String userName;

    /**
     * 状态（1活跃，0休眠）
     **/
    private Integer status;

    /**
     * 0:其他,1:套餐卡, 2:月卡,3:离线卡;4-鉴权卡
     */
    private Integer isPackage;
    /**
     * 紧急充电卡集团客户id
     */
//    private Long merchantId;
    /**
     * 卡片赠送金余额
     */
    private Long cardGiftAmount;
    /**
     * 月卡或年卡的生效时间
     */
    private Date takeEffectTime;
    /**
     * 月卡或年卡的失效时间
     */
    private Date pastDueTime;
    /**
     * 月卡或年卡充电次数
     */
    private Integer chargerTimes;
    /**
     * 有效标志1有效0无效
     */
    private String yxBz;

    /**
     * 鉴权卡所属站点（多个站点用‘，’分割）
     */
    private String stations;
    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 卡读写秘钥
     */
    private String cardKey;


    /**
     * 备注
     */
    private String remark;

    private String cardActivationCode;

    private String blocUserName;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 车辆自编号
     */
    private String carNum;

    /**
     * 车队名称
     */
    private String carDepart;

    /**
     * 线路
     */
    private String lineNum;
    /**
     * 卡对应的授信关系ID
     */
    private Long rBlocUserId;
    /**
     * 卡对应的企业客户ID
     */
    private Long blocUserId;

    /**
     * 兼容充值卡，否0，是1
     */
    private Integer deposit;

    /**
     * 语言，null表示非海外版
     */
    private Locale locale;

    /**
     * 参数检查
     */
    public void paramCheck() {
        RegularExpressionUtil.carNumCheck(this.getCarNum());
        RegularExpressionUtil.carDepartCheck(this.getCarDepart());
        RegularExpressionUtil.lineNumCheck(this.getLineNum());
    }

    /**
     * 海外版，参数检查
     */
    public void essParamCheck() {
        RegularExpressionUtil.carNumEssCheck(this.getCarNum());
        RegularExpressionUtil.carDepartEssCheck(this.getCarDepart());
        RegularExpressionUtil.lineNumEssCheck(this.getLineNum());
    }

    /**
     * 鉴权卡所属站点
     */
    private List<String> siteList;

    /**
     * 企业结算方式
     */
    @Schema(description = "企业结算方式 null 表示不是企业账户")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private SettlementType corpSettlementType;

    /**
     * 是否开启本地鉴权
     */
    private Boolean isLocalAuth;

    /**
     * 本地鉴权所属站点
     */
    private List<String> localAuthSiteList;
}
