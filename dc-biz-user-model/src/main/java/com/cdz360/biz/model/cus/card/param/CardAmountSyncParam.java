package com.cdz360.biz.model.cus.card.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * CardAmountSyncParam
 *
 * @since 7/20/2021 10:48 AM
 * <AUTHOR>
 */

@Data
@Accessors(chain = true)
@Schema(description = "查询")
public class CardAmountSyncParam {
    @Schema(description = "逻辑卡号")
    private String cardNo;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeFrom;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date timeTo;

    private Integer size = 10;
    private Long page = 1L;

    private Long index;
}