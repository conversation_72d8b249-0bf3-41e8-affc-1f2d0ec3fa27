<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyChargeAreaPileRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="areaId" jdbcType="BIGINT" property="areaId"/>
    <result column="evseModelId" jdbcType="BIGINT" property="evseModelId"/>
    <result column="evseCount" jdbcType="INTEGER" property="evseCount"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO">
    select * from t_tj_survey_charge_area_pile where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveyChargeAreaPile"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo">
    insert into t_tj_survey_charge_area_pile (`areaId`,`evseModelId`, `evseCount`, `enable`, `createTime`, `updateTime`)
    values (#{areaId}, #{evseModelId}, #{evseCount},
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveyChargeAreaPile" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo">
    update t_tj_survey_charge_area_pile set
    <if test="evseModelId != null">
      evseModelId = #{evseModelId},
    </if>
    <if test="evseCount != null">
      evseCount = #{evseCount},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveyChargeAreaPileByAreaId">
    update t_tj_survey_charge_area_pile set
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where areaId = #{areaId}
  </update>

  <update id="disableTjSurveyChargeAreaPileBySurveyNo">
    update t_tj_survey_charge_area_pile t1 left join t_tj_survey_charge_area t2
    on t1.areaId =t2.id
    set t1.enable = false,
    t1.updateTime = now()
    where t2.surveyNo = #{surveyNo}
  </update>


</mapper>

