<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.WhiteCardEvseMapper">
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.WhiteCardEvse">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="whiteCardNo" jdbcType="VARCHAR" property="whiteCardNo"/>
        <result column="evseId" jdbcType="VARCHAR" property="evseId"/>
        <result column="passWord" jdbcType="VARCHAR" property="passWord"/>
        <result column="passWordTmp" jdbcType="VARCHAR" property="passWordTmp"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="sendStatus" jdbcType="INTEGER" property="sendStatus"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
        whiteCardNo,
        evseId,
        passWord,
        passWordTmp,
        card_no,
        sendStatus
    </sql>

    <insert id="batchInsert">
        insert into t_r_white_card_evse(
        whiteCardNo,
        evseId,
        passWord,
        passWordTmp,
        card_no,
        sendStatus)
        <foreach collection="whiteCardEvses" open="values" close=""
                 separator="," item="item">
            (#{item.whiteCardNo}, #{item.evseId},
            #{item.passWord},#{item.passWordTmp},#{item.cardNo},#{item.sendStatus})
        </foreach>
    </insert>

    <update id="updateByEvseIdAndCardNo">
        UPDATE t_r_white_card_evse
        <set>
            passWordTmp=#{whiteCardEvse.passWordTmp},
            sendStatus=#{whiteCardEvse.sendStatus}
        </set>
        <where>
            card_no = #{whiteCardEvse.cardNo} and evseId = #{whiteCardEvse.evseId}
        </where>
    </update>

    <select id="selectByEvseAndCardNo" resultType="java.lang.Integer">
        select count(*)
        from t_r_white_card_evse
        where whiteCardNo = #{cardNo}
          and evseId = #{evseId}
          and enable = true
    </select>


    <update id="updateStatusOnEvseIdAndStatus">
        update t_r_white_card_evse
        set `password`= if(1 = sendStatus - #{statusDelta}, passWordTmp, `passWord`),
            sendStatus = sendStatus - #{statusDelta}
        where evseId=#{evseId} and (sendStatus = 3 or sendStatus = 6)
    </update>

    <select id="queryByCondition" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from t_r_white_card_evse
        <where>
            enable = true
            <if test="evseId != null and evseId != ''">
                and evseId = #{evseId}
            </if>
            <if test="whiteCardNo != null and whiteCardNo != ''">
                and whiteCardNo = #{whiteCardNo}
            </if>
            <if test="sendStatus != null and sendStatus > 0">
                and sendStatus = #{sendStatus}
            </if>
            <if test="sendStatusList != null and sendStatusList.size() > 0">
                and sendStatus in
                <foreach collection="sendStatusList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
</mapper>