package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyOperationIncomeRoMapper {

    TjSurveyOperationIncomePo getById(@Param("id") Long id);

    TjSurveyOperationIncomePo findTjSurveyOperationIncomeBySurveyNo(@Param("surveyNo") String surveyNo);

}

