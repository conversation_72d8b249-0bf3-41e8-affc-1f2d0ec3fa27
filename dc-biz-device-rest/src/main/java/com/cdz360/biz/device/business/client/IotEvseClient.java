package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.biz.device.business.constant.EvseDebugMethod;
import com.cdz360.biz.device.business.entity.request.ListEvseRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeRequest;
import com.cdz360.biz.device.business.entity.result.EvseVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_WORKER)
public interface IotEvseClient {


//    @PostMapping(value = "/iot/biz/evse/upgrade")
//    BaseResponse upgradeEvse(@RequestBody UpgradeRequest upgradeRequest);

    @PostMapping(value = "/iot/biz/evse/upgrade/listBySiteId")
    ListResponse<EvseVo> listBySiteIdForUpgrade(@RequestBody ListEvseRequest listEvseRequest);


    @PostMapping(value = "/iot/biz/evse/debug")
    BaseResponse evseDebug(@RequestParam(value = "evseNo") String evseNo,
                           @RequestParam(value = "debugMethod") EvseDebugMethod debugMethod,
                           @RequestParam(value = "msg", required = false) String msg);
}
