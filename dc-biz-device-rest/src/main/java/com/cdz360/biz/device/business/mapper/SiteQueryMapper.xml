<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.SiteQueryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.Site">
        <id column="id" property="id"/>
        <result column="id_no" property="idNo"/>
        <result column="name" property="name"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="geohash" property="geohash"/>
        <result column="address" property="address"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="area" property="area"/>
        <result column="phone" property="phone"/>
        <result column="status" property="status"/>
        <result column="operate_id" property="operateId"/>
        <result column="operate_name" property="operateName"/>
        <result column="merchant_id" property="merchantId"/>
        <result column="contacts" property="contacts"/>
        <result column="contacts_phone" property="contactsPhone"/>
        <result column="remark" property="remark"/>
        <result column="type" property="type"/>
        <result column="service_workday_time" property="serviceWorkdayTime"/>
        <result column="service_holiday_time" property="serviceHolidayTime"/>
        <result column="park" property="park"/>
        <result column="park_fee" property="parkFee"/>
        <result column="appoint" property="appoint"/>
        <result column="scope" property="scope"/>
        <result column="online_date" property="onlineDate"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_time" property="createTime"/>
        <result column="images" property="images"/>
        <result column="brand_ids" property="brandIds"/>
        <result column="bizType" property="bizType"/>
        <result column="bizName" property="bizName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        id_no AS idNo,
        `name`,
        longitude,
        latitude,
        geohash,
        address,
        province,
        city,
        area,
        phone,
        status,
        operate_id AS operateId,
        operate_name AS operateName,
        merchant_id AS merchantId,
        contacts,
        contacts_phone AS contactsPhone,
        remark,
        `type`,
        service_workday_time AS serviceWorkdayTime,
        service_holiday_time AS serviceHolidayTime,
        park,
        park_fee AS parkFee,
        appoint
        scope,
        online_date AS onlineDate,
        update_time AS updateTime,
        create_time AS createTime,
        images,
        brand_ids AS brandIds,
        bizType,
        bizName
    </sql>

    <!--站点简单信息查询结果-->
    <sql id="select_simple_column_list">
        site.id AS siteId,
        site.`name` AS siteName,
        site.siteNo,
      	site.longitude,
        site.latitude,
        site.address AS address,
        site.province AS province,
        site.city AS city,
        site.`status`,
        site.operate_id AS operateId,
        comm.comm_name AS operateName,
        comm.short_name AS operateShortName,
        site.`type`,
        site.service_workday_time AS serviceWorkdayTime,
        site.service_holiday_time AS serviceHolidayTime,
        site.scope,
        site.online_date AS onlineDate,
        site.fee_description AS feeDescription,
        site.fee_min AS feeMin,
        site.fee_max AS feeMax,
        site.invoiced_valid AS invoicedValid,
        site.template_id AS templateId,
        site.template_name AS templateName,
        site.phone AS phone,
        site.contacts_phone AS contactsPhone,
        site.images AS images,
        site.bizType,
        site.bizName
    </sql>

    <!--获取站点简单信息列表-->
    <select id="getSiteSimpleList" resultType="com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>
        FROM t_site site
        left join t_r_commercial comm on comm.id = site.operate_id
        <where>
	        <if test="statuslist !=null and statuslist.size()>0">
		        site.status IN
		        <foreach collection="statuslist" item="item" index="index"  separator="," open="(" close=")">
			        #{item}
		        </foreach>
	        </if>
            <if test="siteId != null">
                AND site.id = #{siteId}
            </if>
            <if test="commercialId != null">
                AND site.operate_id = #{commercialId}
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
                and comm.idChain like CONCAT(#{commIdChain}, '%')
            </if>
            <if test="commercialIdList != null and commercialIdList.size()>0">
                AND site.operate_id IN
                <foreach collection="commercialIdList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="provinceCode != null">
                AND site.province = #{provinceCode}
            </if>
	        <if test="invoicedValid !=null">
		       and site.invoiced_valid =#{invoicedValid}
	        </if>
            <if test="cityCode != null">
                AND site.city = #{cityCode}
            </if>
            <if test="type != null">
                AND site.`type` = #{type}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (
                LOCATE(#{keywords}, CONCAT(site.address,site.`name`,site.siteNo))
                )
            </if>
            <if test="siteName != null and siteName != ''">
                AND LOCATE(#{siteName}, site.name)
            </if>
            <if test="address != null and address != ''">
                AND LOCATE(#{address}, site.address)
            </if>
            <if test="lastQueryTime != null ">
                AND <![CDATA[ site.update_time >= #{lastQueryTime} ]]>
            </if>
        </where>
        ORDER BY site.create_time DESC
    </select>


    <!--获取场站列表-->
    <select id="listSite" resultType="com.cdz360.biz.device.business.entity.po.Site">
        select * from t_site where 1=1
        <if test="templateId!=null">
            and template_id = #{templateId}
        </if>
        <choose>
            <when test="start != null and size != null">
                limit #{start},#{size}
            </when>
            <when test="size != null">
                limit #{size}
            </when>
        </choose>
        <if test="lock == true">
            for update
        </if>
    </select>

    <!--获取场站-->
    <select id="getSiteBySiteId" resultType="com.cdz360.biz.device.business.entity.po.Site">
        select * from t_site
        <where>
            id=#{siteId}
        </where>
    </select>
</mapper>
