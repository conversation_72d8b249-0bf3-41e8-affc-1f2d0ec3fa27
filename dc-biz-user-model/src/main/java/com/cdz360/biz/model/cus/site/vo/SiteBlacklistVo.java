package com.cdz360.biz.model.cus.site.vo;


import com.cdz360.biz.model.cus.site.po.SiteBlacklistPo;
import com.cdz360.biz.model.site.type.OvertimeParkingChargeUserType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站黑名单查看")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class SiteBlacklistVo extends SiteBlacklistPo {

    @Schema(description = "用户名称")
    private String cusName;

    @Schema(description = "用户手机号")
    private String cusPhone;

    @Schema(description = "用户来源渠道")
    private Integer sourceId;

    @Schema(description = "用户可用余额")
    private BigDecimal balance;

    @Schema(description = "用户注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date regTime;

    /////////////////////// 客户充电信息 //////////////////////
    @Schema(description = "订单数")
    private Long orderNum;

    @Schema(description = "电量")
    private BigDecimal elec;

    @Schema(description = "电费")
    private BigDecimal elecFee;

    @Schema(description = "服务费")
    private BigDecimal servFee;

    ////////////////// 停充超时收费信息 //////////////////////
    @Schema(description = "停充超时收费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean overtimeParkFee;

//    @Schema(description = "关联订单号")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private String overtimeParkOrderNo;

    @Schema(description = "充电结束时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date chargeStopTime;

    @Schema(description = "限免时长")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer overtimeParkFreeTime;

    @Schema(description = "限免次数")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer overtimeParkFreeNum;

    @Schema(description = "计费时间开始")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date overtimeParkCalFromTime;

    @Schema(description = "计费时间结束")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private Date overtimeParkCalToTime;

    @Schema(description = "计费标准")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal overtimeParkingPrice;

    @Schema(description = "停充超时费")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal overtimeParkingFee;

    @Schema(description = "缴费状态：0未支付，100已支付")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer overtimePayStatus;


    ///////////////// 场站信息 ///////////////
    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;


    @Schema(description = "企业名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

//    @Schema(description = "授信名称")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String corpUserName;

    @Schema(description = "企业手机号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpPhone;
//    private String userName;
//    private String userPhone;
//    @Schema(description = "商户会员名称")
//    @JsonInclude(JsonInclude.Include.NON_EMPTY)
//    private String commUserName;

    @Schema(description = "用户是否需付费")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private OvertimeParkingChargeUserType overtimeParkingChargeUserType;

    @Schema(description = "未缴费次数")
    private Integer unPayParkingCount;

    @Schema(description = "未缴费总超时费")
    private BigDecimal unPayParkingFee;
}
