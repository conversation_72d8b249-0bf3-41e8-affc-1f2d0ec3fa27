package com.cdz360.biz.model.tj.kc.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "投建区域")
public class TjAreaPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "aid 不能为 null")
    private Long aid;

    @ApiModelProperty(value = "场站组id(t_site_group.gid)")
    @NotNull(message = "gid 不能为 null")
    @Size(max = 10, message = "gid 长度不能超过 10")
    private String gid;

    @ApiModelProperty(value = "用户ID(sys_user.id)")
    @NotNull(message = "uid 不能为 null")
    private Long uid;

    @ApiModelProperty(value = "区域名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 128, message = "name 长度不能超过 128")
    private String name;

    @ApiModelProperty(value = "颜色值(HEX格式)")
    @NotNull(message = "color 不能为 null")
    @Size(max = 6, message = "color 长度不能超过 6")
    private String color;

    @ApiModelProperty(value = "形状: 圆(1)、矩形(2)、多边形(3)、行政区(4)")
    @NotNull(message = "shape 不能为 null")
    private Integer shape;

    @ApiModelProperty(value = "圆半径，shape为圆赋值，其他情况为0(单位: 米)")
    @NotNull(message = "shape 不能为 null")
    private Integer radius;

    @Schema(description = "圆表示中心点[[[经度,纬度]]];其他表示点集合[[经度,纬度],[],...,[]]")
    private List<List<List<BigDecimal>>> paths;

    @Schema(description = "行政区编号(','分割)")
    @JsonInclude(Include.NON_NULL)
    private String adcode;

    @Schema(description = "true有效;false删除")
    @JsonInclude(Include.NON_NULL)
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
}

