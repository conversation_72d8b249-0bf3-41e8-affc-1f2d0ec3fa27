package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.geo.ds.CityRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.DistrictRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.ProvinceRoDs;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.geo.po.ProvincePo;
import com.cdz360.biz.model.geo.vo.CityTreeVo;
import com.cdz360.biz.model.geo.vo.ProvinceTreeVo;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportItem;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationCoefficientRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjDailyChargingDurationCoefficientRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjDailyChargingDurationRwDs;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjDailyChargingDurationService {

    @Autowired
    private TjDailyChargingDurationRoDs tjDailyChargingDurationRoDs;

    @Autowired
    private TjDailyChargingDurationRwDs tjDailyChargingDurationRwDs;

    @Autowired
    private TjDailyChargingDurationCoefficientRoDs tjDailyChargingDurationCoefficientRoDs;

    @Autowired
    private TjDailyChargingDurationCoefficientRwDs tjDailyChargingDurationCoefficientRwDs;

    @Autowired
    private ProvinceRoDs provinceRoDs;

    @Autowired
    private CityRoDs cityRoDs;

    @Autowired
    private DistrictRoDs districtRoDs;
    public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(
        ListTjDailyChargingDurationParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }
        List<TjDailyChargingDurationPo> result = tjDailyChargingDurationRoDs.findTjDailyChargingDuration(param);
        if (CollectionUtils.isEmpty(result)) {
            param.setAreaFullName(null);
            result = tjDailyChargingDurationRoDs.findTjDailyChargingDuration(param);
        }
        return Mono.just(RestUtils.buildListResponse(result,
            Boolean.TRUE.equals(param.getTotal()) ? tjDailyChargingDurationRoDs.countTjDailyChargingDuration(param) : 0L));
    }

    public Mono<TjDailyChargingDurationPo> getTjDailyChargingDurationById(Long id) {
        TjDailyChargingDurationPo tjDailyChargingDurationPo = tjDailyChargingDurationRoDs.getById(id);
        IotAssert.isNotNull(tjDailyChargingDurationPo, "日充电时长唯一ID无效");
        return Mono.just(tjDailyChargingDurationPo);
    }

    public Mono<TjDailyChargingDurationPo> saveTjDailyChargingDuration(TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        List<ProvincePo> provincePoList = provinceRoDs.listProvince();
        List<CityPo> cityPoList = cityRoDs.listCity();
        Map<String, ProvincePo> provincePoMap = provincePoList.stream().collect(Collectors.toMap(ProvincePo::getCode, o -> o));
        Map<String, CityPo> cityPoMap = cityPoList.stream().collect(Collectors.toMap(CityPo::getCode, o -> o));

        if (null == tjDailyChargingDurationPo.getId()) {
            tjDailyChargingDurationPo.setProvinceName(provincePoMap.get(tjDailyChargingDurationPo.getProvince()).getName());
            tjDailyChargingDurationPo.setProvinceFullName(provincePoMap.get(tjDailyChargingDurationPo.getProvince()).getFullname());
            tjDailyChargingDurationPo.setCityName(cityPoMap.get(tjDailyChargingDurationPo.getCity()).getName());
            tjDailyChargingDurationPo.setCityFullName(cityPoMap.get(tjDailyChargingDurationPo.getCity()).getFullname());
            if (StringUtils.isNotBlank(tjDailyChargingDurationPo.getArea())) {
                DistrictPo districtPo = districtRoDs.getDistrict(tjDailyChargingDurationPo.getArea());
                tjDailyChargingDurationPo.setAreaName(districtPo.getName());
                tjDailyChargingDurationPo.setAreaFullName(districtPo.getFullname());
            }
            ListTjDailyChargingDurationParam param = new ListTjDailyChargingDurationParam();
            param.setProvinceName(tjDailyChargingDurationPo.getProvinceName())
                .setCityName(tjDailyChargingDurationPo.getCityName())
                .setAreaName(StringUtils.isEmpty(tjDailyChargingDurationPo.getAreaName()) ? null : tjDailyChargingDurationPo.getAreaName());
            long count = tjDailyChargingDurationRoDs.countTjDailyChargingDuration(param);
            if (count != 0) {
                throw new RuntimeException("区域不能重复录入");
            }
            tjDailyChargingDurationRwDs.insertTjDailyChargingDuration(tjDailyChargingDurationPo);
        } else {
            tjDailyChargingDurationPo.setProvinceName(provincePoMap.get(tjDailyChargingDurationPo.getProvince()).getName());
            tjDailyChargingDurationPo.setProvinceFullName(provincePoMap.get(tjDailyChargingDurationPo.getProvince()).getFullname());
            tjDailyChargingDurationPo.setCityName(cityPoMap.get(tjDailyChargingDurationPo.getCity()).getName());
            tjDailyChargingDurationPo.setCityFullName(cityPoMap.get(tjDailyChargingDurationPo.getCity()).getFullname());
            if (StringUtils.isNotBlank(tjDailyChargingDurationPo.getArea())) {
                DistrictPo districtPo = districtRoDs.getDistrict(tjDailyChargingDurationPo.getArea());
                tjDailyChargingDurationPo.setAreaName(districtPo.getName());
                tjDailyChargingDurationPo.setAreaFullName(districtPo.getFullname());
            }
            ListTjDailyChargingDurationParam param = new ListTjDailyChargingDurationParam();
            param.setProvinceName(tjDailyChargingDurationPo.getProvinceName())
                .setCityName(tjDailyChargingDurationPo.getCityName())
                .setAreaName(StringUtils.isEmpty(tjDailyChargingDurationPo.getAreaName()) ? null : tjDailyChargingDurationPo.getAreaName());
            List<TjDailyChargingDurationPo> tjDailyChargingDurationPoList = tjDailyChargingDurationRoDs.findTjDailyChargingDuration(param);
            if (CollectionUtils.isNotEmpty(tjDailyChargingDurationPoList)) {
                if (tjDailyChargingDurationPo.getId() != tjDailyChargingDurationPoList.get(0).getId()) {
                    throw new RuntimeException("区域不能重复录入");
                }
            }
            tjDailyChargingDurationRwDs.updateTjDailyChargingDuration(tjDailyChargingDurationPo);
        }
        return Mono.just(tjDailyChargingDurationPo);
    }

    @Transactional
    public Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(List<TjDailyChargingDurationImportItem> dataList) {
        AtomicReference<Integer> successTotal = new AtomicReference<>(0);
        List<ProvinceTreeVo> provinceTreeVoList = provinceRoDs.getProvinceTree();
        Map<String, ProvinceTreeVo> provinceMap = provinceTreeVoList.stream().collect(Collectors.toMap(ProvinceTreeVo::getName, value -> value));
        dataList.forEach(e -> {
            ListTjDailyChargingDurationParam listTjDailyChargingDurationParam = new ListTjDailyChargingDurationParam();
            listTjDailyChargingDurationParam.setProvinceName(e.getProvinceName());
            listTjDailyChargingDurationParam.setCityName(e.getCityName());
            listTjDailyChargingDurationParam.setAreaName(e.getAreaName());
            TjDailyChargingDurationPo tjDailyChargingDurationPo = new TjDailyChargingDurationPo();
            ProvinceTreeVo provinceTreeVo = provinceMap.get(e.getProvinceName());
            List<CityTreeVo> cityTreeVoList = provinceTreeVo.getChildren();
            Map<String, CityTreeVo> cityMap = cityTreeVoList.stream().collect(Collectors.toMap(CityTreeVo::getName, value -> value));
            CityTreeVo cityTreeVo = cityMap.get(e.getCityName());
            List<DistrictPo> districtPoList = cityTreeVo.getChildren();
            Map<String, DistrictPo> districtMap = districtPoList.stream().collect(Collectors.toMap(DistrictPo::getName, value -> value));
            DistrictPo districtPo = districtMap.get(e.getAreaName());
            tjDailyChargingDurationPo.setProvince(provinceTreeVo.getCode())
                .setCity(cityTreeVo.getCode())
                .setArea(districtPo != null ? districtPo.getCode() : null)
                .setProvinceName(e.getProvinceName())
                .setCityName(e.getCityName())
                .setAreaName(e.getAreaName())
                .setFirstYearThirdMonth(new BigDecimal(e.getFirstYearThirdMonth()))
                .setFirstYearLastNinthMonth(new BigDecimal(e.getFirstYearLastNinthMonth()))
                .setSecondYear(new BigDecimal(e.getSecondYear()))
                .setThirdYear(new BigDecimal(e.getThirdYear()))
                .setFourthYear(new BigDecimal(e.getFourthYear()))
                .setFifthYear(new BigDecimal(e.getFifthYear()))
                .setSixthYear(new BigDecimal(e.getSixthYear()))
                .setSeventhYear(new BigDecimal(e.getSeventhYear()))
                .setEighthYear(new BigDecimal(e.getEighthYear()))
                .setNinthYear(new BigDecimal(e.getNinthYear()))
                .setTenthYear(new BigDecimal(e.getTenthYear()));
            ListTjDailyChargingDurationParam param = new ListTjDailyChargingDurationParam();
            param.setProvinceName(e.getProvinceName())
                .setCityName(e.getCityName())
                .setAreaName(StringUtils.isEmpty(e.getAreaName()) ? null : e.getAreaName());
            long count = tjDailyChargingDurationRoDs.countTjDailyChargingDuration(param);
            if (count == 0 && tjDailyChargingDurationRwDs.insertTjDailyChargingDuration(tjDailyChargingDurationPo)) {
                successTotal.getAndSet(successTotal.get() + 1);
            }
        });
        ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo> importTjDailyChargingDurationVo = new ImportTjDailyChargingDurationVo<>();
        importTjDailyChargingDurationVo.setSuccessTotal(successTotal.get());
        return Mono.just(importTjDailyChargingDurationVo).map(RestUtils::buildObjectResponse);
    }

    public Mono<TjDailyChargingDurationPo> disableTjDailyChargingDuration(Long id) {
        TjDailyChargingDurationPo tjDailyChargingDurationPo = tjDailyChargingDurationRoDs.getById(id);
        IotAssert.isNotNull(tjDailyChargingDurationPo, "日充电时长唯一ID无效");

        if (Boolean.TRUE.equals(tjDailyChargingDurationPo.getEnable())) {
            boolean b = tjDailyChargingDurationRwDs.disableTjDailyChargingDuration(id);
            log.debug("删除日充电时长: {}", b);
        }

        return Mono.just(tjDailyChargingDurationPo);
    }

    public Mono<TjDailyChargingDurationCoefficientPo> findTjDailyChargingDurationCoefficient() {
        TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo = tjDailyChargingDurationCoefficientRoDs.findTjDailyChargingDurationCoefficient();
        IotAssert.isNotNull(tjDailyChargingDurationCoefficientPo, "日充电时长系数无效");
        return Mono.just(tjDailyChargingDurationCoefficientPo);
    }

    public Mono<TjDailyChargingDurationCoefficientPo> saveTjDailyChargingDurationCoefficient(TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
        if (null == tjDailyChargingDurationCoefficientPo.getId()) {
            throw new DcServiceException("日充电时长系数ID不能为空");
        } else {
            tjDailyChargingDurationCoefficientRwDs.updateTjDailyChargingDurationCoefficient(tjDailyChargingDurationCoefficientPo);
        }
        return Mono.just(tjDailyChargingDurationCoefficientPo);
    }

}
