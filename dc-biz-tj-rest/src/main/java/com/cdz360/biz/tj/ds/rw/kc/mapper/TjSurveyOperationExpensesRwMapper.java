package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyOperationExpensesRwMapper {

    TjSurveyOperationExpensesPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveyOperationExpenses(TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo);

    int updateTjSurveyOperationExpenses(TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo);

    int disableTjSurveyOperationExpensesBySurveyNo(String surveyNo);

}

