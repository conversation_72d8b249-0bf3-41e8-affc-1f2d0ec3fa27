package com.cdz360.biz.tj.ds.rw.kc.ds;

import com.cdz360.biz.tj.ds.rw.kc.mapper.TjCompetitorRwMapper;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjCompetitorRwDs {

    @Autowired
    private TjCompetitorRwMapper tjCompetitorRwMapper;

    public TjCompetitorPo getById(Long id, boolean lock) {
        return this.tjCompetitorRwMapper.getById(id, lock);
    }

    public boolean insertTjCompetitor(TjCompetitorPo tjCompetitorPo) {
        return this.tjCompetitorRwMapper.insertTjCompetitor(tjCompetitorPo) > 0;
    }

    public boolean updateTjCompetitor(TjCompetitorPo tjCompetitorPo) {
        return this.tjCompetitorRwMapper.updateTjCompetitor(tjCompetitorPo) > 0;
    }

    public boolean disableCompetitor(Long competitorId) {
        return this.tjCompetitorRwMapper.disableCompetitor(competitorId) > 0;
    }
}

