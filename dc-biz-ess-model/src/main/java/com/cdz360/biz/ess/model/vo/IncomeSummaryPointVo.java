package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "采样点数据")
@Data
@Accessors(chain = true)
public class IncomeSummaryPointVo {

    @Schema(description = "UNIX时间戳")
    @JsonInclude(Include.NON_NULL)
    private Long unixTime;

    @Schema(description = "数据(收益当前仅作汇总返回，不做单位换算)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal data;
}
