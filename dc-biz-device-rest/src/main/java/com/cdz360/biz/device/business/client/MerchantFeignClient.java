package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.po.Commercial;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * @Classname MerchantFeignClient
 * @Description 商户操作的FeignClient
 * @Date 2019/6/12 11:19
 * @Created by Rafael
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = HystrixMerchantClientFactory.class)
public interface MerchantFeignClient {

//    /**
//     * 根据商户ID获取商户ID及子商户ID集合
//     * @param commId
//     * @return 商户ID及子商户ID集合
//     */
//    @PostMapping("/api/merchant/getCommIdListByCommId")
//    ListResponse<Long> getCommIdListByCommId(@RequestParam("commId") Long commId);
    /**
     * 根据token获取商户
     * @param token
     * @return 商户
     */
    @GetMapping("/api/commercial/getCommercialByToken")
    ObjectResponse<Commercial> getCommercialByToken(@RequestParam("token") String token);
}