<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.BsChargerManageMapper">


    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.BsCharger">
        <id column="bc_id" property="bcId"/>
        <result column="box_code" property="boxCode"/>
        <result column="connector_id" property="connectorId"/>
        <result column="business_id" property="businessId"/>
        <result column="station_code" property="stationCode"/>
        <result column="current_type" property="currentType"/>
        <result column="qr_code" property="qrCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        bc_id AS bcId,
        box_code AS boxCode,
        connector_id AS connectorId,
        business_id AS businessId,
        station_code AS stationCode,
        current_type AS currentType,
        qr_code AS qrCode
    </sql>

</mapper>
