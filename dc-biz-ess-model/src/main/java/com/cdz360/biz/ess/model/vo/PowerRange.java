package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备功率范围")
@Data
@Accessors(chain = true)
public class PowerRange {

    @Schema(description = "最小功率(W)", minimum = "0")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal min;

    @Schema(description = "最大功率(W)", minimum = "0")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal max;
}
