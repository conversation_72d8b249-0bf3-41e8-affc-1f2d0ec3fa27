package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "发送邮件传参")
@Data
@Accessors(chain = true)
public class SendEmailDto {

    @Schema(description = "顶级商户ID")
    private Long topCommId;

    @Schema(description = "邮件地址")
    @JsonInclude(Include.NON_EMPTY)
    private String email;

    @Schema(description = "验证码")
    @JsonInclude(Include.NON_EMPTY)
    private String code;

    @Schema(description = "主题")
    @JsonInclude(Include.NON_EMPTY)
    private String subject;

    @Schema(description = "BCP 47 语言标签(后续用于语言区分)", required = true,
        externalDocs = @ExternalDocumentation(
            description = "BCP 47 Language Tags",
            url = "https://www.rfc-editor.org/info/bcp47"))
    private String language;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
