package com.cdz360.biz.device.business.util;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FeignResponseValidate {
    protected static final Logger logger = LoggerFactory.getLogger(FeignResponseValidate.class);

    public static void check(BaseResponse res) {
        if (res == null || res.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
            logger.warn("res = {}", res);
            throw new DcServiceException(res.getStatus(), res.getError());
        }
        if (res instanceof ObjectResponse) {
            ObjectResponse objRes = (ObjectResponse) res;
            if (((ObjectResponse) res).getData() == null) {
                logger.error("no data.... {}", objRes);
                throw new DcServerException("数据为空");
            }
        } else if (res instanceof ListResponse) {
            ListResponse objRes = (ListResponse) res;
            if (((ListResponse) res).getData() == null) {
                logger.error("no data.... {}", objRes);
                throw new DcServerException("数据为空");
            }
        }
    }
}
