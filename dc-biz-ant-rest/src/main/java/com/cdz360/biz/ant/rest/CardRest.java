package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.biz.ant.aop.CheckAuth;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.constant.Constant;
import com.cdz360.biz.ant.domain.ListResponseEvseList;
import com.cdz360.biz.ant.domain.UrgencyCardDetail;
import com.cdz360.biz.ant.domain.UrgencyCardEvse;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.request.ModifyCardParam;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.reactor.DataCoreDownloadFileFeignClient;
import com.cdz360.biz.ant.service.CardService;
import com.cdz360.biz.ant.service.CorpService;
import com.cdz360.biz.ant.service.LoginService;
import com.cdz360.biz.ant.service.iot.IotEvseCfgService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.download.param.DownloadApplyParam;
import com.cdz360.biz.model.download.type.DownloadFileType;
import com.cdz360.biz.model.download.type.DownloadFunctionType;
import com.cdz360.biz.model.iot.vo.LocalCard;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR> 卡片功能
 * @since 2018.11.22
 */
@Tag(name = "卡片操作相关接口", description = "卡片操作")
@Slf4j
@RestController
public class CardRest extends BaseController {

    @Autowired
    private CardService cardService;
    @Autowired
    private CorpService corpService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private LoginService loginService;

    @Autowired
    private AntUserFeignClient userFeignClient;

    @Autowired
    private IotEvseCfgService iotEvseCfgService;

    @Autowired
    private DataCoreDownloadFileFeignClient dataCoreDownloadFileFeignClient;

    private Map<String, String> cardStatusMap = Arrays.asList(CardStatus.values())
        .stream()
        .collect(Collectors.toMap(e -> e.getCode(), e -> e.getName()));

    /**
     * 根据条件查询鉴权卡
     *
     * @return
     */
    @RequestMapping("/api/card/queryCards")
    @CheckToken(check = "getToken4Aspect")
    public ListResponse<CardListdetailVO> queryCards(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @ModelAttribute CardSearchParam param
    ) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, true);
        param.setTopCommId(super.getCommercialSample2(request).getTopCommId())
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setIsPackage(Constant.IS_PACKAGED_AUTH);
        return cardService.queryCardsByPage(token, page, param);
    }

    /**
     * 根据条件导出鉴权卡
     *
     * @param request
     * @param exh
     * @param param
     * @return
     */
    @PostMapping("/api/card/exportOnlineCardList")
//    @ResponseBody
    @CheckToken(check = "getToken4Aspect")
    public Mono<ObjectResponse<ExcelPosition>> exportOnlineCardList(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody CardSearchParam param
    ) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
//        String token = getToken2(request);
//        BasePage page = getPage2(request, exh);
        param.setTopCommId(super.getCommercialSample2(request).getTopCommId())
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setIsPackage(Constant.IS_PACKAGED_AUTH);

        Locale locale = AntRestUtils.getLocale(request);
        // 获取到的是null就设置为null
        param.setLocale(locale);

        Long sysUid = AntRestUtils.getSysUid(request);
        DownloadApplyParam applyParam = new DownloadApplyParam();
        applyParam.setFileType(DownloadFileType.EXCEL_XLSX)
            .setUid(sysUid)
            .setExFileName("在线卡信息导出")
            .setFunctionMap(DownloadFunctionType.CARD_RECORD)
            .setReqParam(JsonUtils.toJsonString(param));
        return dataCoreDownloadFileFeignClient.downloadFileApply(applyParam);
//        return cardService.exportOnlineCardList(token, page, param);
    }

    /**
     * 根据条件查询离线卡
     *
     * @param beginTime  查询开始时间
     * @param endTime    查询结束时间
     * @param commId     所属商户
     * @param cardChipNo 物理卡号
     * @return
     */
    @Deprecated
    @RequestMapping("/api/card/offLineCardList")
    public ListResponse<CardListdetailVO> offLineCardList(
        ServerWebExchange exh,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "cardChipNo", required = false) String cardChipNo,
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "leaderCommId", required = false) Long leaderCommId,
        ServerHttpRequest request) {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, false);
        CardSearchParam cardSearchParam = new CardSearchParam();
        cardSearchParam.setBeginTime(beginTime).setEndTime(endTime).setKeyword(cardChipNo)
            .setTopCommId(super.getCommercialSample2(request).getTopCommId())
            .setCommId(commId).setLeaderCommId(leaderCommId)
            .setCommIdChain(AntRestUtils.getCommIdChain(request))
            .setIsPackage(Constant.IS_PACKAGED_OFF_LINE);
        return cardService.queryCardsByPage(token, page, cardSearchParam);
    }

    /**
     * 根据条件查询在线卡
     *
     * @param cardStatus 卡状态
     * @param beginTime  查询开始时间
     * @param endTime    查询结束时间
     * @param keyWord    卡号/卡名称 关键字
     * @param commId     所属商户
     * @return
     */
    @GetMapping("/api/card/onlineCardsList")
    public ListResponse<CardListdetailVO> onlineCardsList(
        ServerWebExchange exh,
        @RequestParam(value = "cardStatus", required = false) String cardStatus,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam(value = "cardType", required = false) Long cardType,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        ServerHttpRequest request) {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, false);

        return cardService.queryOnlineCardsByPage(token, page, cardStatus, beginTime, endTime,
            keyWord, cardType, commId, userId,
            super.getCommercialSample2(request).getTopCommId(),
            AntRestUtils.getCommIdChain(request));
    }

    /**
     * 海外版根据条件查询在线卡
     *
     * @param param
     * @return
     */
    @PostMapping("/api/card/commercial/onlineCardsList")
    public ListResponse<CardListdetailVO> essOnlineCardsList(
        ServerHttpRequest request,
        @RequestBody CardRequest param) {
        log.info("海外版条件查询在线卡, param = {}", param);

        param.setTopCommId(AntRestUtils.getTopCommId(request));
        param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return cardService.queryEssOnlineCardsByPage(param);
    }

    /**
     * 根据条件查询紧急充电卡 用于充电管理平台
     *
     * @return
     */
    @PostMapping("/api/card/urgencyCardsList")
    public ListResponse<CardListdetailVO> urgencyCardsList(
        ServerWebExchange exh,
        @RequestBody CardRequest cardRequest, ServerHttpRequest request) {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, true);
        cardRequest.setTopCommId(AntRestUtils.getTopCommId(request));
        //cardRequest.setCommId(super.getCommIdLong(request));
        cardRequest.setCommIdChain(AntRestUtils.getCommIdChain(request));
        return cardService.queryUrgencyCardsByPage(token, page, cardRequest);
    }

    /**
     * 根据条件查询紧急充电卡 用于运营支撑平台(无需考虑权限)
     *
     * @return
     */
    @PostMapping("/api/card/urgencyCardsListOnOperate")
    public ListResponse<CardListdetailVO> urgencyCardsListOnOperate(
        ServerWebExchange exh,
        @RequestBody CardRequest cardRequest, ServerHttpRequest request) {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, true);

        ListResponse<CardListdetailVO> result = cardService.queryUrgencyCardsByPageOnOperate(token,
            page, cardRequest);
        if (result.getTotal() == null) {
            result.setTotal(0L);
        }
        return result;
    }


    @RequestMapping("/api/card/urgencyCardsDetail")
    @CheckToken(check = "getToken4Aspect")
    public ObjectResponse<UrgencyCardDetail> urgencyCardsDetail(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) {
        String token = getToken2(request);

        ObjectResponse<UrgencyCardDetail> res = cardService.urgencyCardsDetail(token,
            cardRequest.getId());

        return res;
    }


    @RequestMapping("/api/card/urgencyCardsDetailEvseList")
    @CheckToken(check = "getToken4Aspect")
    public ListResponseEvseList<UrgencyCardEvse> urgencyCardsDetailEvseList(
        ServerHttpRequest request, @RequestBody CardRequest cardRequest) {
        log.info("查询紧急充电卡-桩列表: {}", JsonUtils.toJsonString(cardRequest));
        String token = getToken2(request);
        ListResponseEvseList<UrgencyCardEvse> ret = cardService.urgencyCardsDetailEvseList(token,
            cardRequest.getId(), cardRequest.getEvse(), cardRequest.get_index(),
            cardRequest.get_size());

        return ret;
    }

    /**
     * 根据条件导出紧急充电卡，不带分页
     *
     * @return
     */
    @PostMapping("/api/card/exportUrgencyCardsList")
    public ObjectResponse exportUrgencyCardsList(
        ServerWebExchange exh,
        @RequestBody CardRequest cardRequest, ServerHttpRequest request,
        HttpServletResponse response) throws IOException {
        String token = getToken2(request);
        OldPageParam page = getPage2(request, exh, true);
        page.setPageSize(Integer.MAX_VALUE);

        ListResponse<CardListdetailVO> ret = cardService.queryUrgencyCardsByPageOnOperate(token,
            page, cardRequest);

        response.setContentType("text/csv; charset=utf-8");
        response.setCharacterEncoding("utf-8");
        response.setHeader("content-disposition", "attachment;filename =card.csv");
        PrintWriter printWriter = response.getWriter();
        printWriter.write(new String(new byte[]{(byte) 0xEF, (byte) 0xBB, (byte) 0xBF}));
//        ServletOutputStream writer = response.getOutputStream();
        log.info("downloading contents to csv");

        final List<String> CSV_TITLE = Arrays.asList("\"序号\"",
            "\"卡号\"",
            "\"卡名称\"",
            "\"客户名称\"",
            "\"客户手机号\"",
            "\"集团客户\"",
            "\"归属商户\"",
            "\"集团商户\"",
            "\"站点名称\"",
            "\"当前有效密码\"",
            "\"卡片状态\"");

        printWriter.print(String.join(",", CSV_TITLE) + "\r\n");

        int i = 0;
        for (CardListdetailVO cardListdetailVO : ret.getData()) {
            i++;
            List<String> oneLine = new ArrayList<>();
            oneLine.add(String.valueOf(i));
            oneLine.add(cardListdetailVO.getCardNo());
            oneLine.add(cardListdetailVO.getCardName());
            oneLine.add(cardListdetailVO.getUserName());
            oneLine.add(cardListdetailVO.getMobile());
            oneLine.add(cardListdetailVO.getCorpName());
            oneLine.add(cardListdetailVO.getCommName());
            oneLine.add(cardListdetailVO.getCommName());//TODO 集团商户
            oneLine.add(cardListdetailVO.getStationName());
            oneLine.add(cardListdetailVO.getCardActivationCode());

            String cardStatus = "未知";
            if (cardStatusMap.get(cardListdetailVO.getCardStatus()) != null) {
                cardStatus = cardStatusMap.get(cardListdetailVO.getCardStatus());
            }
            oneLine.add(cardStatus);

//            writer.println(String.join(",", oneLine));
            printWriter.print(String.join(",",
                oneLine.stream().map(e -> StringUtils.isBlank(e) ? "" : "\"" + e + "\"")
                    .collect(Collectors.toList())) + "\r\n");
        }

//        writer.flush();
//        writer.close();
        printWriter.close();
        printWriter.flush();

        return null;
    }

    @Operation(summary = "在线卡新增卡片（发卡给客户）")
    @PostMapping("/api/card/grantCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "addOnlineCard")
    public BaseResponse grantCard(ServerHttpRequest request,
        @RequestBody ModifyCardParam param) {
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCardNo())
                && StringUtils.isNotBlank(param.getCardChipNo())
                && StringUtils.isNotBlank(param.getStations())
                && param.getUserId() != null && param.getCommId() != null
            , "参数错误");
        BaseResponse res = cardService.grantCard(param.getCarNo(), param.getCarNum(),
            param.getLineNum(), param.getCardNo(), param.getCardName(),
            param.getUserId(), param.getCommId(), param.getStations(),
            param.getCarDepart());
        customerSysLogService.grantCard(param.getCardChipNo(), request);
        return res;
    }

    @Operation(summary = "海外版新增鉴权卡同时发卡给客户")
    @PostMapping("/api/card/commercial/addCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "addOnlineCard")
    public BaseResponse addEssCard(ServerHttpRequest request,
        @RequestBody AddEssCardParam param) {
        // 海外版的鉴权卡就是充电管理平台的在线卡
        log.info("海外版新增鉴权卡, param = {}", JsonUtils.toJsonString(param));
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCardNo())
            && StringUtils.isNotBlank(param.getCardChipNo()) && NumberUtils.gtZero(
            param.getUserId()), "参数错误");
        BaseResponse res = cardService.addEssCard(param);
        customerSysLogService.grantEssCard(param.getCardChipNo(), request);
        return res;
    }

    /**
     * 紧急充电卡新增卡片（发卡给客户）
     *
     * @param request
     * @return
     */
    @RequestMapping("/api/card/grantUrgencyCard")
    @CheckToken(check = "getToken4Aspect")
    public BaseResponse grantUrgencyCard(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) throws ParseException {
        List<Long> commIdList = getCommIdList2(request);
        log.info("commIdList：" + JsonUtils.toJsonString(commIdList));
        IotAssert.isTrue(StringUtils.isNotBlank(cardRequest.getCardChipNo()), "未传入物理卡号");

        BaseResponse res = cardService.grantUrgencyCard(cardRequest, commIdList);
        customerSysLogService.grantUrgencyCardLog(cardRequest.getCardChipNo(), request);
        return res;
    }

    /**
     * 在线卡挂失
     *
     * @param request
     * @param cardNo
     * @return
     */
    @RequestMapping("/api/card/reportLossCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "activateOnlineCard")
    public BaseResponse reportLossCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {

        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }

        BaseResponse res = cardService.reportLossCard(cardNo, commIdList);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 在线卡激活
     *
     * @param request
     * @param cardNo
     * @return
     */
    @RequestMapping("/api/card/reportActivateCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "activateOnlineCard")
    public BaseResponse reportActivateCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {
        BaseResponse res = cardService.reportActivateCard(cardNo);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 在线卡修改 紧急卡修改备注
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping("/api/card/modifyCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "editOnlineCard")
    public BaseResponse modifyCard(ServerHttpRequest request,
        @RequestBody ModifyCardParam param) {
        log.info(LoggerHelper2.formatEnterLog(request));
        IotAssert.isTrue(StringUtils.isNotBlank(param.getCardNo()) && StringUtils.isNotBlank(
            param.getCardChipNo()), "参数错误，卡号不能为空");
        // 获取语言环境，如果是null，说明非国际版
        Locale locale = AntRestUtils.getLocale(request);

        BaseResponse res = cardService.modifyCard(param.getCarDepart(), param.getCarNo(),
            param.getCarNum(), param.getLineNum(), param.getCardNo(), param.getCommId(),
            param.getCardName(), param.getStations(), param.getRemark(), param.getDeposit(),
            param.getIsLocalAuth(), param.getLocalAuthSiteList(), locale);
        customerSysLogService.modifyCard(List.of(param.getCardChipNo()), request);
        return res;
    }

    /**
     * 紧急充电卡修改(该功能需求已删除) (绑定集团客户逻辑需调整)
     *
     * @return
     */
    @Deprecated
    @RequestMapping("/api/card/modifyUrgencyCard")
    @CheckToken(check = "getToken4Aspect")
    public BaseResponse modifyUrgencyCard(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) throws ParseException {
        return cardService.modifyUrgencyCard(cardRequest);
    }

    /**
     * 在线卡删除
     *
     * @param request
     * @param cardNo
     * @return
     */
    @RequestMapping("/api/card/logicDelCard")
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "deleteOnlineCard")
    public BaseResponse logicDelCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {
        BaseResponse res = cardService.logicDelCard(cardNo);
        customerSysLogService.deleteCard(cardChipNo, request);
        return res;
    }

    /**
     * 根据cardNo查询卡
     *
     * @param card
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardByCardNo", method = RequestMethod.POST)
    @CheckToken(check = "getToken4Aspect")
    public ObjectResponse queryCardByCardNo(ServerHttpRequest request,
        @RequestBody Card card) {
        return cardService.queryCardByCardNo(card.getCardNo());
    }

    /**
     * 根据cardChipNo查询卡
     *
     * @param card
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardByCardChipNo")
    public ObjectResponse queryCardByCardChipNo(ServerHttpRequest request,
        @RequestBody Card card) {
        return cardService.queryCardByCardChipNo(card.getCardChipNo());
    }

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardById")
    public ObjectResponse<Card> queryCardById(@RequestParam("id") String id) {
        return cardService.queryCardById(id);
    }

    /**
     * 离线卡解除黑名单
     *
     * @param cardNo
     * @return`
     */
    @RequestMapping(value = "/api/card/removeBlack")
    public BaseResponse removeBlack(String cardNo) {
        return cardService.updateCardStatus(cardNo, Constant.CARD_STATUS_NORMAL);
    }

    /**
     * 删除离线卡
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/deleteOffLineCard")
    public BaseResponse deleteOffLineCard(String cardNo) {
        return cardService.updateCardStatus(cardNo, Constant.CARD_YX_BZ);
    }

    /**
     * 离线卡拉入黑名单
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/pullBlack")
    public BaseResponse pullBlack(String cardNo) {
        return cardService.updateCardStatus(cardNo, Constant.CARD_STATUS_BLACK);
    }

    /**
     * 鉴权卡激活
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/activateAuthCard")
    @CheckAuth(code = "activateOnlineCard")
    public BaseResponse activateAuthCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {
        BaseResponse res = cardService.updateCardStatus(cardNo, Constant.CARD_STATUS_NORMAL);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 鉴权卡冻结
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/blackAuthCard")
    @CheckAuth(code = "frozenOnlineCard")
    public BaseResponse blackAuthCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo) {

        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        //BUG2020-482 （管理平台和企业平台）卡【挂失】【冻结】操作时，删除判断订单结算的逻辑
//        cardService.checkBeforeUpdateCardStatus(cardNo, commIdList);
        BaseResponse res = cardService.updateCardStatusAndCheck(cardNo, Constant.CARD_STATUS_BLACK,
            commIdList);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 鉴权卡解除黑名单
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/unBlackAuthCard")
    @CheckAuth(code = "frozenOnlineCard")
    public BaseResponse unBlackAuthCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {
        BaseResponse res = cardService.updateCardStatus(cardNo, Constant.CARD_STATUS_NORMAL);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 鉴权卡挂失
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/lockAuthCard")
    @CheckAuth(code = "activateOnlineCard")
    public BaseResponse lockAuthCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo) {

        List<Long> commIdList = this.getCommIdList2(request);
        if (org.springframework.util.CollectionUtils.isEmpty(commIdList)) {
            throw new DcArgumentException("参数错误");
        }
        //BUG2020-482 （管理平台和企业平台）卡【挂失】【冻结】操作时，删除判断订单结算的逻辑
//        cardService.checkBeforeUpdateCardStatus(cardNo, commIdList);
        BaseResponse res = cardService.updateCardStatusAndCheck(cardNo, Constant.CARD_STATUS_LOCK,
            commIdList);
        customerSysLogService.modifyCardStatus(cardChipNo, request);
        return res;
    }

    /**
     * 鉴权卡删除
     *
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/deleteAuthCard")
    @CheckAuth(code = "deleteOnlineCard")
    public BaseResponse deleteAuthCard(ServerHttpRequest request,
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "cardChipNo") String cardChipNo
    ) {
        List<String> noSettlementVinList = corpService.getNoSettlementCard(
            Collections.singletonList(cardNo), null);
        if (CollectionUtils.isNotEmpty(noSettlementVinList)) {
            throw new DcServiceException("存在订单尚未处理，请处理后再进行操作");
        }
        BaseResponse res = cardService.updateCardStatus(cardNo, Constant.CARD_YX_BZ);
        customerSysLogService.deleteCard(cardChipNo, request);
        return res;
    }

    /**
     * 离线卡添加功能
     *
     * @param cardNo 卡号
     * @param commId 所属商户
     * @return
     */
    @RequestMapping(value = "/api/card/addOffLineCard")
    public BaseResponse addOffLineCard(@RequestParam("commId") Long commId,
        @RequestParam("cardNo") String cardNo, ServerHttpRequest request) {
        String token = getToken2(request);

        return cardService.addCard(commId, cardNo, token, null, Constant.IS_PACKAGED_OFF_LINE);
    }

    /**
     * 鉴权卡添加功能
     *
     * @param cardNo 卡号
     * @param commId 所属商户
     * @return
     */
    @RequestMapping(value = "/api/card/addAuthCard")
    public BaseResponse addAuthCard(@RequestParam("commId") Long commId,
        @RequestParam("cardNo") String cardNo,
        @RequestParam("stations") String stations,
        ServerHttpRequest request) {
        String token = getToken2(request);

        return cardService.addCard(commId, cardNo, token, stations, Constant.IS_PACKAGED_AUTH);
    }

    /**
     * 鉴权卡更新功能
     *
     * @param cardNo   卡号
     * @param commId   所属商户
     * @param stations 鉴权卡所属站点（多个站点用‘，’分割）
     * @return
     */
    @RequestMapping(value = "/api/card/updateAuthCard")
    public BaseResponse updateAuthCard(@RequestParam("commId") Long commId,
        @RequestParam("cardNo") String cardNo,
        @RequestParam("stations") String stations,
        ServerHttpRequest request) {
        String token = getToken2(request);

        return cardService.updateAuthCard(commId, cardNo, token, stations,
            Constant.IS_PACKAGED_AUTH);
    }


    @RequestMapping(value = "/api/card/queryAllCardList", method = RequestMethod.GET)
    public ListResponse<CardVo> queryAllCardList(@RequestParam("userId") String userId,
        ServerHttpRequest request) {
        String token = this.getToken2(request);
        return cardService.queryAllCardList(token, userId);
    }

    @Operation(summary = "获取可用的逻辑卡号")
//    @CheckToken(check = "getToken4Aspect", login = true)
    @RequestMapping(value = "/api/card/getLogicalCardNum", method = RequestMethod.GET)
    public ListResponse<String> getLogicalCardNum(ServerHttpRequest request,
        ServerWebExchange exh,
        @Parameter(name = "物理卡号")
        @RequestParam(value = "physicsCardNo") String cardNo,
        @Parameter(name = "获取个数最，最多10个，超出也是10个，默认1个")
        @RequestParam(value = "count", defaultValue = "1") Integer count) {
//        String token = this.getToken2(request);
//        CorpOrgLoginVo user = loginService.getUser(token);
//        IotAssert.isNotNull(user, "请登陆");

        return cardService.getLogicalCardNum(cardNo, count);
    }

    @Operation(summary = "制卡软件，绑定逻辑卡号和物理卡号")
//    @CheckToken(check = "getToken4Aspect", login = true)
    @RequestMapping(value = "/api/card/bindLogicalAndPhysics", method = RequestMethod.POST)
    public ObjectResponse<Integer> bindLogicalAndPhysics(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody List<Card> list) {
        log.info("绑定逻辑卡号和物理卡号");
        String token = this.getToken2(request);
        CorpOrgLoginVo user = loginService.getUser(token);
        IotAssert.isNotNull(user, "请登陆");

        return cardService.bindLogicalAndPhysics(list);
    }

    /**
     * 运营支撑平台查询卡列表
     *
     * @param cardStatus
     * @param cardType
     * @param beginTime
     * @param endTime
     * @param keyWord
     * @return
     */
    @RequestMapping(value = "/api/card/queryAllCards", method = RequestMethod.GET)
    public ListResponse<CardMgnVo> queryAllCards(
        ServerWebExchange exh,
        @RequestParam(value = "cardStatus", required = false) String cardStatus,
        @RequestParam(value = "cardType", required = false) String cardType,
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        ServerHttpRequest request) {
        OldPageParam page = getPage2(request, exh, false);
        String token = this.getToken2(request);
        return cardService.queryAllCardsByPage(token, page, cardStatus, cardType, commId, beginTime,
            endTime, keyWord);
    }

    @RequestMapping(value = "/api/card/deleteCardsByIds", method = RequestMethod.DELETE)
    public BaseResponse deleteCardsByIds(@RequestBody List<Long> ids) {
        return cardService.deleteCardsByIds(ids);
    }

    /**
     * 重置（恢复初始）卡片
     *
     * @param ids
     * @return
     */
    @PostMapping(value = "/api/card/resetCardsByIds")
    public BaseResponse resetCardsByIds(@RequestBody List<Long> ids) {
        return cardService.resetCardsByIds(ids);
    }

    /**
     * 批量新增卡
     *
     * @param cards 卡
     * @return
     */
    @PostMapping("/api/card/batchAddCard")
    public ObjectResponse<Integer> batchAddCard(@RequestBody List<Card> cards) {
        log.info("batch insert card: size={}, list={}", cards.size(), cards);

        if (0 == cards.size()) {
            log.info("批量上传卡片大小为0, size={}", cards.size());
            throw new DcArgumentException("请上传有效卡片数据，批量添加卡片数量不能为零.");
        }

        try {
            return cardService.batchAddCard(cards);
        } catch (Exception e) {
            log.error("批量添加卡失败: {}", e.getMessage(), e);
            throw new DcServiceException("批量添加卡失败");
        }
    }

    /**
     * 下发紧急充电卡（单场站）
     *
     * @param cardChipNo
     * @return
     */
    @GetMapping("/api/card/sendWhiteCard")
    public BaseResponse sendWhiteCard(@RequestParam(value = "cardChipNo") String cardChipNo,
        @RequestParam(value = "siteName") String siteName,
        ServerHttpRequest request) {
        log.info("紧急充电卡再次下发。cardChipNo = {}", cardChipNo);
        String token = getToken2(request);
        ObjectResponse<List<String>> res = cardService.sendWhiteCard(cardChipNo, token);
        customerSysLogService.sendWhiteCardLog(cardChipNo, siteName, request);
        return res;
    }

    /**
     * 批量下发紧急充电卡（多场站）
     *
     * @return
     */
    @GetMapping("/api/card/sendBatchWhiteCards")
    public BaseResponse sendBatchWhiteCards(ServerHttpRequest request) {
        log.info("紧急充电卡批量下发。");
        String token = getToken2(request);
        return cardService.sendBatchWhiteCards(token);
    }

    /**
     * 下发紧急充电卡（单场站） 弃用cardChipNo
     *
     * @param cardChipNo
     * @return
     */
    @GetMapping(value = "/api/card/abandonWhiteCard")
    public BaseResponse abandonWhiteCard(@RequestParam(value = "cardChipNo") String cardChipNo,
        ServerHttpRequest request) {
        log.info("紧急充电卡弃用。cardChipNo = {}", cardChipNo);
        String token = getToken2(request);
        ObjectResponse<List<String>> res = cardService.abandonWhiteCard(cardChipNo, token);
        customerSysLogService.abandonWhiteCardLog(cardChipNo, request);
        return res;
    }

    /**
     * 下发紧急充电卡（单场站） 重置密码
     *
     * @param cardChipNo
     * @return
     */
    @GetMapping(value = "/api/card/resetWhiteCardPwd")
    public BaseResponse resetWhiteCardPwd(@RequestParam(value = "cardChipNo") String cardChipNo,
        ServerHttpRequest request) {
        log.info("紧急充电卡弃用。cardChipNo = {}", cardChipNo);
        String token = getToken2(request);
        return cardService.resetWhiteCardPwd(cardChipNo, token);
    }

    /**
     * 根据逻辑卡号获取适用的场站
     *
     * @param cardNo 逻辑卡号
     * @return
     */
    @GetMapping(value = "/api/card/online/site")
    public ListResponse<String> getStationsOfCard(@RequestParam("cardNo") String cardNo,
        @Parameter(name = "1、卡片，2、VIN码") @RequestParam(value = "type", required = false) Integer type,
        ServerHttpRequest request) {

        return cardService.getStationsOfCard(cardNo, type, super.getCommIdChain2(request));

    }

    /**
     * 站点更换商户，获取不能再使用的紧急充电卡
     *
     * @param siteId
     * @param commId
     * @param request
     * @return
     */
    @GetMapping(value = "/api/card/getEmergencyCardBySiteId")
    public ListResponse<String> getEmergencyCardBySiteId(@RequestParam("siteId") String siteId,
        @RequestParam(value = "commId") Long commId,
        ServerHttpRequest request) {
        log.info("siteId={},commId={}", siteId, commId);

        if (siteId == null || commId == null) {
            throw new DcServiceException("请求参数不正确");
        }
        return cardService.getEmergencyCardBySiteId(siteId, commId);

    }

    /**
     * 企业切换商户时，紧急卡修改后的新商户，商户不包含原先紧急卡的站点列表
     *
     * @param corpId  企业id
     * @param commId  转到的目标商户id
     * @param request
     * @return
     */
    @GetMapping(value = "/api/card/getMoveCorpEmergencyCardByCorpId")
    public ObjectResponse<MoveCorpCardList> getMoveCorpEmergencyCardByCorpId(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {
        log.info("企业切换商户，紧急卡，corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        return cardService.getEmergencyCardByCorpId(corpId, commId);

    }

    /**
     * 企业切换商户时，获取在线卡保留/移除授权场站记录
     *
     * @param corpId  企业id
     * @param commId  转到的目标商户id
     * @param request
     * @return
     */
    @GetMapping(value = "/api/card/getMoveCorpCardByCorpId")
    public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpCardByCorpId(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {
        log.info("企业切换商户，在线卡，corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        return cardService.getMoveCorpCardByCorpId(corpId, commId);

    }

    @Operation(summary = "获取卡片账户信息")
    @RequestMapping(value = "/api/card/cardAmount", method = RequestMethod.POST)
    public Mono<ObjectResponse<BigDecimal>> cardAmount(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody CardRequest card) {
        log.info("获取卡片账户信息");
        String token = this.getToken2(request);
        CorpOrgLoginVo user = loginService.getUser(token);
        IotAssert.isNotNull(user, "请登陆");

        Long topCommId = AntRestUtils.getTopCommId(request);
        card.setTopCommId(topCommId);

        return cardService.cardAmount(card);
    }

    @Operation(summary = "同步金额")
    @RequestMapping(value = "/api/card/syncAmount", method = RequestMethod.POST)
    public Mono<ObjectResponse<Boolean>> syncAmount(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody CardRequest card) {
        log.info("同步卡片金额: {}, {}", card.getCardNo(), card.getSyncAmount());
        String token = this.getToken2(request);
        CorpOrgLoginVo user = loginService.getUser(token);
        IotAssert.isNotNull(user, "请登陆");

        Long topCommId = AntRestUtils.getTopCommId(request);
        card.setTopCommId(topCommId);
        card.setUserId(user.getId());//操作者

        IotAssert.isNotBlank(card.getCardNo(), "请传入逻辑卡号");

        return cardService.syncAmount(card);
    }

    @Operation(summary = "获取卡片同步记录列表")
    @PostMapping(value = "/api/card/getSyncAmount")
    public Mono<ListResponse<CardAmountSyncVo>> getSyncAmount(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestBody CardAmountSyncParam param) {
        log.info("param = {}", JsonUtils.toJsonString(param));

        String token = this.getToken2(request);
        CorpOrgLoginVo user = loginService.getUser(token);
        IotAssert.isNotNull(user, "请登陆");

        IotAssert.isNotBlank(param.getCardNo(), "请传入逻辑卡号");

        return cardService.getSyncCardAmount(param);
    }

    @Operation(summary = "本地卡下发到场站")
    @PostMapping(value = "/api/card/cardSiteAuthSend")
    public BaseResponse getSiteListByCard(@RequestBody SiteAuthCardParam param) {
        log.info("下发卡片本地鉴权到场站: {}", JsonUtils.toJsonString(param));
        return cardService.siteAuthCard(param);
    }

    /**
     * 卡本地鉴权批量下发，获取共同场站
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/api/card/getCommonSiteList")
    public ListResponse<SitePo> getCommonSiteList(ServerHttpRequest request,
        @RequestBody CardSearchParam param) {
        return cardService.getCommonSiteList(param);
    }

    @Operation(summary = "获取桩实时CARD本地鉴权列表")
    @GetMapping(value = "/api/card/getEvseCardAuthCfg")
    public ListResponse<LocalCard> getEvseCardAuthCfg(@RequestParam("evseNo") String evseNo) {
        log.info("桩实时获取CARD本地鉴权列表: {}", evseNo);
        return new ListResponse<>(iotEvseCfgService.getEvseCardAuthCfg(evseNo));
    }

    @Operation(summary = "触发桩实时获取CARD本地鉴权列表")
    @GetMapping(value = "/api/card/getEvseCardAuthRealTime")
    public ObjectResponse<Boolean> getEvseCardAuthRealTime(@RequestParam("evseNo") String evseNo) {
        log.info("桩实时获取CARD本地鉴权列表: {}", evseNo);
        return new ObjectResponse<>(iotEvseCfgService.getEvseCardAuthCfgRealTime(evseNo));
    }

    @Operation(summary = "本地CARD鉴权，获取场站绑定的card列表(当前场站配置，可能不是实际生效的card列表)")
    @GetMapping(value = "/api/card/getCardListBySiteId")
    public ListResponse<SiteAuthCardPo> getCardListBySiteId(@RequestParam("siteId") String siteId) {
        return cardService.getCardListBySiteId(siteId);
    }
}
