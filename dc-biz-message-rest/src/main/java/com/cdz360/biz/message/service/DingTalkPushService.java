package com.cdz360.biz.message.service;

import com.cdz360.biz.message.domain.dto.ZkcyWarningDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Service
public class DingTalkPushService {

    @Value("${dingtalk.appKey}")
    private String appKey;

    @Value("${dingtalk.appSecret}")
    private String appSecret;

    @Value("${dingtalk.agentId}")
    private String agentId;

    @Value("${dingtalk.rootDeptId}")
    private Long rootDeptId; // 根部门ID，在配置文件中设置

    private final RestTemplate restTemplate = new RestTemplate();

    DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Async
    public void pushAlarmsToDingTalk(List<ZkcyWarningDTO> alarms, String accessToken) {
        Set<String> allUserIds = getAllUserIds(accessToken); // 获取所有需要推送的用户ID

        if (allUserIds.isEmpty()) {
            // 日志记录没有找到用户
            return;
        }

        // 构建消息内容
        String content = buildMarkdownContent(alarms);

        // 构建钉钉请求体
        Map<String, Object> msgBody = new HashMap<>();
        msgBody.put("agent_id", agentId);
        msgBody.put("userid_list", String.join(",", allUserIds));

        Map<String, Object> msgContent = new HashMap<>();
        msgContent.put("msgtype", "markdown");

        Map<String, String> markdown = new HashMap<>();
        markdown.put("title", "设备告警通知");
        markdown.put("text", content);
        msgContent.put("markdown", markdown);

        msgBody.put("msg", msgContent);

        // 发送请求（注意：钉钉接口限制每次最多1000人，需要分批发送）
        int batchSize = 1000;
        List<String> userIdList = new ArrayList<>(allUserIds);
        for (int i = 0; i < userIdList.size(); i += batchSize) {
            int end = Math.min(i + batchSize, userIdList.size());
            List<String> batchList = userIdList.subList(i, end);
            msgBody.put("userid_list", String.join(",", batchList));
            restTemplate.postForObject(
                    "https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2?access_token=" + accessToken,
                    msgBody,
                    String.class
            );
        }
    }

    // 构建Markdown内容
    private String buildMarkdownContent(List<ZkcyWarningDTO> alarms) {
        StringBuilder content = new StringBuilder("### 新告警通知  \n  ");
        for (ZkcyWarningDTO alarm : alarms) {
            String formattedTime = alarm.getStartTime() != null
                    ? alarm.getStartTime().format(formatter)
                    : "N/A";

            content.append(String.format(
                    "**设备**: %s(%s)  \n  **告警**: %s  \n  **级别**: %s  \n  **时间**: %s  \n  \n  ",
                    alarm.getDeviceName(),
                    alarm.getDeviceSn(),
                    alarm.getName(),
                    alarm.getLevel(),
                    formattedTime
            ));
        }
        return content.toString();
    }

    // 获取所有部门ID（递归获取子部门）
    private List<Long> getDepartmentIds(String accessToken, Long parentDeptId) {
        String url = String.format("https://oapi.dingtalk.com/topapi/v2/department/listsub?access_token=%s", accessToken);

        Map<String, Object> params = new HashMap<>();
        params.put("dept_id", parentDeptId);

        Map response = restTemplate.postForObject(url, params, Map.class);

        log.info(response.toString());

        List<Map<String, Object>> departments = (List<Map<String, Object>>) response.get("result");

        List<Long> deptIds = new ArrayList<>();
        if (departments != null && !departments.isEmpty()) {
            for (Map<String, Object> dept : departments) {
                Long deptId = Long.valueOf(dept.get("dept_id").toString());
                deptIds.add(deptId);
                // 递归获取子部门
                deptIds.addAll(getDepartmentIds(accessToken, deptId));
            }
        }

        return deptIds;
    }

    // 获取部门下的所有用户
    private List<String> getUserIdsByDept(String accessToken, Long deptId) {
        String url = String.format("https://oapi.dingtalk.com/topapi/user/listid?access_token=%s", accessToken);

        Map<String, Object> params = new HashMap<>();
        params.put("dept_id", deptId);

        Map response = restTemplate.postForObject(url, params, Map.class);

        log.info("userInfo::: {}", response.toString());

        Map<String, Object> result = (Map<String, Object>) response.get("result");

        return (List<String>) result.get("userid_list");
    }

    // 获取所有需要推送的用户ID
    private Set<String> getAllUserIds(String accessToken) {
        // 获取根部门（在配置文件中设置）及所有子部门
        List<Long> allDeptIds = new ArrayList<>();
        allDeptIds.add(rootDeptId);
        allDeptIds.addAll(getDepartmentIds(accessToken, rootDeptId));

        // 获取每个部门的用户
        Set<String> allUserIds = new HashSet<>();
        for (Long deptId : allDeptIds) {
            List<String> userIds = getUserIdsByDept(accessToken, deptId);
            if (userIds != null) {
                allUserIds.addAll(userIds);
            }
        }
        return allUserIds;
    }
}