package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.tj.kc.po.TjSurveyImg;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "场站勘察记录")
public class TjSurveyVo {

    @ApiModelProperty(value = "勘察编号")
    @NotNull(message = "no 不能为 null")
    @Size(max = 16, message = "no 长度不能超过 16")
    private String no;

    @ApiModelProperty(value = "场站名称")
    @NotNull(message = "siteName 不能为 null")
    @Size(message = "siteName 长度不能超过 128")
    private String siteName;

    @ApiModelProperty(value = "勘察用户ID(sys_user.id)")
    @NotNull(message = "uid 不能为 null")
    private Long uid;

    @ApiModelProperty(value = "经度[-180,180]")
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度[-90,90]")
    private BigDecimal latitude;

    @ApiModelProperty(value = "省")
    private String province;

    @ApiModelProperty(value = "市")
    private String city;

    @ApiModelProperty(value = "区")
    private String area;

    @ApiModelProperty(value = "详细地址")
    @Size(max = 240, message = "address 长度不能超过 240")
    private String address;

    @ApiModelProperty(value = "场站类型（1：B端，2：C端）")
    @NotNull(message = "场站类型 不能为 null")
    private Integer stationType;

    @ApiModelProperty(value = "场站区域")
    @NotNull(message = "场站区域 不能为 null")
    private String stationArea;

    @ApiModelProperty(value = "合同年限")
    @NotNull(message = "合同年限 不能为 null")
    private BigDecimal contractTerm;

    @ApiModelProperty(value = "初评结果: 符合(1)/不符合(2)")
    @NotNull(message = "score 不能为 null")
    private Integer score;

    @ApiModelProperty(value = "车位数")
    @NotNull(message = "parkNum 不能为 null")
    private Integer parkNum;

    @ApiModelProperty(value = "功率，单位kw")
    private BigDecimal power;

    @ApiModelProperty(value = "用户名称")
    private String username;

    @ApiModelProperty(value = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @ApiModelProperty(value = "备注信息")
    private String remark;

    @ApiModelProperty(value = "所有图片json({flag: xx, images: []})")
    private List<TjSurveyImg> imageInfo;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

