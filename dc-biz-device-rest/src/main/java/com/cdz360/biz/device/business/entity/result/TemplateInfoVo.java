package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.business.constant.TemplateCalculateTypeEnum;
import com.cdz360.biz.device.business.constant.TemplateCalculateUnit;
import com.cdz360.biz.device.business.constant.TemplateChargingTypeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * 计费模板信息
 *
 * <AUTHOR>
 * @date Create on 2018/12/21 19:02
 */
@Data
@ApiModel(value = "计费模板信息")
public class TemplateInfoVo {

    /**
     * 计费模板ID
     */
    @ApiModelProperty(value = "计费模板ID")
    private Long id;
    /**
     * 计费模板编号**唯一标识**
     */
    @ApiModelProperty(value = "计费模板编号**唯一标识**")
    private String code;
    /**
     * 版本号
     */
    @ApiModelProperty(value = "版本号")
    private Integer version;
    /**
     * 模板名称
     */
    @ApiModelProperty(value = "模板名称")
    private String name;

    /**
     * 创建者
     */
    @ApiModelProperty(value = "创建者")
    private String createPhone;

    /**
     * 代理商ID
     */
    @ApiModelProperty(value = "代理商ID")
    private Long commercialId;
    /**
     * 充电计费单位{@link TemplateCalculateUnit}
     */
    @ApiModelProperty(value = "充电计费单位")
    private Integer calculateUnit;
    /**
     * 充电计费方式{@link TemplateCalculateTypeEnum}
     */
    @ApiModelProperty(value = "充电计费方式")
    private Integer calculateType;
    /**
     * 充电收费方式{@link TemplateChargingTypeEnum}
     */
    @ApiModelProperty(value = "充电收费方式")
    private Integer chargingType;
    /**
     * 充电费备注
     */
    @ApiModelProperty(value = "充电费备注")
    private String remarkCharge;
    /**
     * 服务费备注
     */
    @ApiModelProperty(value = "服务费备注")
    private String remarkService;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 免费充电标识
     */
    @ApiModelProperty(value = "免费充电标识")
    private Integer freeChargeFlag;
    /**
     * 计费子模板列表
     */
    @ApiModelProperty(value = "计费子模板列表")
    private List<SubTemplateInfoVo> subTemplateList;

    /**
     * 计费模块列表
     */
    @ApiModelProperty(value = "计费模块列表")
    private Set<TemplateModuleVo> templateModuleList;

    /**
     * 计费模版是否上一级下发
     */
    @ApiModelProperty(value = "计费模版是否上一级下发")
    private Boolean isSuperior;

    /**
     * 计费模版能否修改( 1 能/ 0 否)
     */
    @ApiModelProperty(value = "计费模版能否修改( 1 能/ 0 否)")
    private Integer isModify;

    @ApiModelProperty(value = "计费模板使能状态: true--启用; false--禁用")
    private Boolean enable;
}
