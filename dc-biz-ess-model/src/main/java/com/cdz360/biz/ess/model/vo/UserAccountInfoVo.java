package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户账户信息")
@Data
@Accessors(chain = true)
public class UserAccountInfoVo {

    @Schema(description = "用户ID", required = true)
    @JsonInclude(Include.NON_NULL)
    private Long id;

    @Schema(description = "用户email", required = true)
    @JsonInclude(Include.NON_EMPTY)
    private String email;

    @Schema(description = "用户手机号")
    @JsonInclude(Include.NON_EMPTY)
    private String phone;

    @Schema(description = "BCP 47 语言标签(注册时可填写移动端设备默认语言)",
        externalDocs = @ExternalDocumentation(
            description = "BCP 47 Language Tags",
            url = "https://www.rfc-editor.org/info/bcp47<br />"
                + "https://www.countrycode.org/"))
    private String language;

    @Schema(description = "国家地区代码",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    private String countryCode;
}
