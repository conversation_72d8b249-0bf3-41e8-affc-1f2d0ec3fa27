package com.cdz360.biz.device.business.entity.request;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.business.entity.po.WhiteCardEvse;
import com.cdz360.biz.device.common.dingchong.ChargeV2;
import com.cdz360.biz.device.common.dingchong.WhiteCardV2;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Classname BoxSettingRequest
 * @Description 桩配置信息请求参数
 * @Date 2019/6/6 10:57
 * @Created by JLei
 * @Email <EMAIL>
 */
@Data
public class BoxSettingUpsertRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    // 主键ID
    private Long id;
    // 桩设备ID
    private String boxCode;
    // 设备序列号/桩号
    private String boxOutFactoryCode;
    // 批量配置 批量修改桩配置传多个桩号
    private List<String> boxOutFactoryCodes;
    // 计费模板id
    private Long chargeId;
    // 1下发成功2失败3下发中
    private Integer status;
    // 管理员密码
    private String adminPassword;
    // 二级管理员密码
    private String level2Password;
    // 白天音量
    private Integer dayVolume;
    // 夜晚音量
    private Integer nightVolume;
    // 二维码url
    private String url;
    // 是否支持充电记录查询 （1是0否）
    private Boolean isQueryChargeRecord;
    // 是否支持定时充电 （1是0否）
    private Boolean isTimedCharge;
    // 是否支持无卡充电 （1是0否）
    private Boolean isNoCardCharge;
    // 是否支持扫码充电 （1是0否）
    private Boolean isScanCharge;
    // 是否支持Vin码充电 （1是0否）
    private Boolean isVinCharge;
    // 是否支持刷卡充电 （1是0否）
    private Boolean isCardCharge;
    // 是否支持定额电量充电 （1是0否）
    private Boolean isQuotaEleCharge;
    // 是否支持固定金额充电 （1是0否）
    private Boolean isQuotaMoneyCharge;
    // 是否支持固定时长充电 （1是0否）
    private Boolean isQuotaTimeCharge;
    // 国际协议
    private String internationalAgreement;
    // 自动停充 （1是0否）
    private Integer isAutoStopCharge;
    // 均/轮充设置 0均充 1轮充
    private Integer avgOrTurnCharge;
    // 合充开关 （1开0关）
    private Integer isCombineCharge;
    // 创建时间
    private Date createTime;
    // 更新时间
    private Date updateTime;
    // 操作人id
    private Long updateByUserid;
    // 紧急充电卡下发状态1下发成功2失败3下发中
    private Integer whiteCardsStatus;
    // 紧急充电卡列表，用,分隔
    private String whiteCardList;
    // 计费模板相关数据
//    private CfgEvseParam.CfgEvse.Charge charge;
    private List<ChargeV2> charge;
    private List<WhiteCardV2> whiteCards;
    private List<WhiteCardEvse> whiteCardEvses;
    // 管理员账号配置结果,0x00: 成功 其他表示失败
    private Integer adminCodeResult;
    // 各种开关项配置结果,0x00: 成功 其他表示失败
    private Integer triggerResult;
    // 电价配置结果,0x00: 成功 其他表示失败
    private Integer chargeResult;
    // 二维码配置结果,0x00: 成功 其他表示失败
    private Integer qrResult;
    // 桩长效秘钥
    private String evsePasscode;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
