package com.cdz360.biz.model.cus.corp.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * CorpOrgLoginVo
 *
 * @since 2020/1/6 16:02
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CorpOrgLoginVo extends CorpOrgPo {
    private CorpPo corpPo;
    //    @Schema(description = "商户及子商户id")
//    private List<Long> comIds;
    @Schema(description = "商户及子商户id")
    private List<Long> orgIds;

    @Schema(description = "商户ID链", example = "34474,12345")
    private String commIdChain;

    @Schema(description = "所属的场站组")
    private List<String> gids;

    private Long orgId;

}
