package com.cdz360.biz.ds.cus.ro.balance.param;

import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

/**
 * BalanceApplicationParam
 *
 * @since 6/25/2021 1:10 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BalanceApplicationParam extends BalanceApplicationPo {

    private long index;

    private int size;

    private String idLike;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTimeStart;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createTimeEnd;

    @Schema(description = "申请人账号名")
    private String applier;
    private List<Long> applierIds;

    // 是否是顶级商户
    private boolean isTopComm;
    private String idChain;

}