<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaRoMapper">

  <resultMap id="RESULT_TJ_AREA_PO" type="com.cdz360.biz.model.tj.kc.po.TjAreaPo">
    <id column="aid" jdbcType="BIGINT" property="aid"/>
    <result column="gid" jdbcType="VARCHAR" property="gid"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="color" jdbcType="VARCHAR" property="color"/>
    <result column="shape" jdbcType="INTEGER" property="shape"/>
    <result column="geoText" property="paths"
      typeHandler="com.cdz360.biz.ds.trading.PolygonTypeHandler"/>
    <result column="adcode" jdbcType="VARCHAR" property="adcode"/>
    <result column="enable" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>


  <sql id="FIND_SITE_WITHIN_TJ_AREA_SQL">
    <where>
      site.status in (1, 2, 3)  <!-- 1-待上线; 2-已上线; 3-维护中 -->
      and site.bizType in (0, 1, 2) <!-- 0-未知; 1-自营; 2-非自营; 4-脱机自营 -->
      <if test="null != withinAid">
        AND withinArea.aid = #{withinAid}
        and withinArea.aid is not null
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
        <foreach collection="withinAidList" index="index" item="item"
          open=" AND withinArea.aid in (" separator="," close=")">
          #{item}
        </foreach>
        and withinArea.aid is not null
      </if>
      <if test="null != analysisId and null != analysisPointNum">
        and ST_Distance_Sphere(
        ST_GeomFromText(concat('POINT(', site.longitude , ' ', site.latitude, ')')),
        ap.location) <![CDATA[<=]]> (ap.radius * 1000)
      </if>
    </where>
  </sql>

  <select id="getByAid"
    resultMap="RESULT_TJ_AREA_PO">
    select *, ST_AsText(`geo`) geoText from t_tj_area where aid = #{aid}
  </select>

  <select id="findAllUserArea"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaParam"
    resultMap="RESULT_TJ_AREA_PO">
    select *, ST_AsText(`geo`) geoText from t_tj_area
    <where>
      enable = true
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gid )">
        and gid = #{gid}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
        <foreach collection="gidList" index="index" item="item" open=" AND gid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="null != uid">
        and uid = #{uid}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( uidList )">
        <foreach collection="uidList" index="index" item="item" open=" AND uid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <sql id="FIND_ALL_SQL">
    <where>
      enable = true
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gid )">
        and gid = #{gid}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
        <foreach collection="gidList" index="index" item="item" open=" AND gid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="null != uid">
        and uid = #{uid}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( uidList )">
        <foreach collection="uidList" index="index" item="item" open=" AND uid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </sql>

  <select id="findArea"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaParam"
    resultMap="RESULT_TJ_AREA_PO">
    select *, ST_AsText(`geo`) geoText from t_tj_area
    <include refid="FIND_ALL_SQL"/>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="count"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaParam"
    resultType="long">
    select count(aid) from t_tj_area
    <include refid="FIND_ALL_SQL"/>
  </select>



  <select id="findSiteWithinTjArea"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam"
    resultType="com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo">
    select site.id, site.name, site.address, site.acEvseNum, site.dcEvseNum,
    site.acPlugNum, site.dcPlugNum, site.acPower, site.dcPower,
    concat(site.longitude, ',', site.latitude) location
    from t_site site
    <if test="null != withinAid">
      left join t_tj_area withinArea on ST_Within(
      ST_GeomFromText(concat('POINT(', site.longitude, ' ', site.latitude, ')')), withinArea.geo)
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
      left join t_tj_area withinArea on ST_Within(
      ST_GeomFromText(concat('POINT(', site.longitude, ' ', site.latitude, ')')), withinArea.geo)
    </if>
    <if test="null != analysisId and null != analysisPointNum">
      left join t_tj_area_analysis_point ap on ap.analysisId = #{analysisId} and ap.num =
      #{analysisPointNum}
    </if>
    <include refid="FIND_SITE_WITHIN_TJ_AREA_SQL"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>
  <select id="countSiteWithinTjArea"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam"
    resultType="java.lang.Long">
    select count(1)
    from t_site site
    left join t_tj_area withinArea
    on ST_Within(ST_GeomFromText(concat('POINT(', site.longitude, ' ', site.latitude, ')')),
    withinArea.geo)
    <if test="null != analysisId and null != analysisPointNum">
      left join t_tj_area_analysis_point ap on ap.analysisId = #{analysisId} and ap.num =
      #{analysisPointNum}
    </if>
    <include refid="FIND_SITE_WITHIN_TJ_AREA_SQL"/>
  </select>
</mapper>

