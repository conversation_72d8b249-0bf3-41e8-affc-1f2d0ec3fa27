package com.chargerlinkcar.framework.common.domain.vo;

import com.cdz360.base.utils.JsonUtils;
import com.chargerlinkcar.framework.common.domain.request.BaseRequest;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 *  vin码管理界面请求参数
 * @since 2019/5/14 17:27
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "VIN码管理参数")
@EqualsAndHashCode(callSuper = true)
public class VinParam extends BaseRequest {

    @Schema(description = "排除的SOC策略配置ID")
    @JsonInclude(Include.NON_NULL)
    private Long excludeSocStrategyId;

    @Schema(description = "主键ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long id;

    @Schema(description = "VIN码(车架号)")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String vin;

    @Schema(description = "车牌号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String carNo;

    @Schema(description = "车队名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String carDepart;

    @Schema(description = "车牌自编号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String carNum;

    @Schema(description = "线路")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String lineNum;

    @Schema(description = "品牌")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String brand;

    @Schema(description = "型号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String model;

    @Schema(description = "车长(米)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal carLength;

    @Schema(description = "年份")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String year;

    @Schema(description = "名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String name;

    @Schema(description = "站点列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String station;

    @Schema(description = "本地VIN鉴权siteId列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> vinAuthSiteIdList;

    @Schema(description = "状态")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer status;

    @Schema(description = "顶级集团商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "当前登录商户id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long subCommId;

    @Schema(description = "客户号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long userId;

    @Schema(description = "客户号列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "手机号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String mobile;

    @Schema(description = "企业ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;
    /**
     * 只用于企业平台新增VIN码时，校验组织名称与手机号的绑定关系
     */
    @Schema(description = "组织名称id", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpOrgId;

    @Schema(description = "修改人")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long modifyBy;

    @Schema(description = "是否可用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer enable;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "创建时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date createTime;

    @JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Schema(description = "更新时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Date updateTime;

    @Schema(description = "分页数组开始下标，从0开始", example = "0")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long start;

    @Schema(description = "分页数据数量", example = "10")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer size;

    @Schema(description = "是否是默认车牌号", example = "True")
    private Boolean isPrimary;

    @Schema(description = "去除不需要的vin id", example = "1, 2, 3")
    private List<Long> excludeIds;

    private String commIdChain;

    private List<Long> orgIds;

    private List<String> vinList;

    private Integer current;

    private Locale locale;

    /**
     * "MODIFY" or "MODIFY_STATUS" 用于记录操作日志
     */
    @Schema(description = "操作类型 'MODIFY' or 'MODIFY_STATUS'")
    private String type;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

    /**
     * 参数检查
     */
    public void paramCheck() {
        RegularExpressionUtil.carNumCheck(this.getCarNum());
        RegularExpressionUtil.carDepartCheck(this.getCarDepart());
        RegularExpressionUtil.lineNumCheck(this.getLineNum());
        RegularExpressionUtil.brandCheck(this.getBrand());
        RegularExpressionUtil.modelCheck(this.getModel());
        RegularExpressionUtil.carLengthCheck(this.getCarLength());
        RegularExpressionUtil.yearCheck(this.getYear());
    }

    /**
     * 海外版，参数检查
     */
    public void essParamCheck() {
        RegularExpressionUtil.carNumEssCheck(this.getCarNum());
        RegularExpressionUtil.carDepartEssCheck(this.getCarDepart());
        RegularExpressionUtil.lineNumEssCheck(this.getLineNum());
        RegularExpressionUtil.brandEssCheck(this.getBrand());
        RegularExpressionUtil.modelEssCheck(this.getModel());
        RegularExpressionUtil.carLengthCheck(this.getCarLength());
        RegularExpressionUtil.yearCheck(this.getYear());
    }
}