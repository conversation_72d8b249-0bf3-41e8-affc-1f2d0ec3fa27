package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjMaterialCostRwMapper {

    TjMaterialCostPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjMaterialCost(TjMaterialCostPo tjMaterialCostPo);

    int updateTjMaterialCost(TjMaterialCostPo tjMaterialCostPo);

    int disableTjMaterialCost(@Param("id") Long id);

}

