package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.business.entity.po.WhiteCardEvse;
import com.cdz360.biz.device.common.dingchong.WhiteCardV2;
import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Classname BoxSettingRequest
 * @Description 桩配置信息请求参数
 * @Date 2019/6/6 10:57
 * @Created by J<PERSON><PERSON>
 * @Email <EMAIL>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BoxSettingListRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    // 主键ID
    private Long id;
    // 桩设备ID
    private String boxCode;
    // 设备序列号/桩号
    private String boxOutFactoryCode;
    // 计费模板id
    private Long chargeId;
    // 1下发成功2失败3下发中
    private Integer status;
    // 管理员密码
    private String adminPassword;
    // 二级管理员密码
    private String level2Password;
    // 白天音量
    private Integer dayVolume;
    // 夜晚音量
    private Integer nightVolume;
    // 二维码url
    private String url;
    // 是否支持充电记录查询 （1是0否）
    private Integer isQueryChargeRecord;
    // 是否支持定时充电 （1是0否）
    private Integer isTimedCharge;
    // 是否支持无卡充电 （1是0否）
    private Integer isNoCardCharge;
    // 是否支持扫码充电 （1是0否）
    private Integer isScanCharge;
    // 是否支持Vin码充电 （1是0否）
    private Integer isVinCharge;
    // 是否支持刷卡充电 （1是0否）
    private Integer isCardCharge;
    // 是否支持定额电量充电 （1是0否）
    private Integer isQuotaEleCharge;
    // 是否支持固定金额充电 （1是0否）
    private Integer isQuotaMoneyCharge;
    // 是否支持固定时长充电 （1是0否）
    private Integer isQuotaTimeCharge;
    // 国际协议
    private String internationalAgreement;
    // 自动停充 （1是0否）
    private Integer isAutoStopCharge;
    // 均/轮充设置 0均充 1轮充
    private Integer avgOrTurnCharge;
    // 合充开关 （1开0关）
    private Integer isCombineCharge;
    // 创建时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    // 更新时间
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    // 操作人id
    private Long updateByUserid;

    private String orderFiled;

    private Integer isAsc;

    private List<Integer> statusList;

    private String siteId;

    private List<String> siteIds;

    private String cardNo;

    private String cardChipNo;

//    private List<CfgEvseParam.CfgEvse.WhiteCard> whiteCardList;
    private List<WhiteCardV2> whiteCardList;

    private List<WhiteCardEvse> whiteCardEvses;

    /**
     * 若有值，则认为单桩下发紧急卡
     */
    private String singleEvseNo;

  /**
     * 桩名称或桩编号
     */
    private String evse;

    //主模板编号**唯一标识**
    private String templateCode;
}
