<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjCashOutflowRoMapper">

  <resultMap id="RESULT_TJ_CASH_OUTFLOW_PO" type="com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="stationType" jdbcType="INTEGER" property="stationType"/>
    <result column="stationArea" jdbcType="VARCHAR" property="stationArea"/>
    <result column="channelArea" jdbcType="VARCHAR" property="channelArea"/>
    <result column="channelAreaName" jdbcType="VARCHAR" property="channelAreaName"/>
    <result column="commonRate" jdbcType="DECIMAL" property="commonRate"/>
    <result column="firstYearRate" jdbcType="DECIMAL" property="firstYearRate"/>
    <result column="secondYearRate" jdbcType="DECIMAL" property="secondYearRate"/>
    <result column="thirdYearRate" jdbcType="DECIMAL" property="thirdYearRate"/>
    <result column="fourthYearRate" jdbcType="DECIMAL" property="fourthYearRate"/>
    <result column="fifthYearRate" jdbcType="DECIMAL" property="fifthYearRate"/>
    <result column="sixthYearRate" jdbcType="DECIMAL" property="sixthYearRate"/>
    <result column="seventhYearRate" jdbcType="DECIMAL" property="seventhYearRate"/>
    <result column="eighthYearRate" jdbcType="DECIMAL" property="eighthYearRate"/>
    <result column="ninthYearRate" jdbcType="DECIMAL" property="ninthYearRate"/>
    <result column="tenthYearRate" jdbcType="DECIMAL" property="tenthYearRate"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_CASH_OUTFLOW_PO">
    select * from t_tj_cash_outflow where id = #{id} and enable = true
  </select>

  <select id="findTjCashOutflow"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam"
    resultMap="RESULT_TJ_CASH_OUTFLOW_PO">
    select * from t_tj_cash_outflow where type = #{type} and enable = true
    order by id desc
  </select>

  <select id="findAllTjCashOutflow"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam"
    resultMap="RESULT_TJ_CASH_OUTFLOW_PO">
    select * from t_tj_cash_outflow where enable = true
    order by id desc
  </select>


</mapper>

