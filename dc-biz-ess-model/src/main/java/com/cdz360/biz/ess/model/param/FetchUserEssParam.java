package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.biz.model.iot.type.GtiVendor;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取用户储能设备参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FetchUserEssParam extends BaseListParam {

    @Schema(description = "用户ID", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(name = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIds;

    @Schema(description = "用户ID列表", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "品牌")
    @JsonInclude(Include.NON_NULL)
    private GtiVendor vendor;

    @Schema(description = "数据棒编号(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String dtuSerialNoLike;

    @Schema(description = "序列号(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String dnoLike;

    @Schema(description = "关联用户邮箱(支持模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String linkUserEmailLike;

    @Schema(description = "序列号列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> dnoList;

}
