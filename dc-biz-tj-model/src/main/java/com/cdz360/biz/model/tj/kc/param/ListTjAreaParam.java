package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取投建区域列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjAreaParam extends BaseListParam {

    @Schema(description = "场站组唯一ID")
    @JsonInclude(Include.NON_EMPTY)
    private String gid;

    @Schema(description = "场站组唯一ID列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> gidList;

    @Schema(description = "用户ID")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
