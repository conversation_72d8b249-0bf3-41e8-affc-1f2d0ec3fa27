package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.charge.vo.ChargePriceItem;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.constant.UserConstants;
import com.cdz360.biz.cus.domain.vo.SemiAuthVo;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.AccountScoreService;
import com.cdz360.biz.cus.service.CommScoreService;
import com.cdz360.biz.cus.service.CorpBizService;
import com.cdz360.biz.cus.service.ICardService;
import com.cdz360.biz.cus.service.SiteBlacklistService;
import com.cdz360.biz.cus.service.UserBizService;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserOpenidRoDs;
import com.cdz360.biz.ds.cus.ro.comm.ds.CommCusRefRoDs;
import com.cdz360.biz.ds.cus.ro.discount.ds.DiscountStrategyRoDs;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.ds.cus.ro.settlement.ds.SettlementCfgRoDs;
import com.cdz360.biz.ds.cus.rw.discount.ds.DiscountPriceRwDs;
import com.cdz360.biz.ds.cus.rw.discount.ds.DiscountStrategyRwDs;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.cus.auth.dto.AuthRequest;
import com.cdz360.biz.model.cus.auth.dto.CardDto;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.discount.dto.PriceStrategy;
import com.cdz360.biz.model.cus.discount.po.DiscountPricePo;
import com.cdz360.biz.model.cus.discount.po.DiscountPricePrimaryKey;
import com.cdz360.biz.model.cus.discount.po.DiscountStrategyPo;
import com.cdz360.biz.model.cus.discount.type.DiscountStatus;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDiscountDto;
import com.cdz360.biz.model.cus.site.param.SiteBlacklistEnableParam;
import com.cdz360.biz.model.cus.site.type.AuthMediaType;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.type.UserOpenidType;
import com.cdz360.biz.model.discount.param.DiscountServiceParam;
import com.cdz360.biz.model.discount.type.ProtocolType;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.settle.type.AlipayConstants;
import com.cdz360.biz.model.site.type.SitePayChannelType;
import com.cdz360.biz.utils.feign.site.SiteDataCoreFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.param.AuthMediaParam;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.exception.DcBalanceException;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteBlacklistUserFeignClient;
import com.chargerlinkcar.framework.common.service.AsyncCusBalanceService;
import com.chargerlinkcar.framework.common.service.EvseCacheService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.OptionalUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

/**
 * CardFacadeImpl
 *  调用好几个服务，隐藏实现细节，来实现卡鉴权相关等服务
 * @since 2019/5/22 9:48
 * <AUTHOR>
 */
@Slf4j
@Service
public class AuthFacadeImpl //implements IAuthFacade
{

    private final BigDecimal CCTIA_FROZEN_AMOUNT = BigDecimal.valueOf(20);//单位：元
    private final BigDecimal POST_PAID_BALANCE = BigDecimal.valueOf(999); // 单位: 元
    private final Integer PAY_TYPE_PERSONAL = 1;//默认扣款类型个人
    private final Integer PAY_TYPE_COM = 2;//默认扣款类型集团授权账户
    private final Integer PAY_TYPE_COMMERCIAL = 3;//默认扣款类型商户会员
    private final Integer VIN_STATUS_ACTIVE = 1;//活跃
    private final Integer VIN_STATUS_SUSPEND = 0;//休眠
    @Autowired
    private ICardService cardService;
    @Autowired
    private UserBizService userService;
    @Autowired
    private RBlocUserMapper rBlocUserMapper;
    @Autowired
    private VinMapper vinMapper;
    @Autowired
    private AsyncCusBalanceService dcCusBalanceService;
    @Autowired
    private SiteBlacklistService siteBlacklistService;
    @Autowired
    private CommercialFeignClient commercialFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private DiscountStrategyRoDs discountStrategyRoDs;
    // 默认冻结金额
    @Value("${iot.fee.max:200}")
    private BigDecimal frozenAmount;
    // 默认启动金额
    @Value("${iot.fee.min:5}")
    private BigDecimal startAmount;
    @Autowired
    private CommCusRefRoDs commCusRefRoDs;
    @Autowired
    private CorpBizService corpBizService;
    @Autowired
    private CommercialRoDs commercialRoDs;
    @Autowired
    private CommScoreService commScoreService;
    @Autowired
    private DiscountStrategyRwDs discountStrategyRwDs;
    @Autowired
    private DiscountPriceRwDs discountPriceRwDs;
    @Autowired
    private EvseCacheService evseCacheService;

    @Autowired
    private SiteBlacklistUserFeignClient siteBlacklistUserFeignClient;

//    @Autowired
//    private CorpSettlementService corpSettlementService;

    @Autowired
    private SettlementCfgRoDs settlementCfgRoDs;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;

    @Autowired
    private AccountScoreService accountScoreService;


    @Autowired
    private UserOpenidRoDs userOpenidRoDs;

    /**
     * 根据卡号查询卡鉴权信息
     *
     * @param evseVo       evseVo
     * @param cardNo       卡号
     * @param realTimeFlag realTimeFlag
     * @return
     */
    public Mono<AuthMediaResult> getCardAuth(
        EvseVo evseVo, String cardNo, boolean exactMatch, boolean realTimeFlag, BigDecimal siteFrozenAmount,
        CommercialSimpleVo siteComm, Boolean siteEcny,
        final List<String> siteGids
    ) {
        log.info("卡片鉴权。 cardNo: {}", cardNo);

        Card card = cardService.getCardByCardNoX(cardNo, exactMatch, siteComm.getTopCommId(),
            evseVo.getSiteId());

        log.info("卡片鉴权。 cardNo: {}, card: {}", cardNo, card);

        if (card == null) {
//            log.warn("鉴权失败,请检查在线卡是否存在 在线卡鉴权失败. cardNo = {}", cardNo);
//            throw new DcArgumentException("鉴权失败,请检查在线卡是否存在", Level.WARN);
            log.warn("鉴权失败[卡片不存在/场站不可用]. cardNo = {}", cardNo);
            throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                "鉴权失败(没有充电权限)");
        } else if (CardType.EMERGENCY.getCode() == card.getCardType()) {
            if (EvseProtocolType.OCPP.equals(evseVo.getProtocol())) {
                // OCPP桩允许紧急卡鉴权
            } else {
                log.warn("鉴权失败[卡类型错误]. cardNo = {}", cardNo);
                throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                    "鉴权失败(卡类型错误)");
            }
        }

        // 查询用户是否是该场站的黑名单用户
        this.checkBlacklist(evseVo.getSiteId(), card.getUserId(), siteComm.getTopCommId(),
            card.getCorpId(), card.getRBlocUserId());

        Mono<AuthMediaResult> authMediaResult;
        Pair<UserVo, Boolean> pair = this.getUserAndRelationshipCheck(AuthMediaType.CARD,
            siteComm, siteGids,
            card.getUserId(), card.getCorpId(),
            card.getRBlocUserId(), card.getCommId());

        //鼎充协议的桩
//        if (isDCAuth(evseVo)) {
        SemiAuthVo semiAuthVo = new SemiAuthVo();
        semiAuthVo.setUser(pair.getLeft())
            .setRealTimeFlag(realTimeFlag)
            .setCorpId(card.getCorpId())
            .setCorpBalancId(card.getRBlocUserId())
            .setSiteFrozenAmount(siteFrozenAmount)
            .setEvseCache(evseVo)
            .setSiteComm(siteComm)
            .setSiteAndCorpGidsVerified(pair.getRight())
            .setSiteGids(siteGids);
//            .setSiteEcny(siteEcny);
        authMediaResult = semiAuth(semiAuthVo);
//        } else {
//            authMediaResult = semiAuthForCctia(
//                siteFrozenAmount, pair.getLeft(), evseVo.getSiteId(),
//                siteComm, pair.getRight(),
//                siteGids);
//        }
        authMediaResult = authMediaResult.doOnNext(result -> {
            result.setMediaNo(card.getCardNo());
            result.setMediaStatus(card.getCardStatus());//这个状态的确定，基本依照在线卡的状态规则来确定
            result.setStations(card.getStations());
            result.setCarNo(card.getCarNo());

            CardDto cardDto = new CardDto();
            cardDto.setAuthNo(card.getCardNo())
                .setPrintNo(card.getCardChipNo())
                .setCardType(card.getCardType())
                .setCardName(card.getCardName())
                .setCommId(card.getCommId())
                .setPhone(card.getMobile())
                .setCusId(card.getUserId())
                .setCusName(card.getUserName())
                .setCarNo(card.getCarNo())
                .setCarNum(card.getCarNum())
                .setCarDepart(card.getCarDepart())
                .setLineNum(card.getLineNum());
            result.setCard(cardDto);
            log.debug("result = {}", result);
        });
        return authMediaResult;
    }

    /**
     * 获取用户信息并校验商户关系或者企业场站组关系
     *
     * @return UserVo(扣款用户信息); Boolean(是否已校验充电场站与企业场站组的关系);
     */
    private Pair<UserVo, Boolean> getUserAndRelationshipCheck(Integer authMediaType,
        CommercialSimpleVo siteComm, final List<String> siteGids,
        Long authUserId, Long authCorpId,
        Long authRBlocUserId, Long authCommId
    ) {
        String errorMsg = NumberUtils.equals(AuthMediaType.CARD, authMediaType)
            ? "卡和商户关系校验失败"
            : NumberUtils.equals(AuthMediaType.VIN, authMediaType) ? "VIN和商户关系校验失败"
                : "鉴权介质和商户关系校验失败";

        UserVo user = userService.findInfoByUid(authUserId, siteComm.getTopCommId());
        IotAssert.isTrue(user != null, "用户不存在");
        IotAssert.isTrue(UserConstants.USER_STATUS_NORMAL.equals(user.getStatus()),
            "用户状态不正确: " + user.getStatus());

        boolean siteAndCorpGidsVerified = false; // 是否已校验充电场站与企业场站组的关系

        if ((NumberUtils.gtZero(authCorpId) && NumberUtils.gtZero(authRBlocUserId))
            || NumberUtils.equals(PAY_TYPE_COM, user.getDefaultPayType())) {
            siteAndCorpGidsVerified = this.corpGidsRelationshipCheck(authCorpId, siteGids);
        }

        List<Long> siteCommIds = siteComm.getIdChainList();
        if (!siteAndCorpGidsVerified
            && (authCommId != null && !siteCommIds.contains(authCommId))) {
            log.warn("鉴权失败. {}. siteComm = {}, card.commId = {}", errorMsg, siteComm,
                authCommId);
            throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                "鉴权失败. " + errorMsg);
        }

        return Pair.of(user, siteAndCorpGidsVerified);
    }

    /**
     * 企业场站组关系校验
     *
     * @return 是否已校验充电场站与企业场站组的关系
     */
    private Boolean corpGidsRelationshipCheck(final Long corpId,
        final List<String> siteGids) {
        AtomicBoolean siteAndCorpGidsVerifiedAto = new AtomicBoolean(false); // 是否已校验充电场站与企业场站组的关系

        // 优先校验企业场站组逻辑
        ListResponse<String> response = authCenterFeignClient.getGidsById(corpId);
        FeignResponseValidate.checkIgnoreData(response);
        OptionalUtils.ofEmptyListAble(response.getData())
            .ifPresent(corpGids -> {
                // 企业绑定了场站组
                corpGids.retainAll(siteGids);
                boolean haveIntersection = CollectionUtils.isNotEmpty(corpGids);
                IotAssert.isTrue(haveIntersection, "非企业场站组站点");
                siteAndCorpGidsVerifiedAto.set(true);
            });
        return siteAndCorpGidsVerifiedAto.get();
    }

    private boolean isDCAuth(EvseVo evseVo) {
        if (evseVo == null) {
            return false;
        }
        List<EvseProtocolType> temp = List.of(EvseProtocolType.DC, EvseProtocolType.ACREL);
        if (evseVo.getProtocol() == null || temp.contains(evseVo.getProtocol())) {
            return true;
        }
        return false;
    }

    /**
     * 根据vin查询vin鉴权信息
     *
     * @param evseVo       evseVo
     * @param vin          卡号
     * @param realTimeFlag realTimeFlag
     * @param frozenAmount 站点冻结金额
     * @return
     */
    public Mono<AuthMediaResult> getVinAuth(
        EvseVo evseVo, String vin, boolean realTimeFlag, BigDecimal frozenAmount,
        CommercialSimpleVo siteComm,
        List<SitePayChannelType> sitePayTypes,  // 场站支持的支付方式
        final List<String> siteGids
    ) {

        VinDto vinDto = this.getVinDto(siteComm, sitePayTypes, vin);

        IotAssert.isNotNull(vinDto, "鉴权失败,请检查vin是否存在或vin状态是否正常。");
        Long userid = vinDto.getUserId();

        if (CollectionUtils.isNotEmpty(vinDto.getStationList())) {
            vinDto.setStation(CollectionUtils.join(vinDto.getStationList(), ","));
        }

        // 查询用户是否是该场站的黑名单用户
        this.checkBlacklist(evseVo.getSiteId(), userid, siteComm.getTopCommId(),
            vinDto.getCorpId(), vinDto.getRBlocUserId());

        Mono<AuthMediaResult> authMediaResult;
        Pair<UserVo, Boolean> pair = this.getUserAndRelationshipCheck(AuthMediaType.VIN,
            siteComm, siteGids,
            vinDto.getUserId(), vinDto.getCorpId(),
            vinDto.getRBlocUserId(), vinDto.getSubCommId());

        //鼎充协议的桩
//        if (isDCAuth(evseVo)) {
        SemiAuthVo semiAuthVo = new SemiAuthVo();
        semiAuthVo.setUser(pair.getLeft())
            .setRealTimeFlag(realTimeFlag)
            .setCorpId(vinDto.getCorpId())
            .setCorpBalancId(vinDto.getRBlocUserId())
            .setSiteFrozenAmount(frozenAmount)
            .setEvseCache(evseVo)
            .setSiteComm(siteComm)
            .setSiteAndCorpGidsVerified(pair.getRight())
            .setSiteGids(siteGids)
            .setPayType(vinDto.getPayType())
            .setOpenid(vinDto.getOpenid());
//            .setSiteEcny(siteEcny);
        authMediaResult = semiAuth(semiAuthVo); // 执行鉴权逻辑
//        } else {
//            authMediaResult = semiAuthForCctia(
//                frozenAmount, pair.getLeft(), evseVo.getSiteId(),
//                siteComm, pair.getRight(),
//                siteGids);
//        }

        authMediaResult = authMediaResult
            .doOnNext(result -> {
                result.setMediaNo(vinDto.getVin());
                //这个状态的确定，基本依照在线卡的状态规则来确定
                if (VIN_STATUS_ACTIVE.equals(vinDto.getStatus())) {
                    result.setMediaStatus(CardStatus.ACTIVE.getCode());
                } else if (VIN_STATUS_SUSPEND.equals(vinDto.getStatus())) {
                    result.setMediaStatus(CardStatus.INACTIVE.getCode());
                } else {
                    result.setMediaStatus(CardStatus.FAILURE.getCode());
                }
                result.setStations(vinDto.getStation());
                result.setCarNo(vinDto.getCarNo());
                //authMediaResult.setVin(vinDto);
                var vinD = new com.cdz360.biz.model.cus.auth.dto.VinDto();
                vinD.setCommId(vinDto.getCommId())
                    .setPhone(vinDto.getMobile())
                    .setCusId(vinDto.getUserId())
                    .setCusName(vinDto.getUserName())
                    .setCarNo(vinDto.getCarNo())
                    .setCarNum(vinDto.getCarNum())
                    .setCarDepart(vinDto.getCarDepart())
                    .setLineNum(vinDto.getLineNum())
                    .setBrand(vinDto.getBrand())
                    .setModel(vinDto.getModel())
                ;
                result.setVin(vinD);
            });

        return authMediaResult;
    }

    private VinDto getVinDto(CommercialSimpleVo siteComm,
        List<SitePayChannelType> sitePayTypes,  // 场站支持的支付方式
        String vin) {
        VinDto vinDto = vinMapper.selectByVinWithAuth(vin, siteComm.getTopCommId());
        if (vinDto == null && sitePayTypes != null && sitePayTypes.contains(
            SitePayChannelType.ALIPAY_CREDIT)) {
            // 场站开启了支付宝芝麻信用，如果用户开启支付宝即插即充，可允许充电
            vinDto = this.getVinDtoByAlipayVin(siteComm.getTopCommId(), vin);
            if (vinDto != null) {
                log.info("通过支付宝即插即充查询到 vinDto= {}", JsonUtils.toJsonString(vinDto));
                vinDto.setPayType(PayAccountType.ALIPAY_CREDIT);
            }
        }
        return vinDto;
    }

    /**
     * 用 vin 去查 t_user_openid 表的数据， 主要用于支付宝即插即充
     */
    private VinDto getVinDtoByAlipayVin(Long topCommId, String vin) {
        UserOpenidPo userOpenid = userOpenidRoDs.getUserOpenid(topCommId, UserOpenidType.ALIPAY_VIN,
            vin, null);
        if (userOpenid == null) {
            return null;
        }
        VinDto dto = vinMapper.getVinByUid(topCommId, userOpenid.getUid(), vin);
        if (dto != null) {
            UserOpenidPo alipayOpenid = userOpenidRoDs.getByUid(topCommId,
                UserOpenidType.ALIPAY_OPENID,
                userOpenid.getUid(), null);
            if (alipayOpenid == null) {
                log.warn(
                    "查询客户的支付宝openid信息失败, 不能使用支付宝即插即充. topCommId= {}, uid= {}, vin= {}",
                    topCommId, userOpenid.getUid(), vin);
                dto = null;
            } else if (StringUtils.isBlank(alipayOpenid.getExtraA()) || StringUtils.isBlank(
                alipayOpenid.getExtraB())) {
                log.warn(
                    "客户未开通支付宝芝麻信用, 不能使用支付宝即插即充. topCommId= {}, alipayOpenid= {}, vin= {}",
                    topCommId, alipayOpenid, vin);
                dto = null;
            } else {
                dto.setOpenid(alipayOpenid);
            }
        }
        return dto;
    }

    private void checkBlacklist(String siteId, Long uid, Long commId, Long corpId,
        Long corpUserId) {
        // 查询用户是否是该场站的黑名单用户
        // 您已被禁止在“***”（充电站名称）充电，如有疑问请线上联系客服或拨打（场站电话）或直接与场站工作人员沟通
        SiteBlacklistEnableParam enableParam = new SiteBlacklistEnableParam();
        enableParam
            .setSiteId(siteId)
            .setUid(uid);

        UserVo infoByUid = userService.findInfoByUid(uid, commId);
        if (infoByUid != null) {
            enableParam.setPhone(infoByUid.getPhone());

            // 企业后付费，授信无限制不校验用户是否欠费
            boolean checkDebt = true;
            if (null != corpId && null != corpUserId) {
                RBlocUser corpUser = rBlocUserMapper.findRBlocUserById(corpUserId, false);
                if (null != corpUser && LimitCycle.UNLIMITED.equals(corpUser.getLimitCycle())) {
                    CorpPo corp = corpBizService.getCorp(corpId);
                    if (null != corp && SettlementType.POSTPAID.equals(corp.getSettlementType())) {
                        log.info("企业后付费授信无限制，不受欠费限制: corpId = {}, corpUserId = {}",
                            corpId, corpUserId);
                        checkDebt = false;
                    }
                }
            }

            if (checkDebt) {
                IotAssert.isTrue(Boolean.FALSE == infoByUid.getDebt(),
                    "您存在欠费订单，请将欠费的订单处理再充电");
            }
        }

        ObjectResponse<Boolean> res = siteBlacklistService.userInSiteBlacklist(enableParam);
//        ObjectResponse<Boolean> isSiteBlacklist = siteBlacklistUserFeignClient.userInSiteBlacklist(enableParam);
//        FeignResponseValidate.check(isSiteBlacklist);
        if (res.getData()) {
            log.warn("用户是场站黑名单: siteId = {}, uid = {}", siteId, uid);
            throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                "鉴权失败,用户是场站黑名单", Level.WARN);
//            throw new DcArgumentException("鉴权失败,用户是场站黑名单", Level.WARN);
        }

    }

    /**
     * 通过balance id和扣款类型，获得生成半成品鉴权结果， 结果只包含以下参数冻结金额和账户余额 frozenAmount balance
     *
     * @return
     */
    public Mono<AuthMediaResult> getAuthByBalanceIdAndPayType(AuthMediaParam param) {

        BigDecimal curFrozenAmount =
            param.getRealTimeFlag() ? param.getFrozenAmount() : frozenAmount;

        Mono<AuthMediaResult> ret;

        AuthRequest authRequest = new AuthRequest();
        authRequest.setBalanceId(param.getBalanceId());
        BigDecimal initAmount = param.getInitAmount();
        PayAccountType payType = param.getPayType();
        if (payType == null) {
            payType = PayAccountType.valueOf(param.getDefaultPayType());
        }
//        @NotNull(message = "默认扣款账户不能为空") Integer payType = param.getDefaultPayType();
        if (initAmount != null && DecimalUtils.gtZero(initAmount)) {
            //if (initAmount != null && initAmount > 0) {
            authRequest.setInitAmount(initAmount);
        } else {
            authRequest.setInitAmount(startAmount);
        }
        authRequest.setCommId(param.getTopCommId());
        authRequest.setCurFrozenAmount(curFrozenAmount);
        authRequest.setDefaultPayType(payType.getCode());
        authRequest.setPayType(payType);
        authRequest.setUserId(param.getCusId());
        authRequest.setSiteId(param.getSiteId());
        if (StringUtils.isNotBlank(param.getEvseNo())) {
            authRequest.setEvseCache(evseCacheService.getCachedEvse(param.getEvseNo()));
        }

        authRequest.setSiteGids(param.getSiteGids());

        CommercialSimpleVo siteComm = this.commercialRoDs.getCommerial(param.getSiteCommId());

        if (PAY_TYPE_PERSONAL.equals(payType.getCode())) {
            ret = authByPersonal(authRequest, siteComm);//现金账户
        } else if (PAY_TYPE_COM.equals(payType.getCode())) {
            ret = authByGroup(authRequest, siteComm);//集团账户
        } else if (PAY_TYPE_COMMERCIAL.equals(payType.getCode())) {
            ret = authByCommercial(authRequest, siteComm);//商户会员
        } else {
            throw new DcBalanceException("鉴权失败，默认扣款类型缺失。", Level.WARN);//也作为余额不足对待
        }
        return ret;
    }

    private Mono<AuthMediaResult> semiAuthForCctia(
        BigDecimal siteFrozenAmount, UserVo user, String siteId,
        CommercialSimpleVo siteComm, Boolean siteAndCorpGidsVerified,
        final List<String> siteGids
    ) {
        log.info("鉴权主要逻辑。 siteId: {}", siteId);
        Mono<AuthMediaResult> ret;

        AuthRequest authRequest = new AuthRequest();
        authRequest.setBalanceId(user.getBalanceId());
        authRequest.setInitAmount(siteFrozenAmount);
        authRequest.setCommId(user.getCommId());
        authRequest.setSiteAndCorpGidsVerified(siteAndCorpGidsVerified);
        authRequest.setCurFrozenAmount(siteFrozenAmount);
        authRequest.setDefaultPayType(user.getDefaultPayType());
        authRequest.setPayType(PayAccountType.valueOf(user.getDefaultPayType()));
        authRequest.setUserId(user.getId());
        authRequest.setSiteId(siteId);
        authRequest.setSiteGids(siteGids);
        authRequest.setEvseCache(null);

        if (PAY_TYPE_PERSONAL.equals(user.getDefaultPayType())) {
            ret = authByPersonal(authRequest, siteComm);//现金账户
        } else if (PAY_TYPE_COM.equals(user.getDefaultPayType())) {
            ret = authByGroup(authRequest, siteComm);//集团账户
        } else if (PAY_TYPE_COMMERCIAL.equals(user.getDefaultPayType())) {
            ret = authByCommercial(authRequest, siteComm);//商户会员
        } else {
            throw new DcBalanceException("鉴权失败，默认扣款类型缺失。", Level.WARN);//也作为余额不足对待
        }

        return ret;

    }

    /**
     * 鉴权主要逻辑，生成半成品鉴权结果
     * <p>
     * corpId 可传null,卡或VIN码若创建于企业平台，则存在该值 corpBalancId 可传null,卡或VIN码若创建于企业平台，则存在该值，且需以集团账户进行鉴权
     *
     * @return
     */
    public Mono<AuthMediaResult> semiAuth(SemiAuthVo semiAuthVo) {
        log.info("鉴权主要逻辑。 semiAuthVo: {}", semiAuthVo);
        String siteId =
            semiAuthVo.getEvseCache() != null ? semiAuthVo.getEvseCache().getSiteId() : null;
        CommercialSimpleVo siteComm = semiAuthVo.getSiteComm();
//        Boolean siteEcny = semiAuthVo.getSiteEcny();

        UserVo user = semiAuthVo.getUser();

        Mono<AuthMediaResult> ret;

        BigDecimal curFrozenAmount =
            Boolean.TRUE.equals(semiAuthVo.getRealTimeFlag()) ? semiAuthVo.getSiteFrozenAmount()
                : frozenAmount;

        AuthRequest authRequest = new AuthRequest();
        authRequest.setBalanceId(user.getBalanceId());
        authRequest.setInitAmount(startAmount);
        authRequest.setCommId(user.getCommId());
        authRequest.setSiteAndCorpGidsVerified(semiAuthVo.getSiteAndCorpGidsVerified());
        authRequest.setCurFrozenAmount(curFrozenAmount);
        authRequest.setDefaultPayType(user.getDefaultPayType());
        authRequest.setPayType(PayAccountType.valueOf(user.getDefaultPayType()));
        authRequest.setUserId(user.getId());
        authRequest.setSiteId(siteId);
        authRequest.setSiteGids(semiAuthVo.getSiteGids());
        authRequest.setEvseCache(semiAuthVo.getEvseCache());

        if (semiAuthVo.getCorpId() != null && semiAuthVo.getCorpId() > 0
            && semiAuthVo.getCorpBalancId() != null && semiAuthVo.getCorpBalancId() > 0) {
            log.info("该鉴权介质创建于企业平台，存在企业ID，需以企业账户进行鉴权");
            authRequest.setBalanceId(semiAuthVo.getCorpBalancId());
            authRequest.setDefaultPayType(PAY_TYPE_COM);
            authRequest.setPayType(PayAccountType.CREDIT);
            ret = authByGroup(authRequest, siteComm);//集团账户
        } else if (PayAccountType.ALIPAY_CREDIT == semiAuthVo.getPayType()) {  // 支付宝芝麻信用
            log.info("使用支付宝芝麻信用: siteId= {}, uid= {}", siteId, user.getId());
            AuthMediaResult mediaResult = new AuthMediaResult();
            mediaResult.setCommId(authRequest.getCommId());
            mediaResult.setDefaultPayType(PayAccountType.ALIPAY_CREDIT.getCode());
            mediaResult.setPayAccountId(null);
            mediaResult.setUserId(authRequest.getUserId());
            mediaResult.setFrozenAmount(new BigDecimal(AlipayConstants.CREDIT_ALIPAY_AMOUNT));
            mediaResult.setBalance(new BigDecimal(AlipayConstants.CREDIT_ALIPAY_AMOUNT));
            mediaResult.setPayType(semiAuthVo.getPayType());
            mediaResult.setOpenid(semiAuthVo.getOpenid());
            ret = Mono.just(mediaResult);
        } else if (PAY_TYPE_COM.equals(user.getDefaultPayType())) {
            ret = authByGroup(authRequest, siteComm);//集团账户
        }
//        else if (Boolean.TRUE.equals(siteEcny) && Boolean.TRUE.equals(user.getEcny())) {
//            log.info("使用信用付: siteId = {}, uid = {}", siteId, user.getId());
//
//            AuthMediaResult mediaResult = new AuthMediaResult();
//            mediaResult.setCommId(authRequest.getCommId());
//            mediaResult.setDefaultPayType(PayAccountType.E_CNY.getCode());
//            mediaResult.setPayAccountId(authRequest.getBalanceId());
//            mediaResult.setUserId(authRequest.getUserId());
//            mediaResult.setFrozenAmount(curFrozenAmount);
//            mediaResult.setBalance(POST_PAID_BALANCE);
//
//            ret = Mono.just(mediaResult);
//        }
        else if (PAY_TYPE_PERSONAL.equals(user.getDefaultPayType())) {
            // 直付通商户约束
            if (siteComm.getId() != null) {
                ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                    siteComm.getId());
                FeignResponseValidate.check(commercial);

                Boolean enableBalance = commercial.getData().getEnableBalance();
                if (null != enableBalance && !enableBalance) {
                    log.warn("直付商配置不允许使用个人账户充电: commId = {}", siteComm.getId());
                    throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                        "直付商配置不允许使用个人账户充电");
                }
            }

            ret = authByPersonal(authRequest, siteComm);//现金账户
        } else if (PAY_TYPE_COMMERCIAL.equals(user.getDefaultPayType())) {
            ret = authByCommercial(authRequest, siteComm);//商户会员
        } else {
            throw new DcBalanceException("鉴权失败，默认扣款类型缺失。", Level.WARN);//也作为余额不足对待
        }

        return ret;

    }

    private Mono<AuthMediaResult> authByPersonal(AuthRequest authRequest,
        CommercialSimpleVo siteComm) {

        log.info("个人账户额度校验。authRequest: {}", authRequest);

        UserVo user = userService.findInfoByUid(authRequest.getUserId(), siteComm.getTopCommId());
        if (user == null) {
            throw new DcServiceException("鉴权失败，用户不存在。");
        }
        if (UserConstants.USER_STATUS_BLACK.equals(user.getStatus())) {
            throw new DcServiceException("鉴权失败，用户已经被拉黑。", Level.WARN);
        }
        Mono<AuthMediaResult> mono = dcCusBalanceService.getPoint2(PayAccountType.PERSONAL,
                authRequest.getBalanceId(),
                authRequest.getBalanceId(),
                authRequest.getUserId())
            .switchIfEmpty(Mono.error(new DcServiceException("鉴权失败，非个人账户。")))
//                .doOnNext(cusBalance -> {
//                    IotAssert.isTrue(cusBalance.isPresent(), "鉴权失败，非个人账户。");
//                })
            .map(cusBalance -> {
//                    PointPo cusBalance = cusBalanceRes.get();
                AuthMediaResult ret = new AuthMediaResult();
                //平台手动开启充电时，会根据枪头数改变 authRequest.getInitAmount()的值，从而判断能否开启充电
                if (DecimalUtils.lt(cusBalance.getAvailable(), authRequest.getInitAmount())) {
                    //if (!(DecimalUtils.yuan2fen(cusBalance.getAvailable()) > authRequest.getInitAmount())) {
                    log.warn("cusBalance.available = {}, authRequest.initAmount = {}",
                        cusBalance.getAvailable(), authRequest.getInitAmount());
                    throw new DcBalanceException("鉴权失败，个人可用额度不足。", Level.WARN);
                }

                // 取小的
                BigDecimal frozen = DecimalUtils.min(cusBalance.getAvailable(),
                    authRequest.getCurFrozenAmount());
                // long frozen = Math.min( cusBalance.getAvailable().intValue(), authRequest.getCurFrozenAmount());
                ret.setBalance(cusBalance.getPoint())
                    .setFrozenAmount(frozen);
                ret.setCommId(authRequest.getCommId());
                ret.setDefaultPayType(authRequest.getDefaultPayType());
                ret.setPayType(authRequest.getPayType());
                ret.setPayAccountId(authRequest.getBalanceId());
                ret.setUserId(authRequest.getUserId());

                return ret;
            });

        if (CollectionUtils.isNotEmpty(authRequest.getSiteGids())) {
            return mono.zipWith(accountScoreService.getScoreSettingStrategy(authRequest.getUserId(),
                    authRequest.getSiteGids(), null))
                .map(tu -> {
                    final ScoreSettingDiscountDto discountDto = tu.getT2().getData();
                    if (discountDto != null) {

//                        tu.getT1().setScoreServFeeDiscount(
//                            discountDto.getScoreSettingDto().getCurrentLevel().getDiscount());
//                        tu.getT1().setScoreSettingId(discountDto.getScoreSettingDto().getId());

                        tu.getT1().setScoreSettingDiscountDto(discountDto);
                    }
                    return tu.getT1();
                });
        } else {
            return mono;
        }
    }

    private Mono<AuthMediaResult> authByGroup(AuthRequest authRequest,
        CommercialSimpleVo siteComm) {

        log.info("集团授权账户额度校验。authRequest: {}", authRequest);

        AuthMediaResult ret = new AuthMediaResult();

        RBlocUser rBlocUser = rBlocUserMapper.findRBlocUserById(authRequest.getBalanceId(), false);

        BigDecimal initAmount = authRequest.getInitAmount();//平台手动开启充电时，会根据枪头数改变 authRequest.getInitAmount()的值，从而判断能否开启充电
        log.info("集团授权账户额度校验。balanceId: {}, payType: {}, fee_min: {}, rBlocUser: {}",
            authRequest.getBalanceId(), authRequest.getPayType(), initAmount, rBlocUser);

        IotAssert.isNotNull(rBlocUser, "鉴权失败，非集团账户。");

        IotAssert.isTrue(UserConstants.BLOC_USER_STATUS_NORMAL.equals(rBlocUser.getStatus()),
            "此集团授权账户已禁用");

        boolean unlimitedUser = LimitCycle.UNLIMITED.equals(rBlocUser.getLimitCycle());
        BigDecimal personalBuffer = unlimitedUser ?
            POST_PAID_BALANCE : // 授信账户无限制时，赋值999元
            rBlocUser.getLimitMoney()
                .subtract(rBlocUser.getBalance()).subtract(rBlocUser.getFrozenAmount());//授权可用额度
        if (!unlimitedUser//授信账户无限制时此处不做判断，后面用企业可用余额来判断
            && DecimalUtils.lt(personalBuffer, initAmount)) {
            log.warn("鉴权失败，授权可用额度不足,personalBuffer:{}", personalBuffer);
            throw new DcBalanceException("鉴权失败，授权可用额度不足。", Level.WARN);
        }

        CorpPo corp = corpBizService.getCorp(rBlocUser.getBlocUserId());
        IotAssert.isNotNull(corp, "企业客户账号不存在");
        IotAssert.isTrue(corp.getEnable(), "企业客户账号已停用");

        if (BooleanUtils.isNotTrue(authRequest.getSiteAndCorpGidsVerified())) {
            IotAssert.isNotNull(authRequest.getSiteGids(), "站点对应场站组不能为空");

            Boolean siteAndCorpGidsVerified = this.corpGidsRelationshipCheck(corp.getId(),
                authRequest.getSiteGids());

            if (!siteAndCorpGidsVerified
                && (null != siteComm.getIdChainList() &&
                !siteComm.getIdChainList().contains(corp.getCommId()))) {
                log.warn("鉴权失败. 企业和商户关系校验失败. siteComm = {}, corp = {}", siteComm,
                    corp);
                throw new DcServiceException("鉴权失败. 企业和商户关系校验失败");
            }
        }

        DiscountStrategyPo strategyPo = null;

        // 企业客户后结算模式处理逻辑
        if (SettlementType.POSTPAID == corp.getSettlementType()) {
            ret.setPostPaid(true);
        } else { // 预付费协议价
            ret.setPostPaid(false);

            DiscountPricePrimaryKey key = new DiscountPricePrimaryKey();
            key.setUid(corp.getUid())
                .setSiteId(authRequest.getSiteId())
                .setAccountCode(corp.getTopCommId())
                .setAccountType(PayAccountType.CORP);
            strategyPo = discountStrategyRoDs.getByPrimaryKey(key);
            if (null != strategyPo) {
                ret.setDiscountRefId(strategyPo.getId());
            }
        }

        ret.setCommId(authRequest.getCommId());
        ret.setDefaultPayType(authRequest.getDefaultPayType());
        ret.setPayType(authRequest.getPayType());
        ret.setCorpId(corp.getId());
        ret.setPayAccountId(authRequest.getBalanceId());
        ret.setUserId(authRequest.getUserId());

        // 若3.7桩的鉴权账户配置了协议价，则下发差异化充电价格
        DiscountStrategyPo finalStrategyPo = strategyPo;
        Mono<AuthMediaResult> mono = Mono.just(ret)
            .filter(e -> finalStrategyPo != null && authRequest.getEvseCache() != null
                && authRequest.getEvseCache().getProtocolVer()
                >= DcBizConstants.PROTOCOL_VERSION_370)
            .flatMap(e -> {
                PriceStrategy priceStrategy = (PriceStrategy) finalStrategyPo.getStrategy();
                DiscountServiceParam param = new DiscountServiceParam();
                param.setPriceCode(authRequest.getEvseCache().getPriceCode())
                    .setType(finalStrategyPo.getType())
                    .setDiscount(priceStrategy.getDiscount())
                    .setDiscountCustomFee(priceStrategy.getDiscountCustomFee());
                return siteDataCoreFeignClient.discountServiceFeeByCode(param);
            })
            .doOnNext(FeignResponseValidate::check)
            .map(ObjectResponse::getData)
            .filter(e -> CollectionUtils.isNotEmpty(e.getPriceItemList()))
            .map(e -> {
                ChargePriceVo priceVo = new ChargePriceVo();
                priceVo.setId(authRequest.getEvseCache().getPriceCode())
                    .setItemList(e.getPriceItemList().stream().map(item -> {
                        ChargePriceItem temp = new ChargePriceItem();
                        temp.setCode(item.getCode())
                            .setCategory(item.getCategory())
                            .setStartTime(item.getStartTime())
                            .setEndTime(item.getStopTime())
                            .setElecPrice(
                                item.getDiscountElecPrice() != null ? item.getDiscountElecPrice()
                                    : item.getElecPrice())
                            .setServPrice(
                                item.getDiscountServPrice() != null ? item.getDiscountServPrice()
                                    : item.getServPrice());
                        return temp;
                    }).collect(Collectors.toList()));

                ret.setPriceVo(priceVo);
                return ret;
            })
            .switchIfEmpty(Mono.just("无需下发差异化充电价格").map(e -> ret));

        if (!ret.getPostPaid()) {
//            PointPo corpAcc =
            mono = mono.flatMap(result -> {
                return this.dcCusBalanceService.getPoint2(PayAccountType.PERSONAL,
                        corp.getTopCommId(),
                        corp.getTopCommId(),
                        corp.getUid())
                    .switchIfEmpty(Mono.error(
                        new DcBalanceException("鉴权失败，企业账户余额不足。", Level.ERROR)))
                    .doOnNext(corpAcc -> {
//                            //用专门的DcBalanceException来抛出余额不足的提示
//                            if (corpAccRes.isEmpty()) {
//                                throw new DcBalanceException("鉴权失败，企业账户余额不足。", Level.ERROR);
//                            }
//                            PointPo corpAcc = corpAccRes.get();
                        log.info(
                            "企业授权账户额度校验。balanceId: {}, payType: {}, fee_min: {}, corpAcc: {}",
                            authRequest.getBalanceId(), authRequest.getPayType(), initAmount,
                            corpAcc);
                        if (DecimalUtils.lt(corpAcc.getAvailable(), initAmount)) {
                            // 此处为ERROR级日志，用于及时提醒公交客户充值余额，避免公交无法充电
                            log.error("鉴权失败，企业可用额度不足, 商户: {}, 企业: {}, 可用余额:{}",
                                corp.getCommName(), corp.getCorpName(), corpAcc.getAvailable());
                            throw new DcBalanceException("鉴权失败，企业可用额度不足。", Level.WARN);
                        }
                    })
                    .map(corpAcc -> {
//                            PointPo corpAcc = corpAccRes.get();
                        BigDecimal frozen = DecimalUtils.min(
                            DecimalUtils.min(personalBuffer, corpAcc.getAvailable()),
                            authRequest.getCurFrozenAmount());
                        result.setFrozenAmount(frozen);

                        result.setBalance(
                            DecimalUtils.min(personalBuffer, corpAcc.getAvailable()));
                        result.setCardMakerBalance(result.getBalance());
                        return result;
                    });
            });

        } else {
            mono = mono.doOnNext(result -> {
                BigDecimal companyBuffer = POST_PAID_BALANCE; // 后结算默认值
                BigDecimal frozen = DecimalUtils.min(
                    DecimalUtils.min(personalBuffer, companyBuffer),
                    authRequest.getCurFrozenAmount());

                result.setFrozenAmount(frozen);

                result.setBalance(
                    DecimalUtils.min(personalBuffer, companyBuffer));
                result.setCardMakerBalance(personalBuffer);

            });
        }

        return mono;

    }

    private Mono<AuthMediaResult> authByCommercial(AuthRequest authRequest,
        CommercialSimpleVo siteComm) {

        log.info("商户会员额度校验。authRequest: {}", authRequest);

        //判断账户是否被禁用
        if (commCusRefRoDs.checkCommCusRefDisabled(authRequest.getBalanceId(),
            authRequest.getUserId())) {
            throw new DcServiceException(
                "权益会员卡已停用，若继续充电可更换支付方式或联系商户会员所属商户工作人员。");
        }
        List<Long> siteCommIds = siteComm.getIdChainList();
        if (null != siteCommIds && !siteCommIds.contains(authRequest.getBalanceId())) {
            log.warn("鉴权失败. 商户关系校验失败. siteComm = {}, authRequest = {}", siteComm,
                authRequest);
            throw new DcServiceException("鉴权失败. 商户关系校验失败");
        }
//        PointPo cusBalance =
        return dcCusBalanceService.getPoint2(PayAccountType.COMMERCIAL, authRequest.getCommId(),
                authRequest.getBalanceId(),
                authRequest.getUserId())
            .switchIfEmpty(Mono.error(new DcServiceException("鉴权失败，非商户会员。")))
//                .doOnNext(cusBalance -> {
//                    IotAssert.isTrue(cusBalance.isPresent(), "鉴权失败，非商户会员。");
//                })
            .map(cusBalance -> {
//                    PointPo cusBalance = cusBalanceRes.get();
                AuthMediaResult ret = new AuthMediaResult();
                //平台手动开启充电时，会根据枪头数改变 authRequest.getInitAmount()的值，从而判断能否开启充电
                if (DecimalUtils.lt(cusBalance.getAvailable(), authRequest.getInitAmount())) {
                    //if (!(DecimalUtils.yuan2fen(cusBalance.getAvailable()) > authRequest.getInitAmount())) {
                    log.warn("鉴权失败，商户会员可用额度不足,available:{}",
                        cusBalance.getAvailable());
                    throw new DcBalanceException("鉴权失败，商户会员可用额度不足。", Level.WARN);
                }
                BigDecimal frozen = DecimalUtils.min(cusBalance.getAvailable(),
                    authRequest.getCurFrozenAmount());
                ret.setBalance(cusBalance.getPoint()).setFrozenAmount(frozen);

                ret.setCommId(authRequest.getCommId());
                ret.setDefaultPayType(authRequest.getDefaultPayType());
                ret.setPayType(authRequest.getPayType());
                ret.setPayAccountId(authRequest.getBalanceId());
                ret.setUserId(authRequest.getUserId());

                return ret;
            })
            .flatMap(res -> {

                // STEP 1.查找已有折扣信息
                DiscountPricePrimaryKey key = new DiscountPricePrimaryKey();
                key.setUid(res.getUserId())
                    .setSiteId("") // 固定为""
                    .setAccountCode(res.getPayAccountId())
                    .setAccountType(PayAccountType.COMMERCIAL);
                DiscountStrategyPo strategyPo = discountStrategyRoDs.getByPrimaryKey(key);

                return commScoreService.getDiscount(res.getPayAccountId(),
                        res.getUserId()) // STEP 2.根据商户会员积分重新获取折扣比例，并和已有折扣信息比较
                    .doOnNext(FeignResponseValidate::check)
                    .map(ObjectResponse::getData)
                    .filter(Optional::isPresent) // 若折扣不存在则订单不打折
                    .map(e -> e.get().multiply(BigDecimal.TEN)) // 因为需求的差异，所以乘以10
                    .map(discount -> {

                        if (strategyPo != null
                            && DecimalUtils.eq(
                            ((PriceStrategy) strategyPo.getStrategy()).getDiscount(), discount)) {
                            // 折扣信息已存在，且折扣比例无变化
                            res.setDiscountRefId(strategyPo.getId());
                            return res;
                        }

                        // STEP 3.折扣比例已改变，重新插入折扣信息
                        DiscountStrategyPo discountStrategyPo = new DiscountStrategyPo();
                        discountStrategyPo.setType(ProtocolType.DISCOUNT)
                            .setStrategy(new PriceStrategy()
                                .setDiscount(discount)
                                .setType(ProtocolType.DISCOUNT));
                        boolean boo = discountStrategyRwDs.insert(discountStrategyPo);
                        if (!boo) {
                            log.error(
                                "鉴权(商户会员)时重新插入折扣信息失败，payAccountId: {}, userId: {}, discountStrategyPo: {}",
                                res.getPayAccountId(), res.getUserId(), discountStrategyPo);
                            return res;
                        }
                        DiscountPricePo pricePo = new DiscountPricePo();
                        pricePo.setUid(res.getUserId())
                            .setAccountType(PayAccountType.COMMERCIAL)
                            .setAccountCode(res.getPayAccountId())
                            .setSiteId("") // 固定为""
                            .setStatus(DiscountStatus.ENABLE)
                            .setDiscountRefId(discountStrategyPo.getId())
                            .setUpdateOpId(0L)
                            .setUpdateOpName("系统");
                        boo = discountPriceRwDs.upset(pricePo);
                        if (!boo) {
                            log.error(
                                "鉴权(商户会员)时重新插入折扣信息失败，payAccountId: {}, userId: {}, pricePo: {}",
                                res.getPayAccountId(), res.getUserId(), pricePo);
                            return res;
                        }

                        // STEP 4.协议价优惠ID
                        res.setDiscountRefId(discountStrategyPo.getId());
                        return res;
                    })
                    .switchIfEmpty(Mono.just("未获取到商户会员有效折扣信息，不打折")
                        .map(e -> {
                            log.info(e);
                            return res;
                        }));
            });

    }

}