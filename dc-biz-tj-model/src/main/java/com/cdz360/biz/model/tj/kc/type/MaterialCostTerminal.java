package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "电缆材质")
@Getter
public enum MaterialCostTerminal implements DcEnum {

    UNKNOWN(999, "未知"),

    SINGLE_250(1, "250A终端单枪"),
    DOUBLE_250(2, "250A终端双枪"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostTerminal(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostTerminal valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostTerminal flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return MaterialCostTerminal.UNKNOWN;
    }

}
