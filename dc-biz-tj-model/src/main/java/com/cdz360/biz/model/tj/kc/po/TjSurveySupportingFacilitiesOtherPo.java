package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-其他设施")
public class TjSurveySupportingFacilitiesOtherPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "设施名称")
    private String name;

    @ApiModelProperty(value = "设施数量")
    private Integer facilitiesCount;

    @ApiModelProperty(value = "设施单价")
    private BigDecimal price;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

