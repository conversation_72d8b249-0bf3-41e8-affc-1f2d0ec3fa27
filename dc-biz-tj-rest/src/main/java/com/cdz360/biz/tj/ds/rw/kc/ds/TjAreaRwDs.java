package com.cdz360.biz.tj.ds.rw.kc.ds;

import com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaRwMapper;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaRwDs {

    @Autowired
    private TjAreaRwMapper tjAreaRwMapper;

    public TjAreaPo getByAid(Long aid, boolean lock) {
        return this.tjAreaRwMapper.getByAid(aid, lock);
    }

    public boolean insertTjArea(TjAreaPo tjAreaPo) {
        return this.tjAreaRwMapper.insertTjArea(tjAreaPo) > 0;
    }

    public boolean updateTjArea(TjAreaPo tjAreaPo) {
        return this.tjAreaRwMapper.updateTjArea(tjAreaPo) > 0;
    }

    public boolean disableTjArea(Long aid) {
        return this.tjAreaRwMapper.disableTjArea(aid) > 0;
    }
}

