package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialSiteDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.dto.SiteTinyDto;
import com.cdz360.biz.model.merchant.param.*;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class CommercialService {

    @Autowired
    private CommercialFeignClient commercialFeignClient;

    @Autowired
    private SiteDataCoreFeignClient siteFeignClient;


    public ObjectResponse<CommercialTreeNode> getCommTree(GetCommTreeParam param) {
        ObjectResponse<CommercialTreeNode> tree = this.commercialFeignClient.getCommTree(param.getCommId());
        if (Boolean.TRUE.equals(param.getFetchSite())) {
            Map<Long, CommercialTreeNode> commMap = new HashMap<>();
            commMap.put(tree.getData().getId(), tree.getData());
            this.tree2MapLoop(tree.getData(), commMap);

            ListSiteParam paramX = new ListSiteParam();
            paramX.setCommIdChain(tree.getData().getIdChain())
                    .setStatusList(List.of(SiteStatus.ONLINE, SiteStatus.OPENING, SiteStatus.UNAVAILABLE))
                    .setIncludedHlhtSite(param.getIncludedHlhtSite())
                    .setOnlySiteWithMeter(param.getOnlySiteWithMeter());
            ListResponse<SiteTinyDto> siteListRes = this.siteFeignClient.getSiteTinyList(paramX);
            if (siteListRes.getStatus() == DcConstants.KEY_RES_CODE_SUCCESS) {
                siteListRes.getData().stream().forEach(s -> this.addSite2Comm(s, commMap));
            }
        }
        return tree;
    }

    private void tree2MapLoop(CommercialTreeNode node, Map<Long, CommercialTreeNode> map) {
        if (CollectionUtils.isEmpty(node.getChildren())) {
            return;
        }
        node.getChildren().stream().forEach(n -> {
            map.put(n.getId(), n);
            tree2MapLoop(n, map);
        });
    }

    private void addSite2Comm(SiteTinyDto site, Map<Long, CommercialTreeNode> commMap) {
        CommercialTreeNode comm = commMap.get(site.getCommId());
        if (comm == null) {
            log.error("错误数据. 场站没有归属的商户. site = {}", site);
            return;
        }
        if (comm.getSiteList() == null) {
            comm.setSiteList(new ArrayList<>());
        }
        comm.getSiteList().add(site);
    }
    public ObjectResponse<Commercial> convertCommPayAndRefund(ConvertCommPayParam param) {
        if (null == param.getCommId() || param.getCommId() < 1L) {
            throw new DcArgumentException("商户ID无线, 请刷新重试");
        }
        IotAssert.isNotNull(param.getEnableCorpDeposit(),"企业在线充值状态不能为空");
        IotAssert.isNotNull(param.getEnableCorpRefund(),"企业在线退款状态不能为空");
        IotAssert.isNotNull(param.getEnableOnlinePay(),"商户在线充值状态不能为空");
        IotAssert.isNotNull(param.getEnableCommRefund(),"商户在线退款状态不能为空");
        return commercialFeignClient.convertCommPayAndRefund(param);
    }

    public ObjectResponse<Commercial> convertCommCorpDeposit(Long commId) {
        if (null == commId || commId < 1L) {
            throw new DcArgumentException("商户ID无线, 请刷新重试");
        }
        return commercialFeignClient.convertCommCorpDeposit(commId);
    }

    public ObjectResponse<Commercial> convertCommCorpRefund(Long commId) {
        if (null == commId || commId < 1L) {
            throw new DcArgumentException("商户ID无线, 请刷新重试");
        }
        return commercialFeignClient.convertCommCorpRefund(commId);
    }

    public ObjectResponse<Commercial> convertCommRefund(Long commId) {
        if (null == commId || commId < 1L) {
            throw new DcArgumentException("商户ID无线, 请刷新重试");
        }
        return commercialFeignClient.convertCommRefund(commId);
    }

    /**
     * 新增商户
     */

    public ObjectResponse<Long> addComm(AddCommercialParam param) {
        return this.commercialFeignClient.addComm(param);
    }

    /**
     * 海外版新增商户
     */

    public ObjectResponse<Long> addEssComm(AddCommercialParam param) {
        return this.commercialFeignClient.addEssComm(param);
    }

    /**
     * 更新商户信息
     */
    public BaseResponse updateCommInfo(UpdateCommercialParam param) {
        return this.commercialFeignClient.updateCommInfo(param);
    }

    /**
     * 海外版更新商户信息
     */
    public BaseResponse updateEssCommInfo(UpdateCommercialParam param) {
        return this.commercialFeignClient.updateEssCommInfo(param);
    }

    /**
     * 获取下属（含自己）商户ID列表
     *
     * @param idChain
     * @return 下属（含自己）商户ID列表
     */
    public ListResponse<Long> getSubCommIdList(String idChain) {
        return this.commercialFeignClient.getSubCommIdList(idChain);
    }

    /**
     * 桩管家-监控中心-选择充电站
     * @param param
     * @return
     */
    public ListResponse<CommercialSiteDto> getCommercialSiteList(CommcialSiteParam param) {
        return this.siteFeignClient.getCommercialSiteList(param);
    }

    public ListResponse<CommercialDto> getCommList(ListCommercialParam param) {
        return this.commercialFeignClient.getCommList(param);
    }

    public BaseResponse userAdd(String token, TCommercialUser tcu) {
        return this.commercialFeignClient.addCommercialsUser(token, tcu);
    }

    public ObjectResponse<Commercial> getCommercial(Long commId) {
        return this.commercialFeignClient.getCommercial(commId);
    }

}
