package com.cdz360.biz.model.cus.corp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * CorpOrgVO
 *  企业客户组织
 * @since 2019/12/11 13:56
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CorpOrgVO {
    @Schema(description = "主键 全局唯一")
    private Long id;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "组织级别")
    private Integer orgLevel;
    @Schema(description = "上级组织名称")
    private String pName;
    @Schema(description = "下级组织列表")
    private List<CorpOrgVO> children;
    @Schema(description = "一级组织ID(后续有权限时，此参数要调整)")
    private Long pId;
    @Schema(description = "上级组织id")
    private Long parentId;
    @Schema(description = "一级组织")
    private Long l1Id;
    @Schema(description = "二级组织")
    private Long l2Id;
}
