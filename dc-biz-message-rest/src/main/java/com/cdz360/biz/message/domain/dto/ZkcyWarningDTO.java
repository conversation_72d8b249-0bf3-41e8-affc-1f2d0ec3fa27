package com.cdz360.biz.message.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

@Data
public class ZkcyWarningDTO {
    /**
     * 告警id
     */
    private String id;

    /**
     * 告警类型
     */
    private String alarmType;

    /**
     * 告警内容
     */
    private String content;

    /**
     * 设备名称
     */
    private String deviceName;

    /**
     * 设备sn
     */
    private String deviceSn;

    /**
     * 设备厂家
     */
    private String manufacturer;

    /**
     * 告警开始时间，yyyy-MM-dd HH:mm:ss
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private LocalDateTime startTime;

    /**
     * 告警结束时间，yyyy-MM-dd HH:mm:ss
     */
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "Asia/Shanghai")
    private LocalDateTime endTime;

    /**
     * 告警等级
     */
    private String level;

    /**
     * 告警名称
     */
    private String name;

    /**
     * 告警来源
     */
    private String source;

    /**
     * 告警状态
     */
    private String status;

    /**
     * 处理建议
     */
    private String suggestion;

    /**
     * 业务唯一标识
     */
    private String uniqueIdentify;


    // 重写toString()方法
    @Override
    public String toString() {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        return "ZkcyWarningDTO{" +
                "id='" + id + '\'' +
                ", alarmType='" + alarmType + '\'' +
                ", content='" + content + '\'' +
                ", deviceName='" + deviceName + '\'' +
                ", deviceSn='" + deviceSn + '\'' +
                ", manufacturer='" + manufacturer + '\'' +
                ", startTime=" + (startTime != null ? startTime.format(formatter) : null) +
                ", endTime=" + (endTime != null ? endTime.format(formatter) : null) +
                ", level='" + level + '\'' +
                ", name='" + name + '\'' +
                ", source='" + source + '\'' +
                ", status='" + status + '\'' +
                ", suggestion='" + suggestion + '\'' +
                ", uniqueIdentify='" + uniqueIdentify + '\'' +
                '}';
    }
}
