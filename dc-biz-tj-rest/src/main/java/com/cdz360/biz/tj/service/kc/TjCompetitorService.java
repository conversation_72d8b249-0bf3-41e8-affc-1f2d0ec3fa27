package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.geo.ds.CityRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.DistrictRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjCompetitorRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjCompetitorRwDs;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCompetitorService {

    private static final Map<Integer, String> ADD_PET_ERROR_MSG = new HashMap<>() {{
        put(1, "今日免费查询次数已用光。");
        put(2, "未查询到相关竞争对手场站信息。");
    }};

    @Autowired
    private CityRoDs cityRoDs;

    @Autowired
    private DistrictRoDs districtRoDs;

    @Autowired
    private TjCompetitorRoDs tjCompetitorRoDs;

    @Autowired
    private TjCompetitorRwDs tjCompetitorRwDs;

    @Autowired
    private TjCompetitorSiteService tjCompetitorSiteService;

    public Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(ListTjCompetitorParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjCompetitorRoDs::findTjCompetitor)
            .map(list -> list.stream().map(x -> new TjCompetitorVo()
                    .setId(x.getId()).setName(x.getName()).setLogo(x.getLogo()))
                .collect(Collectors.toList()))
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjCompetitorRoDs.countTjCompetitor(param) : 0L));
    }

    public Mono<ObjectResponse<TjCompetitorVo>> saveCompetitor(UpdateTjCompetitorParam competitor) {
        val dto = new TjCompetitorPo();
        BeanUtils.copyProperties(competitor, dto);
        if (null == dto.getId()) {
            TjCompetitorPo old = tjCompetitorRoDs.getOneByName(dto.getName());

            if (null != old) {
                IotAssert.isTrue(!old.getEnable(), "竞争对手名称已经存在，请直接刷新页面。");
                dto.setId(old.getId());
                tjCompetitorRwDs.updateTjCompetitor(dto);
            } else {
                tjCompetitorRwDs.insertTjCompetitor(dto);
            }

            // 异步触发获取对应场站信息
            int valid = 0;
            if (StringUtils.isNotBlank(competitor.getAdcode())) {
                List<DistrictPo> districtList = districtRoDs.getDistrictByList(
                    Arrays.asList(competitor.getAdcode().split(",")));
                valid = tjCompetitorSiteService.fetchSiteByCompetitorDistrict(districtList, dto);
            } else {
                List<CityPo> cityList = cityRoDs.listCity();
                tjCompetitorSiteService.fetchSiteByCompetitor(cityList, dto);
            }

            TjCompetitorVo data = new TjCompetitorVo()
                .setId(dto.getId())
                .setName(competitor.getName())
                .setLogo(competitor.getLogo());
            ObjectResponse<TjCompetitorVo> result = RestUtils.buildObjectResponse(data);
            result.setError(valid > 0 ? ADD_PET_ERROR_MSG.get(valid) : null);
            return Mono.just(result);
        } else {
            tjCompetitorRwDs.updateTjCompetitor(dto);
        }
        return Mono.just(new TjCompetitorVo()
                .setId(dto.getId())
                .setName(competitor.getName())
                .setLogo(competitor.getLogo()))
            .map(RestUtils::buildObjectResponse);
    }

    @Async
    public void syncCompetitorSite(String provinceCode, String cityCode) {
        ListTjCompetitorParam competitorParam = new ListTjCompetitorParam();
        competitorParam.setSize(100)
            .setStart(0L);

        List<CityPo> cityList = cityRoDs.listCity();

        // 过滤指定更新
        if (StringUtils.isNotBlank(provinceCode)) {
            Set<String> targets = Arrays.stream(provinceCode.split("\\|"))
                .collect(Collectors.toSet());
            cityList = cityList.stream().filter(c -> targets.contains(c.getProvinceCode()))
                .collect(Collectors.toList());
        }
        if (StringUtils.isNotBlank(cityCode)) {
            Set<String> targets = Arrays.stream(cityCode.split("\\|"))
                .collect(Collectors.toSet());
            cityList = cityList.stream().filter(c -> targets.contains(c.getCode()))
                .collect(Collectors.toList());
        }

        if (CollectionUtils.isNotEmpty(cityList)) {
            while (competitorParam.getStart() < competitorParam.getSize() * 100) {
                List<TjCompetitorPo> competitors = tjCompetitorRoDs
                    .findTjCompetitor(competitorParam);
                if (CollectionUtils.isNotEmpty(competitors)) {
                    this.tjCompetitorSiteService.batchFetchSiteByCompetitor(cityList, competitors);
                }

                if (competitors.size() < competitorParam.getSize()) {
                    break;
                }

                competitorParam.setStart(competitorParam.getStart() + competitorParam.getSize());
            }
        }
    }

    public Mono<TjCompetitorVo> disableCompetitor(Long competitorId) {
        val area = tjCompetitorRoDs.getById(competitorId);
        IotAssert.isNotNull(area, "竞争者唯一ID无效");

        if (Boolean.TRUE.equals(area.getEnable())) {
            boolean b = tjCompetitorRwDs.disableCompetitor(competitorId);
            log.debug("删除竞争者: {}", b);
        }

        TjCompetitorVo result = new TjCompetitorVo();
        BeanUtils.copyProperties(area, result);
        return Mono.just(result);
    }
}
