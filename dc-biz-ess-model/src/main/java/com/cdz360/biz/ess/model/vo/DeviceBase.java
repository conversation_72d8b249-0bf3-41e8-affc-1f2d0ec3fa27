package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.es.type.hi.EssType;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.NetType;
import com.cdz360.biz.model.ess.type.EquipAlertStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备基础信息")
@Data
@Accessors(chain = true)
@JsonInclude(Include.NON_NULL)
public class DeviceBase {

    @Schema(description = "设备编号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(description = "网络类型")
    private NetType net;

    @Schema(description = "设备名称", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String deviceName;

    @Schema(description = "设备型号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;

    @Schema(description = "机器类型")
    private EssType type;

    @Schema(description = "设备状态.0,未知;1,在线;2,离线;99,下线")
    @JsonInclude(Include.NON_NULL)
    private EquipStatus status;

    @Schema(description = "设备告警状态: 0,未知;1,正常;2,异常")
    @JsonInclude(Include.NON_NULL)
    private EquipAlertStatus alertStatus;

    @Schema(description = "软件版本号")
    @JsonInclude(Include.NON_EMPTY)
    private String softVersion;

    @Schema(description = "其他版本信息(JSON字符串)")
    @JsonInclude(Include.NON_EMPTY)
    private String otherVersion;

    @Schema(description = "设备品牌品牌")
    @JsonInclude(Include.NON_EMPTY)
    private String brand;

    @Schema(title = "时区", example = "GMT+08:00/UTC+08:00")
    private String timeZone;

}
