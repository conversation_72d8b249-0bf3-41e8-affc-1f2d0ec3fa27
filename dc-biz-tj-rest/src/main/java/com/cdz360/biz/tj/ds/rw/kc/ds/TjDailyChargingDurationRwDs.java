package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjMaterialCostRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDailyChargingDurationRwDs {


    @Autowired

    private TjDailyChargingDurationRwMapper tjDailyChargingDurationRwMapper;


    public TjDailyChargingDurationPo getById(Long id, boolean lock) {

        return this.tjDailyChargingDurationRwMapper.getById(id, lock);

    }


    public boolean insertTjDailyChargingDuration(TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        return this.tjDailyChargingDurationRwMapper.insertTjDailyChargingDuration(tjDailyChargingDurationPo) > 0;
    }


    public boolean updateTjDailyChargingDuration(TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        return this.tjDailyChargingDurationRwMapper.updateTjDailyChargingDuration(tjDailyChargingDurationPo) > 0;
    }

    public boolean disableTjDailyChargingDuration(Long id) {
        return this.tjDailyChargingDurationRwMapper.disableTjDailyChargingDuration(id) > 0;
    }

}

