package com.cdz360.biz.device.business.entity.param;

import com.cdz360.biz.device.common.dingchong.WhiteCardV2;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "桩配置变更请求参数")
public class ModifyEvseCfgParam {

    // 批量配置 批量修改桩配置传多个桩号
    @ApiModelProperty(value = "桩编号列表", example = "010203040506")
    private List<String> evseNoList;

    @ApiModelProperty(value = "桩端长效秘钥版本号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private Integer evsePasscodeVer;

    @ApiModelProperty(value = "桩端长效秘钥")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String evsePasscode;

    //
    @ApiModelProperty(value = "管理员密码", example = "123456")
    private String adminPassword;
    //
    @ApiModelProperty(value = "二级管理员密码", example = "123456")
    private String level2Password;
    //
    @ApiModelProperty(value = "白天音量", example = "5")
    private Integer dayVolume;
    //
    @ApiModelProperty(value = "夜晚音量", example = "5")
    private Integer nightVolume;
    //
    @ApiModelProperty(value = "二维码url", example = "http://aa.bb.cc/")
    private String qrUrl;

    @ApiModelProperty(value = "国际协议")
    private String internationalAgreement;

    @ApiModelProperty(value = "合充开关 （1开0关）")
    private Integer isCombineCharge;

    @ApiModelProperty(value = "辅电电压设置")
    private Integer heatingVoltage;

    @ApiModelProperty(value = "是否支持辅电手动切换")
    private Boolean heating;

    @ApiModelProperty(value = "均/轮充设置 0均充 1轮充")
    private Integer avgOrTurnCharge;

    @ApiModelProperty(value = "是否支持电池反接检测")
    private Boolean batteryCheck;

    @ApiModelProperty(value = "是否支持主动安全检测")
    private Boolean securityCheck;

    @ApiModelProperty(value = "是否支持不拔枪充电")
    private Boolean constantCharge;

    @ApiModelProperty(value = "插枪获取VIN开关")
    private Boolean vinDiscover;

    @ApiModelProperty(value = "订单信息隐私设置开关")
    private Boolean orderPrivacySetting;

    @ApiModelProperty(value = "订单账号显示类型")
    private Integer accountDisplayType;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持充电记录查询", example = "true")
    private Boolean isQueryChargeRecord;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持定时充电", example = "true")
    private Boolean isTimedCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持无卡充电", example = "true")
    private Boolean isNoCardCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持扫码充电", example = "true")
    private Boolean isScanCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持Vin码充电", example = "true")
    private Boolean isVinCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持刷卡充电", example = "true")
    private Boolean isCardCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持定额电量充电", example = "true")
    private Boolean isQuotaEleCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持固定金额充电", example = "true")
    private Boolean isQuotaMoneyCharge;
    //  （1是0否）
    @ApiModelProperty(value = "是否支持固定时长充电", example = "true")
    private Boolean isQuotaTimeCharge;

    //
//    @ApiModelProperty(value = "紧急充电卡列表，用,分隔", example = "123,456,789")
//    private String whiteCardList;

    @ApiModelProperty(value = "计费模板Id，需要修改计费模板时赋值，否则为空null")
    private Long priceSchemeId;

    @ApiModelProperty(value = "定时下发时，定时时间; 即时下发为空null")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", locale = "zh", timezone = "GMT+8")
    private Date schedule;

    // @Nathan 这里不涉及计费模板，暂时不需要
//    @ApiModelProperty(value = "分时段计费", hidden = true)
//    private List<ChargeV2> priceSchemeList;

    @ApiModelProperty(value = "需要下发的紧急充电卡列表")
    private List<WhiteCardV2> whiteCards;
}
