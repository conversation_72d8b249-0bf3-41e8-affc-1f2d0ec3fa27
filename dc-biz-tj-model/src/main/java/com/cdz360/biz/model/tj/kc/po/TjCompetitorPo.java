package com.cdz360.biz.model.tj.kc.po;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "创建投建区域的竞争对手")
public class TjCompetitorPo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "竞争对手名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 128, message = "name 长度不能超过 128")
    private String name;

    @Schema(description = "标记图标")
    @Size(max = 128, message = "logo 长度不能超过 128")
    private String logo;

    @Schema(description = "首次拉取完成时间")
    private Date firstPullFinishTime;

    @Schema(description = "true有效;false删除")
    @JsonInclude(Include.NON_NULL)
    private Boolean enable;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

}

