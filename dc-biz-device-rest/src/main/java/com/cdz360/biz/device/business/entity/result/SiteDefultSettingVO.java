package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Classname SiteDefultSettingVO
 * @Description TODO
 * @Date 2019/6/11
 * @Created by wangzheng
 */
@Data
public class SiteDefultSettingVO implements Serializable {

    private Long id;
    /**
     * 场站编号
     */
    private String siteId;
    /**
     * 计费模板id
     */
    @JsonInclude(Include.NON_NULL)
    private Long chargeId;
    /**
     * 管理员密码
     */
    @JsonInclude(Include.NON_EMPTY)
    private String adminPassword;
    /**
     * 二级管理员密码
     */
    @JsonInclude(Include.NON_EMPTY)
    private String level2Password;
    /**
     * 白天音量
     */
    @JsonInclude(Include.NON_NULL)
    private Integer dayVolume;
    /**
     * 夜晚音量
     */
    @JsonInclude(Include.NON_NULL)
    private Integer nightVolume;
    /**
     * 二维码url
     */
    @JsonInclude(Include.NON_EMPTY)
    private String url;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isQueryChargeRecord;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isTimedCharge;
    /**
     * 是否支持无卡充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isNoCardCharge;
    /**
     * 是否支持扫码充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isScanCharge;
    /**
     * 是否支持Vin码充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isVinCharge;
    /**
     * 是否支持刷卡充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isCardCharge;
    /**
     * 是否支持定额电量充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isQuotaEleCharge;
    /**
     * 是否支持固定金额充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isQuotaMoneyCharge;
    /**
     * 是否支持固定时长充电 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean isQuotaTimeCharge;
    /**
     * 国际协议
     */
    @JsonInclude(Include.NON_EMPTY)
    private String internationalAgreement;
    /**
     * 自动停充 （1是0否）
     */
    @JsonInclude(Include.NON_NULL)
    private Integer isAutoStopCharge;
    /**
     * 均/轮充设置 0均充 1轮充
     */
    @JsonInclude(Include.NON_NULL)
    private Integer avgOrTurnCharge;
    /**
     * 合充开关 （1开0关）
     */
    @JsonInclude(Include.NON_NULL)
    private Integer isCombineCharge;
    /**
     * 是否支持辅电手动切换
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean heating;
    /**
     * 辅电电压设置
     */
    @JsonInclude(Include.NON_NULL)
    private Integer heatingVoltage;
    /**
     * 是否支持电池反接检测
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean batteryCheck;
    /**
     * 是否支持主动安全检测
     */
    @JsonInclude(Include.NON_NULL)
    private Boolean securityCheck;

    // 是否支持不拔枪二次充电
    @JsonInclude(Include.NON_NULL)
    private Boolean constantCharge;

    // 插枪获取VIN开关
    @JsonInclude(Include.NON_NULL)
    private Boolean vinDiscover;

    // 订单信息隐私设置开关（null不做更改 true开启 false关闭）
    @JsonInclude(Include.NON_NULL)
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型 null 不做更改 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    @JsonInclude(Include.NON_NULL)
    private Integer accountDisplayType;
    /**
     * 创建时间
     */
    @JsonInclude(Include.NON_NULL)
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonInclude(Include.NON_NULL)
    private Date updateTime;
    /**
     * 操作人id
     */
    @JsonInclude(Include.NON_NULL)
    private Long updateByUserid;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}