package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备汇总数据")
@Data
@Accessors(chain = true)
public class DeviceSummaryVo {

    @Schema(description = "储能设备充电量(kW·h)", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal essIn;

    @Schema(description = "储能设备放电量(kW·h)", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal essOut;

    @Schema(description = "光伏设备发电量(kW·h)", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pvGen;

    @Schema(description = "上网电量(kW·h)-并网设备", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private BigDecimal toGrid;
}
