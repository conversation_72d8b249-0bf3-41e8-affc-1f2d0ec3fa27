package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaRwDs;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAreaService {

    @Autowired
    private TjAreaRoDs tjAreaRoDs;

    @Autowired
    private TjAreaRwDs tjAreaRwDs;

    public Mono<ListResponse<TjAreaVo>> findArea(ListTjAreaParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }
        List<TjAreaPo> result = tjAreaRoDs.findArea(param);
        return Mono.just(RestUtils.buildListResponse(result.stream().map(x -> {
                val area = new TjAreaVo();
                BeanUtils.copyProperties(x, area);
                return area;
            }).collect(Collectors.toUnmodifiableList()),
            Boolean.TRUE.equals(param.getTotal()) ? tjAreaRoDs.count(param) : 0L));
    }

    public Mono<ListResponse<TjAreaVo>> findUserArea(ListTjAreaParam param) {
        if (null == param.getSize()) {
            param.setSize(10000);
        }
        List<TjAreaPo> result = tjAreaRoDs.findAllUserArea(param);
        return Mono.just(RestUtils.buildListResponse(result.stream().map(x -> {
            val area = new TjAreaVo();
            BeanUtils.copyProperties(x, area);
            return area;
        }).collect(Collectors.toUnmodifiableList())));
    }

    public Mono<TjAreaVo> getTjAreaByAid(Long aid) {
        val area = tjAreaRoDs.getByAid(aid);
        IotAssert.isNotNull(area, "投建区域唯一ID无效");
        TjAreaVo result = new TjAreaVo();
        BeanUtils.copyProperties(area, result);
        return Mono.just(result);
    }

    public Mono<TjAreaVo> saveTjArea(TjAreaVo area) {
        val dto = new TjAreaPo();
        BeanUtils.copyProperties(area, dto);
        if (null == area.getAid()) {
            tjAreaRwDs.insertTjArea(dto);
        } else {
            tjAreaRwDs.updateTjArea(dto);
        }
        return Mono.just(area.setAid(dto.getAid()));
    }

    public Mono<TjAreaVo> disableTjArea(Long aid) {
        val area = tjAreaRoDs.getByAid(aid);
        IotAssert.isNotNull(area, "投建区域唯一ID无效");

        if (Boolean.TRUE.equals(area.getEnable())) {
            boolean b = tjAreaRwDs.disableTjArea(aid);
            log.debug("删除投建区域: {}", b);
        }

        TjAreaVo result = new TjAreaVo();
        BeanUtils.copyProperties(area, result);
        return Mono.just(result);
    }
}
