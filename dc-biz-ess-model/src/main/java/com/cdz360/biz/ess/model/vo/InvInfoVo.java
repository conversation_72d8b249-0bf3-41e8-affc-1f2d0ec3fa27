package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "逆变器信息")
@Data
@Accessors(chain = true)
public class InvInfoVo {

    @Schema(description = "逆变A相电压 V 0x1092")
    @JsonProperty("iva")
    private BigDecimal inverterVoltageA;

    @Schema(description = "逆变B相电压 V")
    @JsonProperty("ivb")
    private BigDecimal inverterVoltageB;

    @Schema(description = "逆变C相电压 V")
    @JsonProperty("ivc")
    private BigDecimal inverterVoltageC;

    @Schema(description = "逆变电压AB线 V")
    @JsonProperty("ivlab")
    private BigDecimal inverterVoltageLineAB;

    @Schema(description = "逆变电压BC线 V")
    @JsonProperty("ivlbc")
    private BigDecimal inverterVoltageLineBC;

    @Schema(description = "逆变电压CA线 V 0x109C")
    @JsonProperty("ivlca")
    private BigDecimal inverterVoltageLineCA;

    @Schema(description = "逆变A相THDU %")
    @JsonProperty("ita")
    private BigDecimal inverterThduA;

    @Schema(description = "逆变B相THDU %")
    @JsonProperty("itb")
    private BigDecimal inverterThduB;

    @Schema(description = "逆变C相THDU %")
    @JsonProperty("itc")
    private BigDecimal inverterThduC;

    @Schema(description = "逆变电压频率 Hz")
    @JsonProperty("ivf")
    private BigDecimal inverterVoltageFrequency;

    @Schema(description = "逆变A相电流有效值 A")
    @JsonProperty("icea")
    private BigDecimal inverterCurrentEffectiveA;

    @Schema(description = "逆变B相电流有效值 A")
    @JsonProperty("iceb")
    private BigDecimal inverterCurrentEffectiveB;

    @Schema(description = "逆变C相电流有效值 A")
    @JsonProperty("icec")
    private BigDecimal inverterCurrentEffectiveC;

    @Schema(description = "逆变N相电流有效值 A")
    @JsonProperty("icen")
    private BigDecimal inverterCurrentEffectiveN;

    @Schema(description = "逆变A相电流THD %")
    @JsonProperty("icta")
    private BigDecimal inverterCurrentThdA;

    @Schema(description = "逆变B相电流THD %")
    @JsonProperty("ictb")
    private BigDecimal inverterCurrentThdB;

    @Schema(description = "逆变C相电流THD % 0x10B2")
    @JsonProperty("ictc")
    private BigDecimal inverterCurrentThdC;

    @Schema(description = "逆变A相电流峰值比 %")
    @JsonProperty("icpra")
    private BigDecimal inverterCurrentPeakRatioA;

    @Schema(description = "逆变B相电流峰值比 %")
    @JsonProperty("icprb")
    private BigDecimal inverterCurrentPeakRatioB;

    @Schema(description = "逆变C相电流峰值比 %")
    @JsonProperty("icprc")
    private BigDecimal inverterCurrentPeakRatioC;

    @Schema(description = "逆变A相视在功率 kVA")
    @JsonProperty("iapa")
    private BigDecimal inverterApparentPowerA;

    @Schema(description = "逆变B相视在功率 kVA")
    @JsonProperty("iapb")
    private BigDecimal inverterApparentPowerB;

    @Schema(description = "逆变C相视在功率 kVA 0x10BE")
    @JsonProperty("iapc")
    private BigDecimal inverterApparentPowerC;

    @Schema(description = "逆变A相有功功率 kW 0x10C0")
    @JsonProperty("iacpa")
    private BigDecimal inverterActivePowerA;

    @Schema(description = "逆变B相有功功率 kW")
    @JsonProperty("iacpb")
    private BigDecimal inverterActivePowerB;

    @Schema(description = "逆变C相有功功率 kW")
    @JsonProperty("iacpc")
    private BigDecimal inverterActivePowerC;

    @Schema(description = "逆变A相无功功率 kVar")
    @JsonProperty("ircpa")
    private BigDecimal inverterReactivePowerA;

    @Schema(description = "逆变B相无功功率 kVar")
    @JsonProperty("ircpb")
    private BigDecimal inverterReactivePowerB;

    @Schema(description = "逆变C相无功功率 kVar")
    @JsonProperty("ircpc")
    private BigDecimal inverterReactivePowerC;

    @Schema(description = "逆变A相基波功率因数")
    @JsonProperty("ifpfa")
    private BigDecimal inverterFundamentalPowerFactorA;

    @Schema(description = "逆变B相基波功率因数")
    @JsonProperty("ifpfb")
    private BigDecimal inverterFundamentalPowerFactorB;

    @Schema(description = "逆变C相基波功率因数")
    @JsonProperty("ifpfc")
    private BigDecimal inverterFundamentalPowerFactorC;

    @Schema(description = "逆变A相功率因数")
    @JsonProperty("ipfa")
    private BigDecimal inverterPowerFactorA;

    @Schema(description = "逆变B相功率因数")
    @JsonProperty("ipfb")
    private BigDecimal inverterPowerFactorB;

    @Schema(description = "逆变C相功率因数 0x10D6")
    @JsonProperty("ipfc")
    private BigDecimal inverterPowerFactorC;

    @Schema(description = "逆变A相负载率 % 0x10D8")
    @JsonProperty("iaa")
    private BigDecimal inverterRateA;

    @Schema(description = "逆变B相负载率 %")
    @JsonProperty("iab")
    private BigDecimal inverterRateB;

    @Schema(description = "逆变C相负载率 % 0x10DC")
    @JsonProperty("iac")
    private BigDecimal inverterRateC;

    @Schema(description = "DCAC温度 ℃ 0x10DE ~ 0x10E6")
    @JsonProperty("datl")
    private List<BigDecimal> dcacTempList;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10E8")
    @JsonProperty("dar1")
    private BigDecimal dcacReserved1;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10EA")
    @JsonProperty("dar2")
    private BigDecimal dcacReserved2;

    @Schema(description = "DCAC预留一路温度 ℃ 0x10EC")
    @JsonProperty("dar3")
    private BigDecimal dcacReserved3;

    @Schema(description = "正直流母线电压 V 0x10EE")
    @JsonProperty("pdbv")
    private BigDecimal positiveDcBusVoltage;

    @Schema(description = "负直流母线电压 V 0x10F0")
    @JsonProperty("ndbv")
    private BigDecimal negativeDcBusVoltage;

}
