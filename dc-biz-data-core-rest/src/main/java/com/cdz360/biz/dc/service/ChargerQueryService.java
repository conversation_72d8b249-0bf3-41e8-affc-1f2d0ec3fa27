package com.cdz360.biz.dc.service;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.charge.type.OrderStopCode;
import com.cdz360.base.model.iot.vo.PlugVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.zft.vo.ZftVo;
import com.cdz360.biz.dc.domain.OrderInMongo;
import com.cdz360.biz.dc.service.parse.BalanceServiceImpl;
import com.cdz360.biz.dc.utils.RedisUtil;
import com.cdz360.biz.ds.trading.ro.comm.ds.CommercialRoDs;
import com.cdz360.biz.ds.trading.ro.coupon.ds.AbcCouponRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderCarRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderPayRoDs;
import com.cdz360.biz.ds.trading.ro.order.ds.ChargerOrderRoDs;
import com.cdz360.biz.ds.trading.ro.score.ds.ScoreDiscountRoDs;
import com.cdz360.biz.ds.trading.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.trading.rw.order.ds.ChargerOrderRefundRwDs;
import com.cdz360.biz.ds.trading.rw.order.ds.PayBillRwDs;
import com.cdz360.biz.model.cus.basic.vo.UserVo;
import com.cdz360.biz.model.cus.post.vo.CusPostVo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingLevelPo;
import com.cdz360.biz.model.cus.score.po.ScoreSettingPo;
import com.cdz360.biz.model.cus.score.type.DiscountType;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.cdz360.biz.model.iot.param.ListPlugParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.site.po.PriceItemPo;
import com.cdz360.biz.model.trading.coupon.po.AbcCouponPo;
import com.cdz360.biz.model.trading.coupon.type.AbcCouponUsed;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.iot.vo.BsChargerMoreVo;
import com.cdz360.biz.model.trading.order.param.GetOrderDetailParam;
import com.cdz360.biz.model.trading.order.po.ChargerOrderCarPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPayPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderRefundPo;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivision;
import com.cdz360.biz.model.trading.order.po.ChargerOrderTimeDivisionEx;
import com.cdz360.biz.model.trading.order.po.PayBillPo;
import com.cdz360.biz.model.trading.order.type.ChargerOrderRefundStatus;
import com.cdz360.biz.model.trading.order.vo.ChargeOrderCache;
import com.cdz360.biz.model.trading.score.po.ScoreDiscountPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.cdz360.biz.utils.config.HlhtCfg;
import com.cdz360.biz.utils.feign.his.HisOrderFeignClient;
import com.cdz360.biz.utils.feign.invoice.InvoiceFeignClient;
import com.cdz360.biz.utils.feign.user.CardFeignClient;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.cdz360.biz.utils.feign.user.VinFeignClient;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.framework.common.constant.OrderStatus;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.OrderDetailV2;
import com.chargerlinkcar.framework.common.domain.invoice.InvoicedRecordVo;
import com.chargerlinkcar.framework.common.domain.order.ChargerOrderWithBLOBs;
import com.chargerlinkcar.framework.common.domain.type.ProcessType;
import com.chargerlinkcar.framework.common.domain.vo.BalanceInfo;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetail;
import com.chargerlinkcar.framework.common.domain.vo.ChargerDetailVo;
import com.chargerlinkcar.framework.common.domain.vo.OrderInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.UpdateOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.feign.AuthCenterFeignClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.service.UserScoreService;
import com.chargerlinkcar.framework.common.utils.DateUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.PlugNoParser;
import com.chargerlinkcar.framework.common.utils.PlugNoUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.ObjectUtils;

/**
 * ChargerQueryService
 *
 * @since 2/11/2020 12:36 PM
 * <AUTHOR>
 */
@Slf4j
@Service
public class ChargerQueryService {

    @Autowired
    private OrderMongoService deviceStatusMongoService;

    @Autowired
    private ChargerOrderRoDs chargerOrderRoDs;
    @Autowired
    private ChargerOrderPayRoDs chargerOrderPayRoDs;
    @Autowired
    private ChargerOrderCarRoDs chargerOrderCarRoDs;

    @Autowired
    private PriceSchemaBizService priceSchemaBizService;

    @Autowired
    private RedisIotReadService redisIotReadService;

    @Autowired
    private CardFeignClient cardFeignClient;

    @Autowired
    private VinFeignClient vinFeignClient;

    @Autowired
    private UserFeignClient userFeignClient;

    @Autowired
    private SiteRoDs siteRoDs;


    @Autowired
    private DeviceBizService deviceBizService;

    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;

    @Autowired
    private CommercialRoDs commercialQueryDs;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private AccountBizService accountBizService;

    @Autowired
    private ChargerOrderRefundRwDs chargerOrderRefundRwDs;

    @Autowired
    private PayBillRwDs payBillRwDs;

    @Autowired
    private OrderDataService orderDataService;

    @Autowired
    private BalanceServiceImpl balanceService;

    @Autowired
    private InvoiceFeignClient invoiceFeignClient;

    @Autowired
    private UserFeignClient reactorUserFeignClient;

    @Autowired
    private AbcCouponRoDs abcCouponRoDs;

    @Autowired
    private HisOrderFeignClient hisOrderFeignClient;

    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;

    @Autowired
    private HlhtCfg hlhtCfg;

    @Autowired
    private ScoreDiscountRoDs scoreDiscountRoDs;

    @Autowired
    private UserScoreService userScoreService;

    //    public ConnectorMeasureInfo getChargerMeasureByDeviceId(String evseNo, Integer connectorId) throws DcServiceException {
    public OrderInMongo getOrderInMongo(String orderNo) throws DcServiceException {
        log.info("根据设备orderNo获取充电接口状态。orderNo: {}", orderNo);

        if (StringUtils.isBlank(orderNo)) {
            return null;
        }

        OrderInMongo orderInMongo = deviceStatusMongoService.findOne(orderNo);

        return orderInMongo;
    }

    public List<ChargerOrderTimeDivision> estimateOrderTimeDivisionListWrapper(
        UpdateOrderVo updateOrderVo) {
        final List<ChargerOrderTimeDivision> chargerOrderTimeDivisions = estimateOrderTimeDivisionList(
            updateOrderVo);

        final ScoreDiscountPo byOrderNo = scoreDiscountRoDs.getByOrderNo(
            updateOrderVo.getOrderNo());

        if (byOrderNo != null &&
            byOrderNo.getDiscountType() != null &&
            DiscountType.FIXED_TOTAL_FEE.name().equals(byOrderNo.getDiscountType().name())) {
            log.info("积分固定单价转换： {}", byOrderNo);

            ScoreSettingLevelPo levelPo = new ScoreSettingLevelPo();
            levelPo.setTotalPrice(byOrderNo.getFixedTotalDiscount());

            userScoreService.timeDivisionDiscount(levelPo, chargerOrderTimeDivisions);

            return chargerOrderTimeDivisions;
        }
        return chargerOrderTimeDivisions;
    }

    private List<ChargerOrderTimeDivision> estimateOrderTimeDivisionList(
        UpdateOrderVo updateOrderVo) {
        log.info("根据心跳推算分时订单信息: {}", JsonUtils.toJsonString(updateOrderVo));

        if (updateOrderVo == null) {
            log.info("入参为空，返回空数组");
            return List.of();
        }

        if (StringUtils.isBlank(updateOrderVo.getOrderNo())) {
            log.info("订单号为空，返回空数组");
            return List.of();
        }

        if (updateOrderVo.getStartTime() == null) {
            log.info("开始为空，返回空数组");
            return List.of();
        }

        if (updateOrderVo.getEndTime() == null) {
            log.info("结束为空，返回空数组");
            return List.of();
        }

        if (updateOrderVo.getStartTime().getTime() >= updateOrderVo.getEndTime().getTime()) {
            log.info("开始时间晚于结束时间，返回空数组");
            return List.of();
        }

        OrderInMongo orderInMongo = deviceStatusMongoService.findOne(updateOrderVo.getOrderNo());

        // 找不到心跳数据，使用入参时间段、当前计费模板估算分时信息
        if (orderInMongo == null || CollectionUtils.isEmpty(orderInMongo.getDetails())) {
            log.info("找不到心跳数据，使用入参时间段、当前计费模板估算分时信息");
            ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPo(updateOrderVo.getOrderNo(),
                false);

            IotAssert.isNotNull(order, "找不到订单，请确认DB中是否存在订单信息。");

            Long priceCode = order.getPriceSchemeId();

            List<PriceItemPo> priceList = priceSchemaBizService.getPriceItemListByTempId(priceCode);

            IotAssert.isTrue(CollectionUtils.isNotEmpty(priceList),
                "无分时计费模板信息: " + priceCode);

            final Date StartTime = new Date(updateOrderVo.getStartTime().getTime());
            final Date EndTime = new Date(updateOrderVo.getEndTime().getTime());
//            Date curTime = new Date(StartTime.getTime());

            List<ChargerOrderTimeDivision> ret =
                this.getDivisionSkeleton(updateOrderVo.getOrderNo(), StartTime, EndTime, priceList);

            if (orderInMongo != null && orderInMongo.getKwh() != null) {
                ChargerOrderTimeDivision tailDiv = ret.get(ret.size() - 1);
                tailDiv.setElectric(orderInMongo.getKwh());
                tailDiv.setElectricPrice(orderInMongo.getKwh().multiply(tailDiv.getElectricUnit())
                    .setScale(2, RoundingMode.HALF_UP));
                tailDiv.setServicePrice(orderInMongo.getKwh()
                    .multiply(tailDiv.getServiceUnit().setScale(2, RoundingMode.HALF_UP)));
            } else {
                ChargerOrderTimeDivision tailDiv = ret.get(ret.size() - 1);
                tailDiv.setElectric(BigDecimal.ZERO)
                    .setElectricPrice(BigDecimal.ZERO)
                    .setServicePrice(BigDecimal.ZERO);
            }

            return ret;
        } else {
            log.info("存在心跳数据，使用心跳数据估算分时数据");

            // 筛选出在时间段内的心跳
            List<ChargerDetail> hbList = orderInMongo.getDetails().stream().filter(e -> {
                if (e.getTimestamp() != null) {
                    Long timestamp = e.getTimestamp() * 1000;
                    return updateOrderVo.getStartTime().getTime() <= timestamp &&
                        timestamp <= updateOrderVo.getEndTime().getTime();
                } else {
                    return false;
                }
            }).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(hbList)) {
                log.info("订单{}时段内[{}, {}]无心跳数据",
                    updateOrderVo.getOrderNo(), updateOrderVo.getStartTime(),
                    updateOrderVo.getEndTime());
                return List.of();
            }

            ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPo(updateOrderVo.getOrderNo(),
                false);
            IotAssert.isNotNull(order, "找不到订单，请确认DB中是否存在订单信息。");
            Long priceCode = order.getPriceSchemeId();

            List<PriceItemPo> priceList = priceSchemaBizService.getPriceItemListByTempId(priceCode);

            IotAssert.isTrue(CollectionUtils.isNotEmpty(priceList),
                "无分时计费模板信息: " + priceCode);

            List<ChargerOrderTimeDivision> ret = new ArrayList<>();

            if (priceList.size() == 1) {
                log.info("仅含一个时段的计费模板");
                PriceItemPo priceItemPo = priceList.get(0);
                ChargerDetail hbHead = orderInMongo.getDetails().get(0);
                ChargerDetail tmpHbTail = this.repickTailHb(orderInMongo.getFeeList());
                // 尝试将结算上报分时信息，作为最后的心跳信息
                ChargerDetail hbTail = tmpHbTail == null ?
                    orderInMongo.getDetails().get(orderInMongo.getDetails().size() - 1) :
                    tmpHbTail;

                if (hbTail.getKwh() == null) {
                    log.error("无法获取电量信息,可能是桩端心跳未上传电量,设定电量为0");
                    hbTail.setKwh(BigDecimal.ZERO);
                }

                ChargerOrderTimeDivision division = new ChargerOrderTimeDivision();
                division.setOrderNo(updateOrderVo.getOrderNo())
                    .setTemplateStartTime(priceItemPo.getStartTime())
                    .setTemplateStopTime(priceItemPo.getStopTime())
                    .setServiceUnit(priceItemPo.getServicePrice())
                    .setElectricUnit(priceItemPo.getPrice())
                    .setTag(priceItemPo.getCategory().getCode())
                    .setDuration(
                        hbTail.getTimestamp().intValue() - hbHead.getTimestamp().intValue())
                    .setElectric(hbTail.getKwh())
//                        .setElectricPrice(hbTail.getElecFee())
                    .setElectricPrice(hbTail.getKwh().multiply(priceItemPo.getPrice())
                        .setScale(4, RoundingMode.HALF_UP))
//                        .setServicePrice(hbTail.getServFee())
                    .setServicePrice(hbTail.getKwh().multiply(priceItemPo.getServicePrice())
                        .setScale(4, RoundingMode.HALF_UP))
                    .setStopTime(DateUtil.date2Str(new Date(hbTail.getTimestamp() * 1000)))
                    .setTemplateId(priceCode)
                    .setStartTime(DateUtil.date2Str(new Date(hbHead.getTimestamp() * 1000)));
                ret.add(division);
                return ret;
            }
            log.info("含多个时段的计费模板");

            // 尝试将结算上报分时信息，作为最后的心跳信息
            ChargerDetail hbTail = this.repickTailHb(orderInMongo.getFeeList());
            if (hbTail != null) {
                hbList.add(hbTail);
            }

            final ChargerDetail HbHead = hbList.get(0);
            final ChargerDetail HbTail = hbList.get(hbList.size() - 1);
            final Date StartTime = new Date(
                Math.min(HbHead.getTimestamp() * 1000, updateOrderVo.getStartTime().getTime()));
            final Date EndTime = new Date(
                Math.max(HbTail.getTimestamp() * 1000, updateOrderVo.getEndTime().getTime()));

            ret = this.getDivisionSkeleton(updateOrderVo.getOrderNo(), StartTime, EndTime,
                priceList);

            final SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 字段扩展
            List<ChargerOrderTimeDivisionEx> retEx = new ArrayList<>();
            try {
                for (ChargerOrderTimeDivision one : ret) {
                    ChargerOrderTimeDivisionEx chargerOrderTimeDivisionEx = new ChargerOrderTimeDivisionEx();
                    BeanUtils.copyProperties(one, chargerOrderTimeDivisionEx);
                    chargerOrderTimeDivisionEx.setStartDateTime(SDF.parse(one.getStartTime()));
                    chargerOrderTimeDivisionEx.setStopDateTime(SDF.parse(one.getStopTime()));
                    retEx.add(chargerOrderTimeDivisionEx);
                }
            } catch (Exception e) {
                log.info("信息获取失败", e, e);
                return List.of();
            }

            int divIndex = 0;
            int updateIndex = -1;// 最近一个被写入的计费区间
            while (divIndex < retEx.size()) {
                ChargerOrderTimeDivisionEx one = retEx.get(divIndex);
                // 根据时间段筛选出心跳区间
                List<ChargerDetail> chargerDetailBlock = this.getChargerDetailBlock(
                    one.getStartDateTime().getTime(),
                    one.getStopDateTime().getTime(),
                    hbList);

                if (CollectionUtils.isEmpty(chargerDetailBlock)) {
                    // 该时段无心跳数据
                    one.setElectric(BigDecimal.ZERO)
                        .setElectricPrice(BigDecimal.ZERO)
                        .setServicePrice(BigDecimal.ZERO);
                } else {
//                    ChargerDetail chargerDetailHead = chargerDetailBlock.get(0);
                    ChargerDetail chargerDetailTail = chargerDetailBlock.get(
                        chargerDetailBlock.size() - 1);

                    BigDecimal tailUpdate;
                    if (updateIndex < 0) {
                        // 不存在最近一个被写入的区间
                        tailUpdate = BigDecimal.ZERO;
                    } else {
                        // 存在最近一个被写入的区间
                        tailUpdate = retEx.get(updateIndex).getLastHbkWh();
                    }

                    one.setLastHbkWh(chargerDetailTail.getKwh());

                    BigDecimal kwh = chargerDetailTail.getKwh().subtract(tailUpdate);

                    one.setElectric(kwh)
                        .setElectricPrice(
                            one.getElectricUnit().multiply(kwh).setScale(2, RoundingMode.HALF_UP))
                        .setServicePrice(
                            one.getServiceUnit().multiply(kwh).setScale(2, RoundingMode.HALF_UP));

                    // 更新最近一个被写入
                    updateIndex = divIndex;
                }

                divIndex++;
            }

            return retEx.stream().collect(Collectors.toList());

        }

    }

    private ChargerDetail repickTailHb(List<OrderDetailV2> feeList) {
        if (CollectionUtils.isEmpty(feeList)) {
            return null;
        }

        ChargerDetail ret = new ChargerDetail();
        OrderDetailV2 orderDetailV2 = feeList.get(feeList.size() - 1);
        return ret.setKwh(orderDetailV2.getKwh())
            .setTimestamp(orderDetailV2.getStopTime());
    }

    /**
     * 心跳分时段
     *
     * @param startTime
     * @param endTime
     * @param list
     * @return
     */
    private List<ChargerDetail> getChargerDetailBlock(long startTime, long endTime,
        List<ChargerDetail> list) {
        return list.stream()
            .filter(
                e -> (startTime <= e.getTimestamp() * 1000) && (e.getTimestamp() * 1000 <= endTime))
            .collect(Collectors.toList());
    }

    /**
     * 初步获取分时计费信息
     *
     * @param orderNo
     * @param startTime
     * @param endTime
     * @param priceList
     * @return
     */
    private List<ChargerOrderTimeDivision> getDivisionSkeleton(String orderNo,
        Date startTime,
        Date endTime,
        List<PriceItemPo> priceList) {
        Date curTime = new Date(startTime.getTime());

        Long priceCode = priceList.get(0).getTemplateId();

        List<ChargerOrderTimeDivision> ret = new ArrayList<>();

        PriceItemPo curPrice = null;
        while (curTime.before(endTime)) {

            // 获取该时间对应的计费信息
            curPrice = this.getPrice(curTime, priceList);

            // 当前秒针的时刻（秒）
            int startSec = DateUtil.getSecondsByDateTime(curTime);

            // 时段结尾时的时刻（秒）
            int endSec = curPrice.getStopTime() * 60;

            // 将时段的时长递增到当前时间上
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(curTime);
            final int Duration = endSec - startSec;
            calendar.add(Calendar.SECOND, Duration);
            curTime = calendar.getTime();

            // 生成对应时段计费信息
            ChargerOrderTimeDivision division = new ChargerOrderTimeDivision();
            division.setOrderNo(orderNo)
                .setTemplateStartTime(curPrice.getStartTime())
                .setTemplateStopTime(curPrice.getStopTime())
                .setServiceUnit(curPrice.getServicePrice())
                .setElectricUnit(curPrice.getPrice())
                .setTag(curPrice.getCategory().getCode())
                .setDuration(Duration)
                .setElectric(BigDecimal.ZERO)
                .setElectricPrice(BigDecimal.ZERO)
                .setServicePrice(BigDecimal.ZERO)
                .setStopTime(DateUtil.date2Str(curTime))
                .setTemplateId(priceCode);

            if (ret.isEmpty()) {
                division.setStartTime(DateUtil.date2Str(startTime));
            } else {
                // 使用上一个分时数据的结束时间填补当前时段的开始时间
                division.setStartTime(ret.get(ret.size() - 1).getStopTime());
            }
            ret.add(division);
        }

        // 修正最后时段计费信息
        if (!curTime.before(endTime) && curPrice != null) {
            final Long Minus = (curTime.getTime() - endTime.getTime()) / 1000;
            // 分时时长修正
            ChargerOrderTimeDivision chargerOrderTimeDivision = ret.get(ret.size() - 1);
            chargerOrderTimeDivision.setDuration(
                chargerOrderTimeDivision.getDuration() - Minus.intValue());

            // 结束时间修正
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(curTime);
            calendar.add(Calendar.SECOND, -Minus.intValue());
            chargerOrderTimeDivision.setStopTime(DateUtil.date2Str(calendar.getTime()));
        }

        return ret;
    }

    private PriceItemPo getPrice(final Date date, final List<PriceItemPo> priceList) {
        final int CurMinutes = DateUtil.getMinutesByDateTime(date);
        int i = 0;
        while (i < priceList.size()) {
            PriceItemPo priceItemPo = priceList.get(i);
            if (priceItemPo.getStartTime() <= CurMinutes
                && CurMinutes < priceItemPo.getStopTime()) {
                return priceItemPo;
            }
            i++;
        }
        return null;
    }


    /**
     * 获取该商户列表下的客户 订单详情
     *
     * @param param
     * @return
     */

    public ObjectResponse<ChargerOrderWithBLOBs> queryOrderDetail(GetOrderDetailParam param) {
        String orderNo = param.getOrderNo();
        List<Long> commIdList = param.getCommIdList();
        String commIdChain = param.getCommIdChain();
        Long userId = param.getUserId();
        List<String> gids = param.getGids();
        log.info("查询订单。commIdChain = {}, orderNo: {}, userId: {}, gids = {}", commIdChain,
            orderNo,
            userId, gids);

        //1、订单基础信息
        ChargerOrderWithBLOBs chargerOrder = chargerOrderRoDs.selectByOrderIdAndCommIdList(orderNo,
            commIdList, commIdChain, userId, gids);

        if (chargerOrder == null) {
            log.info("尝试从his库查询订单。orderNo: {}", orderNo);
            // 查询订单失败，
            ObjectResponse<ChargerOrderWithBLOBs> hisOrderRes = hisOrderFeignClient.getOrderWithBlobs(
                param).block(Duration.ofSeconds(50L));
            if (hisOrderRes.getData() != null) {
                chargerOrder = hisOrderRes.getData();
            }
        }

        // 积分体系,固定单价获取体系名称
        if (null != chargerOrder &&
            chargerOrder.getScoreSettingInfo() != null
            && chargerOrder.getScoreSettingInfo().getId() != null
//            && DiscountType.FIXED_TOTAL_FEE.equals(
//            chargerOrder.getScoreSettingInfo().getDiscountType())
        ) {
            ObjectResponse<ScoreSettingPo> block = userFeignClient.getScoreInfo(
                chargerOrder.getScoreSettingInfo().getId())
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.check(block);
            chargerOrder.getScoreSettingInfo().setName(block.getData().getName());
        }

        // 获取邮箱信息
        if (chargerOrder != null && chargerOrder.getCustomerId() != null) {
            ObjectResponse<UserVo> response = userFeignClient.findInfoByUid(
                chargerOrder.getCustomerId(), null, null).block();
            if (response != null && response.getData() != null) {
                chargerOrder.setEmail(response.getData().getEmail());
            }
        }

        log.info("查询订单。orderNo: {}, chargerOrder: {}", orderNo, chargerOrder);
        Assert.notNull(chargerOrder, "要查询的订单信息不存在");

        // 订单启动方式：管理端手动与批量启动合并为管理端手动，页面兼容显示
        if (chargerOrder.getOrderType() != null && chargerOrder.getOrderType()
            .equals(OrderStartType.MGM_WEB_BATCH.getCode())) {
            chargerOrder.setOrderType(OrderStartType.MGM_WEB_MANUAL.getCode());
        }
        //电表开始读数，结束读数
        chargerOrder.setStartElectricity(
            chargerOrder.getStartElectricity() == null ? BigDecimal.ZERO
                : chargerOrder.getStartElectricity());
        chargerOrder.setEndElectricity(chargerOrder.getEndElectricity() == null ? BigDecimal.ZERO
            : chargerOrder.getEndElectricity());

//        if(chargerOrder.getPriceSchemeId() != null && chargerOrder.getPriceSchemeId() > 0L) {
//            ChargePriceVo price = priceSchemaBizService.getChargePrice(chargerOrder.getPriceSchemeId());
//            if(price != null) {
//                chargerOrder.setCalculateType(price.get)
//            }
//        }
        //2、订单详情信息
        /**
         * 转换枪头编号bcCode格式：桩号-枪头序号
         */
        String bcCode = chargerOrder.getBoxCode() + "-" + chargerOrder.getConnectorId();
        chargerOrder.setBcCode(bcCode);

        //查询物理卡号
        if (StringUtils.isNotBlank(chargerOrder.getCardNo()) && StringUtils.isBlank(
            chargerOrder.getCardChipNo())) {
            ObjectResponse<String> cardRes = cardFeignClient.getCardChipNoByCardNo(
                    chargerOrder.getCardNo())
                .block(Duration.ofSeconds(50L));
            if (cardRes.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                chargerOrder.setCardChipNo(cardRes.getData());
            }
        }
        if (StringUtils.isNotBlank(chargerOrder.getCardNo())) {
            ObjectResponse<Card> cardRes = cardFeignClient.getCardByCardNo(chargerOrder.getCardNo(),
                    chargerOrder.getTopCommId())
                .block(Duration.ofSeconds(50L));
            if (cardRes.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                chargerOrder.setCardType(cardRes.getData().getCardType());
            }
        }

        // 开票状态
        // 暂时处理: InvoicedId 不存在则设置 TaxType.NONE - 未开票
//        chargerOrder.setTaxType(chargerOrder.getInvoicedId() != null ? null : TaxType.UNKNOWN);

        // mongodb中数据
        OrderInMongo one = deviceStatusMongoService.findOne(orderNo);
//        OrderInMongo one = null;
        if (one == null) {
            one = orderDataService.getOssOrderDetail(orderNo);
        }
        if (null != one) {
            if (one.getSupplyType() == null) {
                PlugVo plug = redisIotReadService.getPlugRedisCache(chargerOrder.getBoxCode(),
                    chargerOrder.getConnectorId());
                chargerOrder.setEvseSupply(plug != null ? plug.getSupply() : null);
            } else {
                chargerOrder.setEvseSupply(one.getSupplyType());
            }

            chargerOrder.setStartType(one.getStartType());
            //chargerOrder.setCurrentSoc(one.getCurrSoc() + "");

            if (one.getInsulation() != null) {
                chargerOrder.setInsulationPositive(one.getInsulation().getPositive());
                chargerOrder.setInsulationNegative(one.getInsulation().getNegative());
            }

            // 合充信息填写
            if (one.getSecondPlugIdx() != null) {
                chargerOrder.setSecondPlugIdx(one.getSecondPlugIdx());
                chargerOrder.setSecondStartMeter(one.getSecondStartMeter());
                chargerOrder.setSecondStopMeter(one.getSecondStopMeter());
                chargerOrder.setSecondKwh(one.getSecondKwh());
            }

            if (StringUtils.isNotBlank(one.getStopChargeCode())) {
                chargerOrder.setStopChargeCode(one.getStopChargeCode());
            }

            if (one.getPlugOutTime() != null) {
                chargerOrder.setPlugOutTime(new Date(one.getPlugOutTime()));
            }
        }
        ObjectResponse<VinDto> res = vinFeignClient.selectByVin(chargerOrder.getVin(),
                chargerOrder.getTopCommId())
            .block(Duration.ofSeconds(50L));
        if (res != null && res.getData() != null && chargerOrder.getVin() != null && !"".equals(
            chargerOrder.getVin())) {
            chargerOrder.setVinId(res.getData().getId());
        }
        // 车牌号
        if (StringUtils.isNotBlank(chargerOrder.getVin())
            && StringUtils.isBlank(chargerOrder.getCarNo()) &&
            null != chargerOrder.getTopCommId()) {
//            Map<String, String> vinsMap = null;
//            List<String> vins = new ArrayList<>();
//            vins.add(chargerOrder.getVin());

            if (res != null && res.getData() != null) {
//                vinsMap = (Map<String, String>) res.getData();
                chargerOrder.setCarNo(res.getData().getCarNo());
            }
            chargerOrder.setVin(StringUtils.isNotBlank(chargerOrder.getVin())
                ? chargerOrder.getVin().toUpperCase() : "");
        }

        //集团客户名
        if (chargerOrder.getDefaultPayType() != null
            && OrderPayType.BLOC.getCode() == chargerOrder.getDefaultPayType()) {
            List<Long> rBLocIds = new ArrayList<>();
            rBLocIds.add(chargerOrder.getPayAccountId());
            ListResponse<RBlocUser> rBlocUserList = userFeignClient.selectRBlocUserIds(rBLocIds)
                .block(Duration.ofSeconds(50L));
            if (rBlocUserList != null
                && rBlocUserList.getStatus() == ResultConstant.RES_SUCCESS_CODE
                && CollectionUtils.isNotEmpty(rBlocUserList.getData())) {
                chargerOrder.setBlocUserName(rBlocUserList.getData().get(0).getBlocUserName());
                chargerOrder.setCorpId(rBlocUserList.getData().get(0).getBlocUserId());
            }
        }

        //商户全称
        CommercialSimpleVo deviceCommercial = commercialQueryDs.getCommercial(
            chargerOrder.getDeviceCommercialId());
        if (!ObjectUtils.isEmpty(deviceCommercial)) {
            chargerOrder.setCommercialFullName(deviceCommercial.getCommName());
        }

        //站点名称
        //Site site = siteQueryMapper.getSiteById(chargerOrder.getStationId());
        SitePo site = siteRoDs.getSite(chargerOrder.getStationId());
        if (site != null) {
            chargerOrder.setStationName(site.getSiteName());
            chargerOrder.setSiteNo(site.getSiteNo());
        }

        List<String> boxCodes = new ArrayList<>();
        boxCodes.add(chargerOrder.getBoxCode());
        Boolean thirdQrCode = false;
        if (CollectionUtils.isNotEmpty(this.hlhtCfg.getThirdQrCodeList()) && StringUtils.isNotEmpty(
            chargerOrder.getQrCode())) {
            for (int i = 0; i < this.hlhtCfg.getThirdQrCodeList().size(); i++) {
                PlugNoParser plugNoParser = new PlugNoParser(this.hlhtCfg);
                String thirdPlugNo = plugNoParser.parseThirdQrCode(chargerOrder.getTopCommId(),
                    this.hlhtCfg.getThirdQrCodeList().get(i).getThirdQrCodeRegex(),
                    this.hlhtCfg.getThirdQrCodeList().get(i).getThirdQrCodeOperatorId(),
                    chargerOrder.getQrCode());
                if (thirdPlugNo != null) {
                    thirdQrCode = true;
                    BsChargerMoreVo plug = this.deviceBizService.getBsChargeMoreVo(
                        chargerOrder.getShowId());
                    if (plug != null) {
                        chargerOrder.setChargerName(plug.getChargerName());
                        chargerOrder.setEvseName(plug.getEvseName());
                    }
                    break;
                }
            }
        }
        // 设置订单相关枪头、桩 名称
        if (!thirdQrCode) {
            if (StringUtils.startsWithIgnoreCase(chargerOrder.getQrCode(), "hlht://")) {
                BsChargerMoreVo plug = this.deviceBizService.getBsChargeMoreVo(
                    chargerOrder.getShowId());
                //            log.info("plug = {}", plugRes);
                if (plug != null) {
                    //                BsChargerPo plug = plugRes.getData();
                    //桩名
                    // chargerOrder.setEvseName(plug.get);
                    //枪名
                    chargerOrder.setEvseName(plug.getEvseName());
                    chargerOrder.setChargerName(plug.getChargerName());
                }
            } else {
                if (null != chargerOrder.getBoxCode() && null != chargerOrder.getConnectorId()) {

                    if (chargerOrder.getSecondPlugIdx() != null) {
                        // 合充订单
                        ListPlugParam listPlugParam = new ListPlugParam();
                        listPlugParam.setEvseNoList(List.of(chargerOrder.getBoxCode()));
                        ListResponse<PlugVo> plugListRes = iotDeviceMgmFeignClient.getPlugList(
                            listPlugParam);
                        log.info("plugListRes = {}", plugListRes);
                        if (CollectionUtils.isNotEmpty(plugListRes.getData())) {
                            Map<Integer, PlugVo> collect = plugListRes.getData()
                                .stream()
                                .collect(Collectors.toMap(PlugVo::getIdx, o -> o, (o, n) -> n));
                            PlugVo mainPlug = collect.get(chargerOrder.getConnectorId());
                            if (mainPlug != null) {
                                // 桩名
                                chargerOrder.setEvseName(mainPlug.getEvseName());
                                // 枪名
                                chargerOrder.setChargerName(mainPlug.getName());
                            }
                            PlugVo secondPlug = collect.get(chargerOrder.getSecondPlugIdx());
                            if (secondPlug != null) {
                                // 辅枪名
                                chargerOrder.setSecondChargerName(secondPlug.getName());
                            }
                        }
                    } else {
                        ObjectResponse<PlugVo> plugRes = iotDeviceMgmFeignClient.getPlugInfo(
                            PlugNoUtils.formatPlugNo(chargerOrder.getBoxCode(),
                                chargerOrder.getConnectorId()));
                        log.info("plugRes = {}", plugRes);
                        // FeignResponseValidate.check(plugRes);
                        if (plugRes.getData() != null) {
                            PlugVo plug = plugRes.getData();
                            //桩名
                            chargerOrder.setEvseName(plug.getEvseName());
                            //枪名
                            chargerOrder.setChargerName(plug.getName());
                        }
                    }
                } else {
                    log.warn("该充电订单没有桩枪信息: orderNo = {}", orderNo);
                }
            }
        }

        if (NumberUtils.equals(chargerOrder.getStatus(), OrderStatus.ORDER_STATUS_ERROR_CP)
            || NumberUtils.equals(chargerOrder.getStatus(), OrderStatus.ORDER_STATUS_CHARGING)) {
            ChargeOrderCache cache = redisUtil.getOrderVo(chargerOrder.getOrderNo());
            if (cache != null) {    // 使用缓存中的信息替换掉数据库查出来的内容
                if (cache.getPay() != null) {
                    chargerOrder.setOrderPrice(cache.getPay().getOrderOriginFee());
                    chargerOrder.setActualPrice(cache.getPay().getOrderFee());

                    chargerOrder.setElecPrice(cache.getPay().getElecOriginFee());
                    chargerOrder.setElecActualFee(cache.getPay().getElecFee());

                    chargerOrder.setServicePrice(cache.getPay().getServOriginFee());
                    chargerOrder.setServActualFee(cache.getPay().getServFee());

                } else {
                    chargerOrder.setOrderPrice(cache.getOrderPrice());
                    chargerOrder.setServicePrice(cache.getServicePrice());
                    chargerOrder.setServActualFee(cache.getServActualFee());
                    chargerOrder.setElecPrice(cache.getElecPrice());
                    chargerOrder.setElecActualFee(cache.getElecActualFee());
                    chargerOrder.setActualPrice(cache.getActualPrice());
                }

                chargerOrder.setOrderElectricity(cache.getOrderElectricity());
                chargerOrder.setFrozenAmount(cache.getFrozenAmount());
                chargerOrder.setDuration(cache.getDuration());
            }
        }
        //结算信息
        BalanceInfo bi = new BalanceInfo();
        bi.setOrderPrice(
            chargerOrder.getOrderPrice() == null ? BigDecimal.ZERO : chargerOrder.getOrderPrice());
        bi.setServicePrice(chargerOrder.getServicePrice());
        bi.setElecPrice(chargerOrder.getElecPrice());
        bi.setActualPrice(
            chargerOrder.getActualPrice() == null ? BigDecimal.ZERO : chargerOrder.getOrderPrice());
        bi.setCouponMoney(chargerOrder.getCouponMoney());
        bi.setActivityType("");//暂时没有
        bi.setDefaultPayType(chargerOrder.getDefaultPayType());
        bi.setPayAccountName(accountBizService.getAccountName(chargerOrder.getDefaultPayType(),
            chargerOrder.getPayAccountId()));
        chargerOrder.setBalanceInfo(bi);
        ChargerOrderRefundPo chargerOrderRefundPo = chargerOrderRefundRwDs.getChargerOrderRefundByOrderNo(
            orderNo, false);
        if (chargerOrderRefundPo != null) {
            chargerOrder.setRefundStatus(chargerOrderRefundPo.getStatus());
        } else if (
            NumberUtils.equals(chargerOrder.getDefaultPayType(), PayAccountType.PREPAY.getCode())
                && DecimalUtils.gt(chargerOrder.getFrozenAmount(), chargerOrder.getOrderPrice())) {
            // 即充即退订单没有生成退款单，且存在未消费完的冻结金额，按退款失败处理
            chargerOrder.setRefundStatus(ChargerOrderRefundStatus.FAIL);
        } else {
            chargerOrder.setRefundStatus(ChargerOrderRefundStatus.UNKNOWN);
        }

        // 处理类型
        if (null == chargerOrder.getAbnormal()) {
            chargerOrder.setProcessType(ProcessType.NORMAL);
        } else {
            if (chargerOrder.getManual()) {
                chargerOrder.setProcessType(ProcessType.ABNORMAL_DONE);
            } else {
                chargerOrder.setProcessType(ProcessType.ABNORMAL_UNDO);
            }
        }
        log.info("processType={}", chargerOrder.getProcessType());

        if (chargerOrder.getDefaultPayType() != null &&
            Set.of(OrderPayType.PREPAY.getCode(),
                    OrderPayType.WX_CREDIT.getCode(),
                    OrderPayType.ALIPAY_CREDIT.getCode())
                .contains(chargerOrder.getDefaultPayType())) {
            try {
                // 即充即退订单添加单号信息
                // t_pay_bill
                PayBillPo bill = payBillRwDs.selectByChargeOrderNo(chargerOrder.getOrderNo(),
                    DepositFlowType.IN_FLOW);
                if (null != bill) {
                    chargerOrder.setPayBillNo(bill.getOrderId())
                        .setPayBillTradeNo(bill.getTradeNo())
                        .setZftName(bill.getZftName())
                        .setWxSubMchId(bill.getWxSubMchId())
                        .setAlipaySubMchId(bill.getAlipaySubMchId())
                        .setPayChannel(bill.getPayChannel());
                    ObjectResponse<ZftVo> topCommZft = null;    // 顶级商户直付通
                    //  微信支付  wxSubMchId  为空  或者  NONE   取顶级商户WxMchId
                    if (List.of(PayChannel.WXPAY, PayChannel.WX_CREDIT)
                        .contains(bill.getPayChannel()) && (bill.getTopCommId() != null) && (
                        StringUtils.isEmpty(bill.getWxSubMchId())
                            || "NONE".equals(bill.getWxSubMchId()))) {
                        topCommZft = authCenterFeignClient.getZftByTopCommId(
                            bill.getTopCommId());
                        if (topCommZft != null && topCommZft.getData() != null) {
                            chargerOrder.setWxSubMchId(topCommZft.getData().getWxMchId());
                        }
                    }

                    //  支付宝  alipaySubMchId  为空  或者  NONE   取顶级商户AlipayMchId
                    if (List.of(PayChannel.ALIPAY, PayChannel.ALIPAY_CREDIT)
                        .contains(bill.getPayChannel()) && (bill.getTopCommId() != null) && (
                        StringUtils.isEmpty(bill.getAlipaySubMchId())
                            || "NONE".equals(bill.getAlipaySubMchId()))) {
                        if (topCommZft == null) {
                            topCommZft = authCenterFeignClient.getZftByTopCommId(
                                bill.getTopCommId());
                        }
                        if (topCommZft != null && topCommZft.getData() != null) {
                            chargerOrder.setAlipaySubMchId(topCommZft.getData().getAlipayMchId());
                        }
                    }

                    // 农行信息
                    if (PayChannel.ABC_BANK.equals(bill.getPayChannel())) {
                        AbcCouponPo abcCoupon = abcCouponRoDs.getByOrderNo(
                            chargerOrder.getOrderNo());
                        if (null != abcCoupon &&
                            AbcCouponUsed.ORDER_USED_REACHABLE == abcCoupon.getUsed()) {
                            chargerOrder.setAbcCouponId(abcCoupon.getCouponId())
                                .setAbcPayAmount(abcCoupon.getPayAmount())
                                .setAbcCouponAmount(abcCoupon.getCouponAmount());
                        }
                    }
                }

                // t_charger_order_refund
//                ChargerOrderRefundPo refundPo = chargerOrderRefundRwDs.getChargerOrderRefundByOrderNo(orderNo, false);
                if (null != chargerOrderRefundPo) {
                    chargerOrder.setRefundTradeNo(
                            StringUtils.isBlank(chargerOrderRefundPo.getThirdTradeNo()) ?
                                chargerOrderRefundPo.getSeqNo()
                                : chargerOrderRefundPo.getThirdTradeNo())
                        .setPrepayRefundAmount(chargerOrderRefundPo.getAmount());
                }
            } catch (Exception e) {
                log.error("查看订单详情中填充充值信息异常: err = {}", e.getMessage(), e);
                // 捕获异常后不做处理，不影响查询充电订单信息
            }
        }

        if (chargerOrder.getStopCode() != null
            && chargerOrder.getStopCode().equals(OrderStopCode.C00)
            && chargerOrder.getCompleteCode() != null) {
            // 若订单正常停充，则展示completeCode
            chargerOrder.setStopReason(chargerOrder.getCompleteCode().getDesc());
        }

        return new ObjectResponse<>(chargerOrder);


    }

    /**
     * 通过第三方订单号获取订单信息
     *
     * @param thirdOrderNo
     * @return
     */
    @Transactional(readOnly = true)
    public OrderInfoVo getOrderInfoByThirdOrderNo(String thirdOrderNo, Long customId) {
        ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPoByThirdOrderNo(thirdOrderNo,
            customId, false);
        if (order != null) {
            OrderInfoVo vo = new OrderInfoVo();
            BeanUtils.copyProperties(order, vo);
            return vo;
        }
        return null;
    }

    @Transactional(readOnly = true)
    public OrderInfoVo getOrderDetailInfo(String orderNo) {
        // 订单编号
        if (StringUtils.isBlank(orderNo)) {
            log.info("<< 订单编号不能为空.");
            throw new DcArgumentException("订单编号不能为空");
        }

        ChargerOrderPo order = chargerOrderRoDs.getChargeOrderPo(orderNo, false);

        log.info("<< 获取订单数据ChargerOrder结果: result={}", order);

        if (order == null) {
            throw new DcServiceException("该订单信息不存在, 请输入正确的订单编号.");
        }

        ChargeOrderCache orderCache = redisUtil.getOrderVo(orderNo);

        // mongodb中数据
        OrderInMongo one = deviceStatusMongoService.findOne(orderNo);
        if (null == one) {
            one = orderDataService.getOssOrderDetail(orderNo);
        }

        ChargerOrderPayPo oPay = null;
        if (orderCache != null) {
            oPay = orderCache.getPay();
        }
        if (oPay == null) {
            oPay = chargerOrderPayRoDs.getByOrderno(orderNo);
        }
        ChargerOrderCarPo oCar = null;

        // 转成VO对象
        // 需要获取桩的电流形式
        // 通过VIN码获取车牌号
        OrderInfoVo vo = new OrderInfoVo();
        vo.setOrderNo(orderNo)
            .setEvseId(order.getBoxCode())
            .setDefaultPayType(order.getDefaultPayType())
            .setPlugId(order.getConnectorId());
        if (orderCache != null) {
            vo.setElectricity(orderCache.getOrderElectricity())
                .setCardChipNo(orderCache.getCardChipNo())
                .setEvseId(orderCache.getBoxCode())
                .setPlugId(orderCache.getConnectorId())
                .setKwh(orderCache.getOrderElectricity())
                .setCurrSoc(orderCache.getCurrentSoc())
                .setStartSoc(orderCache.getStartSoc())
                .setStartType(orderCache.getOrderType())

                //                .setDuration() // 通过details中获取
                .setVin(orderCache.getVin())
                .setCarNo(orderCache.getCarNo())
                .setStartTime(orderCache.getChargeStartTime())
//                    .setStopTime(orderCache.getUpdateTime().getTime() / 1000)
                .setOrderStatus(orderCache.getStatus());

            if (null == orderCache.getChargeEndTime()) {
                vo.setStopTime(orderCache.getUpdateTime().getTime() / 1000);
            } else {
                vo.setStopTime(orderCache.getChargeEndTime());
            }
        } else if (null != one) {
            vo.setElectricity(one.getKwh())
                .setCardChipNo(one.getCardChipNo())

                .setEvseId(one.getEvseId())

                .setPlugId(one.getPlugId())
                .setKwh(one.getKwh())

                //                .setCarNo(one.getCarNo()) // 通过 VIM 从新获取

                .setCurrSoc(one.getCurrSoc())
                .setStartSoc(one.getStartSoc())
                //                .setSupplyType() // 通过桩编号获取电流形式
                .setElecFee(one.getElecFee())
                .setServFee(one.getServFee())
                //                .setSiteName() // 通过桩获取场站信息
                .setStartType(one.getStartType())

                //                .setDuration() // 通过details中获取
                .setVin(one.getVin())
                .setCarNo(one.getCarNo())
                .setStartTime(one.getStartTime())
                .setStopTime(one.getUpdateTime())
                .setOrderStatus(one.getOrderStatus())
                .setPriceCode(one.getPriceCode());
        } else {
            // 填充订单关联的车辆信息
            oCar = this.chargerOrderCarRoDs.getChargerOrderCar(orderNo);
            if (oCar != null) {
                vo.setVin(oCar.getVin())
                    .setCarNo(oCar.getCarNo());
            }
        }

        if (null != one) {
            vo.setSupplyType(SupplyType.AC == one.getSupplyType() ? 0 : 1)
                .setEvseName(one.getEvseName())
                .setAccountNo(one.getAccountNo())
                .setCurrent(one.getCurrent())
                .setVoltage(one.getVoltage());

            // 剩余时间
            if (null == one.getDetails() || one.getDetails().size() == 0) {
                vo.setRemainingTime(0);
            } else {
                var detail = one.getDetails().get(one.getDetails().size() - 1);
                if (detail.getRemainingTime() != null) {
                    vo.setRemainingTime(detail.getRemainingTime().intValue());
                } else {
                    vo.setRemainingTime(0);
                }
            }

            // 充电细节信息
            List<ChargerDetail> details = one.getDetails();
            if (null != details && !details.isEmpty()) {
                ChargerDetail last = details.get(details.size() - 1);

                // 充电时长
                vo.setDuration(last.getDuration() != null ? last.getDuration().intValue() : null);

                // 计算采样点
                // 1, 采样算法(方式):
                // a) 采样点必须包含第1个和最后1个数据样本
                // b) 每分钟抽取1个样本, 取每分钟的第一个样本
                // c) 如果当前分钟内没有样本, 则跳过取下一分钟的样本
                // 前端对这些点使用 [贝塞尔曲线] 渲染成曲线
                vo.setDetailList(this.sampling(details));
            }

            // 分时电价
            List<OrderDetailV2> feeList = one.getFeeList();
            if (null != feeList && !feeList.isEmpty()) {
                vo.setFeeList(feeList);
            }

            // 车牌号
            // 根据启动方式获取车牌号
//            log.info("start type = {}", one.getStartType());
//            if (one.getStartType() == null) {
//                log.error("订单 {} 缺少启动方式", one.getOrderNo());
//                throw new DcServiceException("订单数据异常,请联系客服处理");
//            }
            if (StringUtils.isNotBlank(one.getStopChargeCode())) {
                vo.setStopChargeCode(one.getStopChargeCode());
            }
        } else {
            // mongo里没有订单数据时，补充枪头供电类型字段
            PlugVo plug = redisIotReadService.getPlugRedisCache(order.getPlugNo());
            if (plug != null) {
                vo.setSupplyType(SupplyType.AC == plug.getSupply() ? 0 : 1);
            }
        }
        if (vo.getStartType() == null) {
            log.error(
                "订单 {} 缺少启动方式(startType). redisOrder.startType = {}, mongoOrder.startType = {}",
                orderNo, orderCache == null ? null : orderCache.getOrderType(),
                one == null ? null : one.getStartType());
        }
        vo.setOrderStatus2(order.getOrderStatus());
        vo.setChargerName(order.getChargerName());//枪名以订单表中为准
        vo.setSiteId(order.getStationId()); // 站点ID
        vo.setSiteName(order.getStationName());//站点名称取订单表中为准
        vo.setStatus(order.getStatus());//目前从mongodb里获取orderStatus可能有空值，暂时使用t_charger_order的status
        vo.setAbnormal(order.getAbnormal() != null); // @Nathan 不为空就为异常订单
        vo.setManual(order.getManual());
        vo.setChannelId(order.getChannelId().getCode());

        //结算信息
        BalanceInfo bi = new BalanceInfo();
        if (oPay != null) {
            bi.setOrderPrice(
                oPay.getOrderOriginFee() == null ? BigDecimal.ZERO : oPay.getOrderOriginFee());
            bi.setActualPrice(oPay.getOrderFee() == null ? BigDecimal.ZERO : oPay.getOrderFee());
            bi.setCouponMoney(
                oPay.getCouponAmount() == null ? BigDecimal.ZERO : oPay.getCouponAmount());
            vo.setElecFee(oPay.getElecFee())
                .setElecOriginFee(oPay.getElecOriginFee())
                .setServFee(oPay.getServFee())
                .setServOriginFee(oPay.getServOriginFee())
                .setPriceCode(oPay.getPriceCode())
                .setDiscountRefId(oPay.getDiscountRefId());//优惠ID

            if (null != order.getDefaultPayType() &&
                PayAccountType.PREPAY.getCode() == order.getDefaultPayType()) {
                vo.setPayStatus(oPay.getPayStatus());
                vo.setRefundStatus(oPay.getRefundStatus());
            }
            vo.setAccountId(oPay.getAccountId());
            vo.setAccountType(oPay.getAccountType());

            // 积分折扣信息
            bi.setScoreAmount(oPay.getScoreAmount());
        } else {
            bi.setOrderPrice(BigDecimal.ZERO);
            bi.setActualPrice(BigDecimal.ZERO);
            bi.setCouponMoney(BigDecimal.ZERO);
            // 积分折扣信息
            bi.setScoreAmount(BigDecimal.ZERO);
        }
//        bi.setOrderPrice(order.getOrderPrice() == null ? BigDecimal.ZERO : order.getOrderPrice());
//        bi.setActualPrice(order.getActualPrice() == null ? BigDecimal.ZERO : order.getActualPrice());
//        bi.setCouponMoney(order.getCouponMoney());
        bi.setActivityType("");//暂时没有
        bi.setDefaultPayType(order.getDefaultPayType());
        bi.setPayAccountName(
            balanceService.getAccountName(order.getDefaultPayType(), order.getPayAccountId()));

        vo.setBalanceInfo(bi);

        vo.setInvoicedId(order.getInvoicedId());
        if (order.getInvoicedId() != null && order.getInvoicedId() != 0) {
            ListResponse<InvoicedRecordVo> invoicedRecordVo = invoiceFeignClient.findByInvoiceIds(
                    Arrays.asList(order.getInvoicedId()))
                .block(Duration.ofSeconds(50L));
            if (invoicedRecordVo != null && invoicedRecordVo.getData() != null
                && invoicedRecordVo.getData().size() == 1) {
                vo.setInvoicedStatus(invoicedRecordVo.getData().get(0).getInvoicedStatus());
            }
        }

        if (null != order.getCusPostStatus() &&
            !CusPostStatus.UNKNOWN.equals(order.getCusPostStatus())) {
            // 用户评论信息
            ObjectResponse<CusPostVo> block = reactorUserFeignClient.getCusPostByOrderNo(
                    order.getOrderNo())
                .block(Duration.ofSeconds(50L));
            FeignResponseValidate.checkIgnoreData(block);
            if (null != block) {
                vo.setLevel(block.getData().getLevel())
                    .setCusContent(block.getData().getCusContent())
                    .setReplyContent(block.getData().getReplyContent());
            }
        }

        // 支付记录的支付渠道
        if (null != order.getDefaultPayType() &&
            PayAccountType.PREPAY.getCode() == order.getDefaultPayType()) {
            // 获取充值  退款信息
            List<PayBillPo> payBillList = payBillRwDs.selectListByOrderNo(orderNo);

            if (CollectionUtils.isNotEmpty(payBillList)) {
                Optional<PayBillPo> inBill = payBillList.stream()
                    .filter(x -> DepositFlowType.IN_FLOW.equals(x.getFlowType())).findFirst();

                Optional<PayBillPo> outBill = payBillList.stream()
                    .filter(x -> DepositFlowType.OUT_FLOW.equals(x.getFlowType())).findFirst();

                if (inBill.isPresent()) { // 支付方式
                    vo.setPayChannel(inBill.get().getPayChannel());
                    vo.setAbcAmount(inBill.get().getAmount());
                    // 农行支付优惠金额
                    if (PayChannel.ABC_BANK.equals(inBill.get().getPayChannel())) {
                        AbcCouponPo abcCoupon = abcCouponRoDs.getByOrderNo(
                            order.getOrderNo());
                        if (null != abcCoupon
                            && AbcCouponUsed.ORDER_USED_REACHABLE == abcCoupon.getUsed()) {
                            vo.getBalanceInfo().setAbcCouponAmount(abcCoupon.getCouponAmount());
                        }
                    }
                }

                if (outBill.isPresent()) {  // 退款信息  只返回成功  失败
                    if (outBill.get().getStatus() == 1) {
                        vo.setRefundStatus(ChargerOrderRefundStatus.FINISH);
                    } else {
                        vo.setRefundStatus(ChargerOrderRefundStatus.FAIL);
                    }
                    vo.setRefundAmount(outBill.get().getAmount());
                }
            }
        }

        return vo;
    }

    /**
     * 数据采样
     *
     * @param details
     * @return
     */
    private List<ChargerDetailVo> sampling(List<ChargerDetail> details) {
//        log.info(">> 进行数据采样: details={}", details);
        // 1, 采样算法(方式):
        // a) 采样点必须包含第1个和最后1个数据样本
        // b) 每分钟抽取1个样本, 取每分钟的第一个样本
        // c) 如果当前分钟内没有样本, 则跳过取下一分钟的样本

        List<ChargerDetailVo> result = new ArrayList<>();
        Integer lastDuration = null;
        for (int i = 0, len = details.size(); i < len; i++) {
            ChargerDetail detail = details.get(i);

            // 当前功率
            // 直流桩: 直流输出电压 * 直流输出电流
            // 交流桩: A相电流 * A相电压 * 1.73 【交流桩不显示】
            BigDecimal curPower = null;
            BigDecimal voltageO = null;
            BigDecimal currentO = null;
            if (detail.getDcVoltageO() != null && detail.getDcCurrentO() != null) {
                // 直流
//                curPower = detail.getDcVoltageO().multiply(detail.getDcCurrentO())
//                        .divide(BigDecimal.valueOf(1000), 4, RoundingMode.HALF_DOWN);

                voltageO = detail.getDcVoltageO();
                currentO = detail.getDcCurrentO();
            } else if (detail.getAcCurrentA() != null && detail.getAcVoltageA() != null) {

                // 产品: 交流桩取A相
                voltageO = detail.getAcVoltageA();
                currentO = detail.getAcCurrentA();
            }

            if (detail.getPower() != null) {
                curPower = detail.getPower();
            } else if (detail.getDcVoltageO() != null && detail.getDcCurrentO() != null) {
                // 仅直流
                curPower = detail.getDcVoltageO().multiply(detail.getDcCurrentO())
                    .divide(BigDecimal.valueOf(1000), 4, RoundingMode.HALF_DOWN);
            }

            // 充电时长
            Integer duration = detail.getDuration() == null ? 0 : detail.getDuration().intValue();

            // 充电时长单位: 分钟
            if (null == lastDuration || (duration - lastDuration > 1) || i == (len - 1)) {
                // 保留最后一个
                if (i == (len - 1) && (null != lastDuration && lastDuration.equals(duration))) {
                    result.remove(result.size() - 1);
                }

                // 缓存充电时长
                lastDuration = duration;

                // 采样
                ChargerDetailVo chargerDetailVo = new ChargerDetailVo()
                    .setDuration(
                        detail.getDuration() == null ? null : detail.getDuration().longValue())
                    .setKwh(detail.getKwh())
                    .setElecFee(detail.getElecFee())
                    .setServFee(detail.getServFee())
                    .setCurPower(curPower)
                    .setCurSoc(detail.getSoc())
                    .setVoltageO(voltageO)
                    .setCurrentO(currentO);

                if (detail.getBms() != null) {
                    chargerDetailVo.setNeedCurrent(detail.getBms().getNeedCurrent())
                        .setNeedVoltage(detail.getBms().getNeedVoltage());

                }

                if (detail.getBattery() != null) {
                    chargerDetailVo.setTemp(detail.getBattery().getTemp())
                        .setMaxTemp(detail.getBattery().getMaxTemp())
                        .setMinTemp(detail.getBattery().getMinTemp());
                }

                result.add(chargerDetailVo);


            }
        }

        log.info("<< 采样结果: result.size = {}", result.size());
        return result;
    }


}