package com.cdz360.biz.model.tj.kc.po;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建-数据字典-财务数据-通用现金流出配置")
public class TjCashOutflowPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "类型（1：运维费用，2：引流费用，3：平台托管费用，4：电损支出，5：通道费，6：财务费用，7：管理费用）")
    private Integer type;

    @Schema(description = "场站类型（1：B端，2：C端）")
    private Integer stationType;

    @Schema(description = "场站区域")
    private String stationArea;

    @Schema(description = "引流区域名称，格式省省份名称,城市名称,区名称，可为空")
    private String channelAreaName;

    @Schema(description = "引流区域，格式省编码,市编码,区编码，可为空")
    private String channelArea;

    @Schema(description = "通用比率（费用）")
    private BigDecimal commonRate;

    @Schema(description = "第一年比率（费用）")
    private BigDecimal firstYearRate;

    @Schema(description = "第二年比率（费用）")
    private BigDecimal secondYearRate;

    @Schema(description = "第三年比率（费用）")
    private BigDecimal thirdYearRate;

    @Schema(description = "第四年比率（费用）")
    private BigDecimal fourthYearRate;

    @Schema(description = "第五年比率（费用）")
    private BigDecimal fifthYearRate;

    @Schema(description = "第六年比率（费用）")
    private BigDecimal sixthYearRate;

    @Schema(description = "第七年比率（费用）")
    private BigDecimal seventhYearRate;

    @Schema(description = "第八年比率（费用）")
    private BigDecimal eighthYearRate;

    @Schema(description = "第九年比率（费用）")
    private BigDecimal ninthYearRate;

    @Schema(description = "第十年比率（费用）")
    private BigDecimal tenthYearRate;

    @Schema(description = "true有效;false删除")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}

