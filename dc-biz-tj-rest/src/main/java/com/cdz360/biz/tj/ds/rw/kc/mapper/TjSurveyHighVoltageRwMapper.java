package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyHighVoltageRwMapper {

    TjSurveyHighVoltagePo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveyHighVoltage(TjSurveyHighVoltagePo tjSurveyHighVoltagePo);

    int updateTjSurveyHighVoltage(TjSurveyHighVoltagePo tjSurveyHighVoltagePo);

    int disableTjSurveyHighVoltageBySurveyNo(String surveyNo);

}

