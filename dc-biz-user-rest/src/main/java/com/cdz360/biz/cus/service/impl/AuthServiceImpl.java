package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.OrderStartType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.IAuthService;
import com.cdz360.biz.cus.utils.RedisUtil;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.model.cus.auth.dto.CusAuthReqEx;
import com.cdz360.biz.model.cus.auth.dto.CusAuthRes;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.po.SitePo;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.AuthMediaResult;
import com.chargerlinkcar.framework.common.feign.BoxTradingFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteTradingFeignClient;
import com.chargerlinkcar.framework.common.service.EvseCacheService;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import jakarta.annotation.Nonnull;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class AuthServiceImpl implements IAuthService {

    @Autowired
    private RedisUtil redisDao;
    @Autowired
    private AuthFacadeImpl authFacade;
//    @Autowired
//    private BsBoxFeignClient bsBoxFeignClient;

    @Autowired
    private SiteTradingFeignClient siteTradingFeignClient;

    @Autowired
    private BoxTradingFeignClient boxTradingFeignClient;

    @Autowired
    private EvseCacheService evseCacheService;

    @Autowired
    private CommercialRoDs commercialRoDs;

    @Autowired
    private CardMapper cardMapper;

    @Autowired
    private VinMapper vinMapper;

//    @Autowired
//    private SiteBlacklistUserFeignClient siteBlacklistUserFeignClient;

    /**
     * @param authReq
     * @param passcode
     * @return
     */
    @Override
    public Mono<CusAuthRes> authentication(CusAuthReqEx authReq, String passcode) {
        EvseVo evseVo = this.authPreCheck(authReq);

        Mono<CusAuthRes> cusAuthRes;
        if (NumberUtils.equals(OrderStartType.ONLINE_CARD.getCode(), authReq.getAuthType())) {

            cusAuthRes = this.authenticationCard(authReq, false, evseVo);

        } else if (NumberUtils.equals(OrderStartType.ONLINE_VIN.getCode(), authReq.getAuthType())) {

            cusAuthRes = this.authenticationVin(authReq, evseVo);

        } else if (EvseProtocolType.OCPP.equals(evseVo.getProtocol())) {

            if (NumberUtils.equals(OrderStartType.EVSE_AUTO.getCode(), authReq.getAuthType())
                || NumberUtils.equals(OrderStartType.CREDIT_CARD.getCode(),
                authReq.getAuthType())) {

                cusAuthRes = this.ocppEvseAutoAuth(evseVo.getEvseNo());
            } else {
                cusAuthRes = this.authenticationOcpp(authReq, evseVo);
            }
        } else {
            throw new DcArgumentException("无法识别鉴权类型： " + authReq.getAuthType());
        }
        return cusAuthRes;
    }

    /**
     * 卡鉴权
     *
     * @param cusAuthReqEx
     * @param evseVo
     * @return
     */
    private Mono<CusAuthRes> authenticationCard(CusAuthReqEx cusAuthReqEx, boolean exactMatch,
        EvseVo evseVo) {

        return checkAuthReq(cusAuthReqEx, e -> {

            BigDecimal frozenAmount;
            Long siteCommId;

            SitePo siteSimpleInfoVo = siteTradingFeignClient.getSiteById(evseVo.getSiteId())
                .getData();
            if (SiteStatus.ONLINE.getCode() != siteSimpleInfoVo.getStatus()) {
                throw new DcServiceException(
                    DcConstants.KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL, "场站状态异常");
            }
            log.info("获取站点信息. siteId = {}, siteName = {}, commId = {}, frozenAmount = {}",
                siteSimpleInfoVo.getId(), siteSimpleInfoVo.getSiteName(),
                siteSimpleInfoVo.getOperateId(),
                siteSimpleInfoVo.getFrozenAmount());
            cusAuthReqEx.setSiteEcny(siteSimpleInfoVo.getEcny());

            if (StringUtils.isBlank(cusAuthReqEx.getSiteId())) {
                //获取站点冻结金额
                frozenAmount = siteSimpleInfoVo.getFrozenAmount();
                siteCommId = siteSimpleInfoVo.getOperateId();
                cusAuthReqEx.setSiteId(siteSimpleInfoVo.getId());
            } else {
                frozenAmount = cusAuthReqEx.getSiteFrozenAmount();
                siteCommId = cusAuthReqEx.getSiteCommId();
            }

            // 场站关联的商户
            CommercialSimpleVo siteComm = this.commercialRoDs.getCommerial(siteCommId);

            return authFacade.getCardAuth(evseVo, cusAuthReqEx.getAccountNo(), exactMatch,
                evseCacheService.supportRealTmeFee(evseVo), frozenAmount, siteComm,
                cusAuthReqEx.getSiteEcny(),
                siteSimpleInfoVo.getGids()
            ).doOnNext(authMediaResult -> {
                authMediaResult.setSiteId(evseVo.getSiteId());
                log.info("checkAuthReq card callback. cusAuthReqEx: {}, res: {}", cusAuthReqEx,
                    authMediaResult);
            });

        });
    }

    /**
     * VIN鉴权
     *
     * @param cusAuthReqEx
     * @param evseVo
     * @return
     */
    private Mono<CusAuthRes> authenticationVin(CusAuthReqEx cusAuthReqEx, EvseVo evseVo) {

        return checkAuthReq(cusAuthReqEx, e -> {

            BigDecimal frozenAmount;
            Long siteCommId;

            SitePo siteSimpleInfoVo = siteTradingFeignClient.getSiteById(evseVo.getSiteId())
                .getData();
            if (SiteStatus.ONLINE.getCode() != siteSimpleInfoVo.getStatus()) {
                throw new DcServiceException(
                    DcConstants.KEY_RES_CODE_AUTH_FAIL_SITE_STATUS_ABNORMAL, "场站状态异常");
            }
            log.info("获取站点信息. siteId = {}, siteName = {}, commId = {}, frozenAmount = {}",
                siteSimpleInfoVo.getId(), siteSimpleInfoVo.getSiteName(),
                siteSimpleInfoVo.getOperateId(),
                siteSimpleInfoVo.getFrozenAmount());
            cusAuthReqEx.setSiteEcny(siteSimpleInfoVo.getEcny());

            if (StringUtils.isBlank(cusAuthReqEx.getSiteId())) {

                //获取站点冻结金额
                frozenAmount = siteSimpleInfoVo.getFrozenAmount();
                siteCommId = siteSimpleInfoVo.getOperateId();
                cusAuthReqEx.setSiteId(siteSimpleInfoVo.getId());
            } else {
                frozenAmount = cusAuthReqEx.getSiteFrozenAmount();
                siteCommId = cusAuthReqEx.getSiteCommId();
            }

            // 场站关联的商户
            CommercialSimpleVo siteComm = this.commercialRoDs.getCommerial(siteCommId);

            Mono<AuthMediaResult> authMediaResult = authFacade.getVinAuth(
                evseVo, cusAuthReqEx.getAccountNo(), evseCacheService.supportRealTmeFee(evseVo),
                frozenAmount, siteComm, //cusAuthReqEx.getSiteEcny(),
                cusAuthReqEx.getPayTypes(),
                siteSimpleInfoVo.getGids());
            authMediaResult = authMediaResult.doOnNext(result -> {
                //AuthMediaResult authMediaResult = authFacade.getVinAuth(evseVo, cusAuthReqEx.getAccountNo(), evseCacheService.supportRealTmeFee(evseVo), frozenAmount);
                result.setSiteId(evseVo.getSiteId());
                log.info("account = {}, evseNo = {}, res: {}",
                    cusAuthReqEx.getAccountNo(), cusAuthReqEx.getEvseId(), result);
            });

            return authMediaResult;
        });
    }

    private Mono<CusAuthRes> authenticationOcpp(CusAuthReqEx cusAuthReqEx, EvseVo evseVo) {

        String accountNo = cusAuthReqEx.getAccountNo();
        boolean vinAuthEnable = false; // 是否开启VIN鉴权
        boolean checkPlugAndChargeConfigEnable = false; // 是否检查桩即插即充配置
        Mono<CusAuthRes> resMono = null;

        Long topCommId = commercialRoDs.getTopCommId(evseVo.getSiteCommId());
        try {
            // 先将accountNo当作卡来鉴权，判断卡是否存在
            Long cardExistCount = cardMapper.checkCardCount(accountNo, topCommId);
            boolean cardExist = cardExistCount > 0;
            if (cardExist) {
                // 卡存在则进行卡鉴权，判断是否可用
                AtomicBoolean cardValidAto = new AtomicBoolean(false);
                resMono = this.authenticationCard(cusAuthReqEx, true, evseVo).doOnNext(e -> {
                    cardValidAto.set(
                        e != null && (DecimalUtils.gtZero(e.getBalance()) || DecimalUtils.gtZero(
                            e.getPower())));
                });

                if (cardValidAto.get()) {
                    // 卡可用，鉴权成功
                } else {
                    // 卡不可用时，当作VIN来鉴权
                    vinAuthEnable = true;
                }
            } else {
                // 卡不存在时，当作VIN来鉴权
                vinAuthEnable = true;
            }
        } catch (Exception e) {
            log.warn("OCPP-idTag充当卡片鉴权时失败. error: {} accountNo = {}", e.getMessage(),
                accountNo, e);
            // 卡鉴权失败时，当作VIN来鉴权
            vinAuthEnable = true;
        }

        if (vinAuthEnable) {
            resMono = null; // 需先置空
            try {
                // accountNo当作VIN来鉴权，判断VIN是否存在
                Long vinExistCount = vinMapper.checkVinCount(accountNo, topCommId);
                boolean vinExist = vinExistCount > 0;
                if (vinExist) {
                    // VIN存在则进行VIN鉴权，判断是否可用
                    AtomicBoolean vinValidAto = new AtomicBoolean(false);
                    resMono = this.authenticationVin(cusAuthReqEx, evseVo).doOnNext(e -> {
                        vinValidAto.set(e != null && (DecimalUtils.gtZero(e.getBalance())
                            || DecimalUtils.gtZero(e.getPower())));
                    });

                    if (vinValidAto.get()) {
                        // VIN可用，鉴权成功
                    } else {
                        // VIN不可用时，检查桩即插即充配置是否开启
                        checkPlugAndChargeConfigEnable = true;
                    }
                } else {
                    // VIN不存在时，检查桩即插即充配置是否开启
                    checkPlugAndChargeConfigEnable = true;
                }
            } catch (Exception e) {
                log.warn("OCPP-idTag充当VIN鉴权时失败. error: {} accountNo = {}", e.getMessage(),
                    accountNo, e);
                // VIN鉴权失败时，检查桩即插即充配置是否开启
                checkPlugAndChargeConfigEnable = true;
            }
        }

        if (checkPlugAndChargeConfigEnable) {
            resMono = this.ocppEvseAutoAuth(evseVo.getEvseNo());
        }

        return Optional.ofNullable(resMono)
            .orElse(Mono.empty())
            .filter(Objects::nonNull)
            .switchIfEmpty(Mono.error(new DcServiceException("鉴权失败")));
    }

    private Mono<CusAuthRes> ocppEvseAutoAuth(String evseNo) {
        // 检查桩即插即充配置是否开启
        ObjectResponse<BsBoxSettingPo> boxSettingRes = boxTradingFeignClient.getBoxSetting(evseNo);
        FeignResponseValidate.checkIgnoreData(boxSettingRes);

        AtomicBoolean plugAndChargeEnableAto = new AtomicBoolean(false);
        Optional.ofNullable(boxSettingRes).map(ObjectResponse::getData).ifPresent(e -> {
            plugAndChargeEnableAto.set(Boolean.TRUE.equals(e.getIsNoCardCharge()));
        });
        if (plugAndChargeEnableAto.get()) {
            CusAuthRes res = new CusAuthRes();
            res.setOcppEvseAuto(Boolean.TRUE);
            return Mono.just(res);
        }
        return Mono.empty();
    }

    @Override
    public String getAndDelAuthResult(CusAuthReqEx cusAuthReqEx) {
        log.info("getAndDelAuthResult. request: {}", cusAuthReqEx);
        String redisKey = getRedisKey(cusAuthReqEx);

        String authResultCache = redisDao.getAndDel(redisKey);

        log.info("从redis中获取鉴权数据。redisKey: {}, value: {}", redisKey, authResultCache);

        return authResultCache;
    }

    private EvseVo authPreCheck(CusAuthReqEx cusAuthReqEx) {
        IotAssert.isNotNull(cusAuthReqEx, "请求参数异常");
        IotAssert.isNotBlank(cusAuthReqEx.getAccountNo(), "请传入鉴权号码");
        IotAssert.isNotBlank(cusAuthReqEx.getGwno(), "请传入网关编号");
        IotAssert.isNotBlank(cusAuthReqEx.getEvseId(), "请传入桩编号");
        IotAssert.isNotBlank(cusAuthReqEx.getPlugId(), "请传入枪号");

        EvseVo evseVo = evseCacheService.getCachedEvse(cusAuthReqEx.getEvseId());
        if (evseVo == null) {
            log.warn("桩未在线. evseId = {}", cusAuthReqEx.getEvseId());
            throw new DcArgumentException("桩未在线", Level.WARN);
        } else if (StringUtils.isBlank(evseVo.getSiteId())) {
            log.warn("桩还未绑定到场站. evseVo = {}", evseVo);
            throw new DcArgumentException(DcConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_UNBOUND,
                "桩还未绑定到场站");
        } else if (null == evseVo.getPriceCode() || 0 == evseVo.getPriceCode()) {
            log.warn("桩还未绑定计费模板: evseNo = {}", evseVo.getEvseNo());
            throw new DcArgumentException(DcConstants.KEY_RES_CODE_AUTH_FAIL_EVSE_NO_TEMPLATE,
                "桩还未绑定计费模板");
        }
        return evseVo;
    }

    private Mono<CusAuthRes> checkAuthReq(@Nonnull CusAuthReqEx cusAuthReqEx,
        Function<String, Mono<AuthMediaResult>> f) {

        //调用lambda，实现自定义逻辑
        Mono<AuthMediaResult> mono = f.apply(cusAuthReqEx.getAccountNo());

        return mono.map(authMediaResult -> {
            log.info("卡片/VIN鉴权。account = {}, evseNo = {}, authResult: {}",
                cusAuthReqEx.getAccountNo(), cusAuthReqEx.getEvseId(), authMediaResult);
            IotAssert.isNotNull(authMediaResult, "未查到此介质的信息，鉴权失败");

            List<String> allowStatus = List.of(CardStatus.ACTIVE.getCode(),
                CardStatus.ISSUE.getCode(),
                CardStatus.ISSUE_SUCCESS.getCode());
            IotAssert.isTrue(allowStatus.contains(authMediaResult.getMediaStatus()),
                "介质未激活，鉴权失败");

            if (PayAccountType.ALIPAY_CREDIT == authMediaResult.getPayType()) {
                // 使用支付宝芝麻信用，不需要限制场站
            } else if (StringUtils.isBlank(authMediaResult.getStations())) {
                log.warn("鉴权失败, 鉴权介质未绑定到任何场站");
                throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                    "鉴权失败(没有充电权限)");
            } else if (!authMediaResult.getStations().contains(cusAuthReqEx.getSiteId())) {
                log.info("鉴权介质不可以在此站点使用, 鉴权失败");
                throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                    "鉴权失败(没有充电权限)");
            }

            Long cardCommercialId = authMediaResult.getCommId();
            // 余额
            BigDecimal cardDenomination = authMediaResult.getFrozenAmount();

            if (null == cardCommercialId) {
                log.info("介质还未绑定商户，通用的账号异常，鉴权失败");
                throw new DcServiceException(DcConstants.KEY_RES_CODE_ACCOUNT_ERROR,
                    "通用的账号异常");
            }

            if (null == cardDenomination || !(cardDenomination.compareTo(BigDecimal.ZERO) > 0)) {
                log.info("介质对应账户无余额可用，鉴权失败. cardDenomination = {}", cardDenomination);
                throw new DcServiceException(DcConstants.KEY_RES_CODE_BALANCE_ERROR,
                    "余额异常(不足)");
            }

            if (authMediaResult.getDefaultPayType() == OrderPayType.MERCHANT.getCode()) {
                log.info("使用商户会员支付");
                // 获取默认扣款账户商户及子商户Id集合
                log.info("pay Account Id={}", authMediaResult.getPayAccountId());
                List<Long> commIdList = commercialRoDs.listSubCommercialIds2(
                    authMediaResult.getCommId());

                // 判断桩是否数据账户可使用的范围
                // 桩代理商Id
                Long evseSubCommId;
                if (cusAuthReqEx.getSiteCommId() == null) {
                    EvseVo deviceDetailVo = evseCacheService.getCachedEvse(
                        cusAuthReqEx.getEvseId());
                    IotAssert.isNotNull(deviceDetailVo,
                        "设备不存在或者状态不是在线EvseNo:" + cusAuthReqEx.getEvseId());
                    log.info("鉴权 deviceDetailVo: {}", deviceDetailVo);
                    evseSubCommId = deviceDetailVo.getSiteCommId();
                } else {
                    evseSubCommId = cusAuthReqEx.getSiteCommId();
                }
                log.info("evse commId={}", evseSubCommId);
                // 商户会员可用商户Id列表
                if (!commIdList.contains(evseSubCommId)) {
                    log.info("鉴权失败, 当前使用的商户会员不能再当前商户使用");
                    throw new DcServiceException(DcConstants.KEY_RES_CODE_AUTH_FAIL,
                        "当前使用的商户会员不能再当前商户使用");
                }
            }

            log.info("鉴权成功。evseId = {}, account = {}, mediaNo:{}",
                cusAuthReqEx.getEvseId(), cusAuthReqEx.getAccountNo(),
                authMediaResult.getMediaNo());

            CusAuthRes ret = new CusAuthRes();
            ret.setDiscountRefId(authMediaResult.getDiscountRefId())
                .setPriceVo(authMediaResult.getPriceVo())
                .setCard(authMediaResult.getCard())
                .setVin(authMediaResult.getVin())
                .setDefaultPayType(authMediaResult.getDefaultPayType())
                .setPayType(authMediaResult.getPayType())
                .setOpenid(authMediaResult.getOpenid())
                .setPostPaid(authMediaResult.getPostPaid())
                .setCorpId(authMediaResult.getCorpId())
                .setPayAccountId(authMediaResult.getPayAccountId())
                .setUserId(authMediaResult.getUserId())
                .setFrozenAmount(cardDenomination)
                .setBalance(authMediaResult.getBalance())
                .setCarNo(authMediaResult.getCarNo());

            if (Boolean.TRUE.equals(cusAuthReqEx.getSave2Redis())) {
                //将鉴权结果存入Redis
                String redisKey = getRedisKey(cusAuthReqEx);
                String value = ret.toJsonString();
                log.info("鉴权结果存入redis. key: {}, value: {}", redisKey, value);
                redisDao.setMinutes(redisKey, value, 3);
            }
            log.info("end");
            return ret;
        });

    }

    //key 为 账号+网关号+桩号+枪号
    //TODO: 最好加上支付类型（Card/VIN），原因是如果支付类型错误也可以获取到上次的缓存数据。
    private String getRedisKey(CusAuthReqEx cusAuthReqEx) {
        return String.format("%s-%s-%s-%s",
            cusAuthReqEx.getAccountNo(),
            cusAuthReqEx.getGwno(),
            cusAuthReqEx.getEvseId(),
            cusAuthReqEx.getPlugId()
        );
    }

    @FunctionalInterface
    interface Function<Uno, Dos> {

        Dos apply(Uno uno);
    }

}
