package com.cdz360.biz.model.tj.kc.vo;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站智能测算信息")
public class TjSurveyCalculationInfoVo {

    @Schema(description = "勘察站的no")
    private String surveyNo;

    @Schema(description = "勘察站充电区域")
    List<TjSurveyChargeAreaVo> tjSurveyChargeAreaVoList;

    @Schema(description = "勘察站标准设施")
    TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo;

    @Schema(description = "勘察站其他设施")
    List<TjSurveySupportingFacilitiesOtherPo> tjSurveySupportingFacilitiesOtherPoList;

    @Schema(description = "勘察站高压信息")
    List<TjSurveyHighVoltagePo> tjSurveyHighVoltagePoList;

    @Schema(description = "场站勘察站运营收入")
    TjSurveyOperationIncomePo tjSurveyOperationIncomePo;

    @Schema(description = "勘察站运营支出")
    TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo;
}

