package com.cdz360.biz.device.business.entity.request;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.common.param.BaseRequest;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 枪头列表查询
 *
 * <AUTHOR>
 * @since 2018年7月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ChargerListRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备序列号
     */
    private String serialNumber;
    /**
     * 设备名称
     */
    private String evseName;
    /**
     * 设备状态
     */
    private Integer deviceStatus;
    /**
     * 站点列表id
     */
    private List<String> siteIds;
    /**
     * 站点id
     */
    private String siteId;
    /**
     * 代理商ID
     */
    private Long commercialId;
    /**
     * 枪头交直流类型
     */
    private Integer currentType;
    /**
     * 二维码
     */
    private String qrCode;

    /**
     * 枪头序号
     */
    private Integer connectorId;

    /**
     * 运营商ID集合
     */
    @Deprecated
    private List<Long> commercialIdList;

    @ApiModelProperty(value = "商户ID链")
    private String commIdChain;

    /**
     * 桩号集合
     */
    private List<String> evseNoList;

    /**
     * 枪头状态
     */
    private List<Integer> plugStatus;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
