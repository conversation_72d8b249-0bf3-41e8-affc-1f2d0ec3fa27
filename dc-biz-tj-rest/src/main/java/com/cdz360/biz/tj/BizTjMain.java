package com.cdz360.biz.tj;

import com.netflix.discovery.EurekaClient;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import reactivefeign.spring.config.EnableReactiveFeignClients;
import reactor.core.publisher.Hooks;


@SpringBootApplication
@EnableReactiveFeignClients(basePackages = {"com.cdz360.biz.utils.feign.*"})
@EnableFeignClients(basePackages = {
    "com.cdz360.biz.utils.feign.*",
    "com.chargerlinkcar.framework.common.feign"
})
@EnableAsync
@Slf4j
@ComponentScan(basePackages = {"com.cdz360.biz.tj",
    "com.cdz360.biz.ds.trading.ro.geo.ds",
    "com.chargerlinkcar.framework.common.service"})
@MapperScan(basePackages = {"com.cdz360.biz.tj.ds.**.mapper", "com.cdz360.biz.ds.trading.ro.geo.mapper"})
@EnableScheduling
public class BizTjMain {

    @Autowired
    private EurekaClient discoveryClient;


    public static void main(String[] args) {
        log.info("starting dc-biz-tj!!!");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
//        SpringApplication.run(DataCoreApplication.class, args);
        new SpringApplicationBuilder(BizTjMain.class).web(WebApplicationType.REACTIVE).run(args);
    }


    @PreDestroy
    public void destroy() {
        log.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        log.info("eureka de-registered.....");
    }
}
