package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取勘察场站列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjSurveyParam extends BaseListParam {

    @Schema(description = "用户ID")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;

    @Schema(description = "场站名称")
    private String siteName;

    @Schema(description = "场站组ID")
    private List<String> gidList;

    @Schema(description = "人员名称，模糊匹配")
    private String userNameLike;

    @ApiModelProperty(value = "初评结果: 符合(1)/不符合(2)")
    @JsonInclude(Include.NON_NULL)
    private Integer score;

    @Schema(description = "投建区域ID列表(t_tj_area.id)-查投建区域内的所有站点")
    @JsonInclude(Include.NON_NULL)
    private Long withinAid;

    @Schema(description = "投建区域ID列表(t_tj_area.id)-查投建区域内的所有站点")
    @JsonInclude(Include.NON_NULL)
    private List<Long> withinAidList;

    @Schema(description = "投建区域分析ID-查询点位辐射区内的场站")
    @JsonInclude(Include.NON_NULL)
    private Long analysisId;

    @Schema(description = "投建区域分析点位编号-查询点位辐射区内的场站")
    @JsonInclude(Include.NON_NULL)
    private Long analysisPointNum;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
