package com.cdz360.biz.ess.model.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户账户操作参数")
@Data
@Accessors(chain = true)
public class EnableAccountParam {

    @Schema(description = "用户ID", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "启用(true); 禁用(false)", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private Boolean enable;
}
