package com.cdz360.biz.model.cus.auth.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class CusAuthRes extends CusAuthResBase {

    /**
     * true：鉴权已通过，作为桩端自启订单来处理（OCPP逻辑）
     * null or false：无需处理
     */
    private Boolean ocppEvseAuto;

    /**
     * @deprecated 使用 payType 替换
     */
    @Deprecated
    private Integer defaultPayType;

    private Long corpId;// 扣款类型为授信账户时才有值
    private Long payAccountId;
    private Long userId;



    @Schema(description = "是否为后付费标识: true/false")
    private Boolean postPaid; // 是否为后付费标识: true/false


    @Schema(description = "优惠ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long discountRefId;

    @Schema(description = "协议价优惠后的电价信息，下发到桩 鉴权用户使用协议价时存在")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargePriceVo priceVo;

    private CardDto card;

    private VinDto vin;

    @Schema(description = "支付渠道")
    @JsonInclude(Include.NON_NULL)
    private PayAccountType payType;

    @Schema(description = "微信/支付宝 openId， 仅用于信用充")
    private UserOpenidPo openid;
}
