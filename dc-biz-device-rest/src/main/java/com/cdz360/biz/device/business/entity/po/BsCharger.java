package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 基础设备枪头数据表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
@Data
@TableName("bs_charger")
public class BsCharger {

    /**
     * 主键ID
     */
    @TableId(value = "bc_id", type = IdType.AUTO)
    private Long bcId;

    /**
     * 桩编号
     */
    @TableField("evseNo")
    private String evseNo;

    /**
     * 设备ID
     */
    @TableField("box_code")
    private String boxCode;
    /**
     * 充电接口序号
     */
    @TableField("connector_id")
    private Integer connectorId;
    /**
     * 枪头名称
     */
    @TableField("charger_name")
    private String chargerName;
    /**
     * 代理商ID
     */
    @TableField("business_id")
    private String businessId;
    /**
     * 站点ID
     */
    @TableField("station_code")
    private String stationCode;
    /**
     * 交直流类型**0-交流 1-直流**
     */
    @TableField("current_type")
    private Integer currentType;
    /**
     * 二维码
     */
    @TableField("qr_code")
    private String qrCode;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 最后修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

}
