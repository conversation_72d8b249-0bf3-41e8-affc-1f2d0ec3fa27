package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 站点列表查询
 *
 * <AUTHOR>
 * @since 2018年7月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SiteListRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    private String siteId;
    /**
     * 运营商ID
     */
    private Long commercialId;
    /**
     * 所属集团商户ID
     */
    private Long maxCommercialId;
    /**
     * 运营商ID
     */
    private List<Long> commercialIdList;
    private String commIdChain;
    /**
     * 省份区划编码
     */
    private Integer provinceCode;
    /**
     * 城市区划编码
     */
    private Integer cityCode;
    /**
     * 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
     */
    private Integer type;
    /**
     * 站点状态
     */
    private Integer status;

    /**
     * 鼎充专用 开票标识
     */
    private Integer invoicedValid;

    /**
     * 站点状态
     */
    @Deprecated
    private List<Integer> statuslist;

    /**
     * 枪头序号
     */
    private Integer connectorId;

    /**
     * 上次查询时间
     */
    private String lastQueryTime;
    /**
     * 站点名称 用于运营支撑-站点管理
     */
    private String siteName;
    /**
     * 地址 用于运营支撑-站点管理
     */
    private String address;
}
