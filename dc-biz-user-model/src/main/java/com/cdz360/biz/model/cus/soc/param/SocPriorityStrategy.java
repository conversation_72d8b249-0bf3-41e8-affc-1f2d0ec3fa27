package com.cdz360.biz.model.cus.soc.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * SocPriorityStrategy
 * 
 * @since 8/10/2020 2:02 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "场站soc限制-优先策略")
public class SocPriorityStrategy {
    @Schema(description = "用户id列表")
    private List<Long> userIdList;
    @Schema(description = "策略模板")
    private SocStrategyDict socStrategyDict;
}