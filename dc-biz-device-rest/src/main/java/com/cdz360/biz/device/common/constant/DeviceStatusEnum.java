package com.cdz360.biz.device.common.constant;

import com.cdz360.base.model.base.type.EvseStatus;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

/**
 * 设备状态枚举
 *
 * <AUTHOR>
 * @date Create on 2018/7/5 23:01
 */
@Getter
public enum DeviceStatusEnum {

    /**
     * 离线
     */
    STATUS_OFFLINE(0),
    /**
     * 在线
     */
    STATUS_ONLINE(100);

    /**
     * 设备状态对应的状态码
     */
    private int code;

    DeviceStatusEnum(int code) {
        this.code = code;
    }

    /**
     * 根据状态码获取对应的枚举
     *
     * @param code
     * @return
     */
    public static DeviceStatusEnum getEnumByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (DeviceStatusEnum statusEnum : DeviceStatusEnum.values()) {
            if (statusEnum.code == code) {
                return statusEnum;
            }
        }
        return null;
    }


    public static DeviceStatusEnum convertFromEvseStatus(EvseStatus evseStatus) {
        List<EvseStatus> onlineStatus = new ArrayList<>();
        onlineStatus.add(EvseStatus.IDLE);
        onlineStatus.add(EvseStatus.BUSY);
        onlineStatus.add(EvseStatus.CONNECT);
        onlineStatus.add(EvseStatus.ERROR);
        if(onlineStatus.contains(evseStatus)) {
            return DeviceStatusEnum.STATUS_ONLINE;
        }
        else {
            return DeviceStatusEnum.STATUS_OFFLINE;
        }
    }
}
