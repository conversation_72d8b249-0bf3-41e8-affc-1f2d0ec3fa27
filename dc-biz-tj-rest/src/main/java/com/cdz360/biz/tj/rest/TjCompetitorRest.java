package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.param.UpdateTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorVo;
import com.cdz360.biz.tj.service.kc.TjCompetitorService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建竞争对手相关操作接口", description = "投建竞争对手相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/competitor")
public class TjCompetitorRest {

    @Autowired
    private TjCompetitorService tjCompetitorService;

    @Operation(summary = "获取投建竞争者")
    @PostMapping("/findTjCompetitor")
    public Mono<ListResponse<TjCompetitorVo>> findTjCompetitor(
        @RequestBody ListTjCompetitorParam param) {
        log.info("获取投建竞争者: {}", param);
        return tjCompetitorService.findTjCompetitor(param);
    }

    @Operation(summary = "新增或编辑竞争者")
    @PostMapping("/saveCompetitor")
    public Mono<ObjectResponse<TjCompetitorVo>> saveCompetitor(
        @RequestBody UpdateTjCompetitorParam param) {
        log.info("新增或编辑竞争者: {}", param);
        return tjCompetitorService.saveCompetitor(param);
    }


    @Operation(summary = "同步竞争者场站信息")
    @GetMapping("/syncCompetitorSite")
    public Mono<BaseResponse> syncCompetitorSite(
        @RequestParam(value = "provinceCode", required = false) String provinceCode,
        @RequestParam(value = "cityCode", required = false) String cityCode) {
        log.info("同步竞争者场站信息: provinceCode = {}, cityCode = {}", provinceCode, cityCode);
        tjCompetitorService.syncCompetitorSite(provinceCode, cityCode);
        return Mono.just(RestUtils.success());
    }

    @Operation(summary = "删除竞争者")
    @GetMapping(value = "/disableCompetitor")
    public Mono<ObjectResponse<TjCompetitorVo>> disableCompetitor(
        @ApiParam("竞争者唯一ID") @RequestParam("competitorId") Long competitorId) {
        log.debug("删除竞争者: {}", competitorId);
        return tjCompetitorService.disableCompetitor(competitorId)
            .map(RestUtils::buildObjectResponse);
    }
}
