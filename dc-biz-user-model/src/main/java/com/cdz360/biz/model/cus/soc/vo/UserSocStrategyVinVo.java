package com.cdz360.biz.model.cus.soc.vo;

import com.cdz360.biz.model.cus.soc.po.UserSocStrategyPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * UserSocStrategyVinVo
 *  获取企业soc策略-VIN
 * @since 1/4/2021 8:56 AM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserSocStrategyVinVo extends UserSocStrategyPo {

    @Schema(description = "车队名称")
    private String carDepart;

    @Schema(description = "车牌号")
    private String carNo;

    @Schema(description = "线路")
    private String lineNum;

    @Schema(description = "车辆自编号")
    private String carNum;

    @Schema(description = "授信手机号")
    private String phone;

    @Schema(description = "vin码")
    private String vin;
}