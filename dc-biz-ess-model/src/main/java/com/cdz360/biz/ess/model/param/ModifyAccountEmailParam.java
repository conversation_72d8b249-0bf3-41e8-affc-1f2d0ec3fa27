package com.cdz360.biz.ess.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户账户邮件变更参数")
@Data
@Accessors(chain = true)
public class ModifyAccountEmailParam {

    @Schema(description = "顶级商户ID", example = "123", hidden = true)
    private Long topCommId;

//    @NotNull
//    @Schema(description = "用户账户类型", requiredMode = RequiredMode.REQUIRED)
//    private UserAccountType type;

    @Email // 目前仅支持email
    @NotBlank
    @Schema(description = "用户账户", requiredMode = RequiredMode.REQUIRED)
    private String account;

    @NotBlank
    @Schema(description = "用户账户密码", requiredMode = RequiredMode.REQUIRED)
    private String passw;

    @Email
    @Schema(description = "新账户(New Account)")
    private String email;

    @Schema(description = "验证码", requiredMode = RequiredMode.REQUIRED)
    @NotBlank
    private String verificationCode;

}
