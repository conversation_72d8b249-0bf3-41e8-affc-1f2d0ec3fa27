package com.cdz360.biz.ess.model.data.vo;

import com.cdz360.biz.ess.model.data.po.EssEquipTimelyPo;
import io.swagger.annotations.ApiModelProperty;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class EssEquipDailyElecVo extends EssEquipTimelyPo {

    @ApiModelProperty(value = "日期")
    @NotNull(message = "date 不能为 null")
    private LocalDate date;
}
