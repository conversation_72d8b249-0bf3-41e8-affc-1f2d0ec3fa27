<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveySupportingFacilitiesOtherRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_OTHER_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="facilitiesCount" jdbcType="INTEGER" property="facilitiesCount"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_OTHER_PO">
    select * from t_tj_survey_supporting_facilities_other where id = #{id} and enable = true
  </select>

  <select id="findTjSurveySupportingFacilitiesOtherBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_OTHER_PO">
    select * from t_tj_survey_supporting_facilities_other where surveyNo = #{surveyNo} and enable = true
    order by id desc
  </select>


</mapper>

