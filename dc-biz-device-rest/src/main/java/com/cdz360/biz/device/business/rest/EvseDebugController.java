package com.cdz360.biz.device.business.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.data.cache.RedisIotReadService;
import com.cdz360.biz.device.business.client.IotEvseClient;
import com.cdz360.biz.device.business.constant.EvseDebugMethod;
import com.cdz360.biz.device.business.entity.request.EvseDebugRequest;
import com.cdz360.biz.device.business.rest.base.BaseController;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;

/**
 * @ClassName： EvseDebugController
 * @Description: 桩Debug操作相关接口
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/11/8 10:07
 */
@Slf4j
@RequestMapping("/api/debug")
@RestController
@ApiOperation(value = "桩Debug操作相关接口")
@Deprecated
public class EvseDebugController extends BaseController {
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private IotEvseClient iotEvseClient;

    private static final String msgPattern = "^[A-Za-z0-9]{0,255}$";
    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @ApiOperation(value = "开启桩debug功能")
    @PostMapping("/evseDebug")
    public BaseResponse evseDebug(@RequestBody EvseDebugRequest request) {
        log.info(">> 开启桩debug功能: request={}", request);
        String evseNo = request.getEvseNo();
        EvseDebugMethod debugMethod = request.getDebugMethod();
        String msg = request.getMsg();

        Assert.notNull(evseNo, "桩号不能为空");
        Assert.notNull(debugMethod, "Debug开关不能为空 ON/OFF");
        Assert.isTrue(StringUtils.isBlank(msg)
                || Pattern.matches(msgPattern, msg), "设备Debug参数格式错误");

        EvseVo evse = redisIotReadService.getEvseRedisCache(evseNo);
        Assert.isTrue(evse != null && EvseStatus.OFFLINE != evse.getStatus(), "设备离线");

        BaseResponse ret = iotEvseClient.evseDebug(evseNo, debugMethod, msg);
        log.info("<< 开启桩debug功能，结果{}", JsonUtils.toJsonString(ret));
        return ret;
    }

    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @ApiOperation(value = "开启桩debug功能")
    @PostMapping("/batchEvseDebug")
    public BaseResponse batchEvseDebug(@RequestBody EvseDebugRequest request) {
        log.info(">> 批量开启桩debug功能: request={}", request);
        List<String> evseNos = request.getEvseNos();
        EvseDebugMethod debugMethod = request.getDebugMethod();
        Assert.isTrue(CollectionUtils.isNotEmpty(evseNos), "桩号列表不能为空");
        Assert.notNull(debugMethod, "Debug开关不能为空 ON/OFF");
        String msg = request.getMsg();

        Assert.isTrue(StringUtils.isBlank(msg)
                || Pattern.matches(msgPattern, msg), "设备Debug参数格式错误");

        List<EvseVo> evseList = redisIotReadService.getEvseList(evseNos);
        Optional<EvseVo> evseVoOpt = evseList.stream()
                .filter(evseVo -> EvseStatus.OFFLINE == evseVo.getStatus())
                .findFirst();
        Assert.isTrue(!evseVoOpt.isPresent(), "所选设备中，包含离线设备");

        evseNos.stream().forEach(evseNo -> {
            BaseResponse ret = iotEvseClient.evseDebug(evseNo, debugMethod, msg);
            log.info("桩号：{}，开启Debug结果：{}", evseNo, ret.getStatus());
        });
        log.info(">> 批量开启桩debug功能结束");
        return new BaseResponse();
    }

}