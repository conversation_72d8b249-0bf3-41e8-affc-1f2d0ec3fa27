package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.common.constant.ConnectorStatusEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 充电接口简单信息
 *
 * <AUTHOR>
 * @date Create on 2018/7/6 14:08
 */
@Data
public class ChargerInfoVo implements Serializable {

    /**
     * 主键ID
     */
    private Long bcId;
    /**
     * 枪头名称
     */
    private String chargerName;
    /**
     * 有效枪头数量
     */
    private Integer chargerNum;
    /**
     * 设备ID,桩
     */
    private String deviceId;
    /**
     * 设备序列号,页面上的电桩编号
     */
    private String serialNumber;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 设备状态
     */
    private Integer deviceStatus;
    /**
     * 充电接口序号
     */
    private Integer connectorId;
    /**
     * 代理商ID
     */
    private String businessId;
    /**
     * 代理商名称
     */
    private String commercialName;
    /**
     * 站点ID
     */
    private String siteId;
    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 交直流类型**0-交流 1-直流**
     */
    private Integer currentType;
    /**
     * 二维码
     */
    private String qrCode;
    /**
     * 备注
     */
    private String remark;
    /**
     * 充电接口状态{@link ConnectorStatusEnum}
     */
    private Integer status;
    /**
     * 最后心跳时间**毫秒时间戳**
     */
    private Long lastHeartbeatTime;
    /**
     * 计费模板ID
     */
    private Long templateId;
    /**
     * 计费模板名称
     */
    private String templateName;
    /**
     * 电流
     */
    @Deprecated
    private BigDecimal current;
    /**
     * 电压
     */
    @Deprecated
    private BigDecimal voltage;
    /**
     * 电量
     */
    @Deprecated
    private BigDecimal electric;
    /**
     * 枪头编号应急用，后期去掉
     */
    @Deprecated
    private String bcCode;

    /**
     * 桩名
     */
    private String evseName;
    /**
     * 电桩系列
     */
    private String productName;
    /**
     * 枪头进行中订单编号
     */
    private String orderNo;
}
