package com.cdz360.biz.ess.model.data.param;

import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.base.param.TimeRange;
import com.cdz360.biz.ess.model.utils.DateUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询当天发电量参数")
@Data
@Accessors(chain = true)
public class DayKwhParam {

    @Schema(description = "商户ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    private List<String> gids;

    @Schema(description = "年 不传参, 默认今年")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer year;

    @Schema(description = "月份 范围: 1 ~ 12")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer month;

    @Schema(description = "查询最近n天的数据", example = "30")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Integer recentDays;

    /**
     * 客户端的系统时间,用于做时区匹配
     */
    private LocalDateTime time;

    @Schema(description = "时间范围")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter date;

    @Schema(description = "根据采样粒度进行赋值")
    @JsonInclude(Include.NON_NULL)
    private TimeRange range;

    public DayKwhParam initDate() {
        if (null == date) {
            date = new TimeFilter();
        }

        if (null != range) {
            if (null != range.getFrom()) {
                date.setStartTime(DateUtils.timeToDate(range.getFrom()));
            }

            if (null != range.getTo()) {
                date.setEndTime(DateUtils.timeToDate(range.getTo()));
            }
        }

        return this;
    }
}
