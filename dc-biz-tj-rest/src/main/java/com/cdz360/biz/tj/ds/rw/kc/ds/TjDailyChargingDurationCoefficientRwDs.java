package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationCoefficientRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDailyChargingDurationCoefficientRwDs {


    @Autowired

    private TjDailyChargingDurationCoefficientRwMapper tjDailyChargingDurationCoefficientRwMapper;


    public TjDailyChargingDurationCoefficientPo getById(Long id, boolean lock) {
        return this.tjDailyChargingDurationCoefficientRwMapper.getById(id, lock);
    }

    public boolean updateTjDailyChargingDurationCoefficient(TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
        return this.tjDailyChargingDurationCoefficientRwMapper.updateTjDailyChargingDurationCoefficient(tjDailyChargingDurationCoefficientPo) > 0;
    }

}

