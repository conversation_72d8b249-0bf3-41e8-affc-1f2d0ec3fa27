package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveySupportingFacilitiesOtherRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveySupportingFacilitiesRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveySupportingFacilitiesRwDs {


    @Autowired

    private TjSurveySupportingFacilitiesRwMapper tjSurveySupportingFacilitiesRwMapper;


    public TjSurveySupportingFacilitiesPo getById(Long id, boolean lock) {

        return this.tjSurveySupportingFacilitiesRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveySupportingFacilities(TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        return this.tjSurveySupportingFacilitiesRwMapper.insertTjSurveySupportingFacilities(tjSurveySupportingFacilitiesPo) > 0;
    }


    public boolean updateTjSurveySupportingFacilities(TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        return this.tjSurveySupportingFacilitiesRwMapper.updateTjSurveySupportingFacilities(tjSurveySupportingFacilitiesPo) > 0;
    }

    public boolean disableTjSurveySupportingFacilitiesBySurveyNo(String surveyNo) {
        return this.tjSurveySupportingFacilitiesRwMapper.disableTjSurveySupportingFacilitiesBySurveyNo(surveyNo) > 0;
    }

}

