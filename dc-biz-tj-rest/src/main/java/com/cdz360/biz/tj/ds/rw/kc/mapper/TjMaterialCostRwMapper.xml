<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjMaterialCostRwMapper">

  <resultMap id="RESULT_TJ_MATERIAL_COST_PO" type="com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="childType" jdbcType="INTEGER" property="childType"/>
    <result column="capacity" jdbcType="INTEGER" property="capacity"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="installationFee" jdbcType="DECIMAL" property="installationFee"/>
    <result column="grandchildType" jdbcType="INTEGER" property="grandchildType"/>
    <result column="evseModelId" jdbcType="BIGINT" property="evseModelId"/>
    <result column="power" jdbcType="INTEGER" property="power"/>
    <result column="terminal" jdbcType="INTEGER" property="terminal"/>
    <result column="evseBaseFee" jdbcType="DECIMAL" property="evseBaseFee"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_MATERIAL_COST_PO">
    select * from t_tj_material_cost where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjMaterialCost"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo">
    insert into t_tj_material_cost (`type`,
    <if test="null != childType">
      `childType`,
    </if>
    <if test="null != capacity">
      `capacity`,
    </if>
    <if test="null != price">
      `price`,
    </if>
    <if test="null != installationFee">
      `installationFee`,
    </if>
    <if test="null != grandchildType">
      `grandchildType`,
    </if>
    <if test="null != evseModelId">
      `evseModelId`,
    </if>
    <if test="null != power">
      `power`,
    </if>
    <if test="null != terminal">
      `terminal`,
    </if>
    <if test="null != evseBaseFee">
      `evseBaseFee`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{type},
    <if test="null != childType">
      #{childType},
    </if>
    <if test="null != capacity">
      #{capacity},
    </if>
    <if test="null != price">
      #{price},
    </if>
    <if test="null != installationFee">
      #{installationFee},
    </if>
    <if test="null != grandchildType">
      #{grandchildType},
    </if>
    <if test="null != evseModelId">
      #{evseModelId},
    </if>
    <if test="null != power">
      #{power},
    </if>
    <if test="null != terminal">
      #{terminal},
    </if>
    <if test="null != evseBaseFee">
      #{evseBaseFee},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjMaterialCost" parameterType="com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo">
    update t_tj_material_cost set
    <if test="type != null">
      type = #{type},
    </if>
    <if test="childType != null">
      childType = #{childType},
    </if>
    <if test="capacity != null">
      capacity = #{capacity},
    </if>
    <if test="price != null">
      price = #{price},
    </if>
    <if test="installationFee != null">
      installationFee = #{installationFee},
    </if>
    <if test="grandchildType != null">
      grandchildType = #{grandchildType},
    </if>
    <if test="evseModelId != null">
      evseModelId = #{evseModelId},
    </if>
    power = #{power},
    terminal = #{terminal},
    <if test="evseBaseFee != null">
      evseBaseFee = #{evseBaseFee},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjMaterialCost">
    update t_tj_material_cost set
    enable = false, updateTime = now()
    where id = #{id}
  </update>


</mapper>

