package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "物料成本类型")
@Getter
public enum MaterialCostType implements DcEnum {

    UNKNOWN(999, "未知"),

    BOX_TRANSFORMATION(1, "箱变"),
    CABLE(2, "电缆"),
    EVSE(3, "充电桩"),
    STATION_SUPPORTING_FACILITIES(4, "场站配套设施"),
    OTHER(5, "其他"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return MaterialCostType.UNKNOWN;
    }

}
