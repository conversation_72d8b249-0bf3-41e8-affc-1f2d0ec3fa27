package com.cdz360.biz.ds.trading.ro.site.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.ds.trading.ro.site.mapper.BsBoxSettingMapper;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class BsBoxSettingRoDs {

    @Autowired
    private BsBoxSettingMapper bsBoxSettingMapper;

    public BsBoxSettingPo getOneByEvseNo(String evseNo) {
        return bsBoxSettingMapper.getOneByEvseNo(evseNo);
    }

    public List<BsBoxSettingPo> getByEvseNo(List<String> evseNoList, List<Integer> statusList, boolean lock) {
        return bsBoxSettingMapper.getByEvseNo(evseNoList, statusList, lock);
    }

    @Transactional(readOnly = true)
    public List<PriceSchemeSiteVo> getSchemeSiteUsed(ListPriceSchemeSiteUseParam param) {
        return bsBoxSettingMapper.getSchemeSiteUsed(param);
    }

    public int insertOrUpdate(BsBoxSettingPo po) {
        return bsBoxSettingMapper.insertOrUpdate(po);
    }

    public int batchUpdate(List<BsBoxSettingPo> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return 0;
        }

        return bsBoxSettingMapper.batchUpdate(collect);
    }

    public int batchInsert(List<BsBoxSettingPo> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return 0;
        }

        return bsBoxSettingMapper.batchInsert(collect);
    }

    public int delBoxSetting(String evseNo) {

        return bsBoxSettingMapper.delBoxSetting(evseNo);
    }
}
