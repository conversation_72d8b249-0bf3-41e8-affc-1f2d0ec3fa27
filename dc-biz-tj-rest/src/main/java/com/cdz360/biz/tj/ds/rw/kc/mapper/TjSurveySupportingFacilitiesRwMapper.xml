<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveySupportingFacilitiesRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="carParkType" jdbcType="INTEGER" property="carParkType"/>
    <result column="carGearNeed" jdbcType="BOOLEAN" property="carGearNeed"/>
    <result column="intelligentLockNeed" jdbcType="BOOLEAN" property="intelligentLockNeed"/>
    <result column="epoxyFlooringNeed" jdbcType="BOOLEAN" property="epoxyFlooringNeed"/>
    <result column="groundHardeningNeed" jdbcType="BOOLEAN" property="groundHardeningNeed"/>
    <result column="fireProtectionSystemNeed" jdbcType="BOOLEAN" property="fireProtectionSystemNeed"/>
    <result column="lightingType" jdbcType="INTEGER" property="lightingType"/>
    <result column="monitoringSystemNeed" jdbcType="BOOLEAN" property="monitoringSystemNeed"/>
    <result column="canopyNeed" jdbcType="BOOLEAN" property="canopyNeed"/>
    <result column="barrierGateType" jdbcType="INTEGER" property="barrierGateType"/>
    <result column="brandImageType" jdbcType="INTEGER" property="brandImageType"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_PO">
    select * from t_tj_survey_supporting_facilities where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveySupportingFacilities"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo">
    insert into t_tj_survey_supporting_facilities (`surveyNo`,
    <if test="null != carParkType">
      `carParkType`,
    </if>
    <if test="null != carGearNeed">
      `carGearNeed`,
    </if>
    <if test="null != intelligentLockNeed">
      `intelligentLockNeed`,
    </if>
    <if test="null != epoxyFlooringNeed">
      `epoxyFlooringNeed`,
    </if>
    <if test="null != groundHardeningNeed">
      `groundHardeningNeed`,
    </if>
    <if test="null != fireProtectionSystemNeed">
      `fireProtectionSystemNeed`,
    </if>
    <if test="null != lightingType">
      `lightingType`,
    </if>
    <if test="null != monitoringSystemNeed">
      `monitoringSystemNeed`,
    </if>
    <if test="null != canopyNeed">
      `canopyNeed`,
    </if>
    <if test="null != barrierGateType">
      `barrierGateType`,
    </if>
    <if test="null != brandImageType">
      `brandImageType`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != carParkType">
      #{carParkType},
    </if>
    <if test="null != carGearNeed">
      #{carGearNeed},
    </if>
    <if test="null != intelligentLockNeed">
      #{intelligentLockNeed},
    </if>
    <if test="null != epoxyFlooringNeed">
      #{epoxyFlooringNeed},
    </if>
    <if test="null != groundHardeningNeed">
      #{groundHardeningNeed},
    </if>
    <if test="null != fireProtectionSystemNeed">
      #{fireProtectionSystemNeed},
    </if>
    <if test="null != lightingType">
      #{lightingType},
    </if>
    <if test="null != monitoringSystemNeed">
      #{monitoringSystemNeed},
    </if>
    <if test="null != canopyNeed">
      #{canopyNeed},
    </if>
    <if test="null != barrierGateType">
      #{barrierGateType},
    </if>
    <if test="null != brandImageType">
      #{brandImageType},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveySupportingFacilities" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo">
    update t_tj_survey_supporting_facilities set
    <if test="carParkType != null">
      carParkType = #{carParkType},
    </if>
    <if test="carGearNeed != null">
      carGearNeed = #{carGearNeed},
    </if>
    <if test="intelligentLockNeed != null">
      intelligentLockNeed = #{intelligentLockNeed},
    </if>
    <if test="epoxyFlooringNeed != null">
      epoxyFlooringNeed = #{epoxyFlooringNeed},
    </if>
    <if test="groundHardeningNeed != null">
      groundHardeningNeed = #{groundHardeningNeed},
    </if>
    <if test="fireProtectionSystemNeed != null">
      fireProtectionSystemNeed = #{fireProtectionSystemNeed},
    </if>
    <if test="lightingType != null">
      lightingType = #{lightingType},
    </if>
    <if test="monitoringSystemNeed != null">
      monitoringSystemNeed = #{monitoringSystemNeed},
    </if>
    <if test="canopyNeed != null">
      canopyNeed = #{canopyNeed},
    </if>
    <if test="barrierGateType != null">
      barrierGateType = #{barrierGateType},
    </if>
    <if test="brandImageType != null">
      brandImageType = #{brandImageType},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveySupportingFacilitiesBySurveyNo">
    update t_tj_survey_supporting_facilities set
    enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>

</mapper>

