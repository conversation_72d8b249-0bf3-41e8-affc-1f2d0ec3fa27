package com.cdz360.biz.ess.model.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "统计用户设备数量")
@Data
@Accessors(chain = true)
public class CountUserEssParam {

    @Schema(description = "用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> uidList;
}
