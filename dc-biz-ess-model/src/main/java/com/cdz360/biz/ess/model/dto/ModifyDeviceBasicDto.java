package com.cdz360.biz.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新设备基础信息参数")
@Data
@Accessors(chain = true)
public class ModifyDeviceBasicDto {

    @Schema(description = "设备编号", hidden = true)
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备名称")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

    @Schema(description = "设备所在时区", example = "GMT+08:00/UTC+08:00")
    private String timeZone;

    // basic setting
//    @Schema(description = "设备组网类型")
//    @JsonInclude(Include.NON_NULL)
//    private Integer plantType;
//
//    @Schema(description = "旧平台授权码")
//    @JsonInclude(Include.NON_EMPTY)
//    private String oldAuthCode;
//
//    @Schema(description = "新设置的平台授权码")
//    @JsonInclude(Include.NON_EMPTY)
//    private String newAuthCode;

    @Schema(description = "入网价格(后续逻辑没有想好)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal gridPrice;
}
