package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.model.base.dto.ListResponse;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Classname ListResponseEvseList
 * @Description TODO
 * @Date 7/17/2019 11:30 AM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ListResponseEvseList<T> extends ListResponse {

    public ListResponseEvseList() {
        super();
    }

    public ListResponseEvseList(List<T> data) {
        super(data);
    }

    public ListResponseEvseList(List<T> data, Long total) {
        super(data, total);
    }

    private long onlineCount = 0;
    private long offlineCount = 0;
    private Integer currentCardStatus;
}