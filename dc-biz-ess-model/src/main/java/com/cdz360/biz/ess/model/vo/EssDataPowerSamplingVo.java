package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "储能设备数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssDataPowerSamplingVo extends BaseDataSampling {

    public EssDataPowerSamplingVo(Long time) {
        setTime(time);
    }

    public EssDataPowerSamplingVo(LocalDateTime time) {
        setTime((long) (time.getHour() * 60 + time.getMinute()));
    }

    @Schema(description = "光伏功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pvPower;

    @Schema(description = "电池功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batPower;

    @Schema(description = "当前SOC(%)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    @JsonIgnore
    @Schema(description = "电池SOC列表(采集后用于二次处理)")
    private List<BigDecimal> socList;

    @Schema(description = "电网A相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal gridAActivePower;

    @Schema(description = "电网B相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal gridBActivePower;

    @Schema(description = "电网C相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal gridCActivePower;

    @Schema(description = "负载A相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadAActivePower;

    @Schema(description = "负载B相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadBActivePower;

    @Schema(description = "负载C相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadCActivePower;

    @Schema(description = "逆变器A相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal invAActivePower;

    @Schema(description = "逆变器B相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal invBActivePower;

    @Schema(description = "逆变器C相有功功率, 单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal invCActivePower;

}
