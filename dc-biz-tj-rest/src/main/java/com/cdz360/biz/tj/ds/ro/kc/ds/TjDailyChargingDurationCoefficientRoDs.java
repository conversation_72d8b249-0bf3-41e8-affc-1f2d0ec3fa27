package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjDailyChargingDurationCoefficientRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjDailyChargingDurationRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDailyChargingDurationCoefficientRoDs {


    @Autowired

    private TjDailyChargingDurationCoefficientRoMapper tjDailyChargingDurationCoefficientRoMapper;


    public TjDailyChargingDurationCoefficientPo getById(Long id) {

        return this.tjDailyChargingDurationCoefficientRoMapper.getById(id);

    }

    public TjDailyChargingDurationCoefficientPo findTjDailyChargingDurationCoefficient() {
        return this.tjDailyChargingDurationCoefficientRoMapper.findTjDailyChargingDurationCoefficient();
    }

}

