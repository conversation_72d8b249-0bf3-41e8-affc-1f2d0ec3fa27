package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.business.constant.SiteStatusEnum;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 站点简单信息
 *
 * <AUTHOR>
 * @date Create on 2018/8/6 10:36
 */
@Data
public class SiteSimpleInfoVo {

    /**
     * 站点id
     */
    private String siteId;
    /**
     * 站点名称
     */
    private String siteName;
    /**
     * 站点经度
     */
    private BigDecimal longitude;
    /**
     * 站点纬度
     */
    private BigDecimal latitude;
    /**
     * 站点地址
     */
    private String address;
    /**
     * 站点自定义编号
     */
    private String siteNo;

    /**
     * 充电冻结金额
     */
    private BigDecimal frozenAmount;
    /**
     * 省份编码
     */
    private Integer province;
    /**
     * 省份名称
     */
    private String provinceName;
    /**
     * 城市编码
     */
    private Integer city;
    /**
     * 城市名称
     */
    private String cityName;
    /**
     * 站点状态 {@link SiteStatusEnum}
     */
    private Integer status;
    /**
     * 顶级商户ID
     */
    private Long topCommId;
    /**
     * 运营商ID
     */
    private Long operateId;
    /**
     * 运营商全称
     */
    private String operateName;
    /**
     * 运营商简称
     */
    private String operateShortName;
    /**
     * 所属集团商户ID
     */
    private Long maxCommercialId;
    /**
     * 所属集团商户名称
     */
    private String maxCommercialName;
    /**
     * 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
     */
    private Integer type;
    /**
     * 工作日服务时间
     */
    private String serviceWorkdayTime;
    /**
     * 节假日服务时间
     */
    private String serviceHolidayTime;
    /**
     * 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
     */
    private Integer scope;
    /**
     * 上线时间
     */
    private Date onlineDate;
    /**
     * 充电桩总数
     */
    private Integer countOfBox;
    /**
     * 枪头总数
     */
    private Integer countOfCharger;
    /**
     * 收费说明
     */
    private String feeDescription;
    /**
     * 站点收费范围 最低
     */
    private Integer feeMin;
    /**
     * 站点收费范围 最高
     */
    private Integer feeMax;

    /**
     * 鼎充专用 开票标识
     */
    private Integer invoicedValid;

    /**
     * 站点默认计费模板Id
     */
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    private String templateName;

    /**
     * 站点手机号
     */
    private String contactsPhone;

    /**
     * 服务号码
     */
    private String phone;

    /**
     * 图片
     */
    private String images;
    /**
     * 场站下的紧急卡数
     */
    private Integer urgencyCardNum;
    /**
     * 默认扣款类型
     */
    private Integer defaultPayType;
    /**
     * 扣款账户ID
     */
    private Long payAccountId;
    /**
     * 运营属性 0, 未知; 1, 自营; 2, 非自营
     */
    private Integer bizType;

    /**
     * 运营方名称
     */
    private String bizName;
}
