package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.cdz360.biz.tj.service.kc.TjAnalysisService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建竞争对手相关操作接口", description = "投建竞争对手相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/area/analysis")
public class TjAnalysisRest {

    @Autowired
    private TjAnalysisService tjAnalysisService;

    @Operation(summary = "获取投建分析")
    @PostMapping("/findTjAnalysis")
    public Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(
        @RequestBody ListTjAreaAnalysisParam param) {
        log.info("获取投建分析: {}", param);
        return tjAnalysisService.findTjAnalysis(param);
    }

    @Operation(summary = "通过ID获取投建分析")
    @GetMapping(value = "/getTjAnalysisById")
    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> getTjAnalysisById(
        @ApiParam("投建分析唯一ID") @RequestParam("analysisId") Long analysisId) {
        log.debug("通过ID获取投建分析: {}", analysisId);
        return tjAnalysisService.getTjAnalysisById(analysisId)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新增或编辑投建分析")
    @PostMapping("/saveAnalysis")
    public Mono<ObjectResponse<TjAreaAnalysisWithPointVo>> saveAnalysis(
        @RequestBody TjAreaAnalysisWithPointVo param) {
        log.info("新增或编辑投建分析: {}", param);
        return tjAnalysisService.saveAnalysis(param)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除投建分析")
    @GetMapping(value = "/disableAnalysis")
    public Mono<ObjectResponse<TjAreaAnalysisVo>> disableAnalysis(
        @ApiParam("投建分析唯一ID") @RequestParam("analysisId") Long analysisId) {
        log.debug("删除投建分析: {}", analysisId);
        return tjAnalysisService.disableTjAreaAnalysis(analysisId)
            .map(RestUtils::buildObjectResponse);
    }
}
