package com.cdz360.biz.model.cus.corp.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.corp.type.CorpType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListCorpParam extends BaseListParam {

    private List<Long> idList;
    private Long topCommId;
    private Long commId;
    @Deprecated
    private List<Long> commIdList;
    private String commIdChain;

    @Schema(description = "企业名称 模糊查询")
    private String corpName;

    @Schema(description = "企业类型")
    private CorpType corpType;

    @Schema(description = "企业客户用户表中的ID")
    private List<Long> uidList;

    private Boolean enable;

    private Long settlementType;

    private String invoiceWay;

    @Schema(description = "引流人")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String referrer;

}
