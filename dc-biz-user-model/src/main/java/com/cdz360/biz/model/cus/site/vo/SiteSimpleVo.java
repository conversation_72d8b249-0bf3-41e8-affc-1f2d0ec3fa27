package com.cdz360.biz.model.cus.site.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站简易信息")
public class SiteSimpleVo {
    @Schema(description = "场站所属商户的ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站所属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;
}
