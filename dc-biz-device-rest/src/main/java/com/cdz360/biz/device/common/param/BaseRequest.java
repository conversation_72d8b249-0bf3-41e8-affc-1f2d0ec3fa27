package com.cdz360.biz.device.common.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.SortParam;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;
import java.util.List;

/**
 * 从东正未提供代码的jar包里反编译出来的代码, 不确定代码质量. 仅暂时使用, 未来全部替换
 */
public class BaseRequest implements Serializable {
    @ApiModelProperty("页码")
    private Integer page;
    @ApiModelProperty("每页记录数")
    private Integer rows;
    @ApiModelProperty("关键子")
    private String keywords;
    @ApiModelProperty("删除标志-0：正常，1：删除")
    private Integer deleteFlag;
    @ApiModelProperty(value = "排序方式")
    private List<SortParam> sorts;

    public BaseRequest() {
    }

    public Integer getPage() {
        return this.page == null ? 1 : this.page;
    }

    public void setPage(final Integer page) {
        this.page = page;
    }

    public Integer getRows() {
        if (this.rows == null) {
            this.rows = 10;
        }

        if (this.rows > 2000) {
            throw new DcArgumentException("每页记录总数超过最大阈值");
        } else {
            return this.rows;
        }
    }

    public void setRows(final Integer rows) {
        this.rows = rows;
    }

    public String getKeywords() {
        return this.keywords;
    }

    public void setKeywords(final String keywords) {
        this.keywords = keywords;
    }

    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    public void setDeleteFlag(final Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    public boolean equals(final Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof BaseRequest)) {
            return false;
        } else {
            BaseRequest other = (BaseRequest)o;
            if (!other.canEqual(this)) {
                return false;
            } else {
                label59: {
                    Object this$page = this.getPage();
                    Object other$page = other.getPage();
                    if (this$page == null) {
                        if (other$page == null) {
                            break label59;
                        }
                    } else if (this$page.equals(other$page)) {
                        break label59;
                    }

                    return false;
                }

                Object this$rows = this.getRows();
                Object other$rows = other.getRows();
                if (this$rows == null) {
                    if (other$rows != null) {
                        return false;
                    }
                } else if (!this$rows.equals(other$rows)) {
                    return false;
                }

                Object this$keywords = this.getKeywords();
                Object other$keywords = other.getKeywords();
                if (this$keywords == null) {
                    if (other$keywords != null) {
                        return false;
                    }
                } else if (!this$keywords.equals(other$keywords)) {
                    return false;
                }

                Object this$deleteFlag = this.getDeleteFlag();
                Object other$deleteFlag = other.getDeleteFlag();
                if (this$deleteFlag == null) {
                    return other$deleteFlag == null;
                } else return this$deleteFlag.equals(other$deleteFlag);

            }
        }
    }

    protected boolean canEqual(final Object other) {
        return other instanceof BaseRequest;
    }

    public int hashCode() {

        int result = 1;
        Object $page = this.getPage();
        result = result * 59 + ($page == null ? 43 : $page.hashCode());
        Object $rows = this.getRows();
        result = result * 59 + ($rows == null ? 43 : $rows.hashCode());
        Object $keywords = this.getKeywords();
        result = result * 59 + ($keywords == null ? 43 : $keywords.hashCode());
        Object $deleteFlag = this.getDeleteFlag();
        result = result * 59 + ($deleteFlag == null ? 43 : $deleteFlag.hashCode());
        return result;
    }

    public List<SortParam> getSorts() {
        return sorts;
    }

    public void setSorts(List<SortParam> sorts) {
        this.sorts = sorts;
    }

    public String toString() {
        return "BaseRequest(page=" + this.getPage() + ", rows=" + this.getRows() + ", keywords=" + this.getKeywords() + ", deleteFlag=" + this.getDeleteFlag() + ")";
    }
}

