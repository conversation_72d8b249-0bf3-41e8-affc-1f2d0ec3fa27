package com.cdz360.biz.ess.model.data.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能统计数据")
@Data
@Accessors(chain = true)
public class EssDataBi {

    @Schema(description = "收益 可为负值", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal profit;

    @Schema(description = "总放电电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(description = "总放电收入，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal outFee;

    @Schema(description = "总充电电量", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(description = "总充电支出，单位: 元", example = "123.4")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal inFee;
}
