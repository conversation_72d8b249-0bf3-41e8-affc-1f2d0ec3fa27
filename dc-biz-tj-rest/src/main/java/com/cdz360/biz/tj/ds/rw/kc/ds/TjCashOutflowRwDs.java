package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjCashOutflowRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjCashOutflowRwDs {


    @Autowired

    private TjCashOutflowRwMapper tjCashOutflowRwMapper;


    public TjCashOutflowPo getById(Long id, boolean lock) {

        return this.tjCashOutflowRwMapper.getById(id, lock);

    }


    public boolean insertTjCashOutflow(TjCashOutflowPo tjCashOutflowPo) {
        return this.tjCashOutflowRwMapper.insertTjCashOutflow(tjCashOutflowPo) > 0;
    }


    public boolean updateTjCashOutflow(TjCashOutflowPo tjCashOutflowPo) {
        return this.tjCashOutflowRwMapper.updateTjCashOutflow(tjCashOutflowPo) > 0;
    }

    public boolean disableTjCashOutflow(Long id) {
        return this.tjCashOutflowRwMapper.disableTjCashOutflow(id) > 0;
    }

}

