package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.business.constant.TariffTagEnum;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 计费模板**子模板**
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Data
@TableName("t_sub_template")
public class SubTemplate {

    private static final long serialVersionUID = 1L;

    /**
     * 子模板ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 主模板ID
     */
    @TableField("template_id")
    private Long templateId;
    /**
     * 主模板编号**唯一标识**
     */
    private String code;

    //时段编号
    private Integer num;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 开始功率**单位：瓦**
     */
    @TableField("start_power")
    private Integer startPower;
    /**
     * 结束功率**单位：瓦**
     */
    @TableField("stop_power")
    private Integer stopPower;
    /**
     * 开始时间**单位：分钟**
     */
    @TableField("start_time")
    private Integer startTime;
    /**
     * 结束时间**单位：分钟**
     */
    @TableField("stop_time")
    private Integer stopTime;
    /**
     * 充电费价格-分子**单位：元**
     */
    private BigDecimal price;
    /**
     * 充电费单位-分母**分钟或度数**
     */
    private Integer scale;
    /**
     * 服务费价格-分子**单位：元**
     */
    @TableField("service_price")
    private BigDecimal servicePrice;
    /**
     * 服务费单位-分母**分钟或度数**
     */
    @TableField("service_scale")
    private Integer serviceScale;

    /**
     * "尖"、"峰"、"平"、"谷"标签{@link TariffTagEnum}
     */
    @TableField("tariff_tag")
    private Integer tariffTag;

    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
