package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "道闸类型")
@Getter
public enum BarrierGateType implements DcEnum {


    UNKNOWN(999, "未知"),

    BARRIER_GATE_SYSTEM(1, "道闸系统对接"),
    BARRIER_GATE_SYSTEM_EQUIPMENT(2, "道闸系统"),
    BARRIER_GATE_NO(3, "不需要"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    BarrierGateType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static BarrierGateType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (BarrierGateType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return BarrierGateType.UNKNOWN;
    }

}
