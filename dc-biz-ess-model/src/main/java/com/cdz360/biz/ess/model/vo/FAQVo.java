package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "常见问题")
@Data
@Accessors(chain = true)
public class FAQVo {

    @Schema(description = "常见问题ID")
    @JsonInclude(Include.NON_NULL)
    private Long id;

    @Schema(description = "等级")
    @JsonInclude(Include.NON_NULL)
    private Long level;

    @Schema(description = "常见问题摘要信息")
    @JsonInclude(Include.NON_EMPTY)
    private String summary;

    @Schema(description = "处理描述")
    @JsonInclude(Include.NON_EMPTY)
    private String opDescription;
}
