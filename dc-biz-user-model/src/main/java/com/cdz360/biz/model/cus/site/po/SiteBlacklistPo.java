package com.cdz360.biz.model.cus.site.po;

import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.biz.model.cus.site.type.BlacklistFromType;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "场站黑名单")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteBlacklistPo extends BaseObject {
    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "站点编号")
    private String siteId;

    @Schema(description = "用户ID")
    private Long uid;

    @Schema(description = "停充超时次数")
    private Integer overtimeParkingNum;

    @Schema(description = "移出黑名单时的超停次数")
    private Integer overtimeParkingNumReset;

    @Schema(description = "true,在黑名单; false,不在黑名单")
    private Boolean enable;

    @Schema(description = "企业id")
    private Long corpId;

    @Schema(description = "普通客户手机号")
    private String phone;

    @Schema(description = "商户会员")
    private Long commId;

    @Schema(description = "拉黑原因：手动新增、停充超时")
    private BlacklistFromType from;

    @Schema(description = "最近订单")
    private String latestOrderNo;

    @Schema(description = "订单状态:0未支付，100已支付")
    private Integer overtimeOrderStatus;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;
}
