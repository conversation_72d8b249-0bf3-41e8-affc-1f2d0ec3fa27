package com.cdz360.biz.model.tj.kc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "竞争对手场站")
public class TjCompetitorSitePo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "竞争对手ID(t_tj_competitor.id)")
    @NotNull(message = "competitorId 不能为 null")
    private Long competitorId;

    @Schema(description = "高德ID")
    @NotNull(message = "gdId 不能为 null")
    @Size(max = 32, message = "gdId 长度不能超过 32")
    private String gdId;

    @Schema(description = "场站名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 128, message = "name 长度不能超过 128")
    private String name;

    @Schema(description = "省行政编码")
    @NotNull(message = "pCode 不能为 null")
    private String pCode;

    @Schema(description = "省名")
    @NotNull(message = "pName 不能为 null")
    @Size(max = 8, message = "pName 长度不能超过 8")
    private String pName;

    @Schema(description = "市级行政编码")
    @NotNull(message = "cityCode 不能为 null")
    private String cityCode;

    @Schema(description = "城市名称")
    @NotNull(message = "cityName 不能为 null")
    @Size(max = 8, message = "cityName 长度不能超过 8")
    private String cityName;

    @Schema(description = "区县行政编码")
    @NotNull(message = "adCode 不能为 null")
    private String adCode;

    @Schema(description = "区县名称")
    @NotNull(message = "adName 不能为 null")
    @Size(max = 16, message = "adName 长度不能超过 16")
    private String adName;

    @Schema(description = "站点地址")
    @NotNull(message = "address 不能为 null")
    @Size(max = 512, message = "address 长度不能超过 512")
    private String address;

    @Schema(description = "经纬度")
    @NotNull(message = "location 不能为 null")
    private String location;

    @Schema(description = "直流桩数量")
    private Integer dcEvseNum;

    @Schema(description = "直流枪头数量")
    private Integer dcPlugNum;

    @Schema(description = "交流桩数量")
    private Integer acEvseNum;

    @Schema(description = "交流枪头数量")
    private Integer acPlugNum;

    @Schema(description = "总功率,单位kW")
    private BigDecimal power;

    @Schema(description = "最小电费单价，单位：元")
    private BigDecimal minElecFee;

    @Schema(description = "最大电费单价，单位：元")
    private BigDecimal maxElecFee;

    @Schema(description = "最小服务费单价，单位：元")
    private BigDecimal minServFee;

    @Schema(description = "最大服务费单价，单位：元")
    private BigDecimal maxServFee;

    @Schema(description = "附加信息更新时间")
    private Date attachUpdateTime;

    @Schema(description = "创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @Schema(description = "更新时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

}

