package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyChargeAreaPileRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyChargeAreaPileRwDs {


    @Autowired

    private TjSurveyChargeAreaPileRwMapper tjSurveyChargeAreaPileRwMapper;


    public TjSurveyChargeAreaPilePo getById(Long id, boolean lock) {

        return this.tjSurveyChargeAreaPileRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveyChargeAreaPile(TjSurveyChargeAreaPilePo tjSurveyChargeAreaPilePo) {
        return this.tjSurveyChargeAreaPileRwMapper.insertTjSurveyChargeAreaPile(tjSurveyChargeAreaPilePo) > 0;
    }


    public boolean updateTjSurveyChargeAreaPile(TjSurveyChargeAreaPilePo tjSurveyChargeAreaPilePo) {
        return this.tjSurveyChargeAreaPileRwMapper.updateTjSurveyChargeAreaPile(tjSurveyChargeAreaPilePo) > 0;
    }

    public boolean disableTjSurveyChargeAreaPileByAreaId(Long areaId) {
        return this.tjSurveyChargeAreaPileRwMapper.disableTjSurveyChargeAreaPileByAreaId(areaId) > 0;
    }

    public boolean disableTjSurveyChargeAreaPileBySurveyNo(String surveyNo) {
        return this.tjSurveyChargeAreaPileRwMapper.disableTjSurveyChargeAreaPileBySurveyNo(surveyNo) > 0;
    }

}

