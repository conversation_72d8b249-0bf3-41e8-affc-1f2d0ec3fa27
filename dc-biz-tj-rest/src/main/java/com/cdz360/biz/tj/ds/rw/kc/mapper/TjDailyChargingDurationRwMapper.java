package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjDailyChargingDurationRwMapper {

    TjDailyChargingDurationPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjDailyChargingDuration(TjDailyChargingDurationPo tjDailyChargingDurationPo);

    int updateTjDailyChargingDuration(TjDailyChargingDurationPo tjDailyChargingDurationPo);

    int disableTjDailyChargingDuration(@Param("id") Long id);

}

