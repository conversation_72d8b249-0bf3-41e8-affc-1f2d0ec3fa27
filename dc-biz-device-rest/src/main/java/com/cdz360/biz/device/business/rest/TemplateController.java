package com.cdz360.biz.device.business.rest;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.client.MerchantFeignClient;
import com.cdz360.biz.device.business.entity.po.Commercial;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingListRequest;
import com.cdz360.biz.device.business.entity.request.TemplateListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo;
import com.cdz360.biz.device.business.entity.result.SendTemplateResultVo;
import com.cdz360.biz.device.business.entity.result.TemplateFullInfoVo;
import com.cdz360.biz.device.business.entity.result.TemplateInfoVo;
import com.cdz360.biz.device.business.entity.result.*;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.BoxQueryService;
import com.cdz360.biz.device.business.service.BoxSettingService;
import com.cdz360.biz.device.business.service.TemplateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2018年8月30日
 */
@Slf4j
@RestController
@RequestMapping("/api/template")
@Deprecated
public class TemplateController extends BaseController {

    @Autowired
    private TemplateService templateService;


    @Autowired
    private BoxSettingService boxSettingService;

    @Autowired
    private BoxQueryService boxQueryService;

    @Autowired
    private MerchantFeignClient merchantFeignClient;


    /**
     * 分页获取计费模板列表
     * 接口逻辑有误
     * 由getTemplateInfoList方法代替
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getPagedTemplateInfoList")
    public ListResponse<TemplateInfoVo> getPagedTemplateInfoList(@RequestBody TemplateListRequest request) throws DcServiceException {
        log.info("param = {}", request);
        if (request.getCommercialId() == null) {
            if (request.getCommercialIdList() == null || request.getCommercialIdList().isEmpty()) {
                //throw new DcServiceException("必要查询条件不能为空");
                throw new DcArgumentException("必要查询条件不能为空");
            }
        }
        ListResponse<TemplateInfoVo> paginationEntity = templateService.getPagedTemplateInfoList(request);

        return paginationEntity;
        //return new SuccessResultEntity<>(paginationEntity);
    }

    /**
     * 分页获取计费模板列表
     * keywords 模板名
     *
     * @param request
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getTemplateInfoList")
    public ListResponse<TemplateInfoVo> getTemplateInfoList(@RequestBody TemplateListRequest request) throws DcServiceException {
        log.info("param = {}", request);
        ListResponse<TemplateInfoVo> paginationEntity = templateService.getTemplateInfoList(request);

        return paginationEntity;
    }


    /**
     * 根据ID获取计费模板详情
     *
     * @param templateId
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getTemplateDetailById")
    public ObjectResponse<TemplateInfoVo> getTemplateDetailById(@RequestParam("templateId") Long templateId) throws DcServiceException {
        log.info("param = {}", templateId);
        Assert.notNull(templateId, "计费模板ID不能为空");
        TemplateInfoVo templateInfo = templateService.getTemplateDetailById(templateId);

        // 当前商户是否可以编辑
        String token = getToken();
        if (StringUtils.isNotBlank(token)) {
            ObjectResponse<Commercial> res = merchantFeignClient.getCommercialByToken(token);
            if (null == res || null == res.getData()) {
                log.info("当前商户不存在");
                templateInfo.setIsModify(0);
            } else {
                Commercial commercial = res.getData();
                if (templateInfo.getCommercialId().equals(commercial.getId())) {
                    templateInfo.setIsModify(1);
                } else {
                    templateInfo.setIsModify(0);
                }
            }
        } else {
            log.info("该请求不是前端页面请求");
            templateInfo.setIsModify(0);
        }

        return new ObjectResponse<>(templateInfo);
    }

    /**
     * 根据计费模板编号，获取最新的计费模板详情
     *
     * @param code
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getLatestTemplateDetailByCode")
    @Deprecated
    public ObjectResponse<TemplateInfoVo> getLatestTemplateDetailByCode(@RequestParam("code") String code) throws DcServiceException {
        log.error("deprecated!!! param = {}", code);
        Assert.isTrue(com.cdz360.base.utils.StringUtils.isNotBlank(code), "计费模板编号不能为空");
        TemplateInfoVo templateInfo = templateService.getLatestTemplateDetailByCode(code);
        return new ObjectResponse<>(templateInfo);
    }

    /**
     * 根据设备ID，获取设备关联的计费模板详情
     *
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getTemplateDetailByBcId")
    @Deprecated
    public ObjectResponse<TemplateInfoVo> getTemplateDetailByBcId(@RequestParam("evseNo") String evseNo) throws DcServiceException {
        log.error("deprecated!!! param = {}", evseNo);
        Assert.notNull(evseNo, "桩编号不能为空");
        TemplateInfoVo templateInfo = templateService.getTemplateDetailByBcId(evseNo);
        return new ObjectResponse<>(templateInfo);
    }

    /**
     * 根据设备ID，获取设备关联的计费模板详情（包括当前电价）
     *
     * @return
     * @throws DcServiceException
     */
    @PostMapping("/getFullTemplateDetailByBcId")
    @Deprecated
    public ObjectResponse<TemplateFullInfoVo> getFullTemplateDetailByBcId(@RequestParam("evseNo") String evseNo) throws DcServiceException {
        log.error("deprecated!!! param = {}", evseNo);
        Assert.notNull(evseNo, "枪头ID不能为空");
        TemplateFullInfoVo templateInfo = templateService.getFullTemplateDetailByBcId(evseNo);
        return new ObjectResponse<>(templateInfo);
    }



    /**
     * 获取计费模板修改后与其相关联的设备下发计费模板的结果
     *
     * @param templateId
     * @return
     */
//    @Deprecated   // 计费模板详情页仍在调用
    @PostMapping("/getSendTemplateResultVo")
    public ObjectResponse<SendTemplateResultVo> getSendTemplateResultVo(Long templateId) {
        log.info("param = {}", templateId);
        Assert.notNull(templateId, "计费模板Id不能为空");


        TemplateInfoVo templateInfoVo = templateService.getTemplateDetailById(templateId);
        Assert.notNull(templateInfoVo, "找不到计费模板信息: " + templateId);

        BoxSettingListRequest boxSettingListFailRequest = new BoxSettingListRequest();
//        boxSettingListRequest.setChargeId(templateId);
        boxSettingListFailRequest.setTemplateCode(templateInfoVo.getCode());
        boxSettingListFailRequest.setStatus(BoxSettingService.STATUS_KEY_SEND_FAIL);//获取失败的列表
        List<BsBoxSettingBInfoVo> bsBoxFailList = boxSettingService.selectAllBoxSettingList(boxSettingListFailRequest);

        BoxSettingListRequest boxSettingListOKRequest = new BoxSettingListRequest();
//        boxSettingListRequest.setChargeId(templateId);
        boxSettingListOKRequest.setTemplateCode(templateInfoVo.getCode());
        boxSettingListOKRequest.setStatus(BoxSettingService.STATUS_KEY_SEND_OK);//获取失败的列表
        List<BsBoxSettingBInfoVo> bsBoxListOK = boxSettingService.selectAllBoxSettingList(boxSettingListOKRequest);

        BoxSettingListRequest boxSettingListSendingRequest = new BoxSettingListRequest();
//        boxSettingListRequest.setChargeId(templateId);
        boxSettingListSendingRequest.setTemplateCode(templateInfoVo.getCode());
        boxSettingListSendingRequest.setStatus(BoxSettingService.STATUS_KEY_SENDING);//获取失败的列表
        List<BsBoxSettingBInfoVo> bsBoxListSending = boxSettingService.selectAllBoxSettingList(boxSettingListSendingRequest);


        //将失败的桩找出来
        BoxListRequest boxListRequest = new BoxListRequest();
        boxListRequest.setPage(1);
        boxListRequest.setRows(bsBoxFailList.size());
        boxListRequest.setDeviceIdList(bsBoxFailList.stream().map(BsBoxSettingBInfoVo::getBoxCode).collect(Collectors.toList()));

        List<BoxInfoVo> failList;

        if (boxListRequest.getDeviceIdList().isEmpty()) {
            failList = new ArrayList<>();
        } else {
            ListResponse<BoxInfoVo> res = boxQueryService.getPagedBoxSimpleList(boxListRequest);
            failList = res.getData();
        }


        int sendOKCount = bsBoxListOK == null ? 0 : bsBoxListOK.size();
        int sendFailedCount = bsBoxFailList == null ? 0 : bsBoxFailList.size();
        int sendingCount = bsBoxListSending == null ? 0 : bsBoxListSending.size();

        SendTemplateResultVo vo = new SendTemplateResultVo();
        vo.setBoxInfoVoFailList(failList);

        vo.setSendTemlateDeviceCount(sendOKCount + sendFailedCount + sendingCount);
        vo.setSendTemlateDeviceSuccessCount(sendOKCount);
        vo.setSendTemlateDeviceFailedCount(sendFailedCount);
        vo.setSendTemlateDeviceSendingCount(sendingCount);

        return new ObjectResponse<>(vo);
    }

}
