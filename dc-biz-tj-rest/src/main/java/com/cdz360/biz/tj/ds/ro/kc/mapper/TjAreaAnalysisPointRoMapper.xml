<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaAnalysisPointRoMapper">

  <resultMap id="RESULT_TJ_AREA_ANALYSIS_POINT_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo">
    <result column="analysisId" jdbcType="BIGINT" property="analysisId"/>
    <result column="num" property="num"/>
    <result column="status" property="status"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="radius" property="radius"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
  </resultMap>

  <resultMap id="RESULT_ANALYSIS_POINT_VO"
    extends="RESULT_TJ_AREA_ANALYSIS_POINT_PO"
    type="com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo">
    <result column="surveyNum" property="surveyNum"/>
    <result column="operateNum" property="operateNum"/>
    <collection property="competitorSiteBiList"
      column="{analysisId=analysisId,num=num,petIdSetString=petIdSetString}"
      ofType="com.cdz360.biz.model.tj.kc.vo.CompetitorSiteBi"
      select="com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorRoMapper.selectCompetitorSiteBi"/>
  </resultMap>

  <resultMap id="RESULT_ANALYSIS_POINT_WITH_SITE_VO"
    extends="RESULT_TJ_AREA_ANALYSIS_POINT_PO"
    type="com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo">
    <collection property="surveySimpleVos"
      column="{score=surveyScore,analysisId=analysisId,analysisPointNum=num,size=limitSize,withinAid=withinAid}"
      ofType="com.cdz360.biz.model.tj.kc.vo.TjSurveySimpleVo"
      select="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyRoMapper.findTjSurvey"/>
    <collection property="siteWithinTjVos"
      column="{analysisId=analysisId,analysisPointNum=num,size=limitSize,withinAid=withinAid}"
      ofType="com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo"
      select="com.cdz360.biz.ds.trading.ro.site.mapper.SiteRoMapper.findSiteWithinTjArea"/>
    <collection property="competitorSiteVos"
      column="{analysisId=analysisId,analysisPointNum=num,size=limitSize,petIdSetString=petIdSetString,withinAid=withinAid}"
      ofType="com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo"
      select="com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorSiteRoMapper.findTjCompetitorSite"/>
  </resultMap>

  <select id="selectByAnalysisId"
    resultMap="RESULT_TJ_AREA_ANALYSIS_POINT_PO">
    select *, ST_AsText(`location`) locationText from t_tj_area_analysis_point
    where analysisId = #{analysisId}
  </select>

  <sql id="FIND_SQL">
    <where>
      <if test="null != analysisId">
        ap.analysisId = #{analysisId}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( statusList )">
        <foreach collection="statusList" item="status"
          open="and ap.status in (" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( addressLike )">
        and ap.address like CONCAT('%', #{addressLike}, '%')
      </if>
    </where>
  </sql>

  <select id="findTjAnalysisPoint"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam"
    resultMap="RESULT_ANALYSIS_POINT_VO">
    select ap.*,
    (select count(1) from t_tj_survey sv
    left join t_tj_area withinArea on ST_Within(
    ST_GeomFromText(concat('POINT(', sv.longitude, ' ', sv.latitude, ')')), withinArea.geo)
    where sv.score = 1 and ST_Distance_Sphere(
    ST_GeomFromText(concat('POINT(', sv.longitude, ' ', sv.latitude, ')')),
    ap.location) <![CDATA[<=]]> (ap.radius * 1000)
    and withinArea.aid = ana.aid and withinArea.aid is not null) surveyNum,
    (select count(1) from t_site site
    left join t_tj_area withinArea on ST_Within(
    ST_GeomFromText(concat('POINT(', site.longitude, ' ', site.latitude, ')')), withinArea.geo)
    where site.status in (1, 2, 3) and site.bizType in (0, 1, 2) and ST_Distance_Sphere(
    ST_GeomFromText(concat('POINT(', site.longitude , ' ', site.latitude, ')')),
    ap.location) <![CDATA[<=]]> (ap.radius * 1000)
    and withinArea.aid = ana.aid and withinArea.aid is not null) operateNum
    ,
    <choose>
      <when test="null != petIdSetString">
        #{petIdSetString}
      </when>
      <otherwise>null</otherwise>
    </choose>
    as petIdSetString
    from t_tj_area_analysis_point ap
    left join t_tj_area_analysis ana on ana.id = ap.analysisId
    <include refid="FIND_SQL"/>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="findTjAnalysisPointWithSite"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam"
    resultMap="RESULT_ANALYSIS_POINT_WITH_SITE_VO">
    select ap.*, 100 as limitSize, 1 as surveyScore
    ,
    <choose>
      <when test="null != petIdSetString">
        #{petIdSetString}
      </when>
      <otherwise>null</otherwise>
    </choose>
    as petIdSetString
    , ana.aid as withinAid
    from t_tj_area_analysis_point ap
    left join t_tj_area_analysis ana on ana.id = ap.analysisId
    <include refid="FIND_SQL"/>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countTjAnalysisPoint" resultType="java.lang.Long">
    select count(1) from t_tj_area_analysis_point ap
    <include refid="FIND_SQL"/>
  </select>

</mapper>

