package com.cdz360.biz.cus.repository;

import com.cdz360.biz.cus.domain.request.CardRequest;
import com.cdz360.biz.cus.domain.vo.CardListdetailVO;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.cus.user.dto.WhiteCard;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardDetailVO;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.cache.annotation.Cacheable;

@Mapper
public interface CardMapper {

    /**
     * 根据条件查询鉴权卡数据
     *
     * @param params
     * @return
     */
    List<CardListdetailVO> queryCards(Map<String, Object> params);

    /**
     * 添加 卡片信息
     *
     * @param record
     * @return
     */
    Long insertSelective(Card record);

    /**
     * 根据条件查询在线卡数据
     *
     * @param params
     * @return
     */
    List<CardListdetailVO> queryOnlineCardsByPage(Map<String, Object> params);

    /**
     * 根据条件查询企业在线卡数据
     *
     * @param cardRequest
     * @return
     */
    List<CardListdetailVO> queryOnlineCardsByPageOnCorp(CardRequest cardRequest);

//    List<CardListdetailVO> getUsableStationByCardIdList(@Param("cardIdList") List<Long> cardIdList,
//                                                        @Param("commIdChain") String commIdChain);

//    /**
//     * 获取卡片使用范围(适用的场站)
//     *
//     * @param cardNo 逻辑卡号
//     * @return
//     */
//    String getStationsByCardNo(@Param("cardNo") String cardNo);

    /**
     * 根据条件查询紧急充电卡数据
     *
     * @param params
     * @return
     */
    List<CardListdetailVO> queryUrgencyCardsByPage(Map<String, Object> params);

    int updateByCardNoSelective(Card record);

    int updateActivationCode(WhiteCard record);

    /**
     * @return
     */
    Long findlastCardNo();

    /**
     * 查询用户卡片信息
     *
     * @param commericalId 商户id
     * @param userId       用户id
     * @return
     */
    Card findByCommIdAndUserId(@Param("commId") Long commericalId, @Param("userId") Long userId);

    /**
     * 查询用户卡片列表
     *
     * @param userId 用户id
     * @return
     */
    List<Card> findListByUserId(@Param("userId") Long userId);

    /**
     * 根据条卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     * @return
     */
    int updateCardStatus(@Param("cardNo") String cardNo, @Param("cardStatus") String cardStatus);

    /**
     * 根据物理卡号更新卡状态
     *
     * @param cardChipNo 物理卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     * @return
     */
    int updateCardStatusByCardChipNo(@Param("cardChipNo") String cardChipNo,
                                     @Param("cardStatus") String cardStatus);

    /**
     * 根据条卡号更新卡为无效
     *
     * @param cardNo 卡号
     * @return
     */
    int updateCardInvalid(String cardNo);

    int queryCountByCardNo(String cardNo);

    /**
     * 根据卡号查询卡
     *
     * @param cardNo 卡号
     * @return
     */
    Card queryCardByCardNo(String cardNo);

    /**
     * 根据卡号查询卡
     *
     * @param cardNos 卡号
     * @return
     */
    List<WhiteCardCfgVo> queryCardByCardNoList(@Param("cardNos") List<String> cardNos);

    /**
     * 根据Chip卡号查询卡
     *
     * @param cardChipNo Chip卡号
     * @return
     */
    Card queryCardByCardChipNo(String cardChipNo);

    /**
     * 根据卡号物理删除卡（仅限离线卡使用）
     *
     * @param cardNo
     */
    void deleteCardByCardNo(String cardNo);

    /**
     * 修改卡可用站点
     * @param commIdList
     * @param siteId
     * @return
     */
    int updateEnableBySiteId(@Param("commIdList") List<Long> commIdList,@Param("siteId") String siteId);

    /**
     * 根据id物理删除卡(用于未绑定未激活的卡)
     *
     * @param map
     * @return
     */
    int deleteCardsByIds(Map map);

    /**
     * 根据id和状态查询卡片
     *
     * @param map
     * @return
     */
    List<Card> findListByCardStatusAndId(Map map);

    /**
     * 根据卡号获取卡信息
     * card.yx_bz=1 and card.card_status in (10001,10007,10008,10009)
     *
     * @param cardNo
     * @return
     */
    @Cacheable(cacheNames = DcBizConstants.CACHE_NAME_CARD, key = "#topCommId + '_' + #cardNo")
    Card getCardByCardNo(@Param("cardNo") String cardNo, @Param("topCommId") Long topCommId);
    Card getCardByCardNoX(@Param("cardNo") String cardNo,
        @Param("cardExactMatch") boolean cardExactMatch,
        @Param("topCommId") Long topCommId,
        @Param("siteId") String siteId);

    Long checkCardCount(@Param("cardNo") String cardNo, @Param("topCommId") Long topCommId);

    /**
     * 根据物理卡号获取卡信息
     *
     * @param cardChipNo
     * @return
     */
    Card getCardByCardChipNo(@Param("cardChipNo") String cardChipNo);

    /**
     * 更新卡余额
     *
     * @param cardNo
     * @param cardBalance
     * @return
     */
    int updateCardBalance(@Param("cardNo") String cardNo, @Param("cardBalance") long cardBalance);

    /**
     * 根据客户查询该客户下此商户的所有卡片
     *
     * @param uid
     * @return
     */
    List<CardVo> findByUserId(@Param("userId") Long uid);

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    Card queryCardById(Long id);

    /**
     * 根据ID查询卡(包含user信息)
     *
     * @param id 卡id
     * @return
     */
    Card queryCardWithUserById(Long id);

    /**
     * 根据条件查询卡(得到集团客户名下的卡)
     * (用于查询场站下的紧急卡数时，stations用‘=’)
     * 可继续补充条件
     *
     * @return
     */
    List<Card> queryCardByCondition(CardRequest cardRequest);

    /**
     * 根据条件批量查询场站下的有效紧急卡数量
     *
     * @param cardRequest
     * @return
     */
    List<SiteCardCount> findUrgencyCardNumCount(CardRequest cardRequest);

    /**
     * 查询商户及子商户卡列表
     *
     * @param params
     * @return
     */
    List<CardVo> queryAllCardList(Map<String, Object> params);

    /**
     * 根据条件查询卡数据列表
     *
     * @param params
     * @return
     */
    List<CardMgnVo> queryMgmCards(Map<String, Object> params);

    /**
     * 校验卡片是否存在
     * 存在物理卡号或者逻辑卡号则认为存在
     *
     * @param cardNo     逻辑卡号
     * @param cardChipNo 物理卡号
     * @return
     */
    Card checkCard(@Param("cardNo") String cardNo, @Param("cardChipNo") String cardChipNo);

    /**
     * 批量添加卡片
     *
     * @param cards 卡片列表
     * @return
     */
    int insertBatch(@Param("cards") List<Card> cards);

    /**
     * 根据逻辑卡号查询物理卡号
     *
     * @param map
     * @return
     */
    List<Card> selectShipCardNoByCardNos(Map map);

    /**
     * 根据逻辑卡号获取物流卡号
     *
     * @param cardNo
     * @return
     */
    String getCardChipNoByCardNo(@Param("cardNo") String cardNo);

    /**
     * 查询站点下的紧急充电卡
     * 备注：cardChipNo 不为空表示弃用
     *
     * @param siteId
     * @param commIds
     * @param cardChipNo
     * @return
     */
    List<WhiteCardDto> queryWhiteCardDtoMapList(
            @Param("topCommId") Long topCommId,
            @Param("siteId") String siteId,
            @Param("siteList") List<String> siteList,
            @Param("commIds") List<Long> commIds,
            @Param("cardChipNo") String cardChipNo,
            @Param("excludeCardStatusList") List<String> excludeCardStatusList);

    /**
     * 更新紧急充电卡状态，只有处于【已激活】 | 【下发中】 | 【下发失败】状态才会更新
     *
     * @param ids           卡片列表
     * @param cardType      卡片类型，其实可以写死为【1】 来表示紧急卡
     * @param excludeStatus 当该卡片处于此状态时，不更新，传null则不处理
     * @return
     */
    int updateUrgencyCard(@Param("ids") List<String> ids, @Param("status") String status, @Param("cardType") Integer cardType, @Param("excludeStatus") String excludeStatus);

    List<String> queryCardNoListByBlocUserId(@Param("blocUserId") Long blocUserId);

    List<Card> selectBlocUserNameByCardNos(@Param("cardNos") List<String> cardNos);

    /**
     * 获取用的充电卡列表
     *
     * @param userId
     * @param statusList
     * @return
     */
    List<CardDetailVO> findByUserIdAndStatus(
            @Param("offset") Integer offset,
            @Param("limit") Integer limit,
            @Param("userId") Long userId,
            @Param("statusList") List<String> statusList,
            @Param("commIdList") List<Long> commIdList);

    /**
     * 通过用户Id获取一个有效在线卡
     *
     * @param userId
     * @param commIdChain
     * @return
     */
    CardVo findOneByUserId(@Param("userId") Long userId, @Param("commIdChain") String commIdChain);

    List<Card> getEmergencyCardBySiteId(@Param("siteId") String siteId);

    List<Card> getOnlineCardBySiteId(@Param("siteId") String siteId);

    int resetCardById(@Param("ids") List<Long> ids);

    RBlocUserVo getCorpOrgName(@Param("blocUserId") Long blocUserId, @Param("userId") Long userId,
                               @Param("phone") String phone);

    /**
     * 获取卡片列表
     *
     * @param userId  用户id
     * @param corpIds 集团id列表
     * @return
     */
    List<Card> getCardsByUserIdAndCorpIds(@Param("userId") Long userId, @Param("corpIds") List<Long> corpIds);

    /**
     * 得到需要被清洗的在线卡数据
     *
     * @return
     */
    List<Card> getCardCleanData();

    @Deprecated
    List<Card> findAll(@Param("start") Long start, @Param("size") Integer size);

    /**
     * 获取授信账户名下未删除的在线卡数量
     *
     * @param rBlocUserId
     */
    int getCreditAccountCardCount(@Param("rBlocUserId") long rBlocUserId);

    /**
     * 根据卡ID查询可用场站名称
     * @param cardId
     * @return
     */
    List<String> getSiteNameListByCardId(Long cardId);

    /**
     * 原先授信账户的卡转到新授信账户
     * @param rBlocUserId
     * @param userId
     * @param mobile
     * @return
     */
    int swapCreditAccountCard(@Param("rBlocUserId") long rBlocUserId,
                              @Param("userId") long userId,
                              @Param("mobile") String mobile);

    /**
     * 移动企业到新商户
     * @param corpId
     * @param commId
     * @return
     */
    long moveCorp(@Param("corpId") Long corpId, @Param("commId") Long commId);

    long batchUpdateCardDeposit(List<String> cardNos, @Param("deposit") Integer deposit);

    List<Card> getCardByCorIdAndUserId(@Param("corpId") Long corpId, @Param("userId") Long userId);

    List<CardListdetailVO> queryEssOnlineCardsByPage(CardRequest param);
}