package com.cdz360.biz.device.business.rest;


import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.BoxQueryService;
import com.cdz360.biz.device.common.utils.LoggerHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2018年8月30日
 */
@Slf4j
@RestController
@RequestMapping("/api/box")
@Deprecated
public class BoxQueryController extends BaseController {

    @Autowired
    private BoxQueryService boxQueryService;



    /**
     * 根据站点id分页获取站点下设备列表
     *
     * @param param
     * @return
     * @throws DcServiceException
     */

    @PostMapping("/getPagedBoxSimpleList")
    public ListResponse<BoxInfoVo> getPagedBoxSimpleList(@RequestBody BoxListRequest param) throws DcServiceException {
//        log.error("deprecated !!!");
        log.info(LoggerHelper.formatEnterLog(request));
        if (StringUtils.isBlank(param.getSiteId()) && (param.getCommercialIds() == null && param.getCommercialIds().size() == 0)) {
            throw new DcServiceException("无效的查询条件");
        }
        ListResponse<BoxInfoVo> res = boxQueryService.getPagedBoxSimpleList(param);

        return res;
    }



}
