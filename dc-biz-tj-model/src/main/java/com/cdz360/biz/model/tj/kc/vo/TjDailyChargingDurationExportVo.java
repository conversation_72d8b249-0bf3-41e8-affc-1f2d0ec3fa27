package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.biz.model.annotations.ExcelField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "SIM卡列表")
@Data
@Accessors(chain = true)
public class TjDailyChargingDurationExportVo implements Serializable {

    @ExcelField(title = "序号", sort = 1)
    @Schema(description = "序号")
    private Integer idx;

    @ExcelField(title = "省份", sort = 2)
    @Schema(description = "省份")
    private String provinceName;

    @ExcelField(title = "城市", sort = 3)
    @Schema(description = "城市")
    private String cityName;

    @ExcelField(title = "区域", sort = 4)
    @Schema(description = "区域")
    private String areaName;

    @ExcelField(title = "第1年前3个月", sort = 5)
    @Schema(description = "第1年前3个月")
    private BigDecimal firstYearThirdMonth;

    @ExcelField(title = "第1年后9个月", sort = 6)
    @Schema(description = "第1年后9个月")
    private BigDecimal firstYearLastNinthMonth;

    @ExcelField(title = "第2年", sort = 7)
    @Schema(description = "第2年")
    private BigDecimal secondYear;

    @ExcelField(title = "第3年", sort = 8)
    @Schema(description = "第3年")
    private BigDecimal thirdYear;

    @ExcelField(title = "第4年", sort = 9)
    @Schema(description = "第4年")
    private BigDecimal fourthYear;

    @ExcelField(title = "第5年", sort = 10)
    @Schema(description = "第5年")
    private BigDecimal fifthYear;

    @ExcelField(title = "第6年", sort = 11)
    @Schema(description = "第6年")
    private BigDecimal sixthYear;

    @ExcelField(title = "第7年", sort = 12)
    @Schema(description = "第7年")
    private BigDecimal seventhYear;

    @ExcelField(title = "第8年", sort = 13)
    @Schema(description = "第8年")
    private BigDecimal eighthYear;

    @ExcelField(title = "第9年", sort = 14)
    @Schema(description = "第9年")
    private BigDecimal ninthYear;

    @ExcelField(title = "第10年", sort = 15)
    @Schema(description = "第10年")
    private BigDecimal tenthYear;

}
