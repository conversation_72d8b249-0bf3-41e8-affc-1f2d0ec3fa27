<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationIncomeRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_OPERATION_INCOME_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="stationSubsidy" jdbcType="DECIMAL" property="stationSubsidy"/>
    <result column="stationSubsidyYear" jdbcType="INTEGER" property="stationSubsidyYear"/>
    <result column="operationSubsidyPrice" jdbcType="DECIMAL" property="operationSubsidyPrice"/>
    <result column="elecPrice" jdbcType="DECIMAL" property="elecPrice"/>
    <result column="firstYearThirdMonthServicePrice" jdbcType="DECIMAL" property="firstYearThirdMonthServicePrice"/>
    <result column="firstYearLastNinthMonthServicePrice" jdbcType="DECIMAL" property="firstYearLastNinthMonthServicePrice"/>
    <result column="serviceIncrease" jdbcType="DECIMAL" property="serviceIncrease"/>
    <result column="serviceIncreaseMaxPrice" jdbcType="DECIMAL" property="serviceIncreaseMaxPrice"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_OPERATION_INCOME_PO">
    select * from t_tj_survey_operation_income where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveyOperationIncome"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo">
    insert into t_tj_survey_operation_income (`surveyNo`,
    <if test="null != stationSubsidy">
      `stationSubsidy`,
    </if>
    <if test="null != stationSubsidyYear">
      `stationSubsidyYear`,
    </if>
    <if test="null != operationSubsidyPrice">
      `operationSubsidyPrice`,
    </if>
    <if test="null != elecPrice">
      `elecPrice`,
    </if>
    <if test="null != firstYearThirdMonthServicePrice">
      `firstYearThirdMonthServicePrice`,
    </if>
    <if test="null != firstYearLastNinthMonthServicePrice">
      `firstYearLastNinthMonthServicePrice`,
    </if>
    <if test="null != serviceIncrease">
      `serviceIncrease`,
    </if>
    <if test="null != serviceIncreaseMaxPrice">
      `serviceIncreaseMaxPrice`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != stationSubsidy">
      #{stationSubsidy},
    </if>
    <if test="null != stationSubsidyYear">
      #{stationSubsidyYear},
    </if>
    <if test="null != operationSubsidyPrice">
      #{operationSubsidyPrice},
    </if>
    <if test="null != elecPrice">
      #{elecPrice},
    </if>
    <if test="null != firstYearThirdMonthServicePrice">
      #{firstYearThirdMonthServicePrice},
    </if>
    <if test="null != firstYearLastNinthMonthServicePrice">
      #{firstYearLastNinthMonthServicePrice},
    </if>
    <if test="null != serviceIncrease">
      #{serviceIncrease},
    </if>
    <if test="null != serviceIncreaseMaxPrice">
      #{serviceIncreaseMaxPrice},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveyOperationIncome" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo">
    update t_tj_survey_operation_income set
    <if test="stationSubsidy != null">
      stationSubsidy = #{stationSubsidy},
    </if>
    <if test="stationSubsidyYear != null">
      stationSubsidyYear = #{stationSubsidyYear},
    </if>
    <if test="operationSubsidyPrice != null">
      operationSubsidyPrice = #{operationSubsidyPrice},
    </if>
    <if test="elecPrice != null">
      elecPrice = #{elecPrice},
    </if>
    <if test="firstYearThirdMonthServicePrice != null">
      firstYearThirdMonthServicePrice = #{firstYearThirdMonthServicePrice},
    </if>
    <if test="firstYearLastNinthMonthServicePrice != null">
      firstYearLastNinthMonthServicePrice = #{firstYearLastNinthMonthServicePrice},
    </if>
    <if test="serviceIncrease != null">
      serviceIncrease = #{serviceIncrease},
    </if>
    <if test="serviceIncreaseMaxPrice != null">
      serviceIncreaseMaxPrice = #{serviceIncreaseMaxPrice},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveyOperationIncome">
    update t_tj_survey_operation_income set
    enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>


</mapper>

