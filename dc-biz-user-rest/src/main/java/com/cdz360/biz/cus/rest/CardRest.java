package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.BsBoxSettingFeignClient;
import com.cdz360.biz.cus.domain.ListResponseEvseList;
import com.cdz360.biz.cus.domain.request.CardRequest;
import com.cdz360.biz.cus.domain.vo.CardListdetailVO;
import com.cdz360.biz.cus.domain.vo.CardListdetailVOParam;
import com.cdz360.biz.cus.service.AccountServiceImpl;
import com.cdz360.biz.cus.service.ICardService;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardDetailVO;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * 卡片功能
 * <p>
 * BlocUserController
 *
 * <AUTHOR> 卡片功能
 * @since 2018.11.22
 */
@Slf4j
@RestController
@Tag(name = "卡片功能相关接口", description = "卡片功能")
public class CardRest extends BaseController {

    private static final String ONLINE_CARD_FLAG = "onLineCard";
    @Autowired
    private ICardService cardService;
    @Autowired
    private BsBoxSettingFeignClient bsBoxSettingFeignClient;
    @Autowired
    private AccountServiceImpl accountService;

    private static String excelSubDir() {
        return ONLINE_CARD_FLAG + new SimpleDateFormat("yyyyMMdd").format(new Date());
    }

    /**
     * 获取用户充电卡(在线卡)列表 -- 激活
     *
     * @param request
     * @param appCommId
     * @param userId    用户ID
     * @return
     */
    @GetMapping("/api/card/online/user/active/list")
    public ListResponse<CardDetailVO> queryOnlineCardsOfUserActive(
        ServerHttpRequest request,
        ServerWebExchange exh,
//            @RequestParam("token") String token,
        @RequestParam("appCommId") long appCommId,
        @RequestParam(value = "userId") Long userId) {

//        //TODO: token校验呢？
//        UserAndBalanceVo userAndBalanceVo = accountService.getCurrentUserByToken(token);
//        if (ObjectUtils.isEmpty(userAndBalanceVo)) {
//            throw new DcServiceException("获取用户信息失败");
//        }

        OldPageParam page = getPage2(request, exh, false);

        // 激活状态
        List<String> statusList = new ArrayList<>();
        statusList.add(CardStatus.ACTIVE.getCode());

//        log.info("查询已激活的在线卡。token: {}, userId: {}, statusList: {}", token, userId, statusList, userAndBalanceVo.getAppCommId());
        log.info("查询已激活的在线卡。userId: {}, statusList: {}, appCommId: {}", userId, statusList,
            appCommId);

//        List<CardDetailVO> res = cardService.queryOnlineCardsByUser(page, userId, statusList, userAndBalanceVo.getAppCommId());
        List<CardDetailVO> res = cardService.queryOnlineCardsByUser(page, userId, statusList,
            appCommId);
        return new ListResponse<>(res);
    }

    /**
     * 获取用户充电卡(在线卡)列表 -- 挂失
     *
     * @param request
     * @param appCommId
     * @param userId    用户ID
     * @return
     */
    @GetMapping("/api/card/online/user/invalid/list")
    public ListResponse<CardDetailVO> queryOnlineCardsOfUserInvalid(
        ServerHttpRequest request,
        ServerWebExchange exh,
//            @RequestParam("token") String token,
        @RequestParam("appCommId") long appCommId,
        @RequestParam(value = "userId") Long userId) {
//        // TODO: token校验呢？
//        UserAndBalanceVo userAndBalanceVo = accountService.getCurrentUserByToken(token);
//        if (ObjectUtils.isEmpty(userAndBalanceVo)) {
//            throw new DcServiceException("获取用户信息失败");
//        }
        OldPageParam page = getPage2(request, exh, false);

        // 挂失状态
        List<String> statusList = new ArrayList<>();
        statusList.add(CardStatus.LOCK.getCode());

//        log.info("查询已挂失的在线卡。token: {}, userId: {}, statusList: {}", token, userId, statusList);
        log.info("查询已挂失的在线卡。userId: {}, statusList: {}, appCommId: {}", userId, statusList,
            appCommId);

//        List<CardDetailVO> res = cardService.queryOnlineCardsByUser(page, userId, statusList, userAndBalanceVo.getAppCommId());
        List<CardDetailVO> res = cardService.queryOnlineCardsByUser(page, userId, statusList,
            appCommId);
        return new ListResponse<>(res);
    }

    /**
     * 根据提交查询卡列表
     *
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardsByPage", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryCards(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam("token") String token,
        @RequestBody CardSearchParam param) {
        log.info(LoggerHelper2.formatEnterLog(request) + " param = {}", param);
        OldPageParam page = getPage2(request, exh, false);
        CardListdetailVO cardListdetailVO = new CardListdetailVO();
//        cardListdetailVO.setCardChipNo(cardChipNo);
        cardListdetailVO.setBeginTime(param.getBeginTime());
        cardListdetailVO.setEndTime(param.getEndTime());
        cardListdetailVO.setCommId(param.getCommId());
        cardListdetailVO.setCardStatus(param.getCardStatus());
        cardListdetailVO.setIsPackage(param.getIsPackage());
        cardListdetailVO.setCorpName(param.getCorpName());
        cardListdetailVO.setStationName(param.getSiteName());
        cardListdetailVO.setCarNo(param.getCarNo());
        cardListdetailVO.setCardNoList(param.getCardNoList());
        cardListdetailVO.setCardType(param.getCardType());
        cardListdetailVO.setDeposit(param.getDeposit());
        return cardService.queryCardsByPage(token, page, param.getKeyword(), cardListdetailVO,
            param.getTopCommId(),
            param.getCommIdChain());
    }

    /**
     * 根据提交查询在线卡列表
     *
     * @param cardStatus 卡状态
     * @param beginTime  查询开始时间
     * @param endTime    查询结束时间
     * @param keyWord    卡号/卡名称
     * @return
     */
    @RequestMapping(value = "/api/card/queryOnlineCardsByPage", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryOnlineCardsByPage(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam("token") String token,
        @RequestParam(value = "cardStatus", required = false) String cardStatus,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam(value = "cardType", required = false) Long cardType,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "commIdChain") String commIdChain) {
        OldPageParam page = getPage2(request, exh, true);
        CardListdetailVO cardListdetailVO = new CardListdetailVO();
        cardListdetailVO.setCardChipNo(keyWord);
        cardListdetailVO.setBeginTime(beginTime);
        cardListdetailVO.setEndTime(endTime);
        cardListdetailVO.setCardStatus(cardStatus);
        cardListdetailVO.setCommId(commId);
        cardListdetailVO.setUserId(userId);
        cardListdetailVO.setCardType(cardType);
        return cardService.queryOnlineCardsByPage(token, page, cardListdetailVO, topCommId,
            commIdChain);
    }

    /**
     * 根据提交查询在线卡
     *
     * @param cardChipNo cardChipNo
     * @param userId     userId
     * @return
     */
    @RequestMapping(value = "/api/card/queryOnlineCard", method = RequestMethod.POST)
    public ObjectResponse<CardListdetailVO> queryOnlineCard(
        @RequestParam(value = "cardChipNo") String cardChipNo,
        @RequestParam(value = "userId") Long userId) {
        CardListdetailVO cardListdetailVO = new CardListdetailVO();
        cardListdetailVO.setCardChipNo(cardChipNo);
        cardListdetailVO.setUserId(userId);
        return cardService.queryOnlineCard(cardListdetailVO);
    }

    /**
     * 根据提交查询在线卡列表
     *
     * @return
     */
    @RequestMapping(value = "/api/card/queryOnlineCardsByPageOnCorp", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryOnlineCardsByPageOnCorp(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) {
        log.info("企业在线卡参数,cardRequest={}", JsonUtils.toJsonString(cardRequest));
        return cardService.queryOnlineCardsByPageOnCorp(cardRequest);
    }

    /**
     * 根据提交查询紧急充电卡列表 用于充电管理平台
     *
     * @return
     */
    @RequestMapping(value = "/api/card/queryUrgencyCardsByPage", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPage(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam("token") String token,
        @RequestBody CardRequest cardRequest) {
        OldPageParam page = getPage2(request, exh, false);
        CardListdetailVO cardListdetailVO = new CardListdetailVO();
        cardListdetailVO.setBeginTime(cardRequest.getStartTime());
        cardListdetailVO.setEndTime(cardRequest.getEndTime());
        cardListdetailVO.setCardStatus(cardRequest.getCardStatus());
        cardListdetailVO.setCommId(cardRequest.getCommId());
        cardListdetailVO.setStationName(cardRequest.getSiteName());
        cardListdetailVO.setCorpId(cardRequest.getCorpId());
        cardListdetailVO.setQueryType(cardRequest.getQueryType());
        cardListdetailVO.setQueryStr(cardRequest.getQueryStr());
        cardListdetailVO.setStations(cardRequest.getStations());
        cardListdetailVO.setCardNo(cardRequest.getCardNo());
        cardListdetailVO.setStationName(cardRequest.getSiteName());
        return cardService.queryUrgencyCardsByPage(token, page, cardListdetailVO,
            cardRequest.getTopCommId(), cardRequest.getCommId(), cardRequest.getCommIdChain());
    }

    /**
     * 根据提交查询紧急充电卡列表 用于运营支撑平台(无需考虑权限)
     *
     * @return
     */
    @RequestMapping(value = "/api/card/queryUrgencyCardsByPageOnOperate", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(
        ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam("token") String token,
        @RequestBody CardRequest cardRequest) {
        OldPageParam page = getPage2(request, exh, false);
        CardListdetailVOParam cardListdetailVO = new CardListdetailVOParam();
        cardListdetailVO.setBeginTime(cardRequest.getStartTime());
        cardListdetailVO.setEndTime(cardRequest.getEndTime());
        cardListdetailVO.setCardStatus(cardRequest.getCardStatus());
        cardListdetailVO.setCommId(cardRequest.getCommId());
        cardListdetailVO.setStationName(cardRequest.getSiteName());
        cardListdetailVO.setCorpId(cardRequest.getCorpId());
        cardListdetailVO.setQueryType(cardRequest.getQueryType());
        cardListdetailVO.setQueryStr(cardRequest.getQueryStr());
        cardListdetailVO.setStations(cardRequest.getStations());
        cardListdetailVO.setCardNo(cardRequest.getCardNo());
        cardListdetailVO.setExcludeCardStatusList(cardRequest.getExcludeCardStatusList());

        return cardService.queryUrgencyCardsByPageOnOperate(token, page, cardListdetailVO);
    }

    /**
     * 根据离线卡cardId获取相关场站的信息
     *
     * @param token
     * @param cardId
     * @return
     */
    @RequestMapping(value = "/api/card/urgencyCardsDetail", method = RequestMethod.POST)
    public ObjectResponse urgencyCardsDetail(@Deprecated @RequestParam("token") String token,
        @RequestParam("cardId") Long cardId) {
        return cardService.urgencyCardsDetail(cardId);
    }

    /**
     * 根据离线卡cardId获取相关桩的信息
     *
     * @param token
     * @param cardId
     * @return
     */
    @RequestMapping(value = "/api/card/urgencyCardsDetailEvseList", method = RequestMethod.POST)
    public ListResponseEvseList urgencyCardsDetailEvseList(@RequestParam("token") String token,
        @RequestParam("cardId") Long cardId,
        @RequestParam(value = "evse", required = false) String evse,
        @RequestParam(value = "page") Integer page,
        @RequestParam(value = "rows") Integer rows) {
        return cardService.urgencyCardsDetailEvseList(token, cardId, evse, page, rows);
    }

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardById", method = RequestMethod.POST)
    public ObjectResponse<Card> queryCardById(@RequestParam("id") String id) {
        if (StringUtils.isBlank(id)) {
            throw new DcArgumentException("查询条件ID错误");
        }
        ObjectResponse info = cardService.queryCardById(Long.valueOf(id));
        return info;
    }

    /**
     * 根据条件查询卡(得到集团客户名下的卡) 可继续补充条件
     *
     * @return
     */
    @PostMapping("/api/card/queryCardByCondition")
    public ListResponse<Card> queryCardByCondition(@RequestBody CardRequest cardRequest) {
        ListResponse info = cardService.queryCardByCondition(cardRequest);
        return info;
    }

    /**
     * 根据卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单),
     *                   10006已过期，20000卡已删除，20001离线卡正常）
     * @return
     */
    @RequestMapping(value = "/api/card/updateCardStatus", method = RequestMethod.GET)
    public BaseResponse updateCardStatus(@RequestParam("cardNo") String cardNo,
        @RequestParam("cardStatus") String cardStatus) {
        BaseResponse info = cardService.updateCardStatus(cardNo, cardStatus);
        return info;
    }

    /**
     * 根据物理卡号更新卡状态
     *
     * @param cardChipNo 物理卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单),
     *                   10006已过期，20000卡已删除，20001离线卡正常）
     * @return
     */
    @RequestMapping(value = "/api/card/updateCardStatusByChipNo", method = RequestMethod.GET)
    public ObjectResponse updateCardStatusByChipNo(@RequestParam("cardChipNo") String cardChipNo,
        @RequestParam("cardStatus") String cardStatus) {
        if (StringUtils.isEmpty(cardChipNo) || StringUtils.isEmpty(cardStatus)) {
            log.info("cardChipNo={}, cardStatus={}", cardChipNo, cardStatus);
            throw new DcArgumentException("请输入有效的物理卡号和需要变更的状态值");
        }

        return cardService.updateCardStatusByChipNo(cardChipNo, cardStatus);
    }

    /**
     * 新增卡
     *
     * @param card 卡
     * @return
     */
    @RequestMapping(value = "/api/card/addCard", method = RequestMethod.POST)
    public BaseResponse addCard(@RequestBody Card card) {
        try {
            // 调用服务添加
            BaseResponse resultEntity = cardService.addCard(card);
            return resultEntity;
        } catch (Exception e) {
            log.error("纵有千年铁门槛,终须一个土馒头。卡添加失败{}", card, e);
            throw new DcServiceException("卡添加失败");
        }

    }

    /**
     * 批量更新卡 by card_no
     *
     * @param cardList 卡集合
     * @return
     */
    @RequestMapping(value = "/api/card/updateBatchCard", method = RequestMethod.POST)
    public BaseResponse updateBatchCard(@RequestBody List<Card> cardList) {
        log.info(">> 卡片信息更新: cardList={}", JsonUtils.toJsonString(cardList));
        try {
            // 调用服务
            for (Card card : cardList) {
                BaseResponse resultEntity = cardService.updateCard(card);
                log.info("<< 卡片信息更新结果: result={}", JsonUtils.toJsonString(resultEntity));
            }
            return BaseResponse.success();
        } catch (Exception e) {
            log.error("<< 卡信息更新失败: error={},{}", e.getMessage(), e);
            throw new DcServiceException("卡信息更新失败");
        }

    }

    /**
     * 批量新增卡
     *
     * @param cards 卡
     * @return
     */
    @PostMapping("/api/card/batchAddCard")
    public ObjectResponse<Integer> batchAddCard(@RequestBody List<Card> cards) {
        log.info("batch insert card: size={}, list={}", cards.size(), cards);

        if (0 == cards.size()) {
            log.info("批量上传卡片大小为0, size={}", cards.size());
            throw new DcServiceException("请上传有效卡片数据，批量添加卡片数量不能为零.");
        }

        try {
            return cardService.batchAddCard(cards);
        } catch (DcServiceException e) {
            log.error("批量添加卡失败: {}", cards, e);
            throw e;
        } catch (Exception e) {
            log.error("批量添加卡失败: {}", cards, e);//纵有千年铁门槛, 终须一个土馒头.
            throw new DcServiceException("批量添加卡失败");
        }
    }

    /**
     * 企业平台批量新增卡
     *
     * @param cards 卡
     * @return
     */
    @PostMapping("/api/card/batchGrantCard")
    public ObjectResponse<Integer> batchGrantCard(@RequestBody List<Card> cards) {
        if (0 == cards.size()) {
            log.info("批量上传卡片大小为0, size={}", cards.size());
            throw new DcServiceException("请上传有效卡片数据，批量添加卡片数量不能为零.");
        }
        try {
            return cardService.batchGrantCard(cards);
        } catch (DcException e) {
            throw e;
        } catch (Exception e) {
            log.error("批量添加卡失败: {}", cards, e);
            throw new DcServiceException("批量添加卡失败");
        }
    }

    /**
     * 更新卡 by card_no
     *
     * @param card 卡
     * @return
     */
    @RequestMapping(value = "/api/card/updateCard", method = RequestMethod.POST)
    public BaseResponse updateCard(@RequestBody Card card) {
        log.info(">> 卡片信息更新: card={}", JsonUtils.toJsonString(card));
        try {
            // 调用服务
            BaseResponse resultEntity = cardService.updateCard(card);
            log.info("<< 卡片信息更新结果: result={}", JsonUtils.toJsonString(resultEntity));
            return resultEntity;
        } catch (Exception e) {
            log.error("<< 卡信息更新失败: error={},{}", e.getMessage(), e);
            throw new DcServiceException(e.getMessage());
        }

    }


    /**
     * 根据卡号获取卡信息 有固定条件card_status =10001
     *
     * @param cardNo
     * @return
     */
    @ResponseBody
    @PostMapping("/api/card/getCardByCardNo")
    public ObjectResponse<Card> getCardByCardNo(@RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "topCommId") Long topCommId) {
        if (StringUtils.isBlank(cardNo)) {
            throw new DcArgumentException("获取卡信息失败，卡号不能为空");
        }
        Card card = cardService.getCardByCardNo(cardNo, topCommId);
        if (card != null) {
            return new ObjectResponse<>(card);
        } else {
            throw new DcServiceException("获取卡信息失败");
        }
    }

    /**
     * 根据卡号获取卡信息
     *
     * @param cardNo
     * @return
     */
    @ResponseBody
    @PostMapping("/api/card/queryCardByCardNo")
    public ObjectResponse<Card> queryCardByCardNo(String cardNo) {
        if (StringUtils.isNotBlank(cardNo)) {
            try {
                Card card = cardService.queryCardByCardNo(cardNo);
                return new ObjectResponse<>(card);
            } catch (Exception e) {
                throw new DcServiceException(e.getMessage());
            }
        } else {
            throw new DcServiceException("获取卡信息失败，卡号不能为空");
        }
    }

    /**
     * 根据卡号获取卡账户信息
     *
     * @param cardNo
     * @return
     */
    @ResponseBody
    @GetMapping("/api/card/getAccountByCardNo")
    public ObjectResponse<AccountInfoVo> getAccountByCardNo(
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "topCommId") Long topCommId) {
        return RestUtils.buildObjectResponse(cardService.getAccountByCardNo(cardNo, topCommId));
    }

    @PostMapping(value = "/api/card/getCardListByCardNoList")
    public ListResponse<WhiteCardCfgVo> getCardListByCardNoList(
        @RequestBody CardsParam cardsParam) {
        return new ListResponse<>(cardService.queryCardByCardNoList(cardsParam.getCardNos()));
    }

    /**
     * 根据Chip卡号获取卡信息
     *
     * @param cardChipNo
     * @return
     */
    @ResponseBody
    @PostMapping("/api/card/queryCardByCardChipNo")
    public ObjectResponse<Card> queryCardByCardChipNo(String cardChipNo) {
        if (StringUtils.isNotBlank(cardChipNo)) {
            try {
                Card card = cardService.queryCardByCardChipNo(cardChipNo);
                return new ObjectResponse<>(card);
            } catch (Exception e) {
                throw new DcServiceException(e.getMessage());
            }
        } else {
            throw new DcServiceException("获取卡信息失败，卡号不能为空");
        }
    }

//    /**
//     * 卡充值
//     *
//     * @param commIdList  商户id列表
//     * @param operatorId  操作人id
//     * @param cardNo      充值卡号
//     * @param chargeMoney 充值金额（单位分）
//     * @return
//     * <AUTHOR>
//     */
//    @RequestMapping(value = "/api/card/rechargeAuthCard", method = RequestMethod.POST)
//    @ResponseBody
//    public ObjectResponse rechargeAuthCard(@RequestParam(value = "commIdList") List<Long> commIdList,
//                                           String operatorId, String cardNo, long chargeMoney) {
//        //参数检查
//        if (StringUtils.isBlank(cardNo)) {
//            throw new DcArgumentException("卡号不可以为空");
//        }
//        if (chargeMoney <= 0) {
//            throw new DcServiceException("充值金额必须大于0");
//        }
//
//        try {
//            ObjectResponse resultEntity = cardService.rechargerAuthCard(commIdList, operatorId, cardNo, chargeMoney);
//            return resultEntity;
//        } catch (ChargerlinkException e) {
//            throw new DcServiceException(e.getErrorMessage());
//        }
//    }


    /**
     * 更新卡余额
     *
     * @param card 卡信息
     * @return
     */
    @RequestMapping(value = "/api/card/updateCardBalance")
    public ObjectResponse<Integer> updateCardBalance(@RequestBody Card card) {

        try {
            int count = cardService.updateCardBalance(card);
            return new ObjectResponse<>(count);
        } catch (Exception e) {
            throw new DcServiceException(e.getMessage());
        }
    }

    /**
     * 当前商户子商户在线卡信息列表
     *
     * @param token
     * @return
     */
    @Operation(summary = "客户订单筛选--查询卡片信息列表")
    @RequestMapping(value = "/api/card/queryAllCardList")
    public ListResponse<CardVo> queryAllCardList(@Parameter(name = "token令牌") String token,
        @Parameter(name = "客户ID") String userId) {
        return cardService.queryAllCardList(token, userId);
    }

    //    /**
    //     * 根据卡号获取在线卡鉴权信息
    //     *
    //     * @param cardNo
    //     * @return
    //     */
    //    @ResponseBody
    //    @PostMapping("/api/card/getCardAuth")
    //    public ObjectResponse<AuthMediaResult> getCardAuth(@RequestParam(value = "cardNo") String cardNo) {
    //        if (StringUtils.isBlank(cardNo)) {
    //            throw new DcArgumentException("获取卡信息失败，卡号不能为空");
    //        }
    //        //try {
    //        AuthMediaResult authMediaResult = authFacade.getCardAuth(cardNo);
    //        //                Card card = cardService.queryCardVinByCardNo(cardNo);
    //        return new ObjectResponse<>(authMediaResult);
    //        //            }catch(Exception e){
    //        //                //return new FailResultEntityTmpl(e.getMessage());
    //        //                throw
    //        //            }
    //        //        } else {
    //        //            return new FailResultEntityTmpl("获取卡信息失败，卡号不能为空");
    //        //        }
    //    }


    /**
     * 订单筛选-根据VIN码列表查询车牌号列表
     *
     * @param cardNos
     * @return
     */
    @Operation(summary = "根据VIN码列表查询车牌号列表")
    @PostMapping(value = "/api/card/selectShipCardNoByCardNos")
    public ObjectResponse selectShipCardNoByCardNos(@RequestParam("cardNos") List<String> cardNos) {
        Map<String, Object> map = new HashMap<>() {{
            put("cardNos", cardNos);
        }};
        return cardService.selectShipCardNoByCardNos(map);
    }

    @PostMapping(value = "/api/card/getCardChipNoByCardNo")
    public ObjectResponse getCardChipNoByCardNo(@RequestParam("cardNo") String cardNo) {
        if (StringUtils.isBlank(cardNo)) {
            throw new DcArgumentException("获取物理卡号失败，卡号不能为空");
        }
        return cardService.getCardChipNoByCardNo(cardNo);
    }

    /**
     * 下发紧急充电卡（单场站） 重置密码
     *
     * @param token
     * @param cardChipNo
     * @return
     */
    @PostMapping(value = "/api/card/resetWhiteCardPwd")
    public BaseResponse resetWhiteCardPwd(String cardChipNo, String token) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        if (StringUtils.isBlank(cardChipNo)) {
            throw new DcArgumentException("卡号不能为空");
        }

        WhiteCardRequest whiteCardRequest = new WhiteCardRequest().setIsAbandon(false)
            .setIsResetPass(true)
            .setCardChipNo(cardChipNo);

        List<WhiteCardDto> whiteCardDtos = cardService.queryWhiteCardDtoMapList(whiteCardRequest);
        if (whiteCardDtos == null || whiteCardDtos.size() <= 0) {
            throw new DcServiceException("紧急充电卡记录不存在，请检查");
        }
        ListResponse<String> listResponse = bsBoxSettingFeignClient.sendWhiteCard(
            whiteCardDtos.get(0));
        FeignResponseValidate.check(listResponse);
        return listResponse;
    }

    /**
     * 下发紧急充电卡（单场站） 弃用cardChipNo
     *
     * @param token
     * @param cardChipNo
     * @return
     */
    @PostMapping(value = "/api/card/abandonWhiteCard")
    public BaseResponse abandonWhiteCard(String cardChipNo, String token) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        if (StringUtils.isBlank(cardChipNo)) {
            throw new DcArgumentException("卡号不能为空");
        }
        WhiteCardRequest whiteCardRequest = new WhiteCardRequest().setIsAbandon(true)
            .setIsResetPass(false)
            .setCardChipNo(cardChipNo);

        List<WhiteCardDto> whiteCardDtos = cardService.queryWhiteCardDtoMapList(whiteCardRequest);
        if (whiteCardDtos == null || whiteCardDtos.size() <= 0) {
            throw new DcServiceException("紧急充电卡记录不存在，请检查");
        }
        ListResponse<String> listResponse = bsBoxSettingFeignClient.sendWhiteCard(
            whiteCardDtos.get(0));
        FeignResponseValidate.check(listResponse);
        return listResponse;
    }

    /**
     * 单个场站（紧急卡号对应场站）下发紧急充电卡
     *
     * @param cardChipNo
     * @param token
     * @return
     */
    @PostMapping(value = "/api/card/sendWhiteCard")
    public BaseResponse sendWhiteCard(String cardChipNo, String token) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        if (StringUtils.isBlank(cardChipNo)) {
            throw new DcArgumentException("卡号不能为空");
        }

        WhiteCardRequest whiteCardRequest = new WhiteCardRequest().setIsAbandon(false)
            .setIsResetPass(false)
            .setCardChipNo(cardChipNo);

        List<WhiteCardDto> whiteCardDtos = cardService.queryWhiteCardDtoMapList(whiteCardRequest);
        if (whiteCardDtos == null || whiteCardDtos.size() <= 0) {
            throw new DcServiceException("紧急充电卡记录不存在，请检查");
        }
        ListResponse<String> listResponse = bsBoxSettingFeignClient.sendWhiteCard(
            whiteCardDtos.get(0));
        FeignResponseValidate.check(listResponse);
        return listResponse;
    }

    /**
     * 批量下发紧急充电卡（多场站）
     *
     * @param token
     * @return
     */
    @PostMapping(value = "/api/card/sendBatchWhiteCards")
    public BaseResponse sendBatchWhiteCards(@RequestParam("token") String token) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        WhiteCardRequest whiteCardRequest = new WhiteCardRequest().setIsAbandon(false)
            .setIsResetPass(false);
        List<WhiteCardDto> whiteCardDtos = cardService.queryWhiteCardDtoMapList(whiteCardRequest);
        if (whiteCardDtos == null || whiteCardDtos.size() <= 0) {
            throw new DcServiceException("紧急充电卡记录不存在，请检查");
        }
        ObjectResponse<Map<String, Object>> objectResponse = bsBoxSettingFeignClient.sendBatchWhiteCards(
            whiteCardDtos);
        FeignResponseValidate.check(objectResponse);
        return objectResponse;
    }

    /**
     * 多场站下多卡弃用，条件必须为(cardChipNoList必传，siteList必传,isAbandon为true) 不支持重置密码 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    @PostMapping("/api/card/sendBatchBySiteList")
    public BaseResponse sendBatchBySiteList(@RequestBody WhiteCardRequest whiteCardRequest) {
        whiteCardRequest.setIsAbandon(true);
        return cardService.sendBatchBySiteList(whiteCardRequest);
    }

    /**
     * 查询站点下的紧急充电卡 用于多场站下多卡弃用查询DTO时，条件必须为(cardChipNoList必传，siteList必传,isAbandon必传为true)
     * 用于单场站下发查询DTO时，条件为(cardChipNoList不传，site必传,isAbandon为false) 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    @PostMapping("/api/card/queryWhiteCardDtoBySiteList")
    public ListResponse<WhiteCardDto> queryWhiteCardDtoBySiteList(
        @RequestBody WhiteCardRequest whiteCardRequest) {
        return new ListResponse<>(cardService.queryWhiteCardDtoBySiteList(whiteCardRequest));
    }

    /**
     * 根据卡片id（用,分割），更新卡片状态 0x00为下发成功，其他为失败
     *
     * @param cardIds 【,】分割的卡片id
     * @param status
     */
    @PostMapping("/api/card/updateUrgencyCardStatus")
    public ObjectResponse<Integer> updateUrgencyCardStatus(@RequestParam("evseNo") String evseNo,
        @RequestParam("cardIds") String cardIds,
        @RequestParam("status") int status) {
        return new ObjectResponse<>(cardService.updateUrgencyCardStatus(evseNo, cardIds, status));
    }

    @PostMapping("/api/card/updBatchCardStatusByNos")
    public ObjectResponse<Integer> updBatchCardStatusByNos(
        @RequestParam("cardNos") List<String> cardNos,
        @RequestParam("status") int status) {
        return new ObjectResponse<>(cardService.updBatchCardStatusByNos(cardNos, status));
    }

    @GetMapping(value = "/api/card/queryCardNoListByBlocUserId")
    public ListResponse<String> queryCardNoListByBlocUserId(
        @RequestParam("blocUserId") Long blocUserId) {
        if (blocUserId == null) {
            throw new DcServiceException("参数异常");
        }
        return new ListResponse<>(cardService.queryCardNoListByBlocUserId(blocUserId));
    }

    @PostMapping(value = "/api/card/selectBlocUserNameByCardNos")
    public ListResponse<Card> selectBlocUserNameByCardNos(@RequestBody List<String> cardNos) {
        if (CollectionUtils.isEmpty(cardNos)) {
            throw new DcServiceException("参数异常");
        }
        return new ListResponse<>(cardService.selectBlocUserNameByCardNos(cardNos));
    }

    /**
     * 根据条件批量查询场站下的有效紧急卡数量
     *
     * @param siteIdList
     * @return
     */
    @PostMapping(value = "/api/card/findUrgencyCardNumCount")
    public ListResponse<SiteCardCount> findUrgencyCardNumCount(
        @RequestBody List<String> siteIdList) {
        return cardService.findUrgencyCardNumCount(siteIdList);
    }

    /**
     * 根据逻辑卡号获取适用的场站
     *
     * @param cardNo 逻辑卡号
     * @return
     */
    @GetMapping(value = "/api/card/online/site")
    public ListResponse<String> getStationsOfCard(@RequestParam("cardNo") String cardNo,
        @Parameter(name = "1、卡片，2、VIN码") @RequestParam(value = "type", required = false) Integer type,
        @Parameter(name = "idChain") @RequestParam(value = "idChain", required = false) String idChain) {

        List<String> stations = cardService.getStationsOfCard(cardNo, type, idChain);
        return new ListResponse<>(stations, (long) stations.size());
    }

    @GetMapping(value = "/api/card/user/own")
    public ObjectResponse<Boolean> ownCard(@RequestParam("userId") Long userId,
        @RequestParam("commIdChain") String commIdChain) {
        log.info(">> 判断用户是否存在有效VIN列表: userId={}, commIdChain={}", userId, commIdChain);
        Boolean result = cardService.ownCard(userId, commIdChain);
        log.info("<< result={}", result);
        return new ObjectResponse<>(result);
    }

    /**
     * 企业平台数据清洗 旧数据(在线卡，VIN码，授信客户)转移 自动创建一级组织 企业管理平台第一版（2020-02）上线时
     *
     * @return
     */
    @GetMapping(value = "/api/card/user/corpDataCleaning")
    public BaseResponse corpDataCleaning() {
        log.info("企业平台2020-02第一版上线——数据清洗开始!");
        cardService.corpDataCleaning();
        log.info("企业平台2020-02第一版上线——清洗完成!");
        return new BaseResponse();
    }

    @Operation(summary = "同步卡片适用范围")
    @GetMapping(value = "/api/card/user/syncCardSiteAuth")
    public BaseResponse syncCardSiteAuth() {
        cardService.syncCardSiteAuth();
        return RestUtils.success();
    }

    /**
     * @param siteId
     * @param commId
     * @return
     */
    @GetMapping(value = "/api/card/getEmergencyCardBySiteId")
    public ListResponse<String> getEmergencyCardBySiteId(@RequestParam("siteId") String siteId,
        @RequestParam("commId") Long commId) {
        log.info("参数，siteId={},commId={}", siteId, commId);
        if (siteId == null || commId == null) {
            throw new DcServiceException("请求参数不正确，请检查");
        }
        List<String> stations = cardService.getEmergencyCardBySiteId(siteId, commId);
        return new ListResponse<>(stations);
    }

    @GetMapping(value = "/api/card/getEmergencyCardByCorpId")
    public ObjectResponse<MoveCorpCardList> getEmergencyCardByCorpId(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        MoveCorpCardList stations = cardService.getEmergencyCardByCorpId(corpId, commId);
        return new ObjectResponse<>(stations);
    }

    @GetMapping(value = "/api/card/getMoveCorpCardAuthByCorpId")
    public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpCardAuthByCorpId(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "commId") Long commId) {
        log.info("corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        MoveCorpSiteAuthList stations = cardService.getMoveCorpCardAuthByCorpId(corpId, commId);
        return new ObjectResponse<>(stations);
    }

    @PostMapping(value = "/api/card/syncCardAmount")
    public Mono<ObjectResponse<Boolean>> syncCardAmount(
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "amount") BigDecimal amount,
        @RequestParam(value = "result") Integer result,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "userId") Long userId) {
        log.info("cardNo = {}, amount = {}, result = {}, topCommId = {}, userId = {}", cardNo,
            amount, result, topCommId, userId);

        IotAssert.isNotBlank(cardNo, "请传入逻辑卡号");
        IotAssert.isNotNull(amount, "请传入金额");

        return cardService.syncAmount(cardNo, amount, result, topCommId, userId);
    }

    @PostMapping(value = "/api/card/getSyncCardAmount")
    public Mono<ListResponse<CardAmountSyncVo>> getSyncCardAmount(
        @RequestBody CardAmountSyncParam param) {
        log.info("param = {}", JsonUtils.toJsonString(param));

        return cardService.getSyncCardAmount(param);
    }

    @PostMapping(value = "/api/card/getCardAmount")
    public Mono<ObjectResponse<BigDecimal>> getCardAmount(
        @RequestParam(value = "cardNo") String cardNo,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "cardMaker") Boolean cardMaker) {
        log.info("cardNo = {}, topCommId = {}, cardMaker = {}", cardNo, topCommId, cardMaker);
        IotAssert.isNotBlank(cardNo, "请传入逻辑卡号");
        IotAssert.isNotNull(topCommId, "请传入顶级商户");

        return cardService.getCardAmount(cardNo, topCommId, cardMaker);
    }

    /**
     * 按场站下发，传入siteId列表
     *
     * @param param
     * @return
     */
    @Operation(summary = "下发桩本地卡片认证到场站")
    @PostMapping(value = "/api/card/siteAuthCard")
    public ObjectResponse<Integer> siteAuthCard(@RequestBody SiteAuthCardParam param) {
        return RestUtils.buildObjectResponse(cardService.siteAuthCard(param));
    }

    /**
     * 卡本地鉴权批量下发，获取共同场站
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/api/card/getCardCommonSiteList")
    public ListResponse<SitePo> getCardCommonSiteList(ServerHttpRequest request,
        @RequestBody CardSearchParam param) {
        return cardService.getCommonSiteList(param);
    }

    @Operation(summary = "获取场站绑定的card列表")
    @GetMapping(value = "/api/card/getCardListBySiteId")
    public ListResponse<SiteAuthCardPo> getCardListBySiteId(@RequestParam("siteId") String siteId) {
        return RestUtils.buildListResponse(cardService.getCardListBySiteId(siteId));
    }

    @Operation(summary = "海外版，新增卡片并且发卡给用户")
    @PostMapping(value = "/api/card/commercial/addCard")
    public BaseResponse addEssCard(@RequestBody AddEssCardParam param) {
        log.info("海外版，新增卡片并且发卡给用户, param = {}", JsonUtils.toJsonString(param));
        cardService.addEssCard(param);
        return RestUtils.success();
    }

    /**
     * 海外版，条件查询在线卡列表
     *
     * @return
     */
    @RequestMapping(value = "/api/card/commercial/queryOnlineCardsByPage", method = RequestMethod.POST)
    public ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(ServerHttpRequest request,
        @RequestBody CardRequest cardRequest) {
        log.info("海外版, 查询在线卡列表, param={}", JsonUtils.toJsonString(cardRequest));
        return cardService.queryEssOnlineCardsByPage(cardRequest);
    }
}
