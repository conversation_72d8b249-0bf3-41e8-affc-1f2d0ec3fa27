package com.cdz360.biz.model.cus.auth.dto;


import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * CusAuthResThin
 *
 * @since 2019/5/27 11:19
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusAuthResBase extends BaseObject {
    private BigDecimal frozenAmount;
    private BigDecimal balance;//金额扣款账户总余额, 单位'元'
    private String carNo;
    private BigDecimal power;//电量扣款账户总余额, 单位'kwh'
}