package com.cdz360.biz.cus.service;


import com.cdz360.biz.model.cus.auth.dto.CusAuthReqEx;
import com.cdz360.biz.model.cus.auth.dto.CusAuthRes;
import reactor.core.publisher.Mono;


public interface IAuthService {

    /**
     * 鉴权
     *
     * @param cusAuthReqEx
     * @param passcode
     * @return
     */
    Mono<CusAuthRes> authentication(CusAuthReqEx cusAuthReqEx, String passcode);


    /**
     * 获取redis中的鉴权结果
     * @param cusAuthReqEx
     */
    String getAndDelAuthResult(CusAuthReqEx cusAuthReqEx);
}
