package com.cdz360.biz.model.cus.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "企业soc限制主策略")
public class SocMainStrategyPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = " 主策略名称")
	@Size(max = 255, message = "name 长度不能超过 255")
	private String name;

	private Long corpId;

	private Date createTime;

	private Date updateTime;


}
