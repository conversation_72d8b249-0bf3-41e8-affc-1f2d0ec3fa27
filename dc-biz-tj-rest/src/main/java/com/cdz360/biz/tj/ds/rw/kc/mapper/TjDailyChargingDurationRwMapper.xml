<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationRwMapper">

  <resultMap id="RESULT_TJ_DAILY_CHARGING_DURATION_PO" type="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="province" jdbcType="VARCHAR" property="province"/>
    <result column="city" jdbcType="VARCHAR" property="city"/>
    <result column="area" jdbcType="VARCHAR" property="area"/>
    <result column="provinceName" jdbcType="VARCHAR" property="provinceName"/>
    <result column="cityName" jdbcType="VARCHAR" property="cityName"/>
    <result column="areaName" jdbcType="VARCHAR" property="areaName"/>
    <result column="provinceFullName" jdbcType="VARCHAR" property="provinceFullName"/>
    <result column="cityFullName" jdbcType="VARCHAR" property="cityFullName"/>
    <result column="areaFullName" jdbcType="VARCHAR" property="areaFullName"/>
    <result column="firstYearThirdMonth" jdbcType="DECIMAL" property="firstYearThirdMonth"/>
    <result column="firstYearLastNinthMonth" jdbcType="DECIMAL" property="firstYearLastNinthMonth"/>
    <result column="secondYear" jdbcType="DECIMAL" property="secondYear"/>
    <result column="thirdYear" jdbcType="DECIMAL" property="thirdYear"/>
    <result column="fourthYear" jdbcType="DECIMAL" property="fourthYear"/>
    <result column="fifthYear" jdbcType="DECIMAL" property="fifthYear"/>
    <result column="sixthYear" jdbcType="DECIMAL" property="sixthYear"/>
    <result column="seventhYear" jdbcType="DECIMAL" property="seventhYear"/>
    <result column="eighthYear" jdbcType="DECIMAL" property="eighthYear"/>
    <result column="ninthYear" jdbcType="DECIMAL" property="ninthYear"/>
    <result column="tenthYear" jdbcType="DECIMAL" property="tenthYear"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_DAILY_CHARGING_DURATION_PO">
    select * from t_tj_daily_charging_duration where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjDailyChargingDuration"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo">
    insert into t_tj_daily_charging_duration (
    <if test="null != province">
      `province`,
    </if>
    <if test="null != city">
      `city`,
    </if>
    <if test="null != area">
      `area`,
    </if>
    <if test="null != provinceName">
      `provinceName`,
    </if>
    <if test="null != cityName">
      `cityName`,
    </if>
    <if test="null != areaName">
      `areaName`,
    </if>
    <if test="null != provinceFullName">
      `provinceFullName`,
    </if>
    <if test="null != cityFullName">
      `cityFullName`,
    </if>
    <if test="null != areaFullName">
      `areaFullName`,
    </if>
    <if test="null != firstYearThirdMonth">
      `firstYearThirdMonth`,
    </if>
    <if test="null != firstYearLastNinthMonth">
      `firstYearLastNinthMonth`,
    </if>
    <if test="null != secondYear">
      `secondYear`,
    </if>
    <if test="null != thirdYear">
      `thirdYear`,
    </if>
    <if test="null != fourthYear">
      `fourthYear`,
    </if>
    <if test="null != fifthYear">
      `fifthYear`,
    </if>
    <if test="null != sixthYear">
      `sixthYear`,
    </if>
    <if test="null != seventhYear">
      `seventhYear`,
    </if>
    <if test="null != eighthYear">
      `eighthYear`,
    </if>
    <if test="null != ninthYear">
      `ninthYear`,
    </if>
    <if test="null != tenthYear">
      `tenthYear`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (
    <if test="null != province">
      #{province},
    </if>
    <if test="null != city">
      #{city},
    </if>
    <if test="null != area">
      #{area},
    </if>
    <if test="null != provinceName">
      #{provinceName},
    </if>
    <if test="null != cityName">
      #{cityName},
    </if>
    <if test="null != areaName">
      #{areaName},
    </if>
    <if test="null != provinceFullName">
      #{provinceFullName},
    </if>
    <if test="null != cityFullName">
      #{cityFullName},
    </if>
    <if test="null != areaFullName">
      #{areaFullName},
    </if>
    <if test="null != firstYearThirdMonth">
      #{firstYearThirdMonth},
    </if>
    <if test="null != firstYearLastNinthMonth">
      #{firstYearLastNinthMonth},
    </if>
    <if test="null != secondYear">
      #{secondYear},
    </if>
    <if test="null != thirdYear">
      #{thirdYear},
    </if>
    <if test="null != fourthYear">
      #{fourthYear},
    </if>
    <if test="null != fifthYear">
      #{fifthYear},
    </if>
    <if test="null != sixthYear">
      #{sixthYear},
    </if>
    <if test="null != seventhYear">
      #{seventhYear},
    </if>
    <if test="null != eighthYear">
      #{eighthYear},
    </if>
    <if test="null != ninthYear">
      #{ninthYear},
    </if>
    <if test="null != tenthYear">
      #{tenthYear},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjDailyChargingDuration" parameterType="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo">
    update t_tj_daily_charging_duration set
    <if test="province != null">
      province = #{province},
    </if>
    <if test="city != null">
      city = #{city},
    </if>
    <if test="area != null">
      area = #{area},
    </if>
    <if test="provinceName != null">
      provinceName = #{provinceName},
    </if>
    <if test="cityName != null">
      cityName = #{cityName},
    </if>
    <if test="areaName != null">
      areaName = #{areaName},
    </if>
    <if test="provinceFullName != null">
      provinceName = #{provinceFullName},
    </if>
    <if test="cityFullName != null">
      cityName = #{cityFullName},
    </if>
    <if test="areaFullName != null">
      areaName = #{areaFullName},
    </if>
    <if test="firstYearThirdMonth != null">
      firstYearThirdMonth = #{firstYearThirdMonth},
    </if>
    <if test="firstYearLastNinthMonth != null">
      firstYearLastNinthMonth = #{firstYearLastNinthMonth},
    </if>
    <if test="secondYear != null">
      secondYear = #{secondYear},
    </if>
    <if test="thirdYear != null">
      thirdYear = #{thirdYear},
    </if>
    <if test="fourthYear != null">
      fourthYear = #{fourthYear},
    </if>
    <if test="fifthYear != null">
      fifthYear = #{fifthYear},
    </if>
    <if test="sixthYear != null">
      sixthYear = #{sixthYear},
    </if>
    <if test="seventhYear != null">
      seventhYear = #{seventhYear},
    </if>
    <if test="eighthYear != null">
      eighthYear = #{eighthYear},
    </if>
    <if test="ninthYear != null">
      ninthYear = #{ninthYear},
    </if>
    <if test="tenthYear != null">
      tenthYear = #{tenthYear},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjDailyChargingDuration">
    update t_tj_daily_charging_duration set
    enable = false, updateTime = now()
    where id = #{id}
  </update>


</mapper>

