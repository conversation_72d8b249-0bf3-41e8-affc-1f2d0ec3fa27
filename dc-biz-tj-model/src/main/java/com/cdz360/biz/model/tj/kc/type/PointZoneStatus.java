package com.cdz360.biz.model.tj.kc.type;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "分析点状态")
@Getter
public enum PointZoneStatus {
    INIT("初始化", "C2D1C2"),
    GRID_PLAN("网格规划", "D1F0FF"),
    CONTRACTED("已签约", "FFB3B3"),
    CONSTRUCTION("投建中", "FFF0B3"),
    OPERATION("已运营", "B3FFC2");

    private final String desc;
    private final String color;

    PointZoneStatus(String desc, String color) {
        this.desc = desc;
        this.color = color;
    }
}
