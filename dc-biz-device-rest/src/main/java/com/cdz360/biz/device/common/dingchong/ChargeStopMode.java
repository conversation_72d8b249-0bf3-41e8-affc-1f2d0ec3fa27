package com.cdz360.biz.device.common.dingchong;

import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * @Classname ChargeStopMode
 * @Description TODO
 * @Date 11/9/2019 2:03 PM
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ChargeStopMode extends BaseObject {
    private boolean amount;//是否支持按金额充
    private boolean kwh;//是否支持按电量充
    private boolean time;//是否支持按时间充
}