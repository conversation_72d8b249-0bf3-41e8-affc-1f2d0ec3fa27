<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyPo">
    <id column="no" jdbcType="VARCHAR" property="no"/>
    <result column="siteName" jdbcType="VARCHAR" property="siteName"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
    <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
    <result column="province" jdbcType="INTEGER" property="province"/>
    <result column="city" jdbcType="INTEGER" property="city"/>
    <result column="area" jdbcType="INTEGER" property="area"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="stationType" jdbcType="INTEGER" property="stationType"/>
    <result column="stationArea" jdbcType="VARCHAR" property="stationArea"/>
    <result column="contractTerm" jdbcType="DECIMAL" property="contractTerm"/>
    <result column="score" jdbcType="INTEGER" property="score"/>
    <result column="parkNum" jdbcType="INTEGER" property="parkNum"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="images" property="imageInfo"
      typeHandler="com.cdz360.biz.ds.trading.MybatisJsonTypeHandler"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="lastTime" jdbcType="TIMESTAMP" property="lastTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getByNo"
    resultMap="RESULT_TJ_SURVEY_PO">
    select * from t_tj_survey where no = #{no}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurvey"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyPo">
    insert into t_tj_survey (`no`,`siteName`,
    `uid`,
    `longitude`,
    `latitude`,
    `province`,
    `city`,
    `area`,
    `address`,
    `stationType`,
    `stationArea`,
    `contractTerm`,
    `score`,
    `parkNum`,
    <if test="null != power">
      `power`,
    </if>
    <if test="null != imageInfo">
      `images`,
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
      `remark`,
    </if>
    `createTime`,
    `lastTime`,
    `updateTime`)
    values (#{no},#{siteName},
    #{uid},
    #{longitude},
    #{latitude},
    #{province},
    #{city},
    #{area},
    #{address},
    #{stationType},
    #{stationArea},
    #{contractTerm},
    #{score},
    #{parkNum},
    <if test="null != power">
      #{power},
    </if>
    <if test="null != imageInfo">
      #{imageInfo, typeHandler=com.cdz360.biz.ds.trading.MybatisJsonTypeHandler},
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( remark )">
      #{remark},
    </if>
    now(),
    now(),
    now())
  </insert>

  <update id="updateTjSurvey" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyPo">
    update t_tj_survey set
    <if test="siteName != null">
      siteName = #{siteName},
    </if>
    <if test="uid != null">
      uid = #{uid},
    </if>
    <if test="longitude != null">
      longitude = #{longitude},
    </if>
    <if test="latitude != null">
      latitude = #{latitude},
    </if>
    <if test="province != null">
      province = #{province},
    </if>
    <if test="city != null">
      city = #{city},
    </if>
    <if test="area != null">
      area = #{area},
    </if>
    <if test="address != null">
      address = #{address},
    </if>
    <if test="stationType != null">
      stationType = #{stationType},
    </if>
    <if test="stationArea != null">
      stationArea = #{stationArea},
    </if>
    <if test="contractTerm != null">
      contractTerm = #{contractTerm},
    </if>
    <if test="score != null">
      score = #{score},
    </if>
    <if test="parkNum != null">
      parkNum = #{parkNum},
    </if>
    <if test="power != null">
      power = #{power},
    </if>
    <if test="imageInfo != null">
      images = #{imageInfo, typeHandler=com.cdz360.biz.ds.trading.MybatisJsonTypeHandler},
    </if>
    <if test="remark != null">
      remark = #{remark},
    </if>
    <if test="lastTime != null">
      lastTime = #{lastTime},
    </if>
    updateTime = now()
    where no = #{no}
  </update>


</mapper>

