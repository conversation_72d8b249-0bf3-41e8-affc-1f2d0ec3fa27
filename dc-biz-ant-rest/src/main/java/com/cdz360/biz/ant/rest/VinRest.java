package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.aop.CheckAuth;
import com.cdz360.biz.ant.aop.CheckToken;
import com.cdz360.biz.ant.service.VinService;
import com.cdz360.biz.ant.service.iot.IotEvseCfgService;
import com.cdz360.biz.ant.service.sysLog.CustomerSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteListVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.iot.vo.WhiteVin;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.vo.BatteryVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.rest.BaseController;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import java.util.Locale;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * VinRest vin码相关
 *
 * <AUTHOR>
 * @since 2019/5/14 13:45
 */
@Slf4j
@RestController
public class VinRest extends BaseController {


    @Autowired
    private VinService vinService;
    @Autowired
    private CustomerSysLogService customerSysLogService;

    @Autowired
    private IotEvseCfgService iotEvseCfgService;

    @RequestMapping(value = "/api/vin/create", method = RequestMethod.POST)
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "addVin")
    public BaseResponse create(ServerHttpRequest request,
        @RequestBody VinParam vinParam) {
        Locale locale = AntRestUtils.getLocale(request);
        vinParam.setLocale(locale);
        BaseResponse res = vinService.create(vinParam);
        customerSysLogService.createVin(vinParam.getVin().toUpperCase(), request);
        return res;
    }

    @RequestMapping(value = "/api/vin/delete", method = RequestMethod.POST)
    @CheckToken(check = "getToken4Aspect")
    public BaseResponse delete(ServerHttpRequest request,
//                                   @RequestParam("id") Long id,
        @RequestBody VinParam vinParam) {
        log.info("vinParam: {}", JsonUtils.toJsonString(vinParam));
        IotAssert.isTrue(StringUtils.isNotBlank(vinParam.getVin()), "VIN不能为空");
        if (vinParam.getCommId() == null) {
            vinParam.setCommId(AntRestUtils.getTopCommId(request));
        }
        BaseResponse res = vinService.delete(vinParam);
        customerSysLogService.deleteVin(vinParam.getVin().toUpperCase(), request);
        return res;
    }

    @RequestMapping(value = "/api/vin/update", method = RequestMethod.POST)
    @CheckToken(check = "getToken4Aspect")
    @CheckAuth(code = "startVin", codesOr = {"editVin"})

    public BaseResponse update(ServerHttpRequest request,
        @RequestBody VinParam vinParam) {
        log.info("vinParam: {}", JsonUtils.toJsonString(vinParam));
        IotAssert.isTrue(StringUtils.isNotBlank(vinParam.getType()), "操作类型不能为空");
        if (vinParam.getCommId() == null) {
            vinParam.setCommId(AntRestUtils.getTopCommId(request));
        }
        Locale locale = AntRestUtils.getLocale(request);
        vinParam.setLocale(locale);
        BaseResponse res = vinService.update(vinParam);
        customerSysLogService.modifyVin(vinParam.getType(), vinParam.getVin().toUpperCase(),
            request);
        return res;
    }

    @RequestMapping(value = "/api/vin/select", method = RequestMethod.GET)
    @CheckToken(check = "getToken4Aspect")
    public ListResponse<VinDto> select(ServerHttpRequest request,
        ServerWebExchange exh,
        @RequestParam(value = "userId", required = false) Long userId,
        @RequestParam(value = "startTime", required = false) String startTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "status", required = false) Integer status,
        @RequestParam(value = "vin", required = false) String vin,
        @RequestParam(value = "carNo", required = false) String carNo,
        @RequestParam(value = "userName", required = false) String userName,
        @RequestParam(value = "corpName", required = false) String corpName,
        @RequestParam(value = "siteName", required = false) String siteName,
        @Parameter(name = "所属商户") @RequestParam(value = "subCommId", required = false) Long subCommId,
        @RequestParam(value = "leaderCommId", required = false) Long leaderCommId) {
        OldPageParam page = getPage2(request, exh, false);
        String token = getToken2(request);

        VinSearchParam vinSearchParam = new VinSearchParam();
        vinSearchParam.setStartTime(startTime);
        vinSearchParam.setEndTime(endTime);
        vinSearchParam.setStatus(status);
        vinSearchParam.setVin(vin);
        vinSearchParam.setCarNo(carNo);
        vinSearchParam.setUserId(userId);
        vinSearchParam.setStart((long) (page.getPageNum() - 1) * page.getPageSize());
        vinSearchParam.setSize(page.getPageSize());
        vinSearchParam.setUserName(userName);
        vinSearchParam.setCorpName(corpName);
        vinSearchParam.setToken(token);
        vinSearchParam.setSubCommId(subCommId);
        vinSearchParam.setCommIdChain(super.getCommIdChain2(request));
        vinSearchParam.setSiteName(siteName);

        ListResponse<VinDto> res = vinService.select(vinSearchParam, leaderCommId);

        return res;
    }

    @RequestMapping(value = "/api/vin/exportVinList", method = RequestMethod.POST)
    @CheckToken(check = "getToken4Aspect")
    public Mono<ObjectResponse<ExcelPosition>> exportVinList(
        ServerHttpRequest request, ServerWebExchange exh,
        @RequestBody VinSearchParam vinSearchParam) {
        String token = getToken2(request);
        vinSearchParam.setToken(token);
        vinSearchParam.setCommIdChain(super.getCommIdChain2(request));

        Long sysUid = AntRestUtils.getSysUid(request);
        Locale locale = AntRestUtils.getLocale(request);
        // 如果是null就设置为null
        vinSearchParam.setLocale(locale);
        return vinService.exportVinList(sysUid, vinSearchParam, vinSearchParam.getLeaderCommId());
    }

    @GetMapping(value = "/api/vin/getVinDto2ById")
    public ObjectResponse<VinDto2> getVinDto2ById(ServerHttpRequest request,
        @RequestParam(value = "vinId") Long vinId) {
        log.info("getVinDto2ById vinId: {}", vinId);
        return vinService.getVinDto2ById(vinId, super.getCommIdChain2(request));
    }

    @GetMapping(value = "/api/vin/getBatteryVo")
    public ObjectResponse<BatteryVo> getBatteryVo(ServerHttpRequest request,
        @RequestParam(value = "vin") String vin) {
        log.info("getBatteryVo vin: {}", vin);
        return vinService.getBatteryVo(vin, super.getCommIdChain2(request));
    }

    /**
     * 查询当前商户子商户VIN码列表
     *
     * @return
     */
    @GetMapping(value = "/api/vin/selectAllVinList")
    public ListResponse<VinDto> selectAllVinList(@RequestParam("userId") Long userId,
        ServerHttpRequest request) {
        String token = this.getToken2(request);
        return vinService.selectAllVinList(token, userId);
    }

    /**
     * 企业切换商户时，获取VIN保留/移除授权场站记录
     *
     * @param corpId  企业id
     * @param commId  转到的目标商户id
     * @param request
     * @return
     */
    @GetMapping(value = "/api/vin/getMoveCorpVINByCorpId")
    public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpVINByCorpId(
        @RequestParam("corpId") Long corpId,
        @RequestParam("commId") Long commId,
        ServerHttpRequest request) {
        log.info("企业切换商户，VIN，corpId = {}, commId = {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        return vinService.getMoveCorpVINByCorpId(corpId, commId);

    }

    @Operation(summary = "本地VIN鉴权，获取桩绑定的vin")
    @GetMapping(value = "/api/vin/getVinListByEvse")
    public ObjectResponse<SiteAuthVinLogPo> getVinListByEvse(
        @RequestParam("evseId") String evseId) {
        return vinService.getVinListByEvse(evseId);
    }

    @Operation(summary = "本地VIN鉴权，获取场站绑定的vin列表(当前场站配置，可能不是实际生效的vin列表)")
    @GetMapping(value = "/api/vin/getVinListBySiteId")
    public ListResponse<SiteAuthVinPo> getVinListBySiteId(@RequestParam("siteId") String siteId) {
        return vinService.getVinListBySiteId(siteId);
    }

    @Operation(summary = "VIN本地鉴权场站列表")
    @GetMapping(value = "/api/vin/getSiteListByVin")
    public ListResponse<SiteListVo> getSiteListByVin(@RequestParam("vin") String vin,
        @RequestParam("start") Long start,
        @RequestParam("size") Long size) {
        return vinService.getSiteListByVin(vin, start, size);
    }

    @Operation(summary = "本地VIN下发到场站")
    @PostMapping(value = "/api/vin/vinSiteAuthSend")
    public BaseResponse getSiteListByVin(@RequestBody SiteAuthVinParam param) {
        log.info("下发本地VIN鉴权到场站: {}", JsonUtils.toJsonString(param));
        return vinService.siteAuthVin(param);
    }

    @Operation(summary = "触发桩实时获取VIN本地鉴权列表")
    @GetMapping(value = "/api/vin/getEvseVinAuthRealTime")
    public ObjectResponse<Boolean> getEvseVinAuthRealTime(@RequestParam("evseNo") String evseNo) {
        log.info("桩实时获取VIN本地鉴权列表: {}", evseNo);
        return new ObjectResponse<>(iotEvseCfgService.getEvseVinAuthCfgRealTime(evseNo));
    }

    @Operation(summary = "获取桩实时VIN本地鉴权列表")
    @GetMapping(value = "/api/vin/getEvseVinAuthCfg")
    public ListResponse<WhiteVin> getEvseVinAuthCfg(@RequestParam("evseNo") String evseNo) {
        log.info("桩实时获取VIN本地鉴权列表: {}", evseNo);
        // TODO
        return new ListResponse<>(iotEvseCfgService.getEvseVinAuthCfg(evseNo));
    }

//    @Operation(summary = "获取桩相关的场站VIN本地鉴权列表")
//    @GetMapping(value = "/api/vin/getSiteVinAuthCfg")
//    public ListResponse<String> getSiteVinAuthCfg(@RequestParam("evseNo") String evseNo) {
//        log.info("获取桩相关的场站VIN本地鉴权列表: {}", evseNo);
//        return new ListResponse<>(List.of());
//    }

    @Operation(summary = "本地VIN下发到单个场站")
    @PostMapping(value = "/api/vin/vinOneSiteAuthSend")
    public BaseResponse vinOneSiteAuthSend(@RequestBody SiteAuthVinParam param) {
        log.info("本地VIN下发到单个场站: {}", JsonUtils.toJsonString(param));
        return vinService.siteAuthVinOnSite(param);
    }

    /**
     * 本地VIN鉴权批量下发，获取共同场站
     *
     * @param request
     * @param param
     * @return
     */
    @PostMapping(value = "/api/vin/getCommonSiteList")
    public ListResponse<SitePo> getCommonSiteList(ServerHttpRequest request,
        @RequestBody VinParam param) {
        return vinService.getCommonSiteList(param);
    }
}