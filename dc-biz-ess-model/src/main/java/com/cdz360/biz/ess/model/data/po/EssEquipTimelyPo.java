package com.cdz360.biz.ess.model.data.po;

import com.cdz360.base.model.es.type.ChargeFlowType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "储能设备分时段明细数据")
public class EssEquipTimelyPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "时段开始时间")
    @NotNull(message = "startTime 不能为 null")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "时段结束时间")
    @NotNull(message = "endTime 不能为 null")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "储能设备dno")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 32, message = "essDno 长度不能超过 32")
    private String essDno;

    @ApiModelProperty(value = "设备序号,一般为pcs或电表dno")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 32, message = "dno 长度不能超过 32")
    private String dno;

    @ApiModelProperty(value = "场站ID，记录数据时储能设备对应的场站ID")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @ApiModelProperty(value = "时段电价名称,如: 尖、锋、平、谷")
    @Size(max = 32, message = "piName 长度不能超过 32")
    private String piName;

    @ApiModelProperty(value = "时段充放电设定. 0未知,1充电,2放电,3待机")
    private ChargeFlowType flowType;

    @ApiModelProperty(value = "时段内正向电量, 单位kwh")
    private BigDecimal inKwh;


    @ApiModelProperty(value = "正向电量计费金额,单位: 元")
    private BigDecimal inFee;
    @ApiModelProperty(value = "正向电量计费单价,单位: 元/kwh")
    private BigDecimal inPrice;
    @ApiModelProperty(value = "正向电量计费模板ID(t_template.id)")
    private Long inPriceId;
    @ApiModelProperty(value = "总正向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalInKwh;
    @ApiModelProperty(value = "时段内反向电量, 单位kwh")
    private BigDecimal outKwh;
    @ApiModelProperty(value = "反向电量计费金额,单位: 元")
    private BigDecimal outFee;
    @ApiModelProperty(value = "反向电量计费单价,单位: 元/kwh")
    private BigDecimal outPrice;

    @ApiModelProperty(value = "反向电量计费模板ID(t_template.id)")
    private Long outPriceId;


    @ApiModelProperty(value = "总反向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalOutKwh;

    @ApiModelProperty(value = "行内容是否已锁定(true：已锁定内容不可修改，false or null：未锁定内容可修改)")
    private Boolean lock;

    @ApiModelProperty(value = "是否有效")
    @NotNull(message = "enable 不能为 null")
    private Boolean enable;

    @ApiModelProperty(value = "记录创建时间")
    @NotNull(message = "createTime 不能为 null")
    private Date createTime;

    @ApiModelProperty(value = "记录最后修改时间")
    @NotNull(message = "updateTime 不能为 null")
    private Date updateTime;

}

