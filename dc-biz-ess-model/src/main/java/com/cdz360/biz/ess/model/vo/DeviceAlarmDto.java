package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.es.type.DeviceWarnType;
import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.biz.model.ess.type.EquipAlertStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备告警概要信息")
@Data
@Accessors(chain = true)
public class DeviceAlarmDto {

    @Schema(description = "告警编号")
    @JsonInclude(Include.NON_EMPTY)
    private String warnNo;

    @Schema(description = "告警目标设备上属告警类型: 逆变器/整流器")
    @JsonInclude(Include.NON_NULL)
    private DeviceWarnType warnType;

    @Schema(description = "告警状态码")
    @JsonInclude(Include.NON_NULL)
    private Long alarmCode;

    @Schema(description = "告警名称")
    @JsonInclude(Include.NON_EMPTY)
    private String alarmName;

    @Schema(title = "告警开始时间", description = "设备本地时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;


    @Schema(title = "告警结束时间", description = "设备本地时间")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 告警等级
     */
    @JsonInclude(Include.NON_NULL)
    private Integer level;

    @Schema(description = "设备编号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String dno;

    @Schema(description = "设备名称", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String deviceName;

    @Schema(description = "设备型号", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_EMPTY)
    private String deviceModel;

    @Schema(description = "设备状态.0,未知;1,在线;2,离线;99,下线")
    @JsonInclude(Include.NON_NULL)
    private EquipStatus status;

    @Schema(description = "设备告警状态: 0,未知;1,正常;2,异常")
    @JsonInclude(Include.NON_NULL)
    private EquipAlertStatus alarmStatus;

    @JsonInclude(Include.NON_EMPTY)
    private String siteId;

    @JsonInclude(Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "软件版本号")
    @JsonInclude(Include.NON_EMPTY)
    private String softVersion;

    @Schema(description = "设备品牌品牌")
    @JsonInclude(Include.NON_EMPTY)
    private String brand;
}
