<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.BsChargerQueryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.BsCharger">
        <id column="bc_id" property="bcId"/>
        <result column="box_code" property="boxCode"/>
        <result column="connector_id" property="connectorId"/>
        <result column="business_id" property="businessId"/>
        <result column="station_code" property="stationCode"/>
        <result column="current_type" property="currentType"/>
        <result column="qr_code" property="qrCode"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        bc_id AS bcId,
        box_code AS boxCode,
        connector_id AS connectorId,
        business_id AS businessId,
        station_code AS stationCode,
        current_type AS currentType,
        qr_code AS qrCode
    </sql>

    <!--获取枪头信息列表-->
    <select id="getChargerInfoList" resultType="com.cdz360.biz.device.business.entity.result.ChargerInfoVo">
        SELECT
        charger.bc_id AS bcId,
        charger.charger_name AS chargerName,
        charger.box_code AS deviceId,
        charger.connector_id AS connectorId,
        charger.business_id AS businessId,
        charger.station_code AS siteId,
        site.`name` AS siteName,
        charger.current_type AS currentType,
        charger.qr_code AS qrCode,
        charger.remark AS remark,
        box.template_name AS templateName,
        box.box_out_factory_code AS serialNumber,
        box.product_name AS productName,
        box.box_name AS evseName,
        box.charger_num AS chargerNum,
        box.status AS deviceStatus
        FROM d_charger.bs_charger charger
        LEFT JOIN d_charger.bs_box box ON charger.box_code = box.box_code
        LEFT JOIN d_charger.t_site site ON site.id = charger.station_code
        left join t_r_commercial comm on site.operate_id = comm.id
        <where>
            <if test="deviceId != null and deviceId != ''">
                AND charger.box_code = #{deviceId}
            </if>
            <if test="connectorId != null and connectorId != ''">
                AND charger.connector_id = #{connectorId}
            </if>
            <if test="serialNumber != null and serialNumber != ''">
                AND LOCATE(#{serialNumber}, box.box_out_factory_code)
            </if>
            <if test="evseName != null and evseName != ''">
                AND LOCATE(#{evseName},box.box_name)
            </if>
            <!--<if test="deviceStatus != null">-->
            <!--AND box.status = #{deviceStatus}-->
            <!--</if>-->
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(siteIds)">
                <foreach collection="siteIds" index="index" item="item" open=" AND charger.station_code in ("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
                and comm.idChain like CONCAT(#{commIdChain}, '%')
            </if>
            <if test="siteId != null and siteId != ''">
                AND charger.station_code = #{siteId}
            </if>
	        <if test="commercialIdList != null and commercialIdList.size()>0">
		        AND charger.business_id IN
		        <foreach collection="commercialIdList" item="item" index="index" separator="," open="(" close=")">
			        #{item}
		        </foreach>
	        </if>
            <if test="evseNoList != null and evseNoList.size()>0">
                AND box.box_out_factory_code IN
                <foreach collection="evseNoList" item="item" index="index" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if test="currentType != null">
                AND charger.current_type = #{currentType}
            </if>
            <if test="qrCode != null and qrCode != ''">
                AND charger.qr_code = #{qrCode}
            </if>
            <if test="keywords != null and keywords != ''">
                AND (
                charger.bc_id LIKE CONCAT('%', #{keywords}, '%')
                OR charger.box_code LIKE CONCAT('%', #{keywords}, '%')
                OR site.`name` LIKE CONCAT('%', #{keywords}, '%')
                OR LOCATE(#{keywords}, charger.qr_code)
                )
            </if>
        </where>
        <choose>
            <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
                <foreach item="sort" collection="sorts"
                         open="order by" separator="," close=" ">
                    ${sort.columnsString} ${sort.order}
                </foreach>
            </when>
            <otherwise>
                ORDER BY charger.bc_id DESC
            </otherwise>
        </choose>
    </select>

    <!--获取场站枪头信息列表-->
    <select id="getChargerListOfSite" resultType="com.cdz360.biz.device.business.entity.result.ChargerInfoVo">
        SELECT
        charger.bc_id AS bcId,
        charger.charger_name AS chargerName,
        charger.box_code AS deviceId,
        charger.connector_id AS connectorId,
        charger.business_id AS businessId,
        charger.station_code AS siteId,
        charger.current_type AS currentType,
        charger.qr_code AS qrCode,
        charger.remark AS remark,
        box.box_out_factory_code AS serialNumber,
        box.product_name AS productName,
        box.box_name AS evseName,
        box.status AS deviceStatus
        FROM d_charger.bs_charger charger
        LEFT JOIN d_charger.bs_box box ON charger.box_code = box.box_code
        where charger.station_code = #{siteId}
        ORDER BY charger.bc_id DESC
    </select>

    <!-- 查询枪名-->
    <select id="selectChargerNamesByBcIds" resultType="com.cdz360.biz.device.business.entity.result.ChargerInfoVo">
        select bc_id bcId, charger_name chargerName
        from bs_charger
        where 1=1
        <if test="bcIds!=null and bcIds.size()>0">
            AND bc_id in
            <foreach collection="bcIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 查询枪名-->
    <select id="selectChargerNamesByBusinessIds"
            resultType="com.cdz360.biz.device.business.entity.result.ChargerInfoVo">
        select bc_id bcId, charger_name chargerName
        from bs_charger
        where 1=1
        <if test="bcIds!=null and bcIds.size()>0">
            AND business_id in
            <foreach collection="businessIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!-- 根据枪头编号查询所属商户id-->
    <select id="getCommIdByPlugNo"
            resultType="java.lang.Long">
        SELECT
        Operate_Id
        FROM
        d_charger.t_site
        LEFT JOIN d_charger.bs_box ON bs_box.station_code = t_site.id
        LEFT JOIN d_charger.bs_charger ON bs_charger.box_code = bs_box.box_code
        WHERE
        concat( lpad( bs_charger.Current_Type, 2, '0' ), concat( evseNo, lpad( ConnectOr_Id, 2, '0' ) ) ) = #{plugNo}
    </select>
</mapper>
