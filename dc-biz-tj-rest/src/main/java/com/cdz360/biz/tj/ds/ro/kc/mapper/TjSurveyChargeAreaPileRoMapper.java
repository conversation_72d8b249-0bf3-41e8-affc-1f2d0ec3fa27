package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyChargeAreaPileRoMapper {

    TjSurveyChargeAreaPilePo getById(@Param("id") Long id);

    List<TjSurveyChargeAreaPilePo> findTjSurveyChargeAreaPileByAreaId(@Param("areaId") Long areaId);

    List<TjSurveyChargeAreaPilePo> findTjSurveyChargeAreaPileBySurveyNo(@Param("surveyNo") String surveyNo);

}

