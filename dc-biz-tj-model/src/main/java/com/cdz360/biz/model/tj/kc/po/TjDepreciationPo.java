package com.cdz360.biz.model.tj.kc.po;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建-数据字典-财务数据-设备折旧配置")
public class TjDepreciationPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "折旧年限")
    private Integer ageLimit;

    @Schema(description = "残值比例")
    private BigDecimal residualValue;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}

