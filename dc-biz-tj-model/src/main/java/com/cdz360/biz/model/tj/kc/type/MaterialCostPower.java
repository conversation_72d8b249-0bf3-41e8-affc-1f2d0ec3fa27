package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "功率")
@Getter
public enum MaterialCostPower implements DcEnum {

    // 充电站到电源走线功率 begin
    SITE_TO_POWER_SUPPLY_100(100, "100kW"),
    SITE_TO_POWER_SUPPLY_200(200, "200kW"),
    SITE_TO_POWER_SUPPLY_300(300, "300kW"),
    SITE_TO_POWER_SUPPLY_400(400, "400kW"),
    SITE_TO_POWER_SUPPLY_500(500, "500kW"),
    SITE_TO_POWER_SUPPLY_600(600, "600kW"),
    SITE_TO_POWER_SUPPLY_700(700, "700kW"),
    SITE_TO_POWER_SUPPLY_800(800, "800kW"),
    SITE_TO_POWER_SUPPLY_900(900, "900kW"),
    SITE_TO_POWER_SUPPLY_1000(1000, "1000kW"),
    SITE_TO_POWER_SUPPLY_1200(1200, "1200kW"),
    SITE_TO_POWER_SUPPLY_1600(1600, "1600kW"),
    SITE_TO_POWER_SUPPLY_3000(3000, "3000kW"),
    // 充电站到电源走线功率 end

    // 直流桩功率 begin
    DC_60(60, "60kW"),
    DC_80(80, "80kW"),
    DC_120(120, "120kW"),
    DC_160(160, "160kW"),
    DC_240(240, "240kW"),
    DC_480(480, "480kW"),
    // 直流桩功率 end

    // 交流桩功率 begin
    AC_7(7, "7kW"),
    // 交流桩功率 end
    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostPower(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostPower valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostPower flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return null;
    }

}
