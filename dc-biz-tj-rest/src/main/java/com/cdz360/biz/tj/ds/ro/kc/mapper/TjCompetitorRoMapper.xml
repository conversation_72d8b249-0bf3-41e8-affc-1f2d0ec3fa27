<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjCompetitorRoMapper">

  <resultMap id="RESULT_TJ_COMPETITOR_PO" type="com.cdz360.biz.model.tj.kc.po.TjCompetitorPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="logo" jdbcType="VARCHAR" property="logo"/>
    <result column="firstPullFinishTime" jdbcType="TIMESTAMP" property="firstPullFinishTime"/>
    <result column="enable" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_COMPETITOR_PO">
    select * from t_tj_competitor where id = #{id}
  </select>

  <sql id="FIND_ALL_SQL">
    <where>
      <if test="null != enable">
        enable = #{enable}
      </if>
    </where>
  </sql>

  <select id="findTjCompetitor"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam"
    resultMap="RESULT_TJ_COMPETITOR_PO">
    select competitor.*
    from t_tj_competitor competitor
    <include refid="FIND_ALL_SQL"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by id desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countTjCompetitor"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam"
    resultType="java.lang.Long">
    select count(competitor.id)
    from t_tj_competitor competitor
    <include refid="FIND_ALL_SQL"/>
  </select>

  <select id="selectCompetitorSiteBi"
    resultType="com.cdz360.biz.model.tj.kc.vo.CompetitorSiteBi">
    select pet.id, pet.name ,
    (select count(1) from t_tj_competitor_site pets
    left join t_tj_area_analysis_point ap on ap.analysisId = #{analysisId} and ap.num = ${num}
    left join t_tj_area_analysis ana on ana.id = ap.analysisId
    left join t_tj_area withinArea on withinArea.aid = ana.aid
    and ST_Within(pets.location, withinArea.geo)
    where ST_Distance_Sphere(pets.location, ap.location) <![CDATA[<=]]> (ap.radius * 1000)
    and withinArea.aid is not null and pets.competitorId = pet.id) siteNum
    from t_tj_competitor pet
    <where>
      <choose>
        <when test="@com.cdz360.base.utils.StringUtils@isNotBlank( petIdSetString )">
          and find_in_set(pet.id, #{petIdSetString})
        </when>
        <otherwise>
          and pet.id in (0)
        </otherwise>
      </choose>
    </where>
  </select>
  <select id="getOneByName"
    resultMap="RESULT_TJ_COMPETITOR_PO">
    select * from t_tj_competitor where name = #{name} limit 1
  </select>
</mapper>

