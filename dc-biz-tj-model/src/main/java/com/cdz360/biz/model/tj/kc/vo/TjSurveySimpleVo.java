package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "场站勘察记录")
public class TjSurveySimpleVo {

    @ApiModelProperty(value = "勘察编号")
    @NotNull(message = "no 不能为 null")
    @Size(max = 16, message = "no 长度不能超过 16")
    private String no;

    @ApiModelProperty(value = "场站名称")
    @NotNull(message = "siteName 不能为 null")
    @Size(message = "siteName 长度不能超过 128")
    private String siteName;

    @ApiModelProperty(value = "详细地址")
    @Size(max = 240, message = "address 长度不能超过 240")
    private String address;
}

