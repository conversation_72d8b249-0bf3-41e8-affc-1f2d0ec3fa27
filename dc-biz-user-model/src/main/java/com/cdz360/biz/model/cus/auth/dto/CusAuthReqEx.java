package com.cdz360.biz.model.cus.auth.dto;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.site.type.SitePayChannelType;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * CusAuthReqEx
 *  增加了gwid，为了把网关编号传给work，以实现[账号+网关号+桩号+枪号]为key
 * @since 2019/5/31 16:18
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)

public class CusAuthReqEx extends CusAuthReq {
    private String gwno;
    private EvseProtocolType evseProtocolType;

    private Long topCommId;
    @Schema(description = "场站ID. 场站ID不为空时必须传 siteCommId和siteFrozenAmount", example = "123")
    private String siteId;
    private Long siteCommId;

    @Schema(description = "场站设置的启动充电冬季金额", example = "20.00")
    private BigDecimal siteFrozenAmount;

    @Schema(description = "场站是否支持数币支付")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean siteEcny;

    @Schema(description = "场站支持的支付方式")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<SitePayChannelType> payTypes;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}