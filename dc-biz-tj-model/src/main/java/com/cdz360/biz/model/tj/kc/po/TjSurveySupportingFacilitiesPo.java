package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-标准设施")
public class TjSurveySupportingFacilitiesPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "车位类型（1：小车，2：大车）")
    private Integer carParkType;

    @ApiModelProperty(value = "车挡是否需要")
    private Boolean carGearNeed;

    @ApiModelProperty(value = "智能地锁是否需要")
    private Boolean intelligentLockNeed;

    @ApiModelProperty(value = "环氧地坪是否需要")
    private Boolean epoxyFlooringNeed;

    @ApiModelProperty(value = "地面硬化是否需要")
    private Boolean groundHardeningNeed;

    @ApiModelProperty(value = "消防系统是否需要")
    private Boolean fireProtectionSystemNeed;

    @ApiModelProperty(value = "照明类型（1：地上照明，2：地下照明，3：不需要）")
    private Integer lightingType;

    @ApiModelProperty(value = "监控系统是否需要")
    private Boolean monitoringSystemNeed;

    @ApiModelProperty(value = "雨棚是否需要")
    private Boolean canopyNeed;

    @ApiModelProperty(value = "道闸类型（1：道闸系统对接，2：道闸系统，3：不需要）")
    private Integer barrierGateType;

    @ApiModelProperty(value = "品牌形象（1：品牌形象RVI旗舰店，2：品牌形象RVI普通站，3：不需要）")
    private Integer brandImageType;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

