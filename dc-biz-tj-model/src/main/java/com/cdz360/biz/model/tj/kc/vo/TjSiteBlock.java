package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.biz.model.tj.kc.type.CableMaterial;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "场站内的充电区域")
public class TjSiteBlock {

    @Schema(description = "序号")
    private Integer idx;

    @Schema(description = "区域名称")
    private String name;

    @Schema(description = "是否有分支箱, true 有,其他都表示 无")
    private Boolean branchBox;

    @Schema(description = "主电缆长度")
    private Integer mainCableLength;

    @Schema(description = "主电缆材质")
    private CableMaterial mainCableMaterial;
}
