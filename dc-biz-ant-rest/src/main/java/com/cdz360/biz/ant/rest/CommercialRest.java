package com.cdz360.biz.ant.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.ant.service.CommercialService;
import com.cdz360.biz.ant.service.sysLog.CommercialSysLogService;
import com.cdz360.biz.ant.utils.AntRestUtils;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialSiteDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.param.*;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Operation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;

@RestController
@Slf4j
@Tag(name = "商户信息", description = "商户")
public class CommercialRest {

    @Autowired
    private CommercialService commercialService;
    @Autowired
    private CommercialSysLogService commercialSysLogService;


    @PostMapping("/api/comm/getCommTree")
    @Operation(summary = "获取商户树")
    public ObjectResponse<CommercialTreeNode> getCommTree(ServerHttpRequest request,
                                                          @RequestBody GetCommTreeParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        return commercialService.getCommTree(param);
    }

    @PostMapping("/api/comm/convertCommPayAndRefund")
    @Operation(summary = "切换商户在线充值，在线退款")
    public ObjectResponse<Commercial> convertCommPayAndRefund(
            ServerHttpRequest request,
            @RequestBody ConvertCommPayParam param) {
        log.debug("commId = {}", JsonUtils.toJsonString(param));
        commercialSysLogService.updateCommPayLog(param.getCommName(), request);
        return commercialService.convertCommPayAndRefund(param);
    }

    @Operation(summary = "切换企业在线充值")
    @GetMapping("/api/comm/convertCommCorpDeposit")
    public ObjectResponse<Commercial> convertCommCorpDeposit(
            ServerHttpRequest request, @RequestParam(value = "commId") Long commId) {
        log.info("切换企业在线充值: {}", LoggerHelper2.formatEnterLog(request));
        return commercialService.convertCommCorpDeposit(commId);
    }

    @Operation(summary = "切换企业在线退款")
    @GetMapping("/api/comm/convertCommCorpRefund")
    public ObjectResponse<Commercial> convertCommCorpRefund(
            ServerHttpRequest request, @RequestParam(value = "commId") Long commId) {
        log.info("切换企业在线退款: {}", LoggerHelper2.formatEnterLog(request));
        return commercialService.convertCommCorpRefund(commId);
    }

    @Operation(summary = "切换会员在线退款")
    @GetMapping("/api/comm/convertCommRefund")
    public ObjectResponse<Commercial> convertCommRefund(
            ServerHttpRequest request, @RequestParam(value = "commId") Long commId) {
        log.info("切换企业在线退款: {}", LoggerHelper2.formatEnterLog(request));
        return commercialService.convertCommRefund(commId);
    }

    @PostMapping("/api/comm/addComm")
    @Operation(summary = "新增商户")
    public ObjectResponse<Long> addComm(ServerHttpRequest request, @RequestBody AddCommercialParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        ObjectResponse<Long> res = this.commercialService.addComm(param);
        return res;
    }

    @PostMapping("/api/comm/commercial/addComm")
    @Operation(summary = "海外版新增商户")
    public ObjectResponse<Long> addEssComm(ServerHttpRequest request, @RequestBody AddCommercialParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        ObjectResponse<Long> res = this.commercialService.addEssComm(param);
        return res;
    }

    @PostMapping("/api/comm/updateCommInfo")
    @Operation(summary = "更新商户信息")
    public BaseResponse updateCommInfo(ServerHttpRequest request, @RequestBody UpdateCommercialParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        return commercialService.updateCommInfo(param);
    }

    @PostMapping("/api/comm/commercial/updateCommInfo")
    @Operation(summary = "海外版更新商户信息")
    public BaseResponse updateEssCommInfo(ServerHttpRequest request, @RequestBody UpdateCommercialParam param) {
        log.info(LoggerHelper2.formatEnterLog(request, false) + "param = {}", param);
        return commercialService.updateEssCommInfo(param);
    }

    @Operation(summary = "桩管家-监控中心-选择充电站（可能是商户）")
    @PostMapping("/api/comm/getCommercialSiteList")
    public ListResponse<CommercialSiteDto> getCommercialSiteList(ServerHttpRequest request,
                                                                 @RequestBody CommcialSiteParam param) {
        return commercialService.getCommercialSiteList(param);
    }

    @Operation(summary = "获取商户信息列表")
    @PostMapping("/api/comm/getCommList")
    public ListResponse<CommercialDto> getCommList(ServerHttpRequest request,
                                                   @RequestBody ListCommercialParam param) {
        if (Boolean.TRUE.equals(param.getIsQueryByChain())) {
            param.setCommIdChain(AntRestUtils.getCommIdChain(request));
        }
        return commercialService.getCommList(param);
    }

    @PostMapping("/api/comm/user/add")
    public BaseResponse userAdd(ServerHttpRequest request, @RequestBody TCommercialUser tcu) {
        BaseResponse res = commercialService.userAdd(AntRestUtils.getToken2(request), tcu);
        commercialSysLogService.commUserAddLog(tcu.getUsername(), request);
        return res;
    }

    @GetMapping("/api/comm/getCommercial")
    @Operation(summary = "获取商户信息")
    public ObjectResponse<Commercial> getCommercial(ServerHttpRequest request,
                                                     @RequestParam Long commId) {
        log.debug("commId = {}", commId);
        return commercialService.getCommercial(commId);
    }
}
