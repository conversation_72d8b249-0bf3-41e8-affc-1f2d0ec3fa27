package com.cdz360.biz.model.tj.kc.po;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建-数据字典-财务数据-日充电时长")
public class TjDailyChargingDurationPo implements Serializable {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省名称")
    private String provinceName;

    @Schema(description = "省全称")
    private String provinceFullName;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市名称")
    private String cityName;

    @Schema(description = "市全称")
    private String cityFullName;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区名称")
    private String areaName;

    @Schema(description = "区全称")
    private String areaFullName;

    @Schema(description = "第一年前三个月日有效时长")
    private BigDecimal firstYearThirdMonth;

    @Schema(description = "第一年后九个月日有效时长")
    private BigDecimal firstYearLastNinthMonth;

    @Schema(description = "第二年日有效时长")
    private BigDecimal secondYear;

    @Schema(description = "第三年日有效时长")
    private BigDecimal thirdYear;

    @Schema(description = "第四年日有效时长")
    private BigDecimal fourthYear;

    @Schema(description = "第五年日有效时长")
    private BigDecimal fifthYear;

    @Schema(description = "第六年日有效时长")
    private BigDecimal sixthYear;

    @Schema(description = "第七年日有效时长")
    private BigDecimal seventhYear;

    @Schema(description = "第八年日有效时长")
    private BigDecimal eighthYear;

    @Schema(description = "第九年日有效时长")
    private BigDecimal ninthYear;

    @Schema(description = "第十年日有效时长")
    private BigDecimal tenthYear;

    @Schema(description = "true有效;false删除")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}

