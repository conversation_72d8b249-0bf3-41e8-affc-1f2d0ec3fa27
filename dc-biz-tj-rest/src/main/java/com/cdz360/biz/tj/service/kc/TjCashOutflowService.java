package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ds.trading.ro.geo.ds.CityRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.DistrictRoDs;
import com.cdz360.biz.ds.trading.ro.geo.ds.ProvinceRoDs;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.geo.po.ProvincePo;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.type.CashOutflowType;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjCashOutflowRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationCoefficientRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDepreciationRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjCashOutflowRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjDailyChargingDurationCoefficientRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjDailyChargingDurationRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjDepreciationRwDs;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCashOutflowService {

    @Autowired
    private TjDepreciationRoDs tjDepreciationRoDs;

    @Autowired
    private TjDepreciationRwDs tjDepreciationRwDs;

    @Autowired
    private TjCashOutflowRoDs tjCashOutflowRoDs;

    @Autowired
    private TjCashOutflowRwDs tjCashOutflowRwDs;

    @Autowired
    private ProvinceRoDs provinceRoDs;

    @Autowired
    private CityRoDs cityRoDs;

    @Autowired
    private DistrictRoDs districtRoDs;

    public Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(Integer type) {
        List<TjCashOutflowPo> result = tjCashOutflowRoDs.findTjCashOutflow(type);
        return Mono.just(RestUtils.buildListResponse(result));
    }

    public Mono<TjCashOutflowPo> getTjCashOutflowById(Long id) {
        TjCashOutflowPo tjCashOutflowPo = tjCashOutflowRoDs.getById(id);
        IotAssert.isNotNull(tjCashOutflowPo, "通用现金流出配置唯一ID无效");
        return Mono.just(tjCashOutflowPo);
    }

    public Mono<TjCashOutflowPo> saveTjCashOutflow(TjCashOutflowPo tjCashOutflowPo) {
        changeTjCashOutflowPo(tjCashOutflowPo);
        if (null == tjCashOutflowPo.getId()) {
            tjCashOutflowRwDs.insertTjCashOutflow(tjCashOutflowPo);
        } else {
            tjCashOutflowRwDs.updateTjCashOutflow(tjCashOutflowPo);
        }
        return Mono.just(tjCashOutflowPo);
    }

    public Mono<TjCashOutflowPo> disableTjCashOutflow(Long id) {
        TjCashOutflowPo tjCashOutflowPo = tjCashOutflowRoDs.getById(id);
        IotAssert.isNotNull(tjCashOutflowPo, "通用现金流出配置唯一ID无效");

        if (Boolean.TRUE.equals(tjCashOutflowPo.getEnable())) {
            boolean b = tjCashOutflowRwDs.disableTjCashOutflow(id);
            log.debug("删除通用现金流出配置: {}", b);
        }

        return Mono.just(tjCashOutflowPo);
    }

    public Mono<TjDepreciationPo> findTjDepreciation() {
        TjDepreciationPo tjDepreciationPo = tjDepreciationRoDs.findTjDepreciation();
        IotAssert.isNotNull(tjDepreciationPo, "设备折旧配置无效");
        return Mono.just(tjDepreciationPo);
    }

    public Mono<TjDepreciationPo> saveTjDepreciation(TjDepreciationPo tjDepreciationPo) {
        if (null == tjDepreciationPo.getId()) {
            throw new DcServiceException("设备折旧ID不能为空");
        } else {
            tjDepreciationRwDs.updateTjDepreciation(tjDepreciationPo);
        }
        return Mono.just(tjDepreciationPo);
    }

    private TjCashOutflowPo changeTjCashOutflowPo(TjCashOutflowPo tjCashOutflowPo) {
        TjDepreciationPo tjDepreciationPo = tjDepreciationRoDs.findTjDepreciation();

        if (tjCashOutflowPo.getType() == CashOutflowType.CASH_FLOW.getCode()) {
            String channelAreaName = "其他";
            if (StringUtils.isNotEmpty(tjCashOutflowPo.getChannelArea())) {
                String[] channelArea = tjCashOutflowPo.getChannelArea().split(",");
                List<ProvincePo> provincePoList = provinceRoDs.listProvince();
                List<CityPo> cityPoList = cityRoDs.listCity();
                Map<String, ProvincePo> provincePoMap = provincePoList.stream().collect(
                    Collectors.toMap(ProvincePo::getCode, o -> o));
                Map<String, CityPo> cityPoMap = cityPoList.stream()
                    .collect(Collectors.toMap(CityPo::getCode, o -> o));

                if (channelArea.length == 1) {
                    channelAreaName = provincePoMap.get(channelArea[0]).getFullname();
                } else if (channelArea.length == 2) {
                    channelAreaName =
                        provincePoMap.get(channelArea[0]).getFullname() + "," + cityPoMap.get(
                            channelArea[1]).getFullname();
                } else {
                    DistrictPo districtPo = districtRoDs.getDistrict(channelArea[2]);
                    channelAreaName =
                        provincePoMap.get(channelArea[0]).getFullname() + "," + cityPoMap.get(
                            channelArea[1]).getFullname() + "," + districtPo.getFullname();
                }
            }
            tjCashOutflowPo.setChannelAreaName(channelAreaName);
        }
        if (tjCashOutflowPo.getType() == CashOutflowType.CASH_MAINT.getCode()) {
            if (tjDepreciationPo.getAgeLimit() == 1) {
                tjCashOutflowPo.setSecondYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setThirdYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setFourthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setFifthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setSixthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getFirstYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 2) {
                tjCashOutflowPo.setThirdYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setFourthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setFifthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setSixthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getSecondYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 3) {
                tjCashOutflowPo.setFourthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setFifthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setSixthYearRate(tjCashOutflowPo.getThirdYearRate());
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getThirdYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getFirstYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 4) {
                tjCashOutflowPo.setFifthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setSixthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getThirdYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getFourthYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getSecondYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 5) {
                tjCashOutflowPo.setSixthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getThirdYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getFourthYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getFifthYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 6) {
                tjCashOutflowPo.setSeventhYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getThirdYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getFourthYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 7) {
                tjCashOutflowPo.setEighthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getSecondYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getThirdYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 8) {
                tjCashOutflowPo.setNinthYearRate(tjCashOutflowPo.getFirstYearRate());
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getSecondYearRate());
            }
            if (tjDepreciationPo.getAgeLimit() == 9) {
                tjCashOutflowPo.setTenthYearRate(tjCashOutflowPo.getFirstYearRate());
            }
        }
        return tjCashOutflowPo;
    }

}
