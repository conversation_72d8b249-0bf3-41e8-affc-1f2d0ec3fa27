package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.BsBox;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 设备管理：盒子 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
@Mapper
public interface BsBoxManageMapper extends BaseMapper<BsBox> {

    /**
     * 通过deviceCode集合批量更新盒子心跳时间和状态
     *
     * @param deviceIdList
     * @param status
     * @param lastHeartbeatTime
     * @return
     * @throws Exception
     */
//    Integer updateBatchDeviceStatus(@Param("deviceIdList") List<String> deviceIdList,
//                                    @Param("status") Integer status,
//                                    @Param("lastHeartbeatTime") Long lastHeartbeatTime);

    /**
     * 根据站点ID，修改站点下所有设备支持的客户端类型
     *
     * @param stationCode
     * @param clientVersion
     * @return
     */
    Integer updateClientVersionByStationCode(@Param("stationCode") String stationCode, @Param("clientVersion") String clientVersion);

    /**
     * 获取设备简单列表
     *
     * @param siteId
     * @return
     */
    List<BoxInfoVo> getBoxSimpleList(@Param("siteId") String siteId);

    /**
     * 更新桩计费模板信息
     *
     * @param evseNo
     * @param templateId
     * @param templateName
     * @return
     */
    Integer updateEvseTemplateInfoByEvseNo(
            @Param("evseNo") String evseNo,
            @Param("templateId") Long templateId,
            @Param("templateName") String templateName);
}
