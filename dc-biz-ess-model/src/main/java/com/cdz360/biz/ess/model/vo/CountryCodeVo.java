package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "国家代码",
    externalDocs = @ExternalDocumentation(
        description = "iso-3166 Country Codes",
        url = "https://www.iso.org/obp/ui/#search"))
@Data
@Accessors(chain = true)
public class CountryCodeVo {

    @Schema(description = "国家代码英文显示", required = true)
    @JsonInclude(Include.NON_EMPTY)
    private String englishShortName;

    @Schema(description = "国家代码当地显现(根据语言调整)")
    @JsonInclude(Include.NON_EMPTY)
    private String localeShortName;

//    @Schema(description = "国家代码(iso-3166)中的Alpha-2 code", required = true)
//    @JsonInclude(Include.NON_EMPTY)
//    private String alpha2Code;

    @Schema(description = "国家代码(iso-3166)中的Alpha-3 code", required = true)
    @JsonInclude(Include.NON_EMPTY)
    private String alpha3Code;

//    @Schema(description = "国家代码(iso-3166)中的Numberic", required = true)
//    @JsonInclude(Include.NON_EMPTY)
//    private String numberic;

    public static CountryCodeVo builder(String enShortName, String alpha3Code) {
        return new CountryCodeVo()
            .setEnglishShortName(enShortName)
            .setLocaleShortName(enShortName)
            .setAlpha3Code(alpha3Code);
    }
}
