package com.cdz360.biz.device.business.entity.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * @ProjectName chargerlink-device
 * <AUTHOR>
 * @CreateDate 2019/9/16 14:30
 */

@ApiModel(value = "com.cdz360.biz.device.business.entity.po.UpdatePackageModule")
@Data
@Accessors(chain = true)
public class UpdatePackageModule implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private Long id;
    /**
     * 关联的包id
     */
    @ApiModelProperty(value = "关联的包id")
    private Long packageId;
    /**
     * 模块类型,如PC01,PC02,PC01-1
     */
    @ApiModelProperty(value = "模块类型,如PC01,PC02,PC01-1")
    private String moduleType;
    /**
     * 支持的硬件版本号
     */
    @ApiModelProperty(value = "支持的硬件版本号")
    private String hardware;
    /**
     * 支持的软件版本号
     */
    @ApiModelProperty(value = "支持的软件版本号")
    private String software;
    /**
     * 支持的定制版本号
     */
    @ApiModelProperty(value = "支持的定制版本号")
    private String customize;
    /**
     * 升级后的硬件版本号
     */
    @ApiModelProperty(value = "升级后的硬件版本号")
    private String targetHardware;
    /**
     * 升级后的软件版本号
     */
    @ApiModelProperty(value = "升级后的软件版本号")
    private String targetSoftware;
    /**
     * 升级后的定制版本号
     */
    @ApiModelProperty(value = "升级后的定制版本号")
    private String targetCustomize;
    /**
     * PC01下载地址
     */
    @ApiModelProperty(value = "PC01下载地址")
    private String link;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    private Date createTime;
}