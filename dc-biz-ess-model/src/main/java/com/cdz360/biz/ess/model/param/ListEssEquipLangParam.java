package com.cdz360.biz.ess.model.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询设备信息字段的多语言信息")
@Data
@Accessors(chain = true)
//@EqualsAndHashCode(callSuper = true)
public class ListEssEquipLangParam {

    @Schema(description = "设备编号,如 PCS dno")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @NotNull(message = "lang 不能为 null")
    @Size(max = 6, message = "lang 长度不能超过 6")
    @JsonInclude(Include.NON_EMPTY)
    private String lang;
}

