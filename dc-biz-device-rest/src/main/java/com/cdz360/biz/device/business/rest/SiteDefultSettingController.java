package com.cdz360.biz.device.business.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.device.business.constant.DbsUrlConstant;
import com.cdz360.biz.device.business.entity.request.SiteDefultSettingRequest;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.SiteDefultSettingService;
import com.cdz360.biz.device.business.util.DbsAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Map;

/**
 * @Classname SiteDefultSettingController
 * @Description 场站默认配置
 * @Date 2019/6/12
 * @Created by wangzheng
 */
@Slf4j
@RestController
@Deprecated
public class SiteDefultSettingController extends BaseController {

    @Autowired
    private SiteDefultSettingService siteDefultSettingService;

    /**
     * 分页查询对象列表
     *
     * @param request
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_SITE_DEFULT_SELECT_PAGE)
    public BaseResponse selectPageSiteDefultSetting(@RequestBody SiteDefultSettingRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置分页查询参数不能为空");
        Map<String, Object> resultmap = siteDefultSettingService.selectPageSiteDefultSetting(request);
        List<SiteDefultSettingVO> boxSettinglist = (List<SiteDefultSettingVO>) resultmap.get("rows");
        long total = Long.parseLong(resultmap.get("total").toString());
        return new ListResponse<>(boxSettinglist, total);
    }

    /**
     * 根据条件查询对象
     *
     * @param siteId
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_SITE_DEFULT_SELECT_BYSITE)
    public BaseResponse selectBySiteId(@PathVariable("siteId") String siteId) {
        log.info("selectBySiteId param = {}", siteId);
        SiteDefultSettingVO res = siteDefultSettingService.selectBySiteId(siteId);
        return new ObjectResponse<>(res);
    }

    /**
     * @param request 插入对象
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_SITE_DEFULT_INSERT)
    public BaseResponse insertSiteDefultSetting(@RequestBody SiteDefultSettingRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "场站默认配置插入数据不能为空");
        Integer res = siteDefultSettingService.insertSiteDefultSetting(request);
        if (res <= 0) {
            throw new DcServiceException("场站默认配置数据插入失败");
        }
        return new BaseResponse();
    }

    /**
     * 修改对象
     *
     * @param request
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_SITE_DEFULT_UPDATE)
    public BaseResponse modifySiteDefultSetting(@RequestBody SiteDefultSettingRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "场站默认配置修改请求参数为空");
        DbsAssert.isNotNull(request.getId(), "场站默认配置修改主键不能为空");
        try {
            String qrcodeUrl = request.getUrl();
            if (StringUtils.isNotBlank(qrcodeUrl) && !qrcodeUrl.endsWith("/")) {
                qrcodeUrl = qrcodeUrl.concat("/");
                request.setUrl(new URL(qrcodeUrl).toString());
            }
        } catch (MalformedURLException e) {
            throw new DcServiceException("二维码地址配置不规范");
        }
        Integer res = siteDefultSettingService.modifySiteDefultSetting(request);
        if (res <= 0) {
            throw new DcServiceException("场站默认配置数据修改失败，主键：" + request.getId());
        }
        return new BaseResponse();
    }

    /**
     * @param siteId 通过siteId删除对象
     * @return
     */
    @GetMapping(DbsUrlConstant.URL_SITE_DEFULT_DELETE)
    public BaseResponse deleteSiteDefultSetting(@PathVariable("siteId") String siteId) {
        log.info("param = {}", siteId);
        DbsAssert.isNotNull(siteId, "场站默认配置删除主键不能为空");
        Integer res = siteDefultSettingService.deleteSiteDefultSetting(siteId);
        return new BaseResponse();
    }
}