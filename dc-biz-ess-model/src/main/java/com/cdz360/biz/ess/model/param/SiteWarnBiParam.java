package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.param.SamplingParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "场站告警查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteWarnBiParam extends SamplingParam {

    @Schema(description = "场站ID")
    @JsonInclude(Include.NON_NULL)
    private String siteId;
}
