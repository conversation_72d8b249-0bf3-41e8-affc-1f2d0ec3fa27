<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.VinMapper">

  <sql id="selectVinColumns">
    v.id,
    v.vin,
    v.carNo,
    v.car_num as carNum,
    v.lineNum,
    v.name,
    v.commId,
    v.subCommId,
    (SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 2 AND account = v.vin AND topCommId
    = v.commId AND `enable`=TRUE ) as station,
    v.status,
    v.userId,
    v.corpId,
    v.modifyBy,
    v.enable,
    v.isPrimary,
    v.createTime,
    v.updateTime
  </sql>

  <insert id="insert" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.chargerlinkcar.framework.common.domain.vo.VinParam">
    insert into t_vin
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="vin != null">
        vin,
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isBlank( carNo ) == false">
        carNo,
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isBlank( carNum ) == false">
        car_num,
      </if>
      <if test="lineNum != null">
        lineNum,
      </if>
      <if test="carDepart != null">
        car_depart,
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
        brand,
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
        model,
      </if>
      <if test="carLength != null">
        carLength,
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( year )">
        `year`,
      </if>
      <if test="name != null">
        `name`,
      </if>
      status,
      <if test="commId != null">
        commId,
      </if>
      <if test="subCommId != null">
        subCommId,
      </if>
      <if test="userId != null">
        userId,
      </if>
      <if test="corpId != null">
        corpId,
      </if>
      <if test="modifyBy != null">
        modifyBy,
      </if>
      <if test="isPrimary != null">
        isPrimary,
      </if>
      enable,
      createTime,
      updateTime,
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="vin != null">
        UPPER(#{vin,jdbcType=VARCHAR}),
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isBlank( carNo ) == false">
        UPPER(#{carNo,jdbcType=VARCHAR}),
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isBlank( carNum ) == false">
        #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="lineNum != null">
        #{lineNum,jdbcType=VARCHAR},
      </if>
      <if test="carDepart != null">
        #{carDepart,jdbcType=VARCHAR},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
        #{brand},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
        #{model},
      </if>
      <if test="carLength != null">
        #{carLength},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( year )">
        #{year},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      1,
      <if test="commId != null">
        #{commId,jdbcType=BIGINT},
      </if>
      <if test="subCommId != null">
        #{subCommId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=BIGINT},
      </if>
      <if test="modifyBy != null">
        #{modifyBy,jdbcType=BIGINT},
      </if>
      <if test="isPrimary != null">
        #{isPrimary,jdbcType=TINYINT},
      </if>
      <choose>
        <when test="enable == null">
          1,
        </when>
        <otherwise>
          #{enable,jdbcType=TINYINT},
        </otherwise>
      </choose>
      now(),
      now(),
    </trim>
  </insert>
  <insert id="insertBatch">
    INSERT INTO t_vin
    (vin, car_depart, carNo, car_num, lineNum,
    brand, model, carLength, year,
    commId, subCommId, status,userId,corpId,enable,createTime,updateTime)
    VALUES
    <foreach collection="vins" item="item" index="index" separator=",">
      (#{item.vin}, #{item.carDepart}, #{item.carNo}, #{item.carNum}, #{item.lineNum},
      #{item.brand}, #{item.model}, #{item.carLength}, #{item.year},
      #{item.commId}, #{item.subCommId}, 1,#{item.userId},#{item.corpId},1,now(),now())
    </foreach>
  </insert>

  <update id="update" parameterType="com.chargerlinkcar.framework.common.domain.vo.VinParam">
    update t_vin
    <set>
      <if test="vin != null">
        vin = UPPER(#{vin,jdbcType=VARCHAR}),
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isBlank( carNo ) == false">
        carNo = UPPER(#{carNo,jdbcType=VARCHAR}),
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="commId != null">
        commId = #{commId,jdbcType=BIGINT},
      </if>
      <if test="subCommId != null">
        subCommId = #{subCommId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        userId = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corpId = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="modifyBy != null">
        modifyBy = #{modifyBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=TINYINT},
      </if>
      <if test="isPrimary != null">
        isPrimary = #{isPrimary,jdbcType=TINYINT},
      </if>
      <if test="lineNum != null">
        lineNum = #{lineNum,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null">
        car_num = #{carNum,jdbcType=VARCHAR},
      </if>
      <if test="carNo != null">
        carNo = #{carNo,jdbcType=VARCHAR},
      </if>
      <if test="carDepart != null">
        car_depart = #{carDepart,jdbcType=VARCHAR},
      </if>
      <if test="brand != null">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="model != null">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="carLength != null">
        carLength = #{carLength},
      </if>
      <if test="isPrimary != null">
        isPrimary = #{isPrimary},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( year )">
        `year` = #{year,jdbcType=VARCHAR},
      </if>
      updateTime = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByVinAndCommAndCorpOnCorp"
    parameterType="com.chargerlinkcar.framework.common.domain.vo.VinParam">
    update t_vin
    <set>
      <if test="vin != null">
        vin = UPPER(#{vin,jdbcType=VARCHAR}),
      </if>
      <if test="carNo != null">
        carNo = UPPER(#{carNo,jdbcType=VARCHAR}),
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="subCommId != null">
        subCommId = #{subCommId,jdbcType=BIGINT},
      </if>
      <if test="userId != null">
        userId = #{userId,jdbcType=VARCHAR},
      </if>
      <if test="modifyBy != null">
        modifyBy = #{modifyBy,jdbcType=BIGINT},
      </if>
      <if test="enable != null">
        enable = #{enable,jdbcType=TINYINT},
      </if>
      <if test="lineNum != null">
        lineNum = #{lineNum,jdbcType=VARCHAR},
      </if>
      <if test="carNum != null">
        car_num = UPPER(#{carNum,jdbcType=VARCHAR}),
      </if>
      <if test="carDepart != null">
        car_depart = #{carDepart,jdbcType=VARCHAR},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( brand )">
        brand = #{brand,jdbcType=VARCHAR},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( model )">
        model = #{model,jdbcType=VARCHAR},
      </if>
      <if test="carLength != null">
        carLength = #{carLength},
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( year )">
        `year` = #{year,jdbcType=VARCHAR},
      </if>
      updateTime = now()
    </set>
    where corpId = #{corpId}
    and commId = #{commId} and id = #{id}
  </update>

  <update id="delete">
    update t_vin
    <set>
      <if test="modifyBy != null and modifyBy != ''">
        modifyBy = #{modifyBy,jdbcType=BIGINT},
      </if>
      enable = 0,
      updateTime = now()
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!--可用场站数不包括已删除的场站-->
  <select id="select" parameterType="com.cdz360.biz.cus.domain.vo.VinSearchParamComm"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    v.id as id,
    v.vin,
    v.carNo as carNo,
    v.car_num as carNum,
    v.car_depart as carDepart,
    v.lineNum as lineNum,
    v.brand,
    v.model,
    v.carLength,
    v.year,
    v.name as name,
    v.commId as commId,
    v.subCommId as subCommId,
    any_value(v.status) as status,
    v.userId as userId,
    v.corpId as corpId,
    d.bloc_user_name AS corpName,
    v.modifyBy as modifyBy,
    v.enable as enable,
    v.createTime as createTime,
    v.updateTime as updateTime
    from
    (
    select distinct auth.account,auth.topCommId from t_site site left join t_site_auth auth on
    site.id = auth.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where 1=1 and auth.enable = 1 and auth.type = 2
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(siteName )">
      and site.name like CONCAT('%',#{siteName}, '%') and auth.enable = true
    </if>
    ) site
    LEFT JOIN t_vin v ON V.vin = site.account
    LEFT JOIN t_bloc_user d ON v.corpId = d.id

    <where>
      v.enable=1
      and site.topCommId = v.commId
      and v.userId=#{userId}

      <if test="startTime != null and startTime != ''">
        and v.createTime>=str_to_date(#{startTime}, '%Y-%m-%d')
      </if>
      <if test="endTime != null and endTime != ''">
        and DATE_ADD(#{endTime}, INTERVAL 1 day)>=v.createTime
      </if>
      <if test="status != null">
        and v.status = #{status}
      </if>
      <if test="vin != null and vin != ''">
        and UPPER(v.vin) like CONCAT('%', UPPER(#{vin}), '%')
      </if>
      <if test="carNo != null and carNo != ''">
        and UPPER(v.carNo) like CONCAT('%', UPPER(#{carNo}), '%')
      </if>
      <if test="subCommId != null">
        and v.subCommId = #{subCommId}
      </if>
      <if test="subCommIds != null and subCommIds.size()>0">
        and v.subCommId in
        <foreach collection="subCommIds" index="index" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(vinList)">
        <foreach collection="vinList" item="vin" open=" AND v.vin in ("
          separator="," close=")">
          #{vin}
        </foreach>
      </if>
    </where>
    order by v.id desc
  </select>

  <!--可用场站数不包括已删除的场站-->
  <select id="selectComm" parameterType="com.cdz360.biz.cus.domain.vo.VinSearchParamComm"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    v.id,
    v.vin,
    v.carNo,
    v.car_num as carNum,
    v.car_depart as carDepart,
    v.lineNum,
    v.brand,
    v.model,
    v.carLength,
    v.year,
    v.name,
    v.commId,
    v.subCommId,

    v.status,
    v.userId,
    v.modifyBy,
    v.enable,
    v.createTime,
    v.updateTime,
    b.username as userName,
    v.corpId AS corpId,
    d.bloc_user_name AS corpName
    from
    (
    select distinct auth.account,auth.topCommId from t_site site left join t_site_auth auth on
    site.id = auth.siteId
    left join t_r_commercial comm on comm.id = site.commId
    where 1=1 and auth.enable = 1 and auth.type = 2
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(siteName )">
      and site.name like CONCAT('%',#{siteName}, '%') and auth.enable = true
    </if>
    ) site
    LEFT JOIN t_vin v ON v.vin = site.account
    LEFT JOIN t_user b on v.userId=b.id
    LEFT JOIN t_bloc_user d ON v.corpId = d.id

    <where>
      v.enable=1
      and site.topCommId = v.commId
      <if test="userId != null">
        and v.userId=#{userId}
      </if>
      <if test="commIds != null and commIds.size()>0">
        AND v.commId in
        <foreach collection="commIds" index="index" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(vinList)">
        <foreach collection="vinList" item="vin" open=" AND v.vin in ("
          separator="," close=")">
          #{vin}
        </foreach>
      </if>
      <if test="startTime != null and startTime != ''">
        and v.createTime>=str_to_date(#{startTime}, '%Y-%m-%d')
      </if>
      <if test="endTime != null and endTime != ''">
        and DATE_ADD(#{endTime}, INTERVAL 1 day)>=v.createTime
      </if>
      <if test="status != null">
        and v.status = #{status}
      </if>
      <if test="vin != null and vin != ''">
        and UPPER(v.vin) like CONCAT('%', UPPER(#{vin}), '%')
      </if>
      <if test="carNo != null and carNo != ''">
        and UPPER(v.carNo) like CONCAT('%', UPPER(#{carNo}), '%')
      </if>
      <if test="corpName != null and corpName != ''">
        and d.bloc_user_name like CONCAT('%', UPPER(#{corpName}), '%')
      </if>
      <if test="subCommId != null">
        and v.subCommId = #{subCommId}
      </if>
      <if test="subCommIds != null and subCommIds.size()>0">
        and v.subCommId in
        <foreach collection="subCommIds" index="index" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="userName != null and userName != ''">
        and UPPER(b.userName) like CONCAT('%', UPPER(#{userName}), '%')
      </if>
    </where>
    group by v.id
    order by v.id desc
  </select>

  <select id="selectVinOnCorp"
    parameterType="com.chargerlinkcar.framework.common.domain.vo.VinParam"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    v.id,v.vin,v.carNo,v.car_num as carNum,v.lineNum,v.name,v.commId,v.subCommId,v.car_depart as
    carDepart,
    v.brand, v.model, v.carLength, v.year,
    v.status,v.userId,v.corpId,v.modifyBy,v.enable,v.createTime,v.updateTime,rbu.phone as mobile
    from t_vin v

    LEFT JOIN t_r_bloc_user rbu on v.userId=rbu.user_id and v.corpId = rbu.bloc_user_id
    WHERE
    v.`enable`=1
    and rbu.`status`=1
    and v.corpId = #{corpId}
    <if test="status != null">
      and v.status = #{status}
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(uidList)">
      <foreach collection="uidList" item="uid" open=" AND v.userId in ("
        separator="," close=")">
        #{uid}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(orgIds)">
      <foreach collection="orgIds" item="orgId" open=" AND rbu.corpOrgId in ("
        separator="," close=")">
        #{orgId}
      </foreach>
    </if>
    <if test="vin != null and vin != ''">
      and UPPER(v.vin) like CONCAT('%', UPPER(#{vin}), '%')
    </if>
    <if test="carNo != null and carNo != ''">
      and UPPER(v.carNo) like CONCAT('%', UPPER(#{carNo}), '%')
    </if>
    <if test="lineNum != null and lineNum != ''">
      and lineNum like CONCAT('%', #{lineNum}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(carDepart)">
      and v.car_depart like concat('%',#{carDepart},'%')
    </if>
    <if test="carNum != null and carNum != ''">
      and UPPER(v.car_num) like CONCAT('%', UPPER(#{carNum}), '%')
    </if>
    <if test="mobile != null and mobile != ''">
      and rbu.phone like CONCAT('%', #{mobile}, '%')
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(vinList)">
      <foreach collection="vinList" item="item" open=" AND v.vin in ("
        separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="null != excludeSocStrategyId">
      and v.id not in (select vinId from t_user_soc_strategy where strategyId =
      #{excludeSocStrategyId})
    </if>
    order by v.id desc
  </select>


  <select id="selectCountByCommIdSinUserId"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v
    where v.commId = #{commId}
    and v.userId <![CDATA[ <> ]]> #{userId}
    and v.vin = #{vin}
  </select>

  <select id="selectCountByCommIdConUserId"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v where v.commId=#{commId} and v.userId=#{userId} and v.vin=#{vin}
  </select>

  <resultMap id="VIN_RESULT_WITH_AUTH_SITE_LIST"
    type="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    <result column="id" jdbcType="BIGINT" property="id"/>
    <result column="vin" jdbcType="VARCHAR" property="vin"/>
    <result column="carNo" jdbcType="VARCHAR" property="carNo"/>
    <result column="carNum" jdbcType="VARCHAR" property="carNum"/>
    <result column="carDepart" jdbcType="VARCHAR" property="carDepart"/>
    <result column="lineNum" jdbcType="VARCHAR" property="lineNum"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="status" jdbcType="INTEGER" property="status"/>
    <result column="commId" jdbcType="BIGINT" property="commId"/>
    <result column="subCommId" jdbcType="BIGINT" property="subCommId"/>
    <result column="userId" jdbcType="BIGINT" property="userId"/>
    <result column="corpId" jdbcType="BIGINT" property="corpId"/>
    <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
    <result column="userName" jdbcType="VARCHAR" property="userName"/>
    <result column="rBlocUserId" jdbcType="BIGINT" property="rBlocUserId"/>
    <collection property="stationList" ofType="java.lang.String">
      <result column="siteId" property="value"/>
    </collection>
  </resultMap>

  <select id="checkVinCount" resultType="java.lang.Long">
    select count(1)
    from t_vin vn
    where
      vn.`enable` = 1
      and vn.`status` = 1
      and vn.vin = #{vin}
      and vn.commId = #{topCommId}
  </select>

  <select id="selectByVinWithAuth"
    resultMap="VIN_RESULT_WITH_AUTH_SITE_LIST"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    vn.id,
    vn.vin,
    vn.carNo,
    vn.car_num carNum,
    vn.car_depart carDepart,
    vn.lineNum,
    vn.brand,
    vn.model,
    vn.carLength,
    vn.year,
    vn.name,
    sa.siteId,
    vn.status,
    vn.commId,
    vn.subCommId,
    vn.userId,
    vn.corpId,
    user.phone as mobile,
    user.username as userName,
    rbu.id as rBlocUserId
    from t_vin vn
    left join t_user user on vn.userId = user.id
    LEFT JOIN t_r_bloc_user rbu ON vn.corpId = rbu.bloc_user_id AND vn.userId = rbu.user_id AND
    rbu.`status` = 1
    left join t_site_auth sa on sa.account = vn.vin and sa.topCommId = vn.commId
    where vn.`enable` = 1
    and vn.`status` = 1
    and sa.type = 2
    and sa.enable = TRUE
    and vn.vin=#{vin}
    and vn.commId=#{commId}
  </select>

  <select id="selectByVin"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    vn.id,
    vn.vin,
    vn.carNo,
    vn.car_num carNum,
    vn.car_depart carDepart,
    vn.lineNum,
    vn.brand,
    vn.model,
    vn.carLength,
    vn.year,
    vn.name,
    (SELECT group_concat(siteId) FROM t_site_auth WHERE type = 2 AND account = vn.vin AND topCommId
    = vn.commId AND `enable`=TRUE ) as station,
    vn.status,
    vn.commId,
    vn.subCommId,
    vn.userId,
    vn.corpId,
    user.phone as mobile,
    user.username as userName,
    rbu.id as rBlocUserId
    from t_vin vn
    left join t_user user on vn.userId = user.id
    LEFT JOIN t_r_bloc_user rbu ON vn.corpId = rbu.bloc_user_id AND vn.userId = rbu.user_id AND
    rbu.`status` = 1
    where vn.`enable` = 1
    and vn.`status` = 1
    and vn.vin=#{vin}
    <if test="commId != null">
      and vn.commId=#{commId}
    </if>
    limit 1
  </select>

  <select id="getVinByUid"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    vn.id,
    vn.vin,
    vn.carNo,
    vn.car_num carNum,
    vn.car_depart carDepart,
    vn.lineNum,
    vn.brand,
    vn.model,
    vn.carLength,
    vn.year,
    vn.name,
    (SELECT group_concat(siteId) FROM t_site_auth WHERE type = 2 AND account = vn.vin AND topCommId
    = vn.commId AND `enable`=TRUE ) as station,
    vn.status,
    vn.commId,
    vn.subCommId,
    vn.userId,
    vn.corpId,
    user.phone as mobile,
    user.username as userName,
    rbu.id as rBlocUserId
    from t_vin vn
    left join t_user user on vn.userId = user.id
    LEFT JOIN t_r_bloc_user rbu ON vn.corpId = rbu.bloc_user_id AND vn.userId = rbu.user_id AND
    rbu.`status` = 1
    where vn.`enable` = 1
    and vn.`status` = 1
    and vn.vin = #{vin}
    and vn.userId = #{uid}
    <if test="topCommId != null">
      and vn.commId = #{topCommId}
    </if>
    and vn.`enable` = true
    limit 1
  </select>

  <select id="selectByCarNo"
          resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    vn.id,
    vn.vin,
    vn.carNo,
    vn.car_num carNum,
    vn.car_depart carDepart,
    vn.lineNum,
    vn.brand,
    vn.model,
    vn.carLength,
    vn.year,
    vn.name,
    (SELECT group_concat(siteId) FROM t_site_auth WHERE type = 2 AND account = vn.vin AND topCommId
    = vn.commId AND `enable`=TRUE ) as station,
    vn.status,
    vn.commId,
    vn.subCommId,
    vn.userId,
    vn.corpId,
    user.phone as mobile,
    user.username as userName,
    rbu.id as rBlocUserId
    from t_vin vn
    left join t_user user on vn.userId = user.id
    LEFT JOIN t_r_bloc_user rbu ON vn.corpId = rbu.bloc_user_id AND vn.userId = rbu.user_id AND
    rbu.`status` = 1
    where vn.`enable` = 1
    and vn.`status` = 1
    and vn.carNo=#{carNo}
    <if test="userId != null">
      and vn.userId=#{userId}
    </if>
    limit 1
  </select>

  <select id="selectByVinAndCorp"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    vn.id,
    vn.vin,
    vn.carNo,
    vn.car_num carNum,
    vn.car_depart carDepart,
    vn.lineNum,
    vn.brand,
    vn.model,
    vn.carLength,
    vn.year,
    vn.name,
    (SELECT group_concat(siteId) FROM t_site_auth WHERE type = 2 AND account = vn.vin AND topCommId
    = vn.commId AND `enable`=TRUE ) as station,
    vn.status,
    vn.commId,
    vn.subCommId,
    vn.userId,
    vn.corpId,
    user.phone as mobile,
    user.username as userName,
    rbu.id as rBlocUserId
    from t_vin vn
    left join t_user user on vn.userId = user.id
    LEFT JOIN t_r_bloc_user rbu ON vn.corpId = rbu.bloc_user_id AND vn.userId = rbu.user_id AND
    rbu.`status` = 1
    where vn.`enable` = 1
    and vn.`status` = 1
    and vn.vin=#{vin}
    and vn.corpId=#{corpId}
    limit 1
  </select>

  <!-- 订单筛选-关键字查询Vin码列表-->
  <select id="selectAllVinList" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v
    <where>
      v.userId=#{userId}
      <if test="commIds!=null and commIds.size()>0">
        AND v.commId in
        <foreach collection="commIds" index="index" item="item" open="("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </select>
  <!-- 订单筛选-根据VIN查询卡号-->
  <select id="selectCarNoByVins"
    parameterType="com.cdz360.biz.model.cus.vin.param.VINCarNoParam"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select v.carNo, v.vin, v.commId
    from t_vin v
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">
      left join t_r_commercial comm on comm.id = v.subCommId
    </if>
    where v.status = 1
    and v.enable = 1
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    AND v.vin in
    <foreach collection="vinList" index="index" item="item" open="("
      separator="," close=")">
      #{item}
    </foreach>
  </select>

  <!--根据用户Id和VIN状态获取VIN码列表-->
  <select id="findByUserIdAndStatus"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v
    <where>
      v.enable = 1 and
      v.userId=#{userId} and
      v.status=#{status}
      <if test = "isPrimary != null">
        and isPrimary = #{isPrimary}
      </if>
    </where>
    ORDER BY v.createtime DESC
    limit #{offset}, #{limit}
  </select>

  <!--通过VIN码Id获取VIN码-->
  <select id="getById" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v
    where v.`enable` = 1 and id=#{id}
  </select>

  <select id="getVinDto2ById" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto2">
    select
    v.id,
    v.vin,
    v.carNo,
    v.car_depart carDepart,
    v.car_num carNum,
    v.lineNum,
    v.brand,
    v.model,
    v.carLength,
    v.`year`,
    v.name,
    v.commId,
    v.subCommId,
    comm.comm_name as subCommName,
    v.userId,
    u.username,
    u.comm_id as userTopCommId,
    u.phone,
    v.corpId,
    tbu.bloc_user_name corpName,
    v.createTime
    from
    t_vin v
    inner join t_user u on
    v.userId = u.id
    inner join t_r_commercial comm on
    v.subCommId = comm.id
    left join t_bloc_user tbu on
    v.corpId = tbu.id
    where
    v.enable = 1
    and v.id = #{vinId}
  </select>

  <!--通过VIN码Id列表获取VIN码-->
  <select id="getByIdList" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    v.car_depart as carDepart,
    rbu.phone as mobile,
    <include refid="selectVinColumns"/>
    from t_vin v
    LEFT JOIN t_r_bloc_user rbu on v.userId=rbu.user_id and v.corpId = rbu.bloc_user_id
    where
    v.enable = 1
    and v.id in
    <foreach collection="idList" index="index" item="item"
      open="(" separator="," close=")">
      #{item}
    </foreach>
  </select>

  <!--用户是否拥有VIN码-->
  <select id="findOneByUserId" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v where v.enable = true and v.commId=#{commId} and v.userId = #{userId} limit 1
  </select>

  <!--根据用户Id和corpId列表获取VIN码列表-->
  <select id="getCardsByUserIdAndCorpIds"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    <include refid="selectVinColumns"/>
    from t_vin v
    where v.enable = 1
    and v.userId=#{userId}
    and v.status = 1
    <foreach item="item" index="index" collection="corpIds"
      open="and v.corpId in (" separator="," close=")">
      #{item}
    </foreach>
    ORDER BY v.createtime DESC
  </select>

  <!--  得到需要被清洗的VIN码数据(获得关键字段即可)-->
  <!-- 备份 v.vin,v.userId,v.corpId,u.balanceId,u.defaultPayType,rbu.id as rBlocUserId,bu.id as blocUserId-->
  <select id="getVinCleanData" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    v.id,
    bu.id as blocUserId
    FROM
    t_vin v
    INNER JOIN t_user u ON v.userId = u.id AND u.defaultPayType = 2
    INNER JOIN t_r_bloc_user rbu ON u.balanceId = rbu.id
    INNER JOIN t_bloc_user bu ON rbu.bloc_user_id = bu.id
    WHERE
    v.`status` = 1
    AND v.`enable` = 1
    AND v.corpId is NULL
  </select>

  <select id="findAll" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    v.vin,
    v.station,
    v.commId
    FROM
    t_vin v
    WHERE
    v.station IS NOT NULL
    LIMIT #{start}, #{size}
  </select>

  <select id="getCreditAccountVinCount" resultType="java.lang.Integer">
    select
    count(*)
    from
    d_card_manager.t_r_bloc_user rbu
    inner join d_card_manager.t_vin vin on
    rbu.bloc_user_id = vin.`corpId`
    and rbu.user_id = vin.`userId`
    and vin.status = 1
    and vin.enable = 1
    where
    rbu.id = #{rBlocUserId}
  </select>
  <select id="selectVinListByCommId"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    select
    id,status,enable,userId
    from t_vin v
    where v.commId = #{commId}
    and v.vin = #{vin}
  </select>
  <select id="getVinBySiteId" resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    vin.subCommId,
    vin.vin AS vin,
    vin.corpId,
    trc.idChain
    FROM
    d_card_manager.t_site_auth auth
    LEFT JOIN d_card_manager.t_vin vin ON auth.account = vin.vin
    LEFT JOIN d_card_manager.t_r_commercial trc on trc.id = vin.subCommId
    WHERE
    siteId = #{siteId}
    AND auth.`enable` = 1
    AND vin.STATUS = 1
    AND vin.ENABLE = 1
  </select>

  <update id="swapCreditAccountVin">
    update
    d_card_manager.t_vin vin inner join
    d_card_manager.t_r_bloc_user rbu on
    rbu.bloc_user_id = vin.`corpId`
    and rbu.user_id = vin.`userId`
    and vin.status = 1
    and vin.enable = 1
    set
    vin.userId = #{userId}
    where
    rbu.id = #{rBlocUserId}
  </update>
  <update id="updateEnableBySiteId">
    update t_site_auth auth,t_vin vin
    set auth.enable = 0
    where
    auth.type=2
    and auth.account = vin.vin
    and auth.siteId = #{siteId}
    and vin.subCommId in
    <foreach collection="vinCommId" item="item" open="(" separator=","
      close=")" index="index">
      #{item}
    </foreach>
  </update>
  <update id="moveCorp">
    update t_vin set
    updateTime = now(),
    subCommId = #{commId}
    where corpId = #{corpId}
  </update>

  <select id="getCarNoListByUserIds"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    v.carNo,
    v.userId
    FROM
    t_vin v
    WHERE
    v.commId = #{topCommId}
    and v.enable = 1
    and v.carNo <![CDATA[ <> ]]> ''
    and v.carNo is not null
    and v.userId in
    <foreach collection="userIds" item="item" open="(" separator=","
      close=")" index="index">
      #{item}
    </foreach>

  </select>

  <select id="getCarNoListByParam"
    parameterType="com.chargerlinkcar.framework.common.domain.vo.VinParam"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    v.*
    FROM
    t_vin v
    <where>
      v.userId = #{userId}
      <if test="enable != null">
        and v.enable = #{enable}
      </if>
      <if test="id != null">
        and v.id = #{id}
      </if>
      <if test="isPrimary != null">
        and v.isPrimary = #{isPrimary}
      </if>
      <if test="status != null">
        and v.status = #{status}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeIds)">
        <foreach collection="excludeIds" item="vinId" open=" AND v.id not in ("
          separator="," close=")">
          #{vinId}
        </foreach>
      </if>
      and v.carNo <![CDATA[ <> ]]> ''
      and v.carNo is not null
    </where>
    order by v.id desc
    limit #{size}
  </select>

  <select id="getSiteAuthVinTime" resultType="com.cdz360.biz.model.vin.po.SiteAuthVinLogPo">
    select * from t_site_auth_vin_log
    where status = 3
    and latest = 1
    and evseId in
    <foreach collection="evseNoList" item="item" open="(" separator=","
      close=")" index="index">
      #{item}
    </foreach>
  </select>
  <select id="getVinListByEvse" resultType="com.cdz360.biz.model.vin.po.SiteAuthVinLogPo">
    select *
    from t_site_auth_vin_log
    where
    evseId=#{evseId}
    and status = 3
    and latest = 1
    limit 1
  </select>
  <update id="setPrimary">
    update t_vin set
    isPrimary = #{isPrimary}
    <where>
      id in
      <foreach collection="vinId" item="item" open="(" separator=","
        close=")" index="index">
        #{item}
      </foreach>
    </where>
  </update>

  <select id="findVinList"
    resultType="com.chargerlinkcar.framework.common.domain.vo.VinDto">
    SELECT
    vin.*,
    any_value ( corpUser.id ) rBlocUserId,
    any_value ( corpUser.`name` ) userName,
    any_value ( corpUser.phone ) mobile,
    any_value ( corpOrg.`id` ) corpOrgId,
    any_value ( corpOrg.`orgName` ) corpOrgName
    FROM
    t_vin vin
    JOIN t_bloc_user corp on corp.id=vin.corpId
    JOIN t_r_bloc_user corpUser ON vin.corpId = corpUser.bloc_user_id AND vin.userId = corpUser.user_id
    JOIN t_corp_org corpOrg on corpOrg.id=corpUser.corpOrgId
    WHERE
     vin.`enable` = TRUE
     and corpUser.status = 1
     and corpOrg.enable = 1
    <if test="param.topCommId != null">
      and corp.topCommId = #{param.topCommId}
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.vin )">
      and vin like CONCAT('%',#{param.vin}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.carNo )">
      and carNo like CONCAT('%',#{param.carNo}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.carDepart )">
      and carDepart like CONCAT('%',#{param.carDepart}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.carLineNum )">
      and carLineNum like CONCAT('%',#{param.carLineNum}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.carNum )">
      and carNum like CONCAT('%',#{param.carNum}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.cusUsername )">
      and corpUser.name like CONCAT('%',#{param.cusUsername}, '%')
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(param.cusPhone )">
      and corpUser.phone like CONCAT('%',#{param.cusPhone}, '%')
    </if>

    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( param.corpOrgIds )">
      and corpOrg.`id` in
      <foreach collection="param.corpOrgIds" item="corpOrgId" open="("
        separator="," close=")">
        #{corpOrgId}
      </foreach>
    </if>
    GROUP BY
    vin.id
  </select>


</mapper>