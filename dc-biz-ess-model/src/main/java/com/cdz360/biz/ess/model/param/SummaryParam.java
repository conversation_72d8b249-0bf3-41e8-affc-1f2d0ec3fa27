package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.type.SummaryType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.time.LocalDateTime;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "数据汇总请求参数")
@Data
@Accessors(chain = true)
public class SummaryParam {

    @Schema(description = "用户ID", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @NotNull
    @Schema(description = "统计粒度", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private SummaryType summaryType;

    @Schema(description = "查询设备编号列表(不传则认为汇总当前用户所有设备)")
    @JsonInclude(Include.NON_NULL)
    private List<String> essDnos;

    @Schema(description = "商户ID链")
    @JsonInclude(Include.NON_NULL)
    private String commIdChain;

    @Schema(description = "查询场站列表(不传则认为汇总当前用户所有设备)")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "场站组列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> gids;

//    @NotNull
//    @Schema(description = "UNIX时间戳(月份: 当月首天0时时间戳;天: 0时时间戳;)",
//        requiredMode = RequiredMode.REQUIRED)
//    @JsonInclude(Include.NON_EMPTY)
//    private Long unixTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "开始时间", description = "数据为>=开始时间")
    private LocalDateTime startTime;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "结束时间", description = "数据为<结束时间")
    private LocalDateTime endTime;
}
