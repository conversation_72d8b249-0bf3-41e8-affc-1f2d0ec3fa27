package com.cdz360.biz.ess.model.data.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "储能站运行时数据统计")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class SiteEssRtDataBi extends EssDataBi {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;
}
