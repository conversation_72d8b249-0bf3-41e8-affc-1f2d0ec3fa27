package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "车位类型")
@Getter
public enum CarParkType implements DcEnum {


    UNKNOWN(999, "未知"),

    CAR_PARK_SMALL(1, "小车车位"),
    CAR_PARK_BIG(2, "大车车位"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    CarParkType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static CarParkType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (CarParkType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return CarParkType.UNKNOWN;
    }

}
