package com.cdz360.biz.model.cus.corp.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户ID传递")
@Data
@Accessors(chain = true)
public class UserIdDto {

    @Schema(description = "团队标签用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> teamCatalogUserIdList;

    @Schema(description = "通过用户名称查询用户ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> searchNameUserIdList;
}
