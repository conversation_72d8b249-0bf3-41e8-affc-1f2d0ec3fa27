package com.cdz360.biz.auth.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cdz360.base.model.app.type.AppClientType;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServerException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.auth.dao.SysUserComercialDao;
import com.cdz360.biz.auth.dao.SysUserDao;
import com.cdz360.biz.auth.dao.SysUserRoleDao;
import com.cdz360.biz.auth.dao.TCommercialDao;
import com.cdz360.biz.auth.dao.TCommercialManageDao;
import com.cdz360.biz.auth.ds.ro.user.SiteGroupUserRoDs;
import com.cdz360.biz.auth.ds.ro.zft.ZftRoDs;
import com.cdz360.biz.auth.model.dto.TCommercialDto;
import com.cdz360.biz.auth.model.dto.UserCommericalDto;
import com.cdz360.biz.auth.model.exception.ComEditException;
import com.cdz360.biz.auth.model.param.AddUserGroupRequest;
import com.cdz360.biz.auth.model.vo.Rez;
import com.cdz360.biz.auth.model.vo.SysUser;
import com.cdz360.biz.auth.model.vo.SysUserComercial;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.model.vo.TCommercialManage;
import com.cdz360.biz.auth.model.vo.TCommercialUser;
import com.cdz360.biz.auth.utils.IotAssert;
import com.cdz360.biz.auth.zft.po.ZftPo;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.param.AddCommercialParam;
import com.cdz360.biz.model.merchant.param.ConvertCommPayParam;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.param.UpdateCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.data.sync.model.DzCommercial;
import com.cdz360.data.sync.service.DcEventPublisher;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.event.Level;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Service
public class TCommercialService extends ServiceImpl<TCommercialDao, TCommercial> {

    private static final int BUILD_TREE_MAX_LOOP = 2000;

    @Autowired
    private SysUserComercialDao sysUserComercialDao;
    @Autowired
    private TCommercialDao tCommercialDao;
    @Autowired
    private SysUserDao sysUserDao;
    @Autowired
    private SysUserRoleDao sysUserRoleDao;
    @Autowired
    private TCommercialManageDao tCommercialManageDao;

    @Autowired
    private DcEventPublisher dcEventPublisher;

    @Autowired
    private AuthorityService authorityService;

    @Autowired
    private ZftRoDs zftRoDs;
    @Autowired
    private SiteGroupUserRoDs siteGroupUserRoDs;

    @Transactional(readOnly = true)
    public TCommercial selectById(Long id) {
        TCommercial tc = baseMapper.selectById(id);
        log.info("tc.id: {}, tc.zftId: {}", tc.getId(), tc.getZftId());
        SysUserComercial suc = new SysUserComercial();
        suc.setMain(true);
        suc.setCommercialId(id);
        if (StringUtils.isNotBlank(tc.getHlhtSitePayType())) {
            tc.setHlhtSitePayTypeList(Arrays.stream(tc.getHlhtSitePayType().split(","))
                .map(OrderPayType::getValueByName).collect(Collectors.toList()));
        }
        SysUserComercial sucOne = sysUserComercialDao.selectOne(Wrappers.query(suc));
        if (sucOne != null && sucOne.getUserId() != null) {
            SysUser mainUser = sysUserDao.selectById(sucOne.getUserId());
            tc.setMainUser(mainUser);
        }

        // 直付商家
        this.initZftInfo(tc);

        return tc;
    }

    public TCommercial getCommPlatInfo(Long id) {
        TCommercial comm = tCommercialDao.selectById(id);
        if (null == comm) {
            throw new DcServerException("商户信息不存在");
        }
        if (StringUtils.isEmpty(comm.getCommLogo())) {
            return this.getCommPlatByIdChain(comm.getIdChain());
        }
        return comm;
    }

    public TCommercial getCommPlatByIdChain(String idChain) {
        //获取所有上级ID
        List<Long> idList = Arrays.asList(idChain.split(",")).stream()
            .map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        return this.tCommercialDao.getCommercialInfoByIdList(idList);
    }

    public void initZftInfo(TCommercial tc) {
        if (null == tc) {
            return;
        }

        tc.setEnableBalance(false);

        // 顶级商户是否开启
        boolean open = true;
        if (tc.getCommLevel() == 1) {
            open = tc.getEnableZft();
            tc.setEnableUseZft(tc.getEnableZft());
        } else {
            long topCommId = Long.parseLong(tc.getIdChain().split(",")[0]);
            TCommercial topComm = baseMapper.selectById(topCommId);
            open = null != topComm && topComm.getEnableZft();
            tc.setEnableUseZft(topComm.getEnableZft());
        }

        if (open) {
            if (tc.getZftId() != null) {
                ZftPo zftPo = zftRoDs.getById(tc.getZftId());
                if (null != zftPo) {
                    tc.setEnableBalance(zftPo.getEnableBalance());
                    tc.setZftName(zftPo.getName());
                    // 不考虑直付商关联
//                    tc.setEnableCommRefund(zftPo.getEnableRefund());
                    tc.setEcnyPlatformKey(zftPo.getEcnyPlatformKey());
                    tc.setWxMchId(zftPo.getWxMchId());
                    tc.setWxSubMchId(zftPo.getWxSubMchId());
                    tc.setWxCreditServiceId(zftPo.getWxCreditServiceId());
                    tc.setAlipayMchId(zftPo.getAlipayMchId());
                    tc.setAlipaySubMchId(zftPo.getAlipaySubMchId());
                    tc.setWxLiteAppId(zftPo.getWxLiteAppId());
                    tc.setWxAndroidAppId(zftPo.getWxAndroidAppId());
                    tc.setWxIosAppId(zftPo.getWxIosAppId());
                    tc.setAlipaySubMchId(zftPo.getAlipaySubMchId());
                    tc.setAlipayCreditServiceId(zftPo.getAlipayCreditServiceId());
                    tc.setZftCreateTime(zftPo.getCreateTime());
                    tc.setZftCommId(zftPo.getCommId());
                }
            }
        } else {
            tc.setEnableBalance(null);
        }
    }

    public TCommercialManage selectManageByComId(Long comId) {
        TCommercialManage tm = new TCommercialManage();
        tm.setComId(comId);
        TCommercialManage entity = tCommercialManageDao.selectOne(Wrappers.query(tm));
        return entity;
    }

    public Long selectManageByWXAppId(String WXappId) {
        TCommercialManage tm = new TCommercialManage();
        tm.setWxAppid(WXappId);
        TCommercialManage entity = tCommercialManageDao.selectOne(Wrappers.query(tm));
        return entity.getComId();
    }

    public boolean insertUserCommercials(SysUserComercial uc) {
        return sysUserComercialDao.insert(uc) > 0;
    }

    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserCommercials(SysUserComercial uc) {
        return sysUserComercialDao.updateById(uc) > 0;
    }

    public SysUserComercial getUserCommercials(Long userId) {
        return sysUserComercialDao.selectById(userId);
    }

    public TCommercial selectByUserId(Long userId) {
        TCommercial tc = baseMapper.selectByUserId(userId);
        return tc;
    }

    public CommercialSimpleVo findSimpleVoById(Long id) {
        return baseMapper.findSimpleVoById(id);
    }

    @Deprecated
    @Transactional(rollbackFor = Exception.class)
    public Rez insertNewCom(TCommercial entity) throws ComEditException.ComException,
        ComEditException.UserException {
        //用户
        SysUser su = new SysUser();
        su.setEmail(entity.getEmail());
        su.setPhone(entity.getPhone());
        su.setUsername(entity.getPhone());

        su.setName(entity.getContacts());
        su.setPassword(su.getPhone());
        su.setPassword(su.hashedPassword());
        List<SysUser> users = sysUserDao.checkByUserNameOrPhoneOrEmail(su);
        if (!CollectionUtils.isEmpty(users)) {
            return Rez.error(3, "联系人电话或联系人邮箱重复");
        }
        List<TCommercial> tCommercials = baseMapper.checkByShortName(entity.getShortName());
        if (!CollectionUtils.isEmpty(tCommercials)) {
            return Rez.error(4092, "商户简称重复");
        }

        int insertUser = sysUserDao.insert(su);

        if (insertUser < 1) {
            throw new ComEditException.UserException("user insert error");
        }
        sysUserRoleDao.insertUserRoleIdList(su.getId(), Arrays.asList(2019l));
        //商户
        entity.setStatus(1);
        int insertCom = baseMapper.insert(entity);
        if (insertCom < 1) {
            throw new ComEditException.ComException("com insert error");
        }

        entity.setMerchants("A" + entity.getId());
        int updateCom = baseMapper.updateById(entity);
        if (updateCom < 1) {
            throw new ComEditException.ComException("com update error");
        }

        //int comRef = baseMapper.insertTCommercialRef(entity.getId(), entity.getPid(), SysUserUtil.curUserId());
        SysUserComercial suc = new SysUserComercial();
        suc.setUserId(su.getId());
        suc.setCommercialId(entity.getId());
        suc.setCommercialRole(1);
        suc.setMain(true);
        updateCom = sysUserComercialDao.insert(suc);
        if (updateCom < 1) {
            throw new ComEditException.UserComException("com user insert error");
        }
        if (entity.getCommLevel() == 1) {
            // 新增集团商户后，需要加上管理员权限
            AddUserGroupRequest addUserGroupRequest = new AddUserGroupRequest();
            addUserGroupRequest.setUserId(suc.getUserId());
            addUserGroupRequest.setAuthGroupIds(List.of(1L));
            IotAssert.isTrue(authorityService.modifyUserGroupRef(addUserGroupRequest) > 0,
                "集团商户关联默认权限失败");
        }
        this.publishCommercielEvent(entity);
        return Rez.ok();
    }

    @Transactional(readOnly = true)
    public TCommercial selectYoungerByUserId(Long userId) {
        TCommercial tc = selectByUserId(userId);
        if (tc != null) {
            String idChain = tc.getIdChain();
            List<TCommercial> youngers = this.getCommListByIdChain(idChain);
            tc.setYoungers(youngers);
        }
        return tc;
    }


    public TCommercial selectParentByUserId(Long userId) {
        TCommercial tc = selectByUserId(userId);
        if (tc == null) {
            return tc;
        }
        if (tc.getPid() != null) {
            TCommercial parent = selectById(tc.getPid());
            tc.setParent(parent);
        }
        return tc;
    }

    public List<SysUser> selectUserByCommercialsId(UserCommericalDto ucd) {
        return baseMapper.selectUserByCommercials(ucd);
    }


    public ListResponse<TCommercialUser> selectByUserPageByCommercialUser(UserCommericalDto param) {
//        Page page = new Page(ucd.getPage(), ucd.getSize());
//        Map map = new HashMap();
//        map.put("page", page);
//        map.put("ucd", ucd);
        ListResponse<TCommercialUser> result = new ListResponse<>();
        if (param.getSize() == null) {
            param.setSize(param.getLimit());
        }
        if (param.getStart() == null) {
            param.setStart((param.getPage() - 1) * param.getSize());
        }
        result.setTotal(baseMapper.countByUserPageByCommercialUser(param));

        List<TCommercialUser> list = baseMapper.selectByUserPageByCommercialUser(param);
        result.setData(list);
        return result;
    }

    public ListResponse<TCommercial> selectPage(TCommercialDto param) {
//        Page page = new Page(pd.getPage(), pd.getSize());
//        Map map = new HashMap();
//        pd.setStatus(1);    // 仅返回有效的商户
//        map.put("page", page);
//        map.put("td", pd);

        ListResponse<TCommercial> result = new ListResponse<>();
//        if (param.getSize() == null) {
//            param.setSize(param.getLimit());
//        }
        if (param.getStart() == null) {
            param.setStart((param.getPage() - 1) * param.getSize());
        }
        result.setTotal(baseMapper.countByTCommercial(param));
        List<TCommercial> list = baseMapper.selectPageByCom(param);
        result.setData(list);
        return result;
    }

    /**
     * 通过commId 得到对应的最高级集团商户
     */
    public TCommercial getMaxCommercialByCommId(Long commId) {
        TCommercial tc = new TCommercial();
        do {
            if (tc.getId() != null) {
                tc = baseMapper.selectById(tc.getPid());
            } else {
                tc = baseMapper.selectById(commId);
            }
            if (tc == null) {
                return tc;
            }
        } while (tc.getCommLevel() != 1);
        return tc;
    }

    /**
     * 通过commId得到下属商户集合(不含本身)
     *
     * @param commId
     * @return
     */
    public List<TCommercial> getChildByCommId(Long commId) {
        List<TCommercial> list = new ArrayList<>();
        List<TCommercial> tmp = new ArrayList<>();
        do {
            List<TCommercial> tmp1 = new ArrayList<>();
            if (tmp.size() != 0) {
                for (TCommercial t : tmp) {
                    QueryWrapper<TCommercial> ew = Wrappers.query();
//                    EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//                    ew.where(t.getId() != null, "pid={0}", t.getId());
                    if (t.getId() != null) {
                        ew.eq("pid", t.getId());
                    }
                    List<TCommercial> tmp2 = baseMapper.selectList(ew);
                    if (tmp2.size() != 0) {
                        for (TCommercial tm : tmp2) {
                            tmp1.add(tm);
                            list.add(tm);
                        }
                    }
                }
                tmp = tmp1;
            } else {
                QueryWrapper<TCommercial> ew = Wrappers.query();
//                EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//                ew.where(commId != null, "pid={0}", commId);
                if (commId != null) {
                    ew.eq("pid", commId);
                }
                tmp = baseMapper.selectList(ew);
                list = baseMapper.selectList(ew);
            }
        } while (tmp.size() != 0);
        return list;
    }

    /**
     * 通过商户级别查询
     *
     * @param commLevel
     * @return
     */
    public List<TCommercial> getByCommlevel(Integer commLevel) {
        QueryWrapper<TCommercial> ew = Wrappers.query();
        if (commLevel != null) {
            ew.eq("comm_level", commLevel);
        }
//        EntityWrapper<TCommercial> ew = new EntityWrapper<>();
//        ew.where(commLevel != null, "comm_level={0}", commLevel);
        List<TCommercial> list = baseMapper.selectList(ew);
        return list;
    }


    /**
     * 同步商户信息给其他的所有监听方
     */
    public void publishAllCommercialInfo() {
        long pageIdx = 1;
        long size = 100;
        List<TCommercial> list;
        do {
            Page<TCommercial> p = new Page<>(pageIdx, size);
            Page<TCommercial> page = this.getBaseMapper()
                .selectPage(p, Wrappers.query(TCommercial.class));
            pageIdx += 1;
            list = page.getRecords();
            list.stream().forEach(this::publishCommercielEvent);
        } while (com.cdz360.base.utils.CollectionUtils.isNotEmpty(list));
    }

    private void publishCommercielEvent(TCommercial commercial) {
        DzCommercial event = new DzCommercial();
        event.setId(commercial.getId())
            .setPid(commercial.getPid())
            .setCommLevel(commercial.getCommLevel())
            .setMerchants(commercial.getMerchants())
            .setCommType(commercial.getCommType())
            .setTopCommId(commercial.getTopCommId())
            .setCommName(commercial.getCommName())
            .setShortName(commercial.getShortName())
            .setStatus(commercial.getStatus())
            .setPhone(commercial.getPhone())
            .setIdChain(commercial.getIdChain());
        this.dcEventPublisher.publishDzCommercialInfo(event);
    }

    /**
     * 更新商户以及子商户在线退款状态
     *
     * @param commId
     * @return
     */
    public int updateRefundStatus(Long commId, Boolean refundStatus) {
        TCommercial temp = new TCommercial();
        temp.setId(commId);
        TCommercial dbExist = tCommercialDao.selectOne(Wrappers.query(temp));
        if (dbExist != null) {
            int ret = tCommercialDao.updateRefundStatus(commId, refundStatus);
        }
        return 1;
    }


    /**
     * 修正 t_commercial 表的 idChain 字段值
     */
    @Async
    @Transactional
    public void updateIdChains(Long commId, String oldIdChain, String traceId) {
        log.info("更新商户树. commId = {}, traceId = {}", commId, traceId);
        if (commId == null) {
            List<TCommercial> list = this.tCommercialDao.selectList(null);
            list.forEach(this::updateIdChain);
        } else {
            TCommercial comm = this.selectById(commId);
            String idChain = comm.getIdChain();

            log.info("idChain = {}", idChain);

            QueryWrapper<TCommercial> sw = Wrappers.query();
            sw.likeRight(true, "idChain",
                StringUtils.isNotBlank(oldIdChain) ? oldIdChain : idChain);

            List<TCommercial> list = this.tCommercialDao.selectList(sw);
            list.forEach(this::updateIdChain);
        }
    }

    public List<TCommercial> getCommListByIdChain(String idChain) {
        QueryWrapper<TCommercial> sw = Wrappers.query();
        sw.eq("status", 1)
            .likeRight(true, "idChain", idChain)
            .orderBy(true, true, "idChain");
        List<TCommercial> list = this.tCommercialDao.selectList(sw);
        return list;
    }

    private void updateIdChain(TCommercial tc) {
        String idChain = String.valueOf(tc.getId());
        idChain = this.getIdChain(tc.getId(), idChain);
        Integer commLevel = idChain.split(",").length;
        this.tCommercialDao.updateIdChain(tc.getId(), commLevel, idChain);
        tc.setIdChain(idChain);
        tc.setCommLevel(commLevel);
        this.publishCommercielEvent(tc);
    }

    private String getIdChain(Long commId, String idChain) {
        if (commId == 0L) {
            return idChain;
        }
        TCommercial comm = this.tCommercialDao.selectById(commId);
        if (comm == null) {
            log.warn("商户不存在. commId = {}", commId);
            return idChain;
        }
        if (comm.getPid() == null || comm.getPid() == 0L) {
            return idChain;
        } else {
            String idChainRet = String.valueOf(comm.getPid()) + "," + idChain;
            return this.getIdChain(comm.getPid(), idChainRet);
        }

    }

    public List<CommercialDto> getCommercialList(ListCommercialParam param) {
        if (param.getStart() == null) {
            param.setStart(0L);
        }
        if (param.getSize() == null) {
            param.setSize(999);
        }

        List<CommercialDto> list = this.tCommercialDao.getCommercialList(param);

        if (com.cdz360.base.utils.CollectionUtils.isEmpty(list)) {
            return list;
        }
        List<Long> topCommIdList = list.stream().filter(c -> c.getTopCommId() != null)
            .map(CommercialTreeNode::getTopCommId).collect(Collectors.toList());
        if (com.cdz360.base.utils.CollectionUtils.isEmpty(topCommIdList)) {
            return list;
        }
        ListCommercialParam listTopCommParam = new ListCommercialParam();
        listTopCommParam.setCommIdList(topCommIdList).setStart(0L).setSize(999);
        List<CommercialDto> topCommList = this.tCommercialDao.getCommercialList(listTopCommParam);
        if (com.cdz360.base.utils.CollectionUtils.isEmpty(topCommList)) {
            return list;
        }
        list.stream().forEach(c -> {
            //商户是否能够使用直付通
            this.initZft(c);

            for (CommercialDto topComm : topCommList) {
                if (NumberUtils.equals(c.getTopCommId(), topComm.getId())) {
                    c.setTopCommName(topComm.getCommName());
                }
            }
        });
        return list;
    }

    /**
     * 商户是否能使用直付通
     *
     * @param comm
     * @return
     */
    private void initZft(CommercialDto comm) {
        if (comm == null) {
            return;
        }
        //顶级商户
        if (comm.getCommLevel().equals(1) && comm.getEnableZft().equals(true)
            && comm.getZftId() > 0) {
            comm.setEnableUseZft(true);
        } else {
            comm.setEnableUseZft(false);
        }
        //非顶级商户
        TCommercial topComm = baseMapper.selectById(comm.getTopCommId());
        if (topComm.getEnableZft().equals(true) && comm.getZftId() > 0) {
            comm.setEnableUseZft(true);
        } else {
            comm.setEnableUseZft(false);
        }

        //获取是否支持在线充值
        if (!comm.getCommLevel().equals(1)) {
            if (topComm.getEnableOnlinePay().equals(true) && comm.getEnableOnlinePay()
                .equals(true)) {
                comm.setEnableOnlinePay(true);
            } else {
                comm.setEnableOnlinePay(false);
            }
        }
    }

    private CommercialTreeNode toCommercialTreeNode(TCommercial tc) {
        CommercialTreeNode dto = new CommercialTreeNode();
        dto.setId(tc.getId())
            .setMerchants(tc.getMerchants())
            .setCommName(tc.getCommName())
            .setShortName(tc.getShortName())
            .setContacts(tc.getContacts())
            .setPhone(tc.getPhone())
            .setPid(tc.getPid())
            .setCommLevel(tc.getCommLevel())
            .setIdChain(tc.getIdChain())
            .setZftId(tc.getZftId())
            .setZftName(tc.getZftName())
            .setEnableCommRefund(tc.getEnableCommRefund())
            .setEnableCorpDeposit(tc.getEnableCorpDeposit())
            .setEnableCorpRefund(tc.getEnableCorpRefund())
            .setEnableOnlinePay(tc.getEnableOnlinePay())
            .setEnableUseScore(tc.getEnableUseScore());
        return dto;
    }

    public CommercialTreeNode getCommercialTreeById(@Nullable Long commId) {
        CommercialTreeNode root;
        if (commId == null || commId < 1L) {
            root = new CommercialDto();
            root.setId(0L);
            root.setCommLevel(0);
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (tc == null) {
                log.warn("参数错误. 商户不存在. commId = {}", commId);
                throw new DcArgumentException("参数错误. 商户不存在");
            }

            root = this.toCommercialTreeNode(tc);
            if (root.getZftId() > 0L) {
                ZftPo res = tCommercialDao.getZftNameById(root.getZftId());
                root.setZftName(res.getName());
//                root.setEnableRefund(res.getEnableRefund());
            }
        }
        return this.getCommercialTree(root);
    }

    public CommercialTreeNode getCommercialTree(@Nullable CommercialTreeNode comm) {
        List<CommercialTreeNode> list;
        String idChain;
        if (comm == null || comm.getId() == null || comm.getId() < 1L) {
            idChain = "";
        } else {
            idChain = comm.getIdChain() + ",";
        }
        List<TCommercial> tcList = tCommercialDao.getCommListByIdChain(idChain);
        list = tcList.stream().map(o -> this.toCommercialTreeNode(o)).collect(Collectors.toList());
        this.buildCommTree(comm, 0, list);
        return comm;
    }


    private int buildCommTree(CommercialTreeNode parent, int idx, List<CommercialTreeNode> list) {
        if (idx > BUILD_TREE_MAX_LOOP) {
            return BUILD_TREE_MAX_LOOP + 1;
        }
        int idxTmp = 0;
        if (parent.getChildren() == null) {
            parent.setChildren(new ArrayList<>());
        }
        CommercialTreeNode last = parent;
        while (idxTmp + idx < BUILD_TREE_MAX_LOOP) {   // 2000 是为了防止数据问题导致死循环
            if (idxTmp + idx > list.size() - 1) {
                break;
            }
            CommercialTreeNode comm = list.get(idxTmp + idx);
            if (comm.getPid() == null) {
                comm.setPid(0L);
            }
            if (NumberUtils.equals(comm.getPid(), parent.getId())) {
                parent.getChildren().add(comm);
                idxTmp++;
            } else if (comm.getCommLevel().longValue() <= parent.getCommLevel()) {
                break;
            } else if (comm.getCommLevel() > parent.getCommLevel()
                && comm.getPid().longValue() == last.getId()) {
                int ret = this.buildCommTree(last, idxTmp + idx, list);
                if (ret == 0) {
                    ret = 1;    // 防止数据问题导致死循环
                }
                idxTmp += ret;
            } else if (comm.getCommLevel() > parent.getCommLevel()) {
                log.warn(
                    "商户树数据异常, 上级商户已被禁用. comm.id = {}, comm.pid = {}， parent.id = {}, last.id = {}",
                    comm.getId(), comm.getPid(), parent.getId(), last.getId());
                idxTmp++;
            } else {
                if (comm.getPid().longValue() == parent.getId()) {
                    parent.getChildren().add(comm);
                }
                break;
            }

            last = comm;

        }
        return idxTmp;
    }


    /**
     * 更新商户信息
     *
     * @param param 商户信息
     * @return true：商户树有变更; false:
     */
    @Transactional
    public Pair<Boolean, String> updateCommInfo(UpdateCommercialParam param) {
        TCommercial comm = this.tCommercialDao.selectById(param.getId());
        if (comm == null) {
            log.warn("参数错误。商户不存在. param = {}", param);
            throw new DcArgumentException("参数错误。商户不存在");
        }
        TCommercial temp = new TCommercial();
        temp.setPhone(param.getPhone());
        TCommercial dbExist = tCommercialDao.selectOne(Wrappers.query(temp));
        if (dbExist != null && !NumberUtils.equals(dbExist.getId(), param.getId())) {
            throw new DcServiceException("该手机号已被使用");
        }

        // 顶级商户需要提供
        if (comm.getCommLevel() == 1 && null == param.getEnableZft()) {
            throw new DcArgumentException("订单商户需要提供服务商直付模式");
        }

        if (comm.getPid() == null) {
            comm.setPid(0L);
        }
        boolean treeChanged = false; // 商户树的位置是否有变更
        String oldIdChain = comm.getIdChain();
        comm = this.fillTCommercial(comm, param);

        if (!NumberUtils.equals(comm.getPid(), param.getPid())) {
            comm.setPid(param.getPid());
            if (param.getPid() != null && param.getPid() > 0L) {
                TCommercial parent = tCommercialDao.selectById(param.getPid());
                comm.setCommLevel(Optional.ofNullable(parent.getCommLevel()).orElse(0) + 1);
                comm.setIdChain(parent.getIdChain() + "," + comm.getId());
            }
            treeChanged = true;
        }
        this.tCommercialDao.updateById(comm);
        this.publishCommercielEvent(comm);
        return Pair.of(treeChanged, oldIdChain);
    }

    /**
     * 海外版，更新商户信息
     *
     * @param param 商户信息
     * @return true：商户树有变更; false:
     */
    @Transactional
    public Pair<Boolean, String> updateEssCommInfo(UpdateCommercialParam param) {
        TCommercial comm = this.tCommercialDao.selectById(param.getId());
        if (comm == null) {
            log.warn("参数错误。商户不存在. param = {}", param);
            throw new DcArgumentException("参数错误。商户不存在");
        }

        // 顶级商户需要提供
        if (comm.getCommLevel() == 1 && null == param.getEnableZft()) {
            throw new DcArgumentException("订单商户需要提供服务商直付模式");
        }

        if (comm.getPid() == null) {
            comm.setPid(0L);
        }
        boolean treeChanged = false; // 商户树的位置是否有变更
        String oldIdChain = comm.getIdChain();
        comm = this.fillTCommercial(comm, param);

        if (!NumberUtils.equals(comm.getPid(), param.getPid())) {
            comm.setPid(param.getPid());
            if (param.getPid() != null && param.getPid() > 0L) {
                TCommercial parent = tCommercialDao.selectById(param.getPid());
                comm.setCommLevel(Optional.ofNullable(parent.getCommLevel()).orElse(0) + 1);
                comm.setIdChain(parent.getIdChain() + "," + comm.getId());
            }
            treeChanged = true;
        }
        LambdaUpdateWrapper<TCommercial> wrapper = new LambdaUpdateWrapper<>();
        // 兼容空值
        wrapper.eq(TCommercial::getId, comm.getId())
            .set(TCommercial::getCommName, comm.getCommName())
            .set(TCommercial::getShortName, comm.getShortName())
            .set(TCommercial::getPid, comm.getPid())
            .set(TCommercial::getCommCategory, comm.getCommCategory())
            .set(TCommercial::getCommIndustry, comm.getCommIndustry())
            .set(TCommercial::getCommType, comm.getCommType())
            .set(TCommercial::getContacts, comm.getContacts())
            .set(TCommercial::getPhone, comm.getPhone())
            .set(TCommercial::getEmail, comm.getEmail())
            .set(TCommercial::getServicePhone, comm.getServicePhone())
            .set(TCommercial::getCommLogo, comm.getCommLogo())
            .set(TCommercial::getPlatformName, comm.getPlatformName());

        this.tCommercialDao.update(comm, wrapper);
        this.publishCommercielEvent(comm);
        return Pair.of(treeChanged, oldIdChain);
    }

    /**
     * 根据商户号修改logo
     *
     * @return
     */
    public int updateLogoByMerchants(String merchants, String logo, String platformName) {
        TCommercial temp = new TCommercial();
        temp.setMerchants(merchants);
        TCommercial dbExist = tCommercialDao.selectOne(Wrappers.query(temp));
        if (dbExist != null) {
            dbExist.setCommLogo(logo);
            dbExist.setPlatformName(platformName);
            this.tCommercialDao.updateById(dbExist);
        }
        return 1;
    }

    /**
     * 注意： 不set pid
     */
    private TCommercial fillTCommercial(TCommercial tc, UpdateCommercialParam param) {
        if (tc == null) {
            tc = new TCommercial();
        }
        tc.setCommType(param.getCommType());
        tc.setCommName(param.getCommName());
        tc.setShortName(param.getShortName());
        tc.setContacts(param.getContacts());
        tc.setPhone(param.getPhone());
        tc.setCommCategory(param.getCommCategory());
        tc.setCommIndustry(param.getCommIndustry());
        tc.setCommLogo(param.getCommLogo());
        tc.setCommIcon(param.getCommIcon());
        tc.setEmail(param.getEmail());
        tc.setLicense(param.getLicense());
        tc.setServicePhone(param.getServicePhone());
        tc.setCompanyType(param.getCompanyType());
        tc.setProvinceId(param.getProvinceId());
        tc.setCityId(param.getCityId());
        tc.setDetail(param.getDetail());
        tc.setLegalPerson(param.getLegalPerson());
        tc.setRegistrationAmount(param.getRegistrationAmount());
        tc.setRegistrationTime(param.getRegistrationTime());
        tc.setBusinessLicence(param.getBusinessLicence());
        tc.setEnableZft(param.getEnableZft());
        tc.setEnableDigiccy(param.getEnableDigiccy());
        tc.setPlatform(param.getPlatform());
        tc.setPlatformName(param.getPlatformName());
        return tc;
    }


    @Transactional(rollbackFor = Exception.class)
    public Long addComm(AddCommercialParam param) {
        if (StringUtils.isBlank(param.getPhone())) {
            log.warn("参数错误,手机号不能为空. param = {}", param);
            throw new DcArgumentException("参数错误,手机号不能为空");
        }
        //用户
        SysUser su = new SysUser();
        su.setEmail(param.getEmail() == null ? "" : param.getEmail());
        su.setPhone(param.getPhone());
        su.setUsername(param.getPhone());

        su.setPlatform(AppClientType.MGM_WEB.getCode());

        su.setName(param.getContacts());
        su.setPassword(su.getPhone());
        su.setPassword(su.hashedPassword());
        List<SysUser> users = sysUserDao.checkByUserNameOrPhoneOrEmail(su);
        if (!CollectionUtils.isEmpty(users)) {
            log.info("联系人电话或联系人邮箱重复. param = {}", param);
            throw new DcServiceException("联系人电话或联系人邮箱重复", Level.INFO);

        }
        List<TCommercial> tCommercials = baseMapper.checkByShortName(param.getShortName());
        if (!CollectionUtils.isEmpty(tCommercials)) {
            log.info("商户简称重复. param = {}", param);
            throw new DcServiceException(4092, "商户简称重复", Level.INFO);   // 4092是延续之前的代码返回值
            //return Rez.error(4092, "");
        }

        int insertUser = sysUserDao.insert(su);

        if (insertUser < 1) {
            throw new DcServerException("user insert error");
        }
        sysUserRoleDao.insertUserRoleIdList(su.getId(), Arrays.asList(2019l));
        //商户
        TCommercial tc = this.fillTCommercial(null, param);
        tc.setPid(param.getPid());
        tc.setStatus(1);
        tc.setCreateTime(new Date());
        int insertCom = baseMapper.insert(tc);
        if (insertCom < 1) {
            throw new DcServerException("com insert error");
        }

        tc.setMerchants("A" + tc.getId());
        if (param.getPid() != null) {
            TCommercial parent = this.tCommercialDao.selectById(param.getPid());
            tc.setTopCommId(parent.getTopCommId());
            tc.setIdChain(parent.getIdChain() + "," + tc.getId());
            tc.setCommLevel(parent.getCommLevel() + 1);
        } else {
            tc.setTopCommId(tc.getId());
            tc.setIdChain(tc.getId().toString());
            tc.setCommLevel(1);
        }

        tc.setCreateTime(null);
        int updateCom = baseMapper.updateById(tc);
        if (updateCom < 1) {
            throw new DcServerException("com update error");
        }

        //int comRef = baseMapper.insertTCommercialRef(entity.getId(), entity.getPid(), SysUserUtil.curUserId());
        SysUserComercial suc = new SysUserComercial();
        suc.setUserId(su.getId());
        suc.setCommercialId(tc.getId());
        suc.setCommercialRole(1);
        suc.setMain(true);
        updateCom = sysUserComercialDao.insert(suc);

        //修改sys_user中topCommId,commId
        su.setTopCommId(tc.getTopCommId());
        su.setCommId(tc.getId());
        sysUserDao.updateById(su);

        if (updateCom < 1) {
            throw new DcServerException("com user insert error");
        }
        if (tc.getCommLevel() == 1) {
            // 新增集团商户后，需要加上管理员权限
            AddUserGroupRequest addUserGroupRequest = new AddUserGroupRequest();
            addUserGroupRequest.setUserId(suc.getUserId());
            addUserGroupRequest.setAuthGroupIds(List.of(1L));
            IotAssert.isTrue(authorityService.modifyUserGroupRef(addUserGroupRequest) > 0,
                "集团商户关联默认权限失败");
        }
        this.publishCommercielEvent(tc);
        return tc.getId();
    }


    @Transactional(rollbackFor = Exception.class)
    public Long addEssComm(AddCommercialParam param) {
        List<TCommercial> tCommercials = baseMapper.checkByShortName(param.getShortName());
        if (!CollectionUtils.isEmpty(tCommercials)) {
            log.info("商户简称重复. param = {}", param);
            throw new DcServiceException(4092, "商户简称重复", Level.INFO);   // 4092是延续之前的代码返回值
            //return Rez.error(4092, "");
        }
        //新增商户
        TCommercial tc = this.fillTCommercial(null, param);
        tc.setPid(param.getPid());
        tc.setStatus(1);
        tc.setCreateTime(new Date());
        int insertCom = baseMapper.insert(tc);
        if (insertCom < 1) {
            throw new DcServerException("com insert error");
        }

        tc.setMerchants("A" + tc.getId());
        if (param.getPid() != null) {
            TCommercial parent = this.tCommercialDao.selectById(param.getPid());
            tc.setTopCommId(parent.getTopCommId());
            tc.setIdChain(parent.getIdChain() + "," + tc.getId());
            tc.setCommLevel(parent.getCommLevel() + 1);
        } else {
            tc.setTopCommId(tc.getId());
            tc.setIdChain(tc.getId().toString());
            tc.setCommLevel(1);
        }

        tc.setCreateTime(null);
        int updateCom = baseMapper.updateById(tc);
        if (updateCom < 1) {
            throw new DcServerException("com update error");
        }
        this.publishCommercielEvent(tc);
        return tc.getId();
    }

    /**
     * 获取下属（含自己）商户ID列表
     *
     * @param idChain
     * @return 下属（含自己）商户ID列表
     */
    public List<Long> getSubCommIdList(String idChain) {
        QueryWrapper<TCommercial> sw = Wrappers.query();
        sw.likeRight(true, "idChain", idChain);
        sw.select("id");
        List<TCommercial> list = this.tCommercialDao.selectList(sw);
        return list.stream().map(TCommercial::getId).collect(Collectors.toList());
    }

    public Integer getLeastPrivilegesCommLevel(String idChain) {
        return this.tCommercialDao.getLeastPrivilegesCommLevel(idChain);
    }

    public boolean hasZftId(List<Long> commIdList, Long excludeZftId) {
        return this.tCommercialDao.hasZftId(commIdList, excludeZftId) != null;
    }

    public BaseResponse editHlhtSitePayType(Long commId, List<String> payTypeList) {
        this.tCommercialDao.editHlhtSitePayType(commId,
            payTypeList.stream().map(String::valueOf).collect(Collectors.joining(",")));
        return RestUtils.success();
    }

    public List<Long> getCommIdList(Long commId) {
        return this.tCommercialDao.getCommIdList(commId);
    }

    public TCommercial convertCommCorpDeposit(Long commId) {
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }

            tc.setEnableCorpDeposit(
                tc.getEnableCorpDeposit() == null || !tc.getEnableCorpDeposit());
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }

    public TCommercial convertCommScore(Long commId, Boolean enableUseScore) {
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }
            tc.setEnableUseScore(enableUseScore);
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }

    public TCommercial convertCommCorpRefund(Long commId) {
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }

            tc.setEnableCorpRefund(tc.getEnableCorpRefund() == null || !tc.getEnableCorpRefund());
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }

    public TCommercial convertCommRefund(Long commId) {
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }

            tc.setEnableCommRefund(tc.getEnableCommRefund() == null || !tc.getEnableCommRefund());
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }

    public TCommercial convertEnableOnlinePay(Long commId) {
        if (commId == null || commId < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(commId);
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }

            tc.setEnableOnlinePay(tc.getEnableOnlinePay() == null || !tc.getEnableOnlinePay());
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }

    public TCommercial convertCommPayAndRefund(ConvertCommPayParam param) {
        if (param.getCommId() == null || param.getCommId() < 1L) {
            throw new DcArgumentException("商户ID无效");
        } else {
            TCommercial tc = this.tCommercialDao.selectById(param.getCommId());
            if (null == tc) {
                throw new DcArgumentException("商户ID无效");
            }
            tc.setEnableCorpDeposit(param.getEnableCorpDeposit());
            tc.setEnableCorpRefund(param.getEnableCorpRefund());
            tc.setEnableCommRefund(param.getEnableCommRefund());
            tc.setEnableOnlinePay(param.getEnableOnlinePay());
            this.tCommercialDao.updateById(tc);
            return tc;
        }
    }
}
