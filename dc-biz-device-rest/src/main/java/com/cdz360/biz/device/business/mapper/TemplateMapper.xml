<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.TemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.Template">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="version" property="version"/>
        <result column="name" property="name"/>
        <result column="commercial_id" property="commercialId"/>
        <result column="calculate_unit" property="calculateUnit"/>
        <result column="calculate_type" property="calculateType"/>
        <result column="charging_type" property="chargingType"/>
        <result column="remark_charge" property="remarkCharge"/>
        <result column="remark_service" property="remarkService"/>
        <result column="create_time" property="createTime"/>
        <result column="free_charge_flag" property="freeChargeFlag"/>
        <result column="delete_flag" property="deleteFlag"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id,
        code,
        version,
        `name`,
        commercial_id AS commercialId,
        calculate_unit AS calculateUnit,
        calculate_type AS calculateType,
        charging_type AS chargingType,
        remark_charge AS remarkCharge,
        remark_service AS remarkService,
        create_time AS createTime,
        free_charge_flag AS freeChargeFlag,
        delete_flag AS deleteFlag
    </sql>

    <!--根据代理商ID获取计费模板列表-->
    <select id="getTemplateList"
            resultType="com.cdz360.biz.device.business.entity.result.TemplateInfoVo">
        SELECT
        t_template.id,
        code,
        version,
        `name`,
        commercial_id AS commercialId,
        calculate_unit AS calculateUnit,
        calculate_type AS calculateType,
        charging_type AS chargingType,
        remark_charge AS remarkCharge,
        remark_service AS remarkService,
        create_time AS createTime,
        free_charge_flag AS freeChargeFlag,
        phone AS createPhone
        FROM t_template
        LEFT JOIN t_r_commercial on t_template.commercial_id = t_r_commercial.id
        <where>
            <if test="deleteFlag != null">
                AND delete_flag = #{deleteFlag}
            </if>
            <if test="commercialId != null">
                AND commercial_id = #{commercialId}
            </if>
            <if test="commercialIdList != null and commercialIdList.size()>0">
                AND commercial_id in
                <foreach collection="commercialIdList" item="item" index="index" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="code != null">
                AND `code` = #{code}
            </if>
            <if test="keywords != null and keywords != ''">
                AND LOCATE(#{keywords}, `name`)
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="getTemplateListAndBeAuthorized" resultType="com.cdz360.biz.device.business.entity.result.TemplateInfoVo">
        SELECT
            t.id,
            t.`code`,
            t.version,
            t.`name`,
        t.`enable`,
            t.commercial_id AS commercialId,
            t.calculate_unit AS calculateUnit,
            t.calculate_type AS calculateType,
            t.charging_type AS chargingType,
            t.remark_charge AS remarkCharge,
            t.remark_service AS remarkService,
            t.create_time AS createTime,
        t.free_charge_flag AS freeChargeFlag
        FROM
            t_template t
            left join t_r_commercial as comm on t.commercial_id = comm.id
        <where>
            <if test="deleteFlag != null">
                AND t.delete_flag = #{deleteFlag}
            </if>
            <!--<if test="commercialId != null">-->
                <!--AND t.commercial_id = #{commercialId}-->
            <!--</if>-->
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
                and comm.idChain like CONCAT(#{commIdChain}, "%")
            </if>
            <if test="commercialIdList != null and commercialIdList.size()>0">
                AND t.commercial_id in
                <foreach collection="commercialIdList" item="item" index="index" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="keywords != null and keywords != ''">
                AND LOCATE(#{keywords}, t.`name`)
            </if>
            <if test="enable != null">
                AND t.enable = #{enable}
            </if>
        </where>

        UNION

        SELECT
            t2.id,
            t2.`code`,
            t2.version,
            t2.`name`,
        t2.`enable`,
            t2.commercial_id AS commercialId,
            t2.calculate_unit AS calculateUnit,
            t2.calculate_type AS calculateType,
            t2.charging_type AS chargingType,
            t2.remark_charge AS remarkCharge,
            t2.remark_service AS remarkService,
            t2.create_time AS createTime,
            t2.free_charge_flag AS freeChargeFlag
        FROM
            t_template t2
        RIGHT JOIN (
            select
                `code`
            FROM
                t_template t
            left join t_site s on s.template_id = t.id
            where
                s.operate_id
                <choose>
                    <when test="commercialIdList != null and commercialIdList.size()>0">
                        in
                        <foreach collection="commercialIdList" item="item" index="index" separator=","
                                 open="(" close=")">
                            #{item}
                        </foreach>
                    </when>
                    <otherwise>
                        = #{currentCommId}
                    </otherwise>
                </choose>
        ) tmp on tmp.`code` = t2.`code`
        <where>
            <if test="deleteFlag != null">
                AND t2.delete_flag = #{deleteFlag}
            </if>
            <!--<if test="currentCommId != null">-->
            <!--AND s.operate_id = #{currentCommId}-->
            <!--</if>-->
            <if test="keywords != null and keywords != ''">
                AND LOCATE(#{keywords}, t2.`name`)
            </if>
            <if test="enable != null">
                AND t2.enable = #{enable}
            </if>
        </where>
        ORDER BY
            createTime DESC
    </select>

    <!--根据计费模板编号，获取最新的计费模板详情-->
    <select id="getLatestTemplateDetailByCode" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_template
        WHERE `code` = #{code}
        ORDER BY version DESC
        LIMIT 1
    </select>


    <!--根据代理商ID获取计费模板列表-->
    <select id="getTemplateListById"
            resultType="com.cdz360.biz.device.business.entity.result.TemplateInfoVo">
        SELECT
        id,
        code,
        version,
        `name`,
        commercial_id AS commercialId,
        calculate_unit AS calculateUnit,
        calculate_type AS calculateType,
        charging_type AS chargingType,
        remark_charge AS remarkCharge,
        remark_service AS remarkService,
        create_time AS createTime,
        free_charge_flag AS freeChargeFlag
        FROM t_template
        <where>
            <if test="list != null and list.size()>0">
                AND id in
                <foreach collection="list" item="item" index="index" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        AND delete_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!--根据计费模板编号，获取所有的计费模板Id-->
    <select id="getTemplateIdList" resultType="java.lang.Long">
        SELECT id
        FROM t_template
        WHERE `code` = #{code}
    </select>

    <!--根据ID获取计费模板-->
    <select id="findById"
            resultType="com.cdz360.biz.device.business.entity.result.TemplateInfoVo">
        SELECT
        id,
        code,
        version,
        `name`,
        commercial_id AS commercialId,
        calculate_unit AS calculateUnit,
        calculate_type AS calculateType,
        charging_type AS chargingType,
        remark_charge AS remarkCharge,
        remark_service AS remarkService,
        create_time AS createTime,
        free_charge_flag AS freeChargeFlag,
        `enable`
        FROM t_template
        where id=#{id}
        <if test="lock == true">
            for update
        </if>
    </select>

    <!--更新计费模板使能状态-->
    <update id="updateEnable">
        update t_template set `enable`=#{enable}
        where id=#{id}
    </update>

    <!--更新计费模板删除标识-->
    <update id="updateDeleteFlagById">
        update t_template set delete_flag=true
        where id=#{id} and delete_flag=false
    </update>
</mapper>
