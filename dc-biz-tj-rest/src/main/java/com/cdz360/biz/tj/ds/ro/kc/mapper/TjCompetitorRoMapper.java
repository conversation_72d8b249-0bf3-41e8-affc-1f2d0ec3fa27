package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import com.cdz360.biz.model.tj.kc.vo.CompetitorSiteBi;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjCompetitorRoMapper {

    TjCompetitorPo getById(@Param("id") Long id);

    TjCompetitorPo getOneByName(@Param("name") String name);

    List<TjCompetitorPo> findTjCompetitor(ListTjCompetitorParam param);

    long countTjCompetitor(ListTjCompetitorParam param);

    // 投建分析划分区域内 - 竞争者场站统计
    List<CompetitorSiteBi> selectCompetitorSiteBi(
        @Param("analysisId") long analysisId,
        @Param("num") int num,
        @Param("petIdSetString") String petIdSetString);
}

