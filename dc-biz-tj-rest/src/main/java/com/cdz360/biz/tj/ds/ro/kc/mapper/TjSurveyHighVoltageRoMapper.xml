<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_HIGH_VOLTAGE_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="capacity" jdbcType="INTEGER" property="capacity"/>
    <result column="cableDistance" jdbcType="INTEGER" property="cableDistance"/>
    <result column="poleTopSwitch" jdbcType="INTEGER" property="poleTopSwitch"/>
    <result column="ringMainUnit" jdbcType="INTEGER" property="ringMainUnit"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_HIGH_VOLTAGE_PO">
    select * from t_tj_survey_high_voltage where id = #{id} and enable = true
  </select>

  <select id="findTjSurveyHighVoltageBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_HIGH_VOLTAGE_PO">
    select * from t_tj_survey_high_voltage where surveyNo = #{surveyNo} and enable = true
    order by id desc
  </select>


</mapper>

