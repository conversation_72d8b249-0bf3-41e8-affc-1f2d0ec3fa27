package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 *  ${description}
 * @since 2018/11/30 16:59
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,
        fallbackFactory = CardFeignClientHystrix.class)
public interface CardFeignClient {
    /**
     * 有固定条件card_status =10001
     * @param cardNo
     * @return
     */
    @RequestMapping(value = "/api/card/getCardByCardNo", method = RequestMethod.POST)
    ObjectResponse<Card> getCardByCardNo(@RequestParam(value = "cardNo") String cardNo,
                                         @RequestParam(value = "topCommId") Long topCommId);

    /**
     * 无固定条件
     * @param cardNo
     * @return
     */
    @PostMapping("/api/card/queryCardByCardNo")
    ObjectResponse<Card> queryCardByCardNo(@RequestParam(value = "cardNo") String cardNo);


    /**
     * 批量更新卡
     * by card_no
     * @param cardList 卡集合
     * @return
     */
    @RequestMapping(value = "/api/card/updateBatchCard", method = RequestMethod.POST)
    BaseResponse updateBatchCard(@RequestBody List<Card> cardList);

    /**
     * 根据条件批量查询场站下的卡数量
     * @param siteIdList
     * @return
     */
    @PostMapping(value = "/api/card/findUrgencyCardNumCount")
    ListResponse<SiteCardCount> findUrgencyCardNumCount(@RequestBody List<String> siteIdList);

    /**
     * 根据卡、VIN码获取适用场站
     * @param cardNo
     * @param type
     * @return
     */
    @GetMapping(value = "/api/card/online/site")
    ListResponse<String> getStationsOfCard(@RequestParam(value = "cardNo") String cardNo, @RequestParam(value = "type") Integer type,@RequestParam(value = "idChain") String idChain);

    @GetMapping(value = "/api/card/getEmergencyCardBySiteId")
    ListResponse<String> getEmergencyCardBySiteId(@RequestParam(value = "siteId") String siteId, @RequestParam(value = "commId") Long commId);

    @GetMapping(value = "/api/card/getEmergencyCardByCorpId")
    ObjectResponse<MoveCorpCardList> getEmergencyCardByCorpId(@RequestParam(value = "corpId") Long corpId,
                                                              @RequestParam(value = "commId") Long commId);

    @GetMapping(value = "/api/card/getMoveCorpCardAuthByCorpId")
    ObjectResponse<MoveCorpSiteAuthList> getMoveCorpCardAuthByCorpId(@RequestParam(value = "corpId") Long corpId,
                                                  @RequestParam(value = "commId") Long commId);

    /**
     * 海外版，新增卡片并且发卡给用户
     * @param param
     * @return
     */
    @PostMapping(value = "/api/card/commercial/addCard")
    BaseResponse addEssCard(@RequestBody AddEssCardParam param);

//    @GetMapping(value = "/api/card/syncCardAmount")
//    ObjectResponse<Boolean> syncAmount(@RequestParam(value = "cardNo") String cardNo,
//                                       @RequestParam(value = "amount") BigDecimal amount,
//                                       @RequestParam(value = "result") Integer result);
//
//    @GetMapping(value = "/api/card/getSyncCardAmount")
//    ListResponse<CardAmountSyncVo> getSyncCardAmount(CardAmountSyncParam param);
}
