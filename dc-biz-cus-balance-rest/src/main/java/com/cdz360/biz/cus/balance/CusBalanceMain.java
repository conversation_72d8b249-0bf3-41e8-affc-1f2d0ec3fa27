package com.cdz360.biz.cus.balance;

import com.netflix.discovery.EurekaClient;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import jakarta.annotation.PreDestroy;
import reactor.core.publisher.Hooks;

@SpringBootApplication
@ComponentScan({"io.github.alphajiang.hyena",
        "com.cdz360.biz",
        "com.chargerlinkcar.framework.common.config",
        "com.cdz360.biz.cus.balance.config"})
@MapperScan(basePackages = {"io.github.alphajiang.hyena.ds.mapper"})
@EnableTransactionManagement
@EnableScheduling
public class CusBalanceMain {

    private static final Logger logger = LoggerFactory.getLogger(CusBalanceMain.class);


    @Autowired
    private EurekaClient discoveryClient;

    /**
     * 主函数
     */
    public static void main(String[] args) {
        logger.info("starting......");
        Hooks.enableAutomaticContextPropagation();  // 输出调用链traceId
        new SpringApplicationBuilder(CusBalanceMain.class).web(WebApplicationType.REACTIVE).run(args);
        logger.info("started");
    }


    @PreDestroy
    public void destroy() {
        logger.info("going to shutdown");

        //DiscoveryManager.getInstance().shutdownComponent();
        discoveryClient.shutdown();
        logger.info("eureka de-registered.....");
    }
}
