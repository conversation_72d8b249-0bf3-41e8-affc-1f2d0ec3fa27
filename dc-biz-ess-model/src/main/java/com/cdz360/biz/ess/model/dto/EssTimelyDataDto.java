package com.cdz360.biz.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(title = "储能统计数据", description = "主要用于小时/日/月数据统计")
public class EssTimelyDataDto {

    public EssTimelyDataDto() {
    }

    public EssTimelyDataDto(LocalDateTime time) {
        this.time = time;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "时间", description = "数据对应的时间")
    private LocalDateTime time;

    @Schema(description = "充电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal inKwh;

    @Schema(description = "放电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal outKwh;

    @Schema(description = "光伏发电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pvKwh;

    @Schema(description = "入网电量-购电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal purchaseKwh;

    @Schema(description = "上网电量-馈电量(kW·h)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal feedKwh;

    @Schema(description = "收益")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal profit;

}
