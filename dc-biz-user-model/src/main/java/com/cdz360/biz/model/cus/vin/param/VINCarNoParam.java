package com.cdz360.biz.model.cus.vin.param;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询VIN对应的车牌号传输参数")
@Data
@Accessors(chain = true)
public class VINCarNoParam {
    @Schema(description = "VIN 列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> vinList;

    @Schema(description = "商户ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;
}
