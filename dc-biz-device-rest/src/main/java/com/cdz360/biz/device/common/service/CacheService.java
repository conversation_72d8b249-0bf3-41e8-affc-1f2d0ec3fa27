package com.cdz360.biz.device.common.service;

import com.cdz360.biz.device.common.cache.ICacheKey;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@Service
public class CacheService {
    private static final Logger log = LoggerFactory.getLogger(CacheService.class);
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    public CacheService() {
    }

    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.redisTemplate = (RedisTemplate)applicationContext.getBean("redisTemplate");
    }

//    public Set<String> getKeys(String keyPrefix) {
//        if (keyPrefix == null) {
//            return null;
//        } else {
//            Set<String> keys = this.redisTemplate.keys(keyPrefix);
//            return keys;
//        }
//    }

//    public void setKeyTimeout(ICacheKey cacheKey, String keyName, Long timeout) {
//        if (timeout != null && timeout != -1L) {
//            String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//            this.redisTemplate.expire(key, timeout, TimeUnit.SECONDS);
//        }
//    }

    public void setValue(ICacheKey cacheKey, String keyName, String value) {
        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
        this.redisTemplate.opsForValue().set(key, value);
        if (cacheKey.getTimeout() != null) {
            this.redisTemplate.opsForValue().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
        }

    }

//    public boolean setNx(ICacheKey cacheKey, String keyName, String value) {
//        if (cacheKey.getTimeout() == null) {
//            throw new NullPointerException("原子操作时，缓存超时时间不能为空");
//        } else {
//           return  this.redisTemplate.execute(new RedisCallback<Boolean>() {
//                @Override
//                public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
//                    //connection.openPipeline();
//                    String key = cacheKey.getKeyPrefix() + keyName;
//                    Boolean writeSuccess = connection.setNX(key.getBytes(), value.getBytes());
//                    if ( writeSuccess) {
//                        connection.expire(key.getBytes(), cacheKey.getTimeout());
//                    }
//                    return writeSuccess == null ? false : writeSuccess;
//                }
//            });
//
//        }
//    }

//    public Object getAndSetValue(ICacheKey cacheKey, String keyName, Object value) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        Object obj = this.redisTemplate.opsForValue().getAndSet(key, value);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForValue().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//        return obj;
//    }

//    public boolean keyExist(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.hasKey(key);
//    }

    public Object getValue(ICacheKey cacheKey, String keyName) {
        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
        return this.redisTemplate.opsForValue().get(key);
    }

//    public void deleteValue(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.delete(key);
//    }

//    public void putHash(ICacheKey cacheKey, String keyName, String hashKey, Object hashValue) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForHash().put(key, hashKey, hashValue);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForHash().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//    }
//
//    public void putMultiHash(ICacheKey cacheKey, String keyName, Map<String, ? extends Object> hashKeyValueMap) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForHash().putAll(key, hashKeyValueMap);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForHash().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//    }

//    public boolean hashExist(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.hasKey(key);
//    }
//
//    public boolean hashKeyExist(ICacheKey cacheKey, String keyName, String hashKey) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForHash().hasKey(key, hashKey);
//    }
//
//    public List<Object> getAllHash(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForHash().values(key);
//    }
//
//    public Map<Object, Object> getAllHashMap(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForHash().entries(key);
//    }

//    public Object getHashValue(ICacheKey cacheKey, String keyName, String hashKey) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForHash().get(key, hashKey);
//    }
//
//    public List<Object> getMultiHashValue(ICacheKey cacheKey, String keyName, Collection hashKeys) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForHash().multiGet(key, hashKeys);
//    }

//    public void deleteMultiHash(ICacheKey cacheKey, String keyName, List<String> hashKeyList) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        Object[] array = hashKeyList.toArray();
//        this.redisTemplate.opsForHash().delete(key, array);
//    }
//
//    public void deleteAllHash(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.delete(key);
//    }
//
//    public void deleteHashValue(ICacheKey cacheKey, String keyName, String hashKey) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForHash().delete(key, hashKey);
//    }
//
//    public void leftPush(ICacheKey cacheKey, String keyName, Object value) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForList().leftPush(key, value);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForList().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//    }

//    public void leftPushList(ICacheKey cacheKey, String keyName, Collection list) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForList().leftPushAll(key, list);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForList().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//    }

//    public boolean listExist(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.hasKey(key);
//    }

//    public Object rightPop(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForList().rightPop(key);
//    }
//
//    public Long getListSize(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForList().size(key);
//    }

//    public void addSetMember(ICacheKey cacheKey, String keyName, Object value) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForSet().add(key, value);
//        if (cacheKey.getTimeout() != null) {
//            this.redisTemplate.opsForList().getOperations().expire(key, cacheKey.getTimeout(), TimeUnit.SECONDS);
//        }
//
//    }

//    public Set<Object> getAllSetMember(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForSet().members(key);
//    }
//
//    public Long getSetMemberSize(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForSet().size(key);
//    }
//
//    public boolean getSetMemberExist(ICacheKey cacheKey, String keyName, Object member) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        return this.redisTemplate.opsForSet().isMember(key, member);
//    }

//    public void deleteSetMember(ICacheKey cacheKey, String keyName, Object member) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.opsForSet().remove(key, member);
//    }

//    public void deleteAllSet(ICacheKey cacheKey, String keyName) {
//        String key = StringUtils.join(cacheKey.getKeyPrefix(), keyName);
//        this.redisTemplate.delete(key);
//    }
}
