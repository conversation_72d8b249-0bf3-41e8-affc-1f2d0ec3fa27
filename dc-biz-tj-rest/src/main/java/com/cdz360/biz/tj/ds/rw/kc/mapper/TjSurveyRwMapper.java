package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyRwMapper {

    TjSurveyPo getByNo(@Param("id") String no, @Param("lock") boolean lock);

    int insertTjSurvey(TjSurveyPo tjSurveyPo);

    int updateTjSurvey(TjSurveyPo tjSurveyPo);

}

