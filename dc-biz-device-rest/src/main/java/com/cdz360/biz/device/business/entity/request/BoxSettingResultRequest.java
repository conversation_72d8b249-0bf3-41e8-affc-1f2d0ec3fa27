package com.cdz360.biz.device.business.entity.request;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * @Classname BoxSettingResultRequest
 * @Description 桩配置结果查询参数
 * @Date 2019/6/12 13:18
 * @Created by Rafael
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BoxSettingResultRequest extends BaseRequest {
    private List<Integer> status;
    private Long commId;
    private String commIdChain;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}