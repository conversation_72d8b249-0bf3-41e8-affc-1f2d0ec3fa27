package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询相同勘察场站参数")
@Data
@Accessors(chain = true)
public class RepeatSurveyParam {

    @ApiModelProperty(value = "场站名称")
    @JsonInclude(Include.NON_NULL)
    private String siteName;

    @ApiModelProperty(value = "场站地址")
    @JsonInclude(Include.NON_NULL)
    private String address;

    @ApiModelProperty(value = "经度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal longitude;

    @ApiModelProperty(value = "纬度")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal latitude;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
