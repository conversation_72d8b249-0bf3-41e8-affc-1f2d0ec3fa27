package com.cdz360.biz.model.cus.card.vo;

import com.cdz360.biz.model.cus.card.po.CardAmountSyncPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * CardAmountSyncVo
 *
 * @since 7/20/2021 10:54 AM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@Schema(description = "充值余额同步记录VO")
@EqualsAndHashCode(callSuper = true)
public class CardAmountSyncVo extends CardAmountSyncPo {
    private String phone;
}