package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.tj.kc.type.PointZoneStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "获取投建分析划分点列表参数")
@Data
@Accessors(chain = true)
public class UpdateAnalysisPointStatusParam {

    @Schema(description = "投建分析记录ID", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private Long analysisId;

    @Schema(description = "点位辐射区状态", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private PointZoneStatus status;

    @Schema(description = "需求变更标号列表", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(Include.NON_NULL)
    private List<Long> numList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
