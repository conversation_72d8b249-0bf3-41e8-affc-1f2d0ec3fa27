<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaAnalysisRwMapper">

  <resultMap id="RESULT_TJ_AREA_ANALYSIS_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="aid" jdbcType="BIGINT" property="aid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="radius" property="radius"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_AREA_ANALYSIS_PO">
    select * from t_tj_area_analysis where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjAreaAnalysis" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo">
    insert into t_tj_area_analysis (`aid`,
    `name`,
    `radius`,
    `location`,
    `staticmap`,
    `createTime`,
    `updateTime`)
    values (#{aid},
    #{name},
    #{radius},
    ST_GeomFromText(#{location, jdbcType=JAVA_OBJECT,
    typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
    #{staticmap},
    now(),
    now())
  </insert>

  <update id="updateTjAreaAnalysis"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo">
    update t_tj_area_analysis set
    <if test="aid != null">
      aid = #{aid},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="radius != null">
      radius = #{radius},
    </if>
    <if test="location != null">
      location =
      ST_GeomFromText(#{location, jdbcType=JAVA_OBJECT,
      typeHandler=com.cdz360.biz.ds.trading.PointTypeHandler}),
    </if>
    <if test="staticmap != null">
      staticmap = #{staticmap},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjAreaAnalysis">
    update t_tj_area_analysis set
    enable = false, updateTime = now()
    where id = #{id}
  </update>

</mapper>

