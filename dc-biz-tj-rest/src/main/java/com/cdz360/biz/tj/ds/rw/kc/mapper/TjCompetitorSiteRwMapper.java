package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjCompetitorSiteRwMapper {

    TjCompetitorSitePo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjCompetitorSite(TjCompetitorSitePo tjCompetitorSitePo);

    int updateTjCompetitorSite(TjCompetitorSitePo tjCompetitorSitePo);

    int batchUpset(@Param(value = "poList") List<TjCompetitorSitePo> poList);
}

