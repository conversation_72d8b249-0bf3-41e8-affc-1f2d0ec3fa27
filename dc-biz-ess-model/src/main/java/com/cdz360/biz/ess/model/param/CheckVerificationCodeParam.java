package com.cdz360.biz.ess.model.param;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "验证码正确性验证参数")
@Data
public class CheckVerificationCodeParam {

    @NotBlank
    @Schema(description = "用户账户")
    @Email // 当前仅支持email
    private String account;

    @Schema(description = "验证码")
    @NotBlank
    private String verificationCode;

}
