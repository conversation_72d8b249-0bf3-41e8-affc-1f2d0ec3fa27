package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询光储ESS列表")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEssParam extends BaseListParam {

    @Schema(title = "帐号ID")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "ESS编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String dno;

    @Schema(description = "商户Id链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "场站组id列表 仅用于管理后台相关接口")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> gids;
}
