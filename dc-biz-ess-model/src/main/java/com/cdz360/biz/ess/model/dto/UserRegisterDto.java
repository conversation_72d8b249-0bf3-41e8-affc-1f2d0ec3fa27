package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.model.app.type.AppClientType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户注册参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserRegisterDto extends BaseUserAccount {

    @Schema(description = "注册来源")
    @JsonInclude(Include.NON_NULL)
    private AppClientType clientType;

    @Schema(description = "用户所在位置经度")
    private BigDecimal lng;

    @Schema(description = "用户所在位置纬度")
    private BigDecimal lat;
}
