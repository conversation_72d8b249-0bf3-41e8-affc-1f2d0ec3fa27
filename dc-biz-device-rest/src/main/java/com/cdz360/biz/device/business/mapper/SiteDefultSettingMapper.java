package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.SiteDefultSetting;
import com.cdz360.biz.device.business.entity.request.SiteDefultSettingRequest;
import com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Interfacename SiteDefultSettingMapper
 * @Description TODO
 * @Date 2019/6/5
 * @Created by wangzheng
 */
@Mapper
public interface SiteDefultSettingMapper extends BaseMapper<SiteDefultSetting> {
    /**
     * 查询
     * @param request
     * @return
     */
    List<SiteDefultSettingVO> selectSiteDefultSettingList(SiteDefultSettingRequest request);

    /**
     * 通过设备序列号/桩号查询场站通用配置
     * @param boxOutFactoryCode
     * @return
     */
    SiteDefultSettingVO selectByBoxOutFactoryCode(@Param("boxOutFactoryCode") String boxOutFactoryCode);

    int updateSiteDefaultPriceScheme(
            @Param("siteId") String siteId,
            @Param("priceSchemeId") Long priceSchemeId);
}
