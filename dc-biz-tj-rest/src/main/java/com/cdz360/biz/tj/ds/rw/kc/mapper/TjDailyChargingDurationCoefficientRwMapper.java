package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjDailyChargingDurationCoefficientRwMapper {

    TjDailyChargingDurationCoefficientPo getById(@Param("id") Long id, @Param("lock") boolean lock);
    int updateTjDailyChargingDurationCoefficient(TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo);

}

