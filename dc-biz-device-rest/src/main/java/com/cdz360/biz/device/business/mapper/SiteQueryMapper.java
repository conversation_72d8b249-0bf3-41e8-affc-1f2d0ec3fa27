package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.Site;
import com.cdz360.biz.device.business.entity.request.SiteListRequest;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 站点表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-23
 */
@Mapper
public interface SiteQueryMapper extends BaseMapper<Site> {

    /**
     * 获取站点简单信息列表
     *
     * @param query
     * @return
     */
    List<SiteSimpleInfoVo> getSiteSimpleList(SiteListRequest query);


    /**
     * 获取场站列表
     *
     * @return
     */
    List<Site> listSite(@Param("start") long start,
                        @Param("size") int size,
                        @Param("templateId") Long templateId,
                        @Param("lock") boolean lock);

    /**
     * 获取场站
     *
     * @return
     */
    Site getSiteBySiteId(@Param("siteId") String siteId);
}
