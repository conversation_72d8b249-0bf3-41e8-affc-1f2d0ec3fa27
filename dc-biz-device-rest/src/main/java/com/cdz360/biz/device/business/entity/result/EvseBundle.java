package com.cdz360.biz.device.business.entity.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.time.DateFormatUtils;

import java.io.Serializable;
import java.time.ZoneId;
import java.util.Date;
import java.util.TimeZone;


/**
 * @ClassName： EvseBundle
 * @Description: 桩升级包信息
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/18 13:13
 */
@ApiModel(value = "com.cdz360.biz.device.business.entity.result.EvseBundle")
@Data
@Accessors(chain = true)
public class EvseBundle implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    private Long id;
    /**
     * 升级包唯一编号
     */
    @ApiModelProperty(value = "升级包唯一编号")
    private Long version;
    /**
     * 上传升级压缩包的文件名
     */
    @ApiModelProperty(value = "上传升级压缩包的文件名")
    private String fileName;
    /**
     * 升级要点
     */
    @ApiModelProperty(value = "升级要点")
    private String releaseNote;
    /**
     * 操作人ID
     */
    @ApiModelProperty(value = "操作人ID")
    private Long opId;
    /**
     * 操作人姓名
     */
    @ApiModelProperty(value = "操作人姓名")
    private String opName;
    /**
     * 自描述文件版本
     */
    @ApiModelProperty(value = "自描述文件版本")
    private Integer protocol;
    /**
     * 自描述文件内容
     */
    @ApiModelProperty(value = "自描述文件内容")
    private String context;
    /**
     * 新增时间
     */
    @ApiModelProperty(value = "新增时间")
    private Date createTime;
    /**
     * 最后更新时间
     */
    @ApiModelProperty(value = "最后更新时间")
    private Date updateTime;
    /**
     * 是否有效
     */
    @ApiModelProperty(value = "是否有效")
    private Boolean enable;

    /**
     * 上传进度
     */
    @ApiModelProperty(value = "上传进度 上传中:0-100 上传失败:-1")
    private Integer progress;


    public String getCreateTimeDisplay() {
        return DateFormatUtils.format(createTime, "yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone(ZoneId.of("GMT+8")));
    }

    public String getUpdateTimeDisplay() {
        return DateFormatUtils.format(updateTime, "yyyy-MM-dd HH:mm:ss", TimeZone.getTimeZone(ZoneId.of("GMT+8")));
    }
}