package com.cdz360.biz.model.cus.corp.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;


/**
 * CorpOrgPo
 *  企业客户组织
 * @since 2019/12/11 13:56
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class CorpOrgPo {
    @Schema(description = "主键 全局唯一")
    private Long id;
    @Schema(description = "企业id")
    private Long corpId;
    @Schema(description = "组织名称")
    private String orgName;
    @Schema(description = "组织级别")
    private Integer orgLevel;
    @Schema(description = "一级组织id")
    private Long l1Id;
    @Schema(description = "二级组织id")
    private Long l2Id;
    private Boolean enable;
    private String account;
    private String password;
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private Date updateTime;
    private Integer commId;

}
