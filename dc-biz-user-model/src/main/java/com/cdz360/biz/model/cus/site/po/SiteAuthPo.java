package com.cdz360.biz.model.cus.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "鉴权介质使用范围(可使用场站)")
public class SiteAuthPo {

    /**
     * {@link com.cdz360.biz.model.cus.site.type.AuthMediaType}
     */
    @Schema(description = "鉴权介质类型")
    private Integer type;

    @Schema(description = "顶级商户Id")
    private Long topCommId;

    @Schema(description = "根据介质类型判断为VIN码/卡片逻辑卡号")
    private String account;

    @Schema(description = "场站Id")
    private String  siteId;

    @Schema(description = "记录使能状态: （true有效false无效）")
    private Boolean enable;
}
