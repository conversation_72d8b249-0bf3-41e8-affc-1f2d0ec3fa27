<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationIncomeRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_OPERATION_INCOME_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="stationSubsidy" jdbcType="DECIMAL" property="stationSubsidy"/>
    <result column="stationSubsidyYear" jdbcType="INTEGER" property="stationSubsidyYear"/>
    <result column="operationSubsidyPrice" jdbcType="DECIMAL" property="operationSubsidyPrice"/>
    <result column="elecPrice" jdbcType="DECIMAL" property="elecPrice"/>
    <result column="firstYearThirdMonthServicePrice" jdbcType="DECIMAL" property="firstYearThirdMonthServicePrice"/>
    <result column="firstYearLastNinthMonthServicePrice" jdbcType="DECIMAL" property="firstYearLastNinthMonthServicePrice"/>
    <result column="serviceIncrease" jdbcType="DECIMAL" property="serviceIncrease"/>
    <result column="serviceIncreaseMaxPrice" jdbcType="DECIMAL" property="serviceIncreaseMaxPrice"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_OPERATION_INCOME_PO">
    select * from t_tj_survey_operation_income where id = #{id}
  </select>

  <select id="findTjSurveyOperationIncomeBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_OPERATION_INCOME_PO">
    select * from t_tj_survey_operation_income where surveyNo = #{surveyNo} and enable=true
  </select>


</mapper>

