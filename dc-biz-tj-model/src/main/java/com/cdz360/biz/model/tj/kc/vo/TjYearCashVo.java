package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "勘察站现金流明细")
public class TjYearCashVo {

    @Schema(description = "第几年")
    private Integer year;

    @Schema(description = "预计估算电量")
    private BigDecimal kwh;

    @Schema(description = "高压投资额")
    private BigDecimal tzHighVoltageFee;

    @Schema(description = "低压投资额")
    private BigDecimal tzLowVoltageFee;

    @Schema(description = "设备投资额")
    private BigDecimal tzEquipmentsFee;

    @Schema(description = "设备更新投资额")
    private BigDecimal tzUpdateEquipmentsFee;

    @Schema(description = "后期投入")
    private BigDecimal tzOtherFee;

    @Schema(description = "折旧（高低压)")
    private BigDecimal zjHighAndLowVoltageFee;

    @Schema(description = "折旧（设备）")
    private BigDecimal zjEquipmentsFee;

    @Schema(description = "折旧（设备更新投入）")
    private BigDecimal zjUpdateEquipmentsFee;

    @Schema(description = "税前运营收入费用")
    private BigDecimal operationIncomeFee;

    @Schema(description = "税前运营补贴收入费用")
    private BigDecimal operationSubsidyFee;

    @Schema(description = "建站补贴费用")
    private BigDecimal stationSubsidyFee;

    @Schema(description = "通道费")
    private BigDecimal channelFee;

    @Schema(description = "平台托管费")
    private BigDecimal platformFee;

    @Schema(description = "运维费")
    private BigDecimal maintFee;

    @Schema(description = "引流费用")
    private BigDecimal flowFee;

    @Schema(description = "电费支出")
    private BigDecimal eleFee;

    @Schema(description = "电损支出")
    private BigDecimal eleLossFee;

    @Schema(description = "服务费分成")
    private BigDecimal serviceSharingFee;

    @Schema(description = "场地租金")
    private BigDecimal rentFee;

    @Schema(description = "其他运营费用")
    private BigDecimal otherOperationFee;

    @Schema(description = "管理费用")
    private BigDecimal manageFee;

    @Schema(description = "财务费用")
    private BigDecimal financeFee;

    @Schema(description = "增值税")
    private BigDecimal vatFee;

    @Schema(description = "企业所得税")
    private BigDecimal citFee;

    @Schema(description = "累计现金流量（含当期）")
    private BigDecimal totalCashFee;

    @Schema(description = "税前利润总额")
    private BigDecimal profitBeforeTaxFee;

    @Schema(description = "累计现金流量（不含当期）")
    private BigDecimal totalBeforeCashFee;

    @Schema(description = "累计税前利润总额（不含当期）")
    private BigDecimal totalBeforeProfitBeforeTaxFee;

    @Schema(description = "累计企业所得税（不含当期）")
    private BigDecimal totalBeforeCitFee;

}

