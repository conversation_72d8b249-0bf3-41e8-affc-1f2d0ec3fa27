package com.cdz360.biz.model.cus.corp.param;

import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class BatchAddCreditUserParam {

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long topCommId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long commId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long corpId;

    @Schema(description = "组织ID")
    private Long orgId;

    @Schema(accessMode = Schema.AccessMode.READ_ONLY)
    private Long opUid;

    private List<CreditUser> userList;

    @Data
    @Accessors(chain = true)
    public static class CreditUser {

        @Schema(accessMode = Schema.AccessMode.READ_ONLY)
        private Long uid;

        @Schema(description = "手机号")
        private String phone;

        @Schema(description = "姓名")
        private String name;

        @Schema(description = "限额周期")
        private LimitCycle durType;

        @Schema(description = "额度", example = "100.00")
        private BigDecimal limitMoney;
    }
}
