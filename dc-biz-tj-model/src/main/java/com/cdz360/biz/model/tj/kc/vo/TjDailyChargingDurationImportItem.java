package com.cdz360.biz.model.tj.kc.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class TjDailyChargingDurationImportItem {

    private String provinceName;
    private String cityName;
    private String areaName;
    private String firstYearThirdMonth;
    private String firstYearLastNinthMonth;
    private String secondYear;
    private String thirdYear;
    private String fourthYear;
    private String fifthYear;
    private String sixthYear;
    private String seventhYear;
    private String eighthYear;
    private String ninthYear;
    private String tenthYear;

}
