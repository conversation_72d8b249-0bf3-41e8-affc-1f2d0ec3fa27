package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.device.business.constant.BizType;
import com.cdz360.biz.device.business.constant.SiteStatusEnum;
import com.cdz360.biz.device.common.constant.ConnectorStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 站点详细信息
 *
 * <AUTHOR>
 * @date Create on 2018/7/9 11:40
 */
@Data
@Accessors(chain = true)
@ApiModel("站点详细信息")
public class SiteDetailInfoVo implements Serializable {


    /**
     * 站点ID
     */
    @ApiModelProperty("站点ID")
    private String id;
    /**
     * 站点编号
     */
    @ApiModelProperty("站点编号")
    private String idNo;
    /**
     * 站点名称
     */
    @ApiModelProperty("站点名称")
    private String siteName;

    /**
     * 站点自定义编号
     */
    @ApiModelProperty("站点自定义编号")
    private String siteNo;

    /**
     * 充电冻结金额
     */
    @ApiModelProperty("充电冻结金额")
    private BigDecimal frozenAmount;


    /**
     * 场站支持电流形式
     */
    @ApiModelProperty(value = "场站支持电流形式")
    private SupplyType supplyType;

    /**
     * 经度
     */
    @ApiModelProperty("经度")
    private BigDecimal longitude;
    /**
     * 纬度
     */
    @ApiModelProperty("纬度")
    private BigDecimal latitude;
    /**
     * 经度（微信小程序使用【预留】)
     */
    @ApiModelProperty("经度（微信小程序使用【预留】)")
    private Double longitudeWX;
    /**
     * 纬度(微信小程序使用【预留】)
     */
    @ApiModelProperty("纬度(微信小程序使用【预留】)")
    private Double latitudeWX;
    /**
     * 站点地址
     */
    @ApiModelProperty("站点地址")
    private String address;
    /**
     * 省份编码
     */
    @ApiModelProperty("省份编码")
    private Integer province;
    /**
     * 省份名称
     */
    @ApiModelProperty("省份名称")
    private String provinceName;
    /**
     * 城市编码
     */
    @ApiModelProperty("城市编码")
    private Integer city;
    /**
     * 城市名称
     */
    @ApiModelProperty("城市名称")
    private String cityName;
    /**
     * 区域编码
     */
    @ApiModelProperty("区域编码")
    private Integer area;
    /**
     * 区域名称
     */
    @ApiModelProperty("区域名称")
    private String areaName;
    /**
     * 服务号码
     */
    @ApiModelProperty("服务号码")
    private String phone;
    /**
     * 站点状态{@link SiteStatusEnum}
     */
    @ApiModelProperty("站点状态")
    private Integer status;
    /**
     * 运营商ID
     */
    @ApiModelProperty("运营商ID")
    private Long operateId;
    /**
     * 运营商名称
     */
    @ApiModelProperty("运营商名称")
    private String operateName;
    /**
     * 所属集团商户ID
     */
    @ApiModelProperty("所属集团商户ID")
    private Long maxCommercialId;
    /**
     * 所属集团商户名称
     */
    @ApiModelProperty("所属集团商户名称")
    private String maxCommercialName;
    /**
     * 物业ID或代理商账号ID
     */
    @ApiModelProperty("物业ID或代理商账号ID")
    private Long merchantId;
    /**
     * 联系人
     */
    @ApiModelProperty("联系人")
    private String contacts;
    /**
     * 联系人号码
     */
    @ApiModelProperty("联系人号码")
    private String contactsPhone;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
     */
    @ApiModelProperty("站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**")
    private Integer type;
    /**
     * 工作日服务时间
     */
    @ApiModelProperty("工作日服务时间")
    private String serviceWorkdayTime;
    /**
     * 节假日服务时间
     */
    @ApiModelProperty("节假日服务时间")
    private String serviceHolidayTime;
    /**
     * 停车是否收费 **0-未知 1-收费 2-免费**
     */
    @ApiModelProperty("停车是否收费 **0-未知 1-收费 2-免费**")
    private Integer park;
    /**
     * 停车费: 单位元 
     */
    // TODO: 2019/8/31 数据库字段类型待调整，暂时还原为String
    @ApiModelProperty("停车费: 单位元 ")
    private String parkFee;
    /**
     * 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
     */
    @ApiModelProperty("是否需要预约 **0-未知 1-需要预约 2-不需要预约**")
    private Integer appoint;
    /**
     * 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
     */
    @ApiModelProperty("使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**")
    private Integer scope;
    /**
     * 上线时间
     */
    @ApiModelProperty("上线时间")
    private Date onlineDate;
    /**
     * 最后更新时间
     */
    @ApiModelProperty("最后更新时间")
    private Date updateTime;
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Date createTime;
    /**
     * 站点图片
     */
    @ApiModelProperty("站点图片")
    private String images;
    /**
     * 支持品牌
     */
    @ApiModelProperty("支持品牌")
    private String brandIds;
    /**
     * 支付方式
     */
    @ApiModelProperty("支付方式")
    private String payMod;
    /**
     * 充电接口状态信息统计: key-充电接口状态{@link ConnectorStatusEnum} value-充电接口数量
     */
    @ApiModelProperty("充电接口状态信息统计")
    private Map<String, Integer> chargerStatusMap;
    /**
     * 收费说明
     */
    @ApiModelProperty("收费说明")
    private String feeDescription;
    /**
     * 发票描述
     */
    @ApiModelProperty("发票提供方")
    private String invoiceDesc;
    /**
     * 站点收费范围 最低
     */
    @ApiModelProperty("站点收费范围 最低")
    private Integer feeMin;
    /**
     * 站点收费范围 最高
     */
    @ApiModelProperty("站点收费范围 最高")
    private Integer feeMax;
    /**
     * 距离,单位：米
     */
    @ApiModelProperty("距离,单位：米")
    private Double distance;
    /**
     * 时长，单位：秒
     */
    @ApiModelProperty("时长，单位：秒")
    private Double duration;

    /**
     * 站点默认计费模板Id
     */
    @ApiModelProperty("站点默认计费模板Id")
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    @ApiModelProperty("站点默认计费模板名称")
    private String templateName;

    @ApiModelProperty(value = "运营属性 0, 未知; 1, 自营; 2, 非自营")
    private BizType bizType;

    @ApiModelProperty(value = "运营方名称")
    private String bizName;

}
