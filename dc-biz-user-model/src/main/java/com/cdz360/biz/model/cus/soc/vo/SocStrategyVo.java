package com.cdz360.biz.model.cus.soc.vo;

import com.cdz360.biz.model.cus.soc.po.SocStrategyPo;
import com.cdz360.biz.model.cus.soc.po.UserSocTimePo;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * SocPriorityStrategyFlat
 * 
 * @since 8/10/2020 7:09 PM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@Schema(description = "场站soc限制-优先策略Vo")
public class SocStrategyVo extends SocStrategyPo {
    private List<Long> userIdList;

    @Schema(description = "时间列表")
    private List<UserSocTimePo> timeList;
}