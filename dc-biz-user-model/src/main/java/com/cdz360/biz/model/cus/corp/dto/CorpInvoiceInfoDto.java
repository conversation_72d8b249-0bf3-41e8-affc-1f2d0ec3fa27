package com.cdz360.biz.model.cus.corp.dto;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.base.vo.BaseObject;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@Schema(description = "企业开票设置")
public class CorpInvoiceInfoDto extends BaseObject {

    @Schema(description = "企业客户ID t_corp.id", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业客户名称 t_corp.corpName")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String corpName;

    @Schema(description = "企业客户的用户ID t_user.id", required = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    @Schema(description = "开票主体所属商户 开票主体的主键")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "开票主体ID 通过这个可以查找到开票主体")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long tempSalId;

    @Schema(description = "企业开票的模板ID t_invoice_sal_temp.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long productTempId;

    @Schema(description = "是否有效", example = "true")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;
}
