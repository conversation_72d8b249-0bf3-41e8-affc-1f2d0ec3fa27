package com.cdz360.biz.ant.service.sysLog;

import com.cdz360.base.model.base.vo.KvAny;
import com.cdz360.base.model.base.vo.KvObject;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.domain.request.BatchRmParam;
import com.cdz360.biz.ant.domain.request.RemoveSiteBlacklistParam;
import com.cdz360.biz.auth.user.dto.SysUserLoginResult;
import com.cdz360.biz.auth.user.po.SysUserLogPo;
import com.cdz360.biz.auth.user.type.LogOpType;
import com.cdz360.biz.model.cus.site.param.SiteWhitelistParam;
import com.cdz360.biz.model.cus.site.vo.SiteBlacklistVo;
import com.cdz360.biz.model.trading.deposit.vo.DepositFlowType;
import com.cdz360.biz.model.trading.profit.conf.vo.CorpProfitBaseVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户相关系统操作日志
 */
@Slf4j
@Service
public class CustomerSysLogService {

    @Autowired
    private BaseSysLogService sysUserLogService;

    /**
     * 登录日志
     */
    public void login(SysUserLoginResult sysUser, ServerHttpRequest request) {
        SysUserLogPo uLog = new SysUserLogPo();
        uLog.setTopCommId(sysUser.getTopCommId())
                .setCommId(sysUser.getCommId())
                .setSysUid(sysUser.getId())
                .setLoginName(sysUser.getUsername())
                .setLoginUser(sysUser.getName());
        sysUserLogService.buildLoginOpLog(uLog, request);
    }

    /**
     * 新增普通用户
     */
    public void createUserLog(String mobile, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("客户手机号", mobile),
                request);
    }

    /**
     * 设置默认账户
     */
    public void setDefaultAccountLog(String mobile, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("客户手机号", mobile),
                request);
    }

    /**
     * 用户设置券自动抵扣
     */
    public void setCouponAutoDeductLog(String mobile, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("客户手机号", mobile),
                request);
    }

    /**
     * 用户拉黑/解除黑名单
     */
    public void userModifyStatusLog(String mobile, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("客户手机号", mobile),
                request);
    }

    /**
     * 用户拉黑/解除黑名单
     */
    public void batchRmFromBlacklistLog(BatchRmParam param, ServerHttpRequest request) {
        KvObject object = new KvObject();
        object.setKey("客户手机号");
        object.setValue(param.getPhone());
        KvObject object2 = new KvObject();
        object2.setKey("站点名称");
        object2.setValue(param.getSiteNameList());
        if(StringUtils.isNotBlank(param.getCorpName())) {
            KvObject object3 = new KvObject();
            object3.setKey("企业名称");
            object3.setValue(param.getCorpName());
            this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                    List.of(object, object2, object3),
                    request);
        } else {
            this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                    List.of(object, object2),
                    request);
        }
    }
    public void removeBlacklistBySite(RemoveSiteBlacklistParam param, ServerHttpRequest request) {
        KvObject object = new KvObject();
        object.setKey("黑名单id");
        object.setValue(param.getList().stream().map(SiteBlacklistVo::getId).collect(Collectors.toList()));
        KvObject object2 = new KvObject();
        object2.setKey("站点名称");
        object2.setValue(param.getSiteName());
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(object, object2),
                request);
    }

    /**
     * 用户充值
     */
    public void rechargeLog(String username, String phone, DepositFlowType flowType, BigDecimal money, ServerHttpRequest request) {
        List<KvObject> opObject = new ArrayList<>();
        KvObject object = new KvObject();
        object.setKey("客户手机号");
        object.setValue(phone);
        KvObject object2 = new KvObject();
        object2.setKey("账户名称");
        object2.setValue(username);
        opObject.add(object);
        opObject.add(object2);
        KvObject object3 = new KvObject();
        if (DepositFlowType.IN_FLOW == flowType) {
            object3.setKey("增加金额");
            object3.setValue(money + "(元)");
            opObject.add(object3);
        } else if (DepositFlowType.OUT_FLOW == flowType) {
            object3.setKey("减少金额");
            object3.setValue(money + "(元)");
            opObject.add(object3);
        }
        this.sysUserLogService.buildObjectOpLog(LogOpType.RECHARGE,
                opObject,
                request);
    }

    /**
     * 新增紧急卡
     */
    public void grantUrgencyCardLog(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("紧急卡号", cardChipNo),
                request);
    }

    /**
     * 下发紧急卡
     */
    public void sendWhiteCardLog(String cardChipNo, String siteName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DELIVERY,
                List.of(KvAny.of("紧急卡号", cardChipNo),
                        KvAny.of("站点名称", siteName)),
                request);
    }

    /**
     * 弃用紧急卡
     */
    public void abandonWhiteCardLog(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("紧急卡号", cardChipNo),
                request);
    }

    /**
     * 客户详情/新增卡片
     * 日志
     */
    public void grantCard(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("在线卡号", cardChipNo),
                request);
    }

    /**
     * 客户详情/新增卡片
     * 日志
     */
    public void grantEssCard(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
               KvAny.of("卡面编号", cardChipNo)
               ,request);
    }

    /**
     * 客户详情/修改卡片
     * 日志
     */
    public void modifyCard(List<String> cardChipNo, ServerHttpRequest request) {
        KvObject temp = new KvObject();
        temp.setKey("在线卡号");
        temp.setValue(cardChipNo);
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(temp),
                request);
    }

    /**
     * 客户详情/修改卡片状态
     * 日志
     */
    public void modifyCardStatus(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("在线卡号", cardChipNo),
                request);
    }

    /**
     * 客户详情/删除卡片
     * 日志
     */
    public void deleteCard(String cardChipNo, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("在线卡号", cardChipNo),
                request);
    }

    /**
     * 客户详情/新增VIN码
     * 日志
     */
    public void createVin(String vin, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("VIN码", vin),
                request);
    }

    /**
     * 客户详情/修改VIN
     * 日志
     */
    public void modifyVin(String type, String vin, ServerHttpRequest request) {
        if (StringUtils.equals(type, LogOpType.MODIFY.name())) {
            this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                    KvAny.of("VIN码", vin),
                    request);
        } else if (StringUtils.equals(type, LogOpType.MODIFY_STATUS.name())) {
            this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                    KvAny.of("VIN码", vin),
                    request);
        }
    }

    public void modifyVin(List<String> vinList, ServerHttpRequest request) {
        KvObject temp = new KvObject();
        temp.setKey("VIN码");
        temp.setValue(vinList);
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(temp),
                request);
    }

    /**
     * 客户详情/删除VIN
     * 日志
     */
    public void deleteVin(String vin, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                KvAny.of("VIN码", vin),
                request);
    }

    /**
     * 账号管理/编辑
     * 充电平台 and 桩管家
     * 日志
     */
    public void userEdit(String username, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("账号", username),
                request);
    }


    /**
     * 企业 - 设置 - 新建账号
     * 日志
     */
    public void userAdd(String username, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
                KvAny.of("账户名", username),
                request);
    }

    /**
     * 企业 - 设置 - 账号修改
     * 日志
     */
    public void userModify(String username, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("账户名", username),
                request);
    }

    /**
     * 企业 - 设置 - 账号修改状态
     * 日志
     */
    public void changeState(String username, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
                KvAny.of("账户名", username),
                request);
    }

    public void addWhitelist(SiteWhitelistParam param, ServerHttpRequest request) {
        KvObject k1 = new KvObject();
        k1.setKey("站点名称");
        k1.setValue(param.getSiteName());
        KvObject k2 = new KvObject();
        k2.setKey("企业名称");
        k2.setValue(param.getCorpName());
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(k1, k2),
                request);
    }

    public void removeWhitelist(List<SiteWhitelistParam> param, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
                KvAny.of("站点名称", param.get(0).getSiteName()),
                request);
    }


    public void accountRefund(String account, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.REFUND,
                KvAny.of("账户", account),
                request);
    }

    public void addRecorder(String siteName, String deviceName, String deviceSerial, ServerHttpRequest request) {
        KvObject k1 = new KvObject();
        k1.setKey("站点名称");
        k1.setValue(siteName);
        KvObject k2 = new KvObject();
        k2.setKey("设备名称");
        k2.setValue(deviceName);
        KvObject k3 = new KvObject();
        k2.setKey("设备序列号");
        k2.setValue(deviceSerial);
        this.sysUserLogService.buildObjectOpLog(LogOpType.CREATE,
                List.of(k1, k2, k3),
                request);
    }

    public void deleteRecorder(String siteName, String deviceName, String deviceSerial, ServerHttpRequest request) {
        KvObject k1 = new KvObject();
        k1.setKey("站点名称");
        k1.setValue(siteName);
        KvObject k2 = new KvObject();
        k2.setKey("设备名称");
        k2.setValue(deviceName);
        KvObject k3 = new KvObject();
        k2.setKey("设备序列号");
        k2.setValue(deviceSerial);
        this.sysUserLogService.buildObjectOpLog(LogOpType.DISABLE,
                List.of(k1, k2, k3),
                request);
    }

    public void updateRecorder(String siteName, String deviceName, ServerHttpRequest request) {
        KvObject k1 = new KvObject();
        k1.setKey("站点名称");
        k1.setValue(siteName);
        KvObject k2 = new KvObject();
        k2.setKey("设备名称");
        k2.setValue(deviceName);
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(k1, k2),
                request);
    }

    public void updateChannel(String siteName, String deviceName, ServerHttpRequest request) {
        KvObject k1 = new KvObject();
        k1.setKey("站点名称");
        k1.setValue(siteName);
        KvObject k2 = new KvObject();
        k2.setKey("设备名称");
        k2.setValue(deviceName);
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                List.of(k1, k2),
                request);
    }

    /**
     *
     * @param params
     * @param request
     */
    public void editCorpProfitConf(CorpProfitBaseVo params,
                                   ServerHttpRequest request) {
        String corpName = params.getCorpName();
        String baseExpression = params.getElecFeeExpression() + ", " + params.getServFeeExpression();
        String subIntervals = null;
        if(CollectionUtils.isNotEmpty(params.getCorpProfitSubList())) {
            subIntervals = params.getCorpProfitSubList()
                    .stream()
                    .map(e ->
                            e.getIntervalExpression() + ", " +
                                    e.getElecFeeSubExpression() + ", " +
                                    e.getServFeeSubExpression()
                    )
                    .collect(Collectors.joining(", "));
        }
        String profitUp = null;
        if(StringUtils.isNotBlank(params.getElecFeeUpExpression())) {
            profitUp = params.getElecFeeUpExpression() + ", " + params.getServFeeUpExpression();
        }
        String profitLow = null;
        if(StringUtils.isNotBlank(params.getElecFeeLowExpression())) {
            profitLow = params.getElecFeeLowExpression() + ", " + params.getServFeeLowExpression();
        }
        List<KvObject> logList = new ArrayList<>();
        logList.add(new KvObject("企业名称", corpName));
        logList.add(new KvObject("基本公式", baseExpression));
        if(StringUtils.isNotBlank(subIntervals)) {
            logList.add(new KvObject("特殊区间", subIntervals));
        }
        if(StringUtils.isNotBlank(profitUp)) {
            logList.add(new KvObject("总收益封顶", profitUp));
        }
        if(StringUtils.isNotBlank(profitLow)) {
            logList.add(new KvObject("总收益保底", profitLow));
        }
        this.sysUserLogService.buildObjectOpLog(LogOpType.MODIFY,
                logList,
                request);
    }

    public void disableCorpProfitConf(String corpName, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
                List.of(KvAny.of("收益配置", "禁用"),
                        KvAny.of("企业名称", corpName)),
                request);
    }

    /**
     * 新增体系
     * @param name
     * @param request
     */
    public void addAccountScoreSettingLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.CREATE,
            KvAny.of("体系名称", name),
            request);
    }

    /**
     * 删除体系
     * @param name
     * @param request
     */
    public void deleteAccountScoreSettingLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.DISABLE,
            KvAny.of("体系名称", name),
            request);
    }

    /**
     * 修改体系
     * @param name
     * @param request
     */
    public void updateAccountScoreSettingLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY,
            KvAny.of("体系名称", name),
            request);
    }

    /**
     * 修改体系状态
     * @param name
     * @param request
     */
    public void updateStatusAccountScoreSettingLog(String name, ServerHttpRequest request) {
        this.sysUserLogService.buildOpLog(LogOpType.MODIFY_STATUS,
            KvAny.of("体系名称", name),
            request);
    }
}
