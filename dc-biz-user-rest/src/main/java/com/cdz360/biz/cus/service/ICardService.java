package com.cdz360.biz.cus.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.cus.domain.ListResponseEvseList;
import com.cdz360.biz.cus.domain.request.CardRequest;
import com.cdz360.biz.cus.domain.vo.CardListdetailVO;
import com.cdz360.biz.cus.domain.vo.CardListdetailVOParam;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardDetailVO;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import reactor.core.publisher.Mono;

public interface ICardService {

    /**
     * 根据条件查询鉴权卡分页数据
     *
     * @param token
     * @param page             分页
     * @param keyword          查询关键字: 物理卡号/卡片名称
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号 commId 所选商户
     * @return
     */
    ListResponse<CardListdetailVO> queryCardsByPage(String token, OldPageParam page,
        String keyword, CardListdetailVO cardListdetailVO,
        Long topCommId,
        String commIdChain);

    /**
     * 根据条件查询在线卡分页数据
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    ListResponse<CardListdetailVO> queryOnlineCardsByPage(String token, OldPageParam page,
        CardListdetailVO cardListdetailVO,
        Long topCommId,
        String commIdChain);

    ObjectResponse<CardListdetailVO> queryOnlineCard(CardListdetailVO cardListdetailVO);

    /**
     * 根据条件查询在线卡分页数据
     *
     * @param cardRequest 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus 卡状态
     * @return
     */
    ListResponse<CardListdetailVO> queryOnlineCardsByPageOnCorp(CardRequest cardRequest);

    /**
     * 根据条件查询紧急充电卡分页数据 用于充电管理平台
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    ListResponse<CardListdetailVO> queryUrgencyCardsByPage(String token, OldPageParam page,
        CardListdetailVO cardListdetailVO,
        Long topCommId,
        Long commId,
        String commIdChain);

    /**
     * 根据条件查询紧急充电卡分页数据 用于运营支撑平台(无需考虑权限)
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(String token, OldPageParam page,
        CardListdetailVOParam cardListdetailVO);

    /**
     * 根据离线卡cardId获取相关场站的信息
     *
     * @param cardId
     * @return
     */
    ObjectResponse urgencyCardsDetail(Long cardId);

    /**
     * 根据离线卡cardId获取相关桩的信息
     *
     * @param token
     * @param cardId
     * @return
     */
    ListResponseEvseList urgencyCardsDetailEvseList(String token, Long cardId, String evse,
        Integer page, Integer rows);

    /**
     * 查找用户虚拟卡
     *
     * @param commericalId 商户id
     * @param userId       用户id
     * @return
     */
    Card findByCommIdAndUserId(Long commericalId, Long userId);

    /**
     * 查询用户卡片列表
     *
     * @param userId 用户id
     * @return
     */
    List<Card> findListByUserId(Long userId);

    /**
     * 生成默认虚拟卡
     *
     * @param commericalId 商户id
     * @param uid          用户id
     * @param mobile       手机号
     * @return
     */
    Long initUserCard(Long commericalId, Long uid, String mobile);

    Long findlastCardNo();

    String getCardNo(String type);

    /**
     * 根据条件更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     * @return
     */
    BaseResponse updateCardStatus(String cardNo, String cardStatus);

    /**
     * 根据条件更新卡状态
     *
     * @param cardChipNo 物理卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     * @return
     */
    ObjectResponse updateCardStatusByChipNo(String cardChipNo, String cardStatus);

    /**
     * 添加卡
     *
     * @param card
     * @return
     */
    BaseResponse addCard(Card card);

    /**
     * 批量添加卡
     *
     * @param cards
     * @return
     */
    ObjectResponse<Integer> batchAddCard(List<Card> cards);

    /**
     * 批量添加卡
     *
     * @param cards
     * @return
     */
    ObjectResponse<Integer> batchGrantCard(List<Card> cards);

    /**
     * 更新卡
     *
     * @param card
     * @return
     */
    BaseResponse updateCard(Card card);

    /**
     * 根据卡号获取卡信息
     *
     * @param cardNo
     * @return
     */
    Card getCardByCardNo(String cardNo, Long topCommId);

    Card getCardByCardNoX(String cardNo, boolean cardExactMatch, Long topCommId, String siteId);

    /**
     * 根据企业和用户id获取卡片列表 根据
     *
     * @param corpId
     * @param userId
     * @return
     */
    List<Card> getCardByCorIdAndUserId(Long corpId, Long userId);

    /**
     * 根据Chip卡号查询卡
     *
     * @param cardChipNo
     * @return
     */
    Card queryCardByCardChipNo(String cardChipNo);

    /**
     * 根据卡号查询卡
     *
     * @param cardNo 卡号
     * @return
     */
    Card queryCardByCardNo(String cardNo);

    AccountInfoVo getAccountByCardNo(String cardNo, Long topCommId);

    /**
     * 根据卡号查询卡
     *
     * @param cardNo 卡号
     * @return
     */
    List<WhiteCardCfgVo> queryCardByCardNoList(List<String> cardNo);

//    /**
//     * 商户给卡片充值
//     *
//     * @param commIdList
//     * @param operatorId
//     * @param cardNo
//     * @param chargeMoney
//     * @return
//     * @throws ChargerlinkException
//     */
//    ObjectResponse rechargerAuthCard(List<Long> commIdList, String operatorId,
//                                     String cardNo, long chargeMoney) throws ChargerlinkException;

    /**
     * 更新卡余额并增加流水记录
     *
     * @param card 卡信息
     * @return
     */
    int updateCardBalance(Card card);

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    ObjectResponse<Card> queryCardById(Long id);

    /**
     * 根据条件查询卡(得到集团客户名下的卡) 可继续补充条件
     *
     * @return
     */
    ListResponse<Card> queryCardByCondition(CardRequest cardRequest);

    /**
     * 查询商户子商户卡列表
     *
     * @param token
     * @return
     */
    ListResponse<CardVo> queryAllCardList(String token, String userId);

    /**
     * 解析 excel 文件
     *
     * @param file
     * @return
     */
    ObjectResponse parseExcel(List<List<Object>> file);

    /**
     * 解析企业平台Card excel文件
     */
    ObjectResponse parseCorpCardExcel(List<List<Object>> list, Long corpId);

    /**
     * 根据逻辑卡号查询物理卡号
     *
     * @param map
     * @return
     */
    ObjectResponse selectShipCardNoByCardNos(Map map);

    /**
     * 根据逻辑卡号查询物理卡号
     */
    ObjectResponse getCardChipNoByCardNo(String cardNo);

    /**
     * 查询站点下的紧急充电卡 备注：cardChipNo 不为空表示弃用
     */
    List<WhiteCardDto> queryWhiteCardDtoMapList(WhiteCardRequest whiteCardRequest);

    /**
     * 查询站点下的紧急充电卡 用于多场站下多卡弃用查询DTO时，条件必须为(cardChipNoList必传，siteList必传,isAbandon必传为true)
     * 用于单场站下发查询DTO时，条件为(cardChipNoList不传，site必传,isAbandon为false) 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    List<WhiteCardDto> queryWhiteCardDtoBySiteList(WhiteCardRequest whiteCardRequest);

    /**
     * 根据卡片id（用,分割），更新卡片状态 0x00为下发成功，其他为失败
     *
     * @param cardIds
     * @param status
     */
    int updateUrgencyCardStatus(String evseNo, String cardIds, int status);

    /**
     * 批量更新卡状态
     *
     * @param cardNos
     * @param status
     * @return
     */
    int updBatchCardStatusByNos(List<String> cardNos, int status);

    /**
     * 多场站下多卡弃用，条件必须为(cardChipNoList必传，siteList必传,isAbandon为true) 不支持重置密码 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    BaseResponse sendBatchBySiteList(WhiteCardRequest whiteCardRequest);

    List<String> queryCardNoListByBlocUserId(Long blocUserId);

    List<Card> selectBlocUserNameByCardNos(List<String> cardNos);

    /**
     * 根据条件批量查询场站下的有效紧急卡数量
     *
     * @param siteIdList
     * @return
     */
    ListResponse<SiteCardCount> findUrgencyCardNumCount(List<String> siteIdList);

    /**
     * 获取用户充电卡(在线卡)列表
     *
     * @param page
     * @param userId     用户ID
     * @param statusList 卡片状态
     * @return
     */
    List<CardDetailVO> queryOnlineCardsByUser(OldPageParam page, Long userId,
        List<String> statusList, Long commId);

    /**
     * 根据逻辑卡号获取适用的场站
     *
     * @param cardNo 逻辑卡号
     * @return
     */
    List<String> getStationsOfCard(String cardNo, Integer type, String idChain);

    /**
     * 通过站点Id 获取紧急充电卡
     *
     * @param siteId
     * @param commId
     * @return
     */
    List<String> getEmergencyCardBySiteId(String siteId, Long commId);

    /**
     * 通过企业Id 获取紧急充电卡
     *
     * @param corpId
     * @return
     */
    MoveCorpCardList getEmergencyCardByCorpId(Long corpId, Long commId);

    /**
     * 企业切换商户 获取在线卡切换详情
     *
     * @param corpId
     * @return
     */
    MoveCorpSiteAuthList getMoveCorpCardAuthByCorpId(Long corpId, Long commId);

    /**
     * 判断用户是否拥有有效的在线卡
     *
     * @param userId
     * @param commIdChain
     * @return
     */
    Boolean ownCard(Long userId, String commIdChain);

    /**
     * 企业平台数据清洗 旧数据(在线卡，VIN码，授信客户)转移 自动创建一级组织 企业管理平台第一版（2020-02）上线时
     */
    void corpDataCleaning();

    void syncCardSiteAuth();

    Mono<ObjectResponse<Boolean>> syncAmount(String cardNo, BigDecimal amount, Integer result,
        Long topCommId, Long userId);

    Mono<ListResponse<CardAmountSyncVo>> getSyncCardAmount(CardAmountSyncParam param);

    Mono<ObjectResponse<BigDecimal>> getCardAmount(String cardNo, Long topCommId,
        Boolean cardMaker);

    Integer siteAuthCard(SiteAuthCardParam param);

    ListResponse<SitePo> getCommonSiteList(CardSearchParam param);

    List<SiteAuthCardPo> getCardListBySiteId(String siteId);

    void addEssCard(AddEssCardParam param);

    ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(CardRequest cardRequest);
}
