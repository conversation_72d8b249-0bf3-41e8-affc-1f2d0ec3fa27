package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyChargeAreaRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyChargeAreaRwDs {


    @Autowired

    private TjSurveyChargeAreaRwMapper tjSurveyChargeAreaRwMapper;


    public TjSurveyChargeAreaPo getById(Long id, boolean lock) {

        return this.tjSurveyChargeAreaRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveyChargeArea(TjSurveyChargeAreaPo tjSurveyChargeAreaPo) {
        return this.tjSurveyChargeAreaRwMapper.insertTjSurveyChargeArea(tjSurveyChargeAreaPo) > 0;
    }


    public boolean updateTjSurveyChargeArea(TjSurveyChargeAreaPo tjSurveyChargeAreaPo) {
        return this.tjSurveyChargeAreaRwMapper.updateTjSurveyChargeArea(tjSurveyChargeAreaPo) > 0;
    }

    public boolean disableTjSurveyChargeAreaBySurveyNo(String surveyNo) {
        return this.tjSurveyChargeAreaRwMapper.disableTjSurveyChargeAreaBySurveyNo(surveyNo) > 0;
    }

}

