package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "设备基础信息信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeviceBaseInfoVo extends DeviceBase {

    @Schema(description = "DTU设备序列号")
    @JsonInclude(Include.NON_NULL)
    private String dtuSerialNo;

    @Schema(description = "设备功率范围")
    @JsonInclude(Include.NON_NULL)
    private PowerRange powerRange;

    @Schema(description = "设备容量范围")
    @JsonInclude(Include.NON_NULL)
    private CapacityRange capacityRange;
}
