package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "日充电时长导入")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class TjDailyChargingDurationVo extends TjDailyChargingDurationImportItem {

    @Schema(description = "失败原因")
    @JsonInclude(Include.NON_EMPTY)
    private String errMsg;
}
