package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssEquipTinyDto {

    private EssEquipType equipType;
    private String dno;
    private String name;
    private EquipStatus status;
    private String essDno;
}
