package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class BoxTradingFeignClientHystrix implements FallbackFactory<BoxTradingFeignClient> {

    @Override
    public BoxTradingFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_TRADING,
            throwable.getStackTrace());

        return new BoxTradingFeignClient() {
            @Override
            public ObjectResponse<BsBoxSettingPo> getBoxSetting(String evseNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }
        };
    }
}