package com.cdz360.biz.device.business.entity.po;


import com.cdz360.biz.device.common.dingchong.WhiteCardV2;
import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Classname WhiteCardVO
 * @Description 下发紧急充电卡请求参数
 * @Date 2019/7/10 14:57
 * @Created by JLei
 * @Email <EMAIL>
 */
@Data
public class WhiteCardDto implements Serializable {
    /**
     * 场站ID
     */
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;
    private List<WhiteCardV2> whiteCardList;
    private List<WhiteCardEvse> whiteCardEvses;

}
