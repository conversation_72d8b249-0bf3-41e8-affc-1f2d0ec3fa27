package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.tj.service.kc.TjAreaService;
import com.cdz360.biz.tj.service.kc.TjMaterialCostService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "物料成本相关操作接口", description = "物料成本相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/materialCost")
public class TjMaterialCostRest {

    @Autowired
    private TjMaterialCostService tjMaterialCostService;

    @Operation(summary = "获取物料成本")
    @PostMapping(value = "/findTjMaterialCost")
    public Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(@RequestBody ListTjMaterialCostParam param) {
        log.info("获取获取物料成本: param = {}", JsonUtils.toJsonString(param));
        return tjMaterialCostService.findTjMaterialCost(param);
    }

    @Operation(summary = "获取物料成本")
    @GetMapping(value = "/getTjMaterialCostById")
    public Mono<ObjectResponse<TjMaterialCostVo>> getTjMaterialCostById(
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id) {
        log.info("获取物料成本: {}", id);
        return tjMaterialCostService.getTjMaterialCostById(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新建或编辑物料成本")
    @PostMapping(value = "/saveTjMaterialCost")
    public Mono<ObjectResponse<TjMaterialCostVo>> saveTjMaterialCost(@RequestBody TjMaterialCostVo tjMaterialCostVo) {
        log.info("保存物料成本: param = {}", JsonUtils.toJsonString(tjMaterialCostVo));
        return tjMaterialCostService.saveTjMaterialCost(tjMaterialCostVo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除物料成本")
    @GetMapping(value = "/disableTjMaterialCost")
    public Mono<ObjectResponse<TjMaterialCostVo>> disableTjMaterialCost(
        @ApiParam("物料成本唯一ID") @RequestParam("id") Long id) {
        log.info("删除物料成本: {}", id);
        return tjMaterialCostService.disableTjMaterialCost(id)
            .map(RestUtils::buildObjectResponse);
    }
}
