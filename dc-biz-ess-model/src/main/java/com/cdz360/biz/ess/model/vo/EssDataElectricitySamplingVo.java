package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "储能设备数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssDataElectricitySamplingVo extends BaseDataSampling {

    public EssDataElectricitySamplingVo(Long time) {
        setTime(time);
    }

    @Schema(description = "光伏当天发电量, 单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal pvElectricityToday;

//    @Schema(description = "光伏输入电量, 单位: kW·h")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal pvInElectricity;
//
//    @Schema(description = "光伏输出电量, 单位: kW·h")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal pvOutElectricity;

    @Schema(description = "当天电池充量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batChargeElectricityToday;

    @Schema(description = "当天电池放量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batDischargeElectricityToday;

//    @Schema(description = "EPS今日消耗电量, 单位: kW·h")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal epsDayConsumedElectricity;
//
//    @Schema(description = "GEN今日消耗电量, 单位: kW·h")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal genDayConsumedElectricity;

    @Schema(description = "当天负载用电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadUsingElectricityToday;

    @Schema(description = "当天购电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal purchaseElectricityToday;

    @Schema(description = "当天馈电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal offerElectricityToday;

}
