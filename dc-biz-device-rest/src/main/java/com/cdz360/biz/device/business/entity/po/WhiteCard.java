package com.cdz360.biz.device.business.entity.po;


import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;

/**
 * @Classname WhiteCardVO
 * @Description 下发紧急充电卡请求参数
 * @Date 2019/7/10 14:57
 * @Created by JLei
 * @Email <EMAIL>
 */
@Data
public class WhiteCard implements Serializable {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cardNumber;//逻辑卡号
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String passcode;//充电密码
}
