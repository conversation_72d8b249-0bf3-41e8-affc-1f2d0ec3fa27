package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.param.AddCommercialParam;
import com.cdz360.biz.model.merchant.param.ConvertCommPayParam;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.param.UpdateCommercialParam;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.CommercialManage;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.vo.NotifyPay;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class CommercialFeignClientHystrix implements FallbackFactory<CommercialFeignClient> {
    @Override
    public CommercialFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_AUTH, throwable.getStackTrace());

        return new CommercialFeignClient() {
            @Override
            public ObjectResponse<Commercial> getCommercial(Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Commercial> convertCommCorpDeposit(Long commId) {
                log.error("【服务熔断】切换企业在线充值[{}]: commId = {}", "convertCommCorpDeposit", commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Commercial> convertCommPayAndRefund(ConvertCommPayParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Commercial> convertCommCorpRefund(Long commId) {
                log.error("【服务熔断】切换企业在线退款[{}]: commId = {}", "convertCommCorpRefund", commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Commercial> convertCommRefund(Long commId) {
                log.error("【服务熔断】切换会员在线退款[{}]: commId = {}", "convertCommRefund", commId);
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommercialTreeNode> getCommTree(Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> addComm(AddCommercialParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> addEssComm(AddCommercialParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Commercial> getPlatInfo(String merchants) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateCommInfo(UpdateCommercialParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateEssCommInfo(UpdateCommercialParam param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Long> getSubCommIdList(String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CommercialManage> getCommercialManage(Long topCommId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateLogoByMerchants(String merchants, String logo,String platformName) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<CommercialDto> getCommList(ListCommercialParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse addCommercialsUser(String token, TCommercialUser tcu) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse chargeNotify(NotifyPay params) {
                return RestUtils.serverBusy();
            }
        };
    }
}
