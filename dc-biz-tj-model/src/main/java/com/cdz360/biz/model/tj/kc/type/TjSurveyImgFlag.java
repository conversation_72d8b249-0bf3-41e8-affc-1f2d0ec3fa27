package com.cdz360.biz.model.tj.kc.type;

import lombok.Getter;

@Getter
public enum TjSurveyImgFlag {

    OUT_IN_STREET(1, "出入口外街道"),
    OUT_IN(2, "场站出入口"),
    IN_2_PARK_ROAD(3, "入口至充电车位过道"),
    EVSE_POSITION(4, "充电桩安装位置"),
    BOX_TRANSFORMER_POSITION(5, "箱变安装位置");
    private final int code;
    private final String desc;

    TjSurveyImgFlag(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
