package com.cdz360.biz.model.cus.site.param;

import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.cus.site.type.BlacklistFromType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Schema(description = "查询场站用户黑名单列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ListSiteBlacklistParam extends BaseListParam {

    @Schema(description = "站点编号")
    private String siteId;

    @Schema(description = "站点名称")
    private String siteName;

    @Schema(description = "客户姓名")
    private String cusName;

    @Schema(description = "客户手机号")
    private String cusPhone;

    @Schema(description = "客户ID")
    private Long cusId;

    @Schema(description = "是否获取账户余额")
    private Boolean balance;

    @Schema(description = "拉黑原因：手动新增、停充超时")
    private BlacklistFromType from;

    @Schema(description = "最近占位订单缴费状态；0或100")
    private Integer overtimeOrderStatus;

    @Schema(description = "黑名单类型：1个人或商户会员账户，2企业授信账户")
    private Integer blackType;

}
