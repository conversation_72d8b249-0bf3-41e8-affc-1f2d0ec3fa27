package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.type.CableMaterial;
import com.cdz360.biz.model.tj.kc.type.MaterialCostBoxCapacity;
import com.cdz360.biz.model.tj.kc.type.MaterialCostChildType;
import com.cdz360.biz.model.tj.kc.type.MaterialCostGrandchildType;
import com.cdz360.biz.model.tj.kc.type.MaterialCostPower;
import com.cdz360.biz.model.tj.kc.type.MaterialCostTerminal;
import com.cdz360.biz.model.tj.kc.type.MaterialCostType;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjMaterialCostRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjMaterialCostRwDs;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjMaterialCostService {

    @Autowired
    private TjMaterialCostRoDs tjMaterialCostRoDs;

    @Autowired
    private TjMaterialCostRwDs tjMaterialCostRwDs;

    public Mono<ListResponse<TjMaterialCostVo>> findTjMaterialCost(ListTjMaterialCostParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }
        List<TjMaterialCostVo> result = tjMaterialCostRoDs.findTjMaterialCost(param);
        return Mono.just(RestUtils.buildListResponse(result.stream().map(x -> {
                TjMaterialCostVo tjMaterialCostVo = new TjMaterialCostVo();
                BeanUtils.copyProperties(x, tjMaterialCostVo);
                return tjMaterialCostVo;
            }).collect(Collectors.toUnmodifiableList()),
            Boolean.TRUE.equals(param.getTotal()) ? tjMaterialCostRoDs.countTjMaterialCost(param) : 0L));
    }

    public Mono<TjMaterialCostVo> getTjMaterialCostById(Long id) {
        TjMaterialCostPo tjMaterialCostPo = tjMaterialCostRoDs.getById(id);
        IotAssert.isNotNull(tjMaterialCostPo, "物料成本唯一ID无效");
        TjMaterialCostVo result = new TjMaterialCostVo();
        BeanUtils.copyProperties(tjMaterialCostPo, result);
        return Mono.just(result);
    }

    public Mono<TjMaterialCostVo> saveTjMaterialCost(TjMaterialCostVo tjMaterialCost) {
        TjMaterialCostPo dto = new TjMaterialCostPo();
        BeanUtils.copyProperties(tjMaterialCost, dto);
        checkParam(tjMaterialCost);
        if (null == tjMaterialCost.getId()) {
            tjMaterialCostRwDs.insertTjMaterialCost(dto);
        } else {
            tjMaterialCostRwDs.updateTjMaterialCost(dto);
        }
        return Mono.just(tjMaterialCost.setId(dto.getId()));
    }

    public Mono<TjMaterialCostVo> disableTjMaterialCost(Long id) {
        TjMaterialCostPo tjMaterialCostPo = tjMaterialCostRoDs.getById(id);
        IotAssert.isNotNull(tjMaterialCostPo, "物料成本唯一ID无效");

        if (Boolean.TRUE.equals(tjMaterialCostPo.getEnable())) {
            boolean b = tjMaterialCostRwDs.disableTjMaterialCost(id);
            log.debug("删除物料成本: {}", b);
        }

        TjMaterialCostVo result = new TjMaterialCostVo();
        BeanUtils.copyProperties(tjMaterialCostPo, result);
        return Mono.just(result);
    }

    private void checkParam(TjMaterialCostVo tjMaterialCost) {
        if (MaterialCostType.BOX_TRANSFORMATION.getCode() == tjMaterialCost.getType()) {
            tjMaterialCost.setChildType(MaterialCostChildType.BOX_TRANSFORMATION.getCode());
            List<Integer> capacityList = List.of(MaterialCostBoxCapacity.CAPACITY_315.getCode(),
                MaterialCostBoxCapacity.CAPACITY_500.getCode(),
                MaterialCostBoxCapacity.CAPACITY_630.getCode(),
                MaterialCostBoxCapacity.CAPACITY_800.getCode(),
                MaterialCostBoxCapacity.CAPACITY_1000.getCode(),
                MaterialCostBoxCapacity.CAPACITY_1250.getCode(),
                MaterialCostBoxCapacity.CAPACITY_1600.getCode());
            if (!capacityList.contains(tjMaterialCost.getCapacity())) {
                throw new DcArgumentException("箱变容量错误");
            }
            if (tjMaterialCost.getPrice() == null) {
                throw new DcArgumentException("单价不能为空");
            }
            if (tjMaterialCost.getInstallationFee() == null) {
                throw new DcArgumentException("安装费不能为空");
            }
        } else if (MaterialCostType.CABLE.getCode() == tjMaterialCost.getType()) {
            List<Integer> cableList = List.of(MaterialCostChildType.HIGH_VOLTAGE_CABLE.getCode(),
                MaterialCostChildType.SITE_TO_POWER_SUPPLY_WIRING.getCode(),
                MaterialCostChildType.SINGLE_DC_CABLE.getCode(),
                MaterialCostChildType.SINGLE_AC_CABLE.getCode());
            if (!cableList.contains(tjMaterialCost.getChildType())) {
                throw new DcArgumentException("项目类型错误");
            }
            if (tjMaterialCost.getPrice() == null) {
                throw new DcArgumentException("单价不能为空");
            }
            if (tjMaterialCost.getChildType() != MaterialCostChildType.HIGH_VOLTAGE_CABLE.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.VOLTAGE_CABLE_COPPER.getCode(), MaterialCostGrandchildType.VOLTAGE_CABLE_ALUMINIUM.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("材料类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.SITE_TO_POWER_SUPPLY_WIRING.getCode()) {
                List<Integer> siteToPowerSupplyPowerList = List.of(MaterialCostPower.SITE_TO_POWER_SUPPLY_100.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_200.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_300.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_400.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_500.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_600.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_700.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_800.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_900.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_1000.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_1200.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_1600.getCode(),
                    MaterialCostPower.SITE_TO_POWER_SUPPLY_3000.getCode());
                if (!siteToPowerSupplyPowerList.contains(tjMaterialCost.getPower())) {
                    throw new DcArgumentException("充电站到电源走线功率错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.SINGLE_DC_CABLE.getCode()) {
                if (tjMaterialCost.getTerminal() == null && tjMaterialCost.getPower() != null) {
                    List<Integer> dcPowerList = List.of(MaterialCostPower.DC_60.getCode(),
                        MaterialCostPower.DC_80.getCode(),
                        MaterialCostPower.DC_120.getCode(),
                        MaterialCostPower.DC_160.getCode(),
                        MaterialCostPower.DC_240.getCode(),
                        MaterialCostPower.DC_480.getCode());
                    if (!dcPowerList.contains(tjMaterialCost.getPower())) {
                        throw new DcArgumentException("直流桩功率错误");
                    }
                } else if (tjMaterialCost.getTerminal() != null && tjMaterialCost.getPower() == null) {
                    List<Integer> terminalList = List.of(MaterialCostTerminal.DOUBLE_250.getCode(), MaterialCostTerminal.SINGLE_250.getCode());
                    if (!terminalList.contains(tjMaterialCost.getTerminal())) {
                        throw new DcArgumentException("终端类型错误");
                    }
                } else {
                    throw new DcArgumentException("直流桩功率或者终端有且只能选择一项");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.SINGLE_AC_CABLE.getCode()) {
                List<Integer> acPowerList = List.of(MaterialCostPower.AC_7.getCode());
                if (!acPowerList.contains(tjMaterialCost.getPower())) {
                    throw new DcArgumentException("交流桩功率错误");
                }
            }
        } else if (MaterialCostType.EVSE.getCode() == tjMaterialCost.getType()) {
            tjMaterialCost.setChildType(MaterialCostChildType.EVSE.getCode());
            if (tjMaterialCost.getEvseModelId() == null) {
                throw new DcArgumentException("型号不能为空");
            }
            if (tjMaterialCost.getPrice() == null) {
                throw new DcArgumentException("单价不能为空");
            }
            if (tjMaterialCost.getInstallationFee() == null) {
                throw new DcArgumentException("充电桩安装费不能为空");
            }
            if (tjMaterialCost.getEvseBaseFee() == null) {
                throw new DcArgumentException("充电桩基础费不能为空");
            }
        } else if (MaterialCostType.STATION_SUPPORTING_FACILITIES.getCode() == tjMaterialCost.getType()) {
            List<Integer> aluminiumList = List.of(MaterialCostChildType.BIG_CAR_GEAR.getCode(),
                MaterialCostChildType.SMALL_CAR_GEAR.getCode(),
                MaterialCostChildType.INTELLIGENT_LOCK.getCode(),
                MaterialCostChildType.EPOXY_FLOORING.getCode(),
                MaterialCostChildType.GROUND_HARDENING.getCode(),
                MaterialCostChildType.FIRE_PROTECTION_SYSTEM.getCode(),
                MaterialCostChildType.GROUND_LIGHTING.getCode(),
                MaterialCostChildType.UNDERGROUND_LIGHTING.getCode(),
                MaterialCostChildType.CCTV_MONITORING_SYSTEM.getCode(),
                MaterialCostChildType.FLAGSHIP_STORE.getCode(),
                MaterialCostChildType.ORDINARY_STATION.getCode(),
                MaterialCostChildType.BARRIER_GATE_SYSTEM.getCode(),
                MaterialCostChildType.BARRIER_GATE_SYSTEM_EQUIPMENT.getCode(),
                MaterialCostChildType.CANOPY.getCode());
            if (!aluminiumList.contains(tjMaterialCost.getChildType())) {
                throw new DcArgumentException("项目类型错误");
            }
            if (tjMaterialCost.getPrice() == null) {
                throw new DcArgumentException("单价不能为空");
            }
        } else if (MaterialCostType.OTHER.getCode() == tjMaterialCost.getType()) {
            List<Integer> otherList = List.of(MaterialCostChildType.POLE_TOP_SWITCH_AND_INSTALLATION.getCode(),
                MaterialCostChildType.RING_MAIN_UNIT_AND_INSTALLATION.getCode(),
                MaterialCostChildType.BRIDGE_STRUCTURE_AND_INSTALLATION.getCode(),
                MaterialCostChildType.CABLE_DIRECT_BURIAL_EXCAVATION.getCode(),
                MaterialCostChildType.DESIGN_FEE.getCode(),
                MaterialCostChildType.OTHER.getCode());
            if (!otherList.contains(tjMaterialCost.getChildType())) {
                throw new DcArgumentException("项目类型错误");
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.POLE_TOP_SWITCH_AND_INSTALLATION.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.LOWER_COLUMN_SWITCH.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.RING_MAIN_UNIT_AND_INSTALLATION.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.NUMBER_OF_RING_NETWORK_INTERVALS.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.BRIDGE_STRUCTURE_AND_INSTALLATION.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.GALVANIZED_CABLE_TRAY_AND_INSTALLATION.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.CABLE_DIRECT_BURIAL_EXCAVATION.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.ORDINARY_SOIL_EXCAVATION.getCode(),
                    MaterialCostGrandchildType.EXCAVATION_OF_PEDESTRIAN_WALKWAY.getCode(),
                    MaterialCostGrandchildType.CONCRETE_EXCAVATION.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.DESIGN_FEE.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.DESIGN_HIGH_VOLTAGE_PROJECT.getCode(),
                    MaterialCostGrandchildType.DESIGN_LOW_VOLTAGE_PROJECT.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getChildType() == MaterialCostChildType.OTHER.getCode()) {
                List<Integer> grandchildList = List.of(MaterialCostGrandchildType.OTHER_TYPE.getCode());
                if (!grandchildList.contains(tjMaterialCost.getGrandchildType())) {
                    throw new DcArgumentException("类型错误");
                }
            }
            if (tjMaterialCost.getPrice() == null) {
                throw new DcArgumentException("单价不能为空");
            }
        } else {
            throw new DcArgumentException("物料成本类型错误");
        }
    }

}
