package com.cdz360.biz.model.cus.soc.vo;

import com.cdz360.biz.model.cus.soc.po.UserSocStrategyPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * UserSocStrategyCreditCusVo
 *  获取企业soc策略-授信账户
 * @since 1/4/2021 8:55 AM
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class UserSocStrategyCreditCusVo extends UserSocStrategyPo {

    @Schema(description = "授信手机号")
    private String phone;

    @Schema(description = "姓名")
    private String name;

    @Schema(description = "所属组织名称")
    protected String orgName;
}