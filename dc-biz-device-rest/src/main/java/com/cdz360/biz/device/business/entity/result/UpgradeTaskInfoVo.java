package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.model.evse.po.PackageInfoItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

import java.util.Date;

/**
 * @Classname UpgradeTaskInfoVo
 * @Description TODO
 * @Date 9/24/2019 8:48 AM
 * @Created by Rafael
 */
@Data
@ApiModel("升级详情-升级信息")
public class UpgradeTaskInfoVo {
    @ApiModelProperty(value = "升级时间")
    private Date upgradeTime;//升级时间
    @ApiModelProperty(value = "升级包编码：")
    private Long bundleVersion;//升级包编码：
    @ApiModelProperty(value = "升级包名称：")
    private String bundleName;//升级包名称：
    @ApiModelProperty(value = "升级包Id")
    private Long bundleId;//升级包Id
    @ApiModelProperty(value = "PC01目标版本号：硬件-软件-定制号：(升级后的版本)")
    private String pc01Ver;//PC01目标版本号：硬件-软件-定制号：(升级后的版本)
    @ApiModelProperty(value = "PC02目标版本号：硬件-软件-定制号：(升级后的版本)")
    private String pc02Ver;//PC02目标版本号：硬件-软件-定制号：(升级后的版本)
    @ApiModelProperty(value = "PC03目标版本号：硬件-软件-定制号：(升级后的版本)")
    private String pc03Ver;//PC03目标版本号：硬件-软件-定制号：(升级后的版本)

    @Schema(description = "升级包信息-海外版使用")
    private List<PackageInfoItem> packageInfo;
    // 由于海外版的升级包对应的t_evse_package表，里边的version字段数据类型是varchar(255)，因此此处单独处理
    @Schema(description = "升级包版本-海外版使用")
    private String packageVersion;
}