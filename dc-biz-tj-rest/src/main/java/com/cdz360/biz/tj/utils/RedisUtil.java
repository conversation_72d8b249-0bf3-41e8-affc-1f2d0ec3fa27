package com.cdz360.biz.tj.utils;

import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 */
@Repository
public class RedisUtil {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

//    @Autowired
//    private RedisChargeOrderReadService redisChargeOrderReadService;

    public static String competitorSiteKey(String cityCode, Long competitorId) {
        return "gaode:" + cityCode + ":" + competitorId;
    }

    public void set(String key, String value, int timeout) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.HOURS);
    }

    public void setMinutes(String key, String value, int timeout) {
        stringRedisTemplate.opsForValue().set(key, value, timeout, TimeUnit.MINUTES);
    }

    public String get(String key) {
        return stringRedisTemplate.opsForValue().get(key);
    }

//    public ChargeOrderCache getOrderVo(String orderNo) {
//        return redisChargeOrderReadService.getOrder(orderNo, ChargeOrderCache.class);
//    }

    public void del(String key) {
        stringRedisTemplate.delete(key);
    }
}
