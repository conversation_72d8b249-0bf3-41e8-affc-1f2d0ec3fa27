package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaVo;
import com.cdz360.biz.tj.service.kc.TjAreaService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建区域相关操作接口", description = "投建区域相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/area")
public class TjAreaRest {

    @Autowired
    private TjAreaService tjAreaService;

    @Operation(summary = "获取投建区域")
    @PostMapping(value = "/findArea")
    public Mono<ListResponse<TjAreaVo>> findArea(@RequestBody ListTjAreaParam param) {
        log.debug("获取用户的投建区域: param = {}", JsonUtils.toJsonString(param));
        return tjAreaService.findArea(param);
    }

    @Operation(summary = "获取用户的投建区域")
    @PostMapping(value = "/findUserArea")
    public Mono<ListResponse<TjAreaVo>> findUserArea(@RequestBody ListTjAreaParam param) {
        log.debug("获取用户的投建区域: param = {}", JsonUtils.toJsonString(param));
        return tjAreaService.findUserArea(param);
    }

    @Operation(summary = "获取投建区域")
    @GetMapping(value = "/getTjAreaByAid")
    public Mono<ObjectResponse<TjAreaVo>> getTjAreaByAid(
        @ApiParam("投建区域唯一ID") @RequestParam("aid") Long aid) {
        log.debug("获取投建区域: {}", aid);
        return tjAreaService.getTjAreaByAid(aid)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新建或编辑投建区域")
    @PostMapping(value = "/saveTjArea")
    public Mono<ObjectResponse<TjAreaVo>> saveTjArea(@RequestBody TjAreaVo area) {
        log.debug("保存投建区域: param = {}", JsonUtils.toJsonString(area));
        return tjAreaService.saveTjArea(area)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除投建区域")
    @GetMapping(value = "/disableTjArea")
    public Mono<ObjectResponse<TjAreaVo>> editTjArea(
        @ApiParam("投建区域唯一ID") @RequestParam("aid") Long aid) {
        log.debug("删除投建区域: {}", aid);
        return tjAreaService.disableTjArea(aid)
            .map(RestUtils::buildObjectResponse);
    }
}
