package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-充电区域")
public class TjSurveyChargeAreaPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "变压器到充电区域电缆类型（1：使用分支箱，2：无分支箱，3：不需要）")
    private Integer transformerToChargeCable;

    @ApiModelProperty(value = "主电缆距离")
    private Integer transformerToChargeCableDistance;

    @ApiModelProperty(value = "变压器块的电缆类型（2001001：铜缆，2001002：铝缆）")
    private Integer transformerToChargeCableType;

    @ApiModelProperty(value = "充电桩到分支箱或变压器的电缆距离")
    private Integer chargeCableDistance;

    @ApiModelProperty(value = "主机到终端的距离")
    private Integer masterToTerminalDistance;

    @ApiModelProperty(value = "充电块的电缆类型（2001001：铜缆，2001002：铝缆）")
    private Integer chargeCableType;

    @ApiModelProperty(value = "架桥距离")
    private Integer galvanizedCableTrayDistance;

    @ApiModelProperty(value = "普通土开挖距离")
    private Integer ordinarySoilExcavationDistance;

    @ApiModelProperty(value = "人行道开挖")
    private Integer pedestrianWalkwayExcavationDistance;

    @ApiModelProperty(value = "混泥土开挖距离")
    private Integer concreteExcavationDistance;

    @ApiModelProperty(value = "是否需要电桩基础（水泥浇筑台等）")
    private Boolean evseBaseNeed;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

