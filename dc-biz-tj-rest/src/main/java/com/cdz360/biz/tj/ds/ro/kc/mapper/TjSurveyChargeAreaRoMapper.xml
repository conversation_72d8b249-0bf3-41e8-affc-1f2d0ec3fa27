<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyChargeAreaRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_CHARGE_AREA_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="transformerToChargeCable" jdbcType="INTEGER" property="transformerToChargeCable"/>
    <result column="transformerToChargeCableDistance" jdbcType="INTEGER" property="transformerToChargeCableDistance"/>
    <result column="transformerToChargeCableType" jdbcType="INTEGER" property="transformerToChargeCableType"/>
    <result column="chargeCableDistance" jdbcType="INTEGER" property="chargeCableDistance"/>
    <result column="masterToTerminalDistance" jdbcType="INTEGER" property="masterToTerminalDistance"/>
    <result column="chargeCableType" jdbcType="INTEGER" property="chargeCableType"/>
    <result column="galvanizedCableTrayDistance" jdbcType="INTEGER" property="galvanizedCableTrayDistance"/>
    <result column="ordinarySoilExcavationDistance" jdbcType="INTEGER" property="ordinarySoilExcavationDistance"/>
    <result column="pedestrianWalkwayExcavationDistance" jdbcType="INTEGER" property="pedestrianWalkwayExcavationDistance"/>
    <result column="concreteExcavationDistance" jdbcType="INTEGER" property="concreteExcavationDistance"/>
    <result column="evseBaseNeed" jdbcType="BOOLEAN" property="evseBaseNeed"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PO">
    select * from t_tj_survey_charge_area where id = #{id} and enable = true
  </select>

  <select id="findTjSurveyChargeAreaBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PO">
    select * from t_tj_survey_charge_area where surveyNo = #{surveyNo} and enable = true
    order by id desc
  </select>


</mapper>

