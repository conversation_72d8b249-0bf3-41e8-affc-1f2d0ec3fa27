package com.cdz360.biz.ess.model.param;


import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "查询设备故障告警语言信息")
@Data
@Accessors(chain = true)
public class ListAlarmLangParam {

    @NotNull(message = "lang 不能为 null")
    @Size(max = 6, message = "lang 长度不能超过 6")
    @JsonInclude(Include.NON_EMPTY)
    private String lang;

    @Schema(description = "设备DNO列表")
    private List<String> dnos;

    @JsonInclude(Include.NON_NULL)
    private List<EssEquipType> equipTypes;

    /**
     * 故障编码 EssEquipAlarmLangPo.code
     */
    @JsonInclude(Include.NON_NULL)
    private List<String> codes;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

