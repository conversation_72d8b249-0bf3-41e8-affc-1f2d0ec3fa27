package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "照明类型")
@Getter
public enum LightingType implements DcEnum {


    UNKNOWN(999, "未知"),

    GROUND_LIGHTING(1, "地上照明"),
    UNDERGROUND_LIGHTING(2, "地下照明"),
    NO_LIGHTING(3, "不需要"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    LightingType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static LightingType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (LightingType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return LightingType.UNKNOWN;
    }

}
