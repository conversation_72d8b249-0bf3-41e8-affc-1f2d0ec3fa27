package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjCompetitorSiteRoMapper {

    TjCompetitorSitePo getById(@Param("id") Long id);

    List<TjCompetitorSiteVo> findTjCompetitorSite(ListTjCompetitorSiteParam param);

    long countTjCompetitorSite(ListTjCompetitorSiteParam param);
}

