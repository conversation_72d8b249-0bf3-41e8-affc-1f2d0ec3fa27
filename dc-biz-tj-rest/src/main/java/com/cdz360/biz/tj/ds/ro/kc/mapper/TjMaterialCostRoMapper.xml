<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjMaterialCostRoMapper">

  <resultMap id="RESULT_TJ_MATERIAL_COST_PO" type="com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="type" jdbcType="INTEGER" property="type"/>
    <result column="childType" jdbcType="INTEGER" property="childType"/>
    <result column="capacity" jdbcType="INTEGER" property="capacity"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="installationFee" jdbcType="DECIMAL" property="installationFee"/>
    <result column="grandchildType" jdbcType="INTEGER" property="grandchildType"/>
    <result column="evseModelId" jdbcType="BIGINT" property="evseModelId"/>
    <result column="power" jdbcType="INTEGER" property="power"/>
    <result column="terminal" jdbcType="INTEGER" property="terminal"/>
    <result column="evseBaseFee" jdbcType="DECIMAL" property="evseBaseFee"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="RESULT_TJ_MATERIAL_COST_VO" extends="RESULT_TJ_MATERIAL_COST_PO" type="com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo">

  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_MATERIAL_COST_PO">
    select * from t_tj_material_cost where id = #{id} and enable = true
  </select>

  <select id="findTjMaterialCost"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam"
    resultMap="RESULT_TJ_MATERIAL_COST_VO">
    select * from t_tj_material_cost where 1=1
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="childType != null">
      and childType = #{childType}
    </if>
    and enable = true order by id desc
  </select>

  <select id="countTjMaterialCost"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam"
    resultType="java.lang.Long">
    select count(*) from t_tj_material_cost where 1=1
    <if test="id != null">
      and id = #{id}
    </if>
    <if test="childType != null">
      and childType = #{childType}
    </if>
    and enable = true
  </select>


</mapper>

