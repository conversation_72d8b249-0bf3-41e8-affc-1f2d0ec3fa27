package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "智能测算结果")
@Getter
public enum CalculationResult implements DcEnum {


    UNKNOWN(999, "未知"),

    RESULT_PASS(1, "符合投建"),
    RESULT_NO_PASS(2, "不符合投建"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    CalculationResult(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static CalculationResult valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (CalculationResult flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return CalculationResult.UNKNOWN;
    }

}
