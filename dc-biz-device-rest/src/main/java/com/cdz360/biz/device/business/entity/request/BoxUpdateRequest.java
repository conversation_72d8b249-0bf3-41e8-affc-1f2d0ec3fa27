package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.business.constant.ChargingWayEnum;
import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 更新设备信息请求
 *
 * <AUTHOR>
 * @since 2018年7月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BoxUpdateRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 交直流类型
     */
    private Integer currentType;
    /**
     * 设备名称
     */
    private String boxName;
    /**
     * 备注
     */
    private String remark;

    /**
     * 收费方式{@link ChargingWayEnum}
     */
    private Integer chargingWay;

    /**
     * 计费模板Id
     */
    private Long templateId;

    /**
     * 计费模板名称
     */
    private String templateName;

    /**
     * 是否关联场站默认计费(1：是 0：否)
     */
    private Integer isAssociateSiteTemplate;

    /**
     * 是否使用场站默认桩配置 1是0否
     */
    private Integer isUseSiteDefaultSetting;

    /**
     * 桩的额定功率
     */
    private Integer ratedPower;
}
