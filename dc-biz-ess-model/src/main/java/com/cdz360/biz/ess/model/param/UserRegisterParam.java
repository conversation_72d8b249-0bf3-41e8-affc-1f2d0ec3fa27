package com.cdz360.biz.ess.model.param;

import com.cdz360.biz.ess.model.dto.BaseUserAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.math.BigDecimal;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户注册请求参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserRegisterParam extends BaseUserAccount {

    @Schema(description = "验证码", requiredMode = RequiredMode.REQUIRED)
    @NotBlank
    private String verificationCode;

    @NotBlank
    @Schema(description = "确认密码", requiredMode = RequiredMode.REQUIRED)
    private String confirmPassw;

    @Schema(description = "用户所在位置经度")
    private BigDecimal lng;

    @Schema(description = "用户所在位置纬度")
    private BigDecimal lat;
}
