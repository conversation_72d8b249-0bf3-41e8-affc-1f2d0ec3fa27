<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.cus.repository.CardMapper">
    <resultMap id="BaseResultMap" type="com.chargerlinkcar.framework.common.domain.vo.Card">
        <result column="id" jdbcType="BIGINT" property="id"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="card_type" jdbcType="TINYINT" property="cardType"/>
        <result column="card_chip_no" jdbcType="VARCHAR" property="cardChipNo"/>
        <result column="card_channel" jdbcType="VARCHAR" property="cardChannel"/>
        <result column="card_denomination" jdbcType="BIGINT" property="cardDenomination"/>
        <result column="card_activation_code" jdbcType="VARCHAR" property="cardActivationCode"/>
        <result column="card_status" jdbcType="VARCHAR" property="cardStatus"/>
        <result column="card_activation_date" jdbcType="TIMESTAMP" property="cardActivationDate"/>
        <result column="card_create_date" jdbcType="TIMESTAMP" property="cardCreateDate"/>
        <result column="card_update_date" jdbcType="TIMESTAMP" property="cardUpdateDate"/>
        <result column="corpId" jdbcType="BIGINT" property="corpId"/>
        <result column="comm_id" jdbcType="BIGINT" property="commId"/>
        <!--<result column="station_id" jdbcType="VARCHAR" property="stationId" />-->
        <!--<result column="station_name" jdbcType="VARCHAR" property="stationName" />-->
        <result column="mobile" jdbcType="VARCHAR" property="mobile"/>
        <result column="user_id" jdbcType="BIGINT" property="userId"/>
        <!--<result column="status" jdbcType="INTEGER" property="status" />-->
        <!--<result column="type" jdbcType="INTEGER" property="type" />-->
        <result column="is_package" jdbcType="TINYINT" property="isPackage"/>
<!--        <result column="merchant_id" jdbcType="BIGINT" property="merchantId"/>-->
        <result column="card_gift_amount" jdbcType="BIGINT" property="cardGiftAmount"/>
        <result column="take_effect_time" jdbcType="TIMESTAMP" property="takeEffectTime"/>
        <result column="past_due_time" jdbcType="TIMESTAMP" property="pastDueTime"/>
        <result column="charger_times" jdbcType="INTEGER" property="chargerTimes"/>
        <result column="yx_bz" jdbcType="VARCHAR" property="yxBz"/>
        <result column="stations" jdbcType="VARCHAR" property="stations"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="cardKey" jdbcType="VARCHAR" property="cardKey"/>
        <result column="card_name" jdbcType="VARCHAR" property="cardName"/>
        <result column="deposit" jdbcType="INTEGER" property="deposit"/>
    </resultMap>
    <resultMap id="WhiteCardcfgVoResultMap" type="com.cdz360.biz.model.iot.vo.WhiteCardCfgVo">
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
        <result column="card_chip_no" jdbcType="VARCHAR" property="cardChipNo"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="card_name" jdbcType="VARCHAR" property="cardName"/>
        <result column="blocUserName" jdbcType="VARCHAR" property="blocUserName"/>
        <result column="blocUserPhone" jdbcType="VARCHAR" property="blocUserPhone"/>
        <result column="username" jdbcType="VARCHAR" property="username"/>
        <result column="commName" jdbcType="VARCHAR" property="commName"/>
        <result column="phone" jdbcType="VARCHAR" property="phone"/>
    </resultMap>
    <resultMap id="BaseResultWithUserMap" type="com.chargerlinkcar.framework.common.domain.vo.Card"
               extends="BaseResultMap">
        <result column="userName" jdbcType="VARCHAR" property="userName"/>
    </resultMap>

    <resultMap id="CARD_RESULT_WITH_AUTH_SITE_LIST" type="com.chargerlinkcar.framework.common.domain.vo.Card">
        <result column="id" jdbcType="BIGINT" property="id" />
        <result column="cardType" jdbcType="TINYINT" property="cardType" />
        <result column="cardNo" jdbcType="VARCHAR" property="cardNo" />
        <result column="cardName" jdbcType="VARCHAR" property="cardName" />
        <result column="carNo" jdbcType="VARCHAR" property="carNo" />
        <result column="carNum" jdbcType="VARCHAR" property="carNum" />
        <result column="lineNum" jdbcType="VARCHAR" property="lineNum" />
        <result column="carDepart" jdbcType="VARCHAR" property="carDepart" />
        <result column="cardChipNo" jdbcType="VARCHAR" property="cardChipNo" />
        <result column="cardStatus" jdbcType="VARCHAR" property="cardStatus" />
        <result column="isPackage" jdbcType="TINYINT" property="isPackage" />
        <result column="cardDenomination" jdbcType="BIGINT" property="cardDenomination" />
        <result column="corpId" jdbcType="BIGINT" property="corpId" />
        <result column="commId" jdbcType="BIGINT" property="commId" />
<!--        <result column="siteId" jdbcType="VARCHAR" property="siteId" />-->
        <result column="mobile" jdbcType="VARCHAR" property="mobile" />
        <result column="userId" jdbcType="BIGINT" property="userId" />
<!--        <result column="merchantId" jdbcType="BIGINT" property="merchantId" />-->
        <result column="userName" jdbcType="VARCHAR" property="userName" />
        <result column="rBlocUserId" jdbcType="BIGINT" property="rBlocUserId" />
        <result column="deposit" jdbcType="INTEGER" property="deposit" />
        <collection property="siteList" ofType="java.lang.String">
            <result column="siteId" property="value" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        id, card_no, card_type, card_chip_no, card_channel, card_denomination, card_activation_code, card_status,
        card_activation_date, card_create_date,
        card_update_date, corpId, comm_id, mobile, user_id,is_package,
--         merchant_id,
        card_gift_amount, take_effect_time,
        past_due_time, charger_times, yx_bz,
        (SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = card_no AND `enable`=TRUE ) as
        stations,
        remark,cardKey,card_name, lineNum, deposit
    </sql>


    <select id="queryCardById" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_card
        WHERE id=#{id}
    </select>

    <select id="queryCardWithUserById" resultMap="BaseResultWithUserMap">
        SELECT
        c.id, c.card_no, c.card_type, c.card_chip_no, c.card_channel, c.card_denomination, c.card_activation_code,
        c.card_status, c.card_activation_date, c.card_create_date,
        c.card_update_date, c.comm_id, c.mobile, c.user_id,c.is_package,
--         c.merchant_id,
        c.card_gift_amount,
        c.take_effect_time, c.past_due_time, c.charger_times, c.yx_bz,
        (SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE ) as
        stations,
        c.remark,c.cardKey,c.card_name,u.userName
        from t_card c left join t_user u on c.user_id = u.id
        WHERE c.id=#{id}
    </select>

    <select id="queryCardByCondition" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_card
        <where>
            yx_bz = 1
            <if test="corpId != null and corpId > 0">
                AND corpId = #{corpId}
            </if>
            <if test="cardType != null">
                AND card_type = #{cardType}
            </if>
            <if test="excludeCardStatusList != null and excludeCardStatusList.size() > 0">
                AND card_status not in
                <foreach collection="excludeCardStatusList" item="item" open="(" separator=","
                         close=")" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="userId != null and userId > 0">
                AND user_id = #{userId}
            </if>
            <if test="mobile != null and mobile != ''">
                AND mobile = #{mobile}
            </if>
            <!--<if test="stations != null and stations != ''">-->
            <!--AND stations = #{stations}-->
            <!--</if>-->
        </where>
    </select>

    <select id="findUrgencyCardNumCount" resultType="com.cdz360.biz.model.trading.site.vo.SiteCardCount">
        select
        sa.`siteId` as siteId,
        count(*) as urgencyCardNum
        from
        d_card_manager.t_site_auth sa
        left join d_card_manager.t_card c
        on c.card_no = sa.account
        and sa.`type` = 1
        and sa.`enable`=TRUE
        where
        c.yx_bz = 1
        and c.card_type = #{cardType}
        and c.card_status not in
        <foreach collection="excludeCardStatusList" item="item" open="(" separator=","
                 close=")" index="index">
            #{item}
        </foreach>
        and sa.`siteId` in
        <foreach collection="siteIdList" item="item" open="(" separator=","
                 close=")" index="index">
            #{item}
        </foreach>
        group by sa.`siteId`
    </select>

    <insert id="insertSelective" parameterType="com.chargerlinkcar.framework.common.domain.vo.Card">
        insert into t_card
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="cardNo != null">
                card_no,
            </if>
            <if test="cardChipNo != null">
                card_chip_no,
            </if>
            <if test="cardType != null">
                card_type,
            </if>
            <if test="cardChannel != null">
                card_channel,
            </if>
            <if test="cardDenomination != null">
                card_denomination,
            </if>
            <if test="cardStatus != null">
                card_status,
            </if>
            <if test="cardActivationDate != null">
                card_activation_date,
            </if>
            <if test="cardCreateDate != null">
                card_create_date,
            </if>
            <if test="cardUpdateDate != null">
                card_update_date,
            </if>
            <if test="commId != null">
                comm_id,
            </if>
            <if test="mobile != null">
                mobile,
            </if>
            <if test="userId != null">
                user_id,
            </if>
            <if test="isPackage != null">
                is_package,
            </if>
            <if test="yxBz != null">
                yx_bz,
            </if>
            <!--<if test="stations != null">-->
            <!--stations,-->
            <!--</if>-->
            <if test="cardKey != null">
                cardKey,
            </if>
            <if test="corpId != null">
                corpId,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="cardNo != null">
                #{cardNo,jdbcType=VARCHAR},
            </if>
            <if test="cardChipNo != null">
                #{cardChipNo,jdbcType=VARCHAR},
            </if>
            <if test="cardType != null">
                #{cardType,jdbcType=TINYINT},
            </if>
            <if test="cardChannel != null">
                #{cardChannel,jdbcType=VARCHAR},
            </if>
            <if test="cardDenomination != null">
                #{cardDenomination,jdbcType=BIGINT},
            </if>
            <if test="cardStatus != null">
                #{cardStatus,jdbcType=VARCHAR},
            </if>
            <if test="cardActivationDate != null">
                #{cardActivationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cardCreateDate != null">
                #{cardCreateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cardUpdateDate != null">
                #{cardUpdateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="commId != null">
                #{commId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                #{userId,jdbcType=BIGINT},
            </if>
            <if test="isPackage != null">
                #{isPackage,jdbcType=TINYINT},
            </if>
            <if test="yxBz != null">
                #{yxBz,jdbcType=VARCHAR},
            </if>
            <!--<if test="stations != null">-->
            <!--#{stations,jdbcType=VARCHAR},-->
            <!--</if>-->
            <if test="cardKey != null">
                #{cardKey,jdbcType=VARCHAR},
            </if>
            <if test="corpId != null">
                #{corpId,jdbcType=VARCHAR},
            </if>
        </trim>
    </insert>

    <!--  carNo条件需为!= null,因为前端用户可能传"",将车牌号置为空-->
    <update id="updateByCardNoSelective" parameterType="com.chargerlinkcar.framework.common.domain.vo.Card">
        update t_card
        <set>
            <if test="carNo != null">
                carNo = UPPER(#{carNo,jdbcType=VARCHAR}),
            </if>
            <if test="cardType != null">
                card_type = #{cardType,jdbcType=TINYINT},
            </if>
            <if test="cardChannel != null">
                card_channel = #{cardChannel,jdbcType=VARCHAR},
            </if>
            <if test="cardDenomination != null">
                card_denomination = #{cardDenomination,jdbcType=BIGINT},
            </if>
            <if test="cardStatus != null">
                card_status = #{cardStatus,jdbcType=VARCHAR},
            </if>
            <if test="cardActivationCode != null and cardActivationCode != ''">
                card_activation_code = #{cardActivationCode,jdbcType=VARCHAR},
            </if>
            <if test="cardActivationDate != null">
                card_activation_date = #{cardActivationDate,jdbcType=TIMESTAMP},
            </if>
            <if test="cardCreateDate != null">
                card_create_date = #{cardCreateDate,jdbcType=TIMESTAMP},
            </if>
            <if test="corpId != null">
                corpId = #{corpId,jdbcType=BIGINT},
            </if>
            <if test="commId != null">
                comm_id = #{commId,jdbcType=BIGINT},
            </if>
            <if test="mobile != null">
                mobile = #{mobile,jdbcType=VARCHAR},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="isPackage != null">
                is_package = #{isPackage,jdbcType=TINYINT},
            </if>
            <!--
            <if test="merchantId != null">
                merchant_id = #{merchantId,jdbcType=BIGINT},
            </if>
            -->
            <if test="yxBz != null">
                yx_bz = #{yxBz,jdbcType=VARCHAR},
            </if>
            <!--<if test="stations != null">-->
            <!--stations = #{stations,jdbcType=VARCHAR},-->
            <!--</if>-->
            <if test="remark != null">
                remark = #{remark,jdbcType=VARCHAR},
            </if>
            <if test="lineNum != null">
                lineNum = #{lineNum,jdbcType=VARCHAR},
            </if>
            <if test="carDepart != null">
                car_depart = #{carDepart,jdbcType=VARCHAR},
            </if>
            <if test="cardName != null">
                card_name = #{cardName,jdbcType=VARCHAR},
            </if>
            <if test="carNum != null">
                car_num = UPPER(#{carNum,jdbcType=VARCHAR}),
            </if>
            <if test="deposit != null">
                deposit = #{deposit},
            </if>
            card_update_date = NOW()
        </set>
        where card_no = #{cardNo,jdbcType=VARCHAR}
    </update>

    <update id="updateActivationCode" parameterType="com.cdz360.biz.model.cus.user.dto.WhiteCard">
        update t_card
        <set>
            card_activation_code = #{passcode},
            card_update_date = now()
        </set>
        where card_no=#{cardNumber}
    </update>

    <select id="findlastCardNo" resultType="java.lang.Long">
        SELECT id FROM `t_card` ORDER BY id DESC LIMIT 0,1 ;
    </select>

    <select id="findByCommIdAndUserId" resultMap="BaseResultMap"
            parameterType="com.chargerlinkcar.framework.common.domain.vo.Card">
        select id, card_no from t_card where
        comm_id=#{commId} and user_id=#{userId}
    </select>
    <select id="queryCardByCardNo" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="Base_Column_List"/>
        from t_card
        where
        card_no=#{cardNo}
    </select>
    <select id="queryCardByCardNoList" resultMap="WhiteCardcfgVoResultMap" parameterType="java.util.List">
        select a.card_no,a.card_chip_no,a.card_name, remark,
        case when a.corpId is null then '' else b.bloc_user_name end as blocUserName,
        case when a.corpId is null then '' else b.phone end as blocUserPhone,
        case when a.corpId is null then c.phone else '' end as phone,
        comm.comm_name commName,
        case when a.corpId is null then c.username else '' end username
        from t_card a
        left join t_r_commercial comm on comm.id = a.comm_id
        left join t_bloc_user b on a.corpId=b.id
        left join t_user c on a.user_id=c.id
        where
        trim(LEADING '0' from card_no) in
        <foreach collection="cardNos" item="item" open="(" separator="," close=")">
            trim(LEADING '0' from #{item})
        </foreach>
    </select>
    <select id="queryCountByCardNo" resultType="java.lang.Integer" parameterType="string">
        select count(card_no)
        from t_card
        where
        card_no = #{cardNo}
        and card_no is not null
        and card_no!=''
    </select>
    <select id="queryCardByCardChipNo" resultMap="BaseResultMap" parameterType="string">
        select
        <include refid="Base_Column_List"/>
        from t_card
        where
        card_chip_no=#{cardChipNo}
    </select>
    <select id="findListByUserId" resultMap="BaseResultMap"
            parameterType="com.chargerlinkcar.framework.common.domain.vo.Card">
        select
        <include refid="Base_Column_List"/>
        from t_card where
        user_id=#{userId}
    </select>
    <sql id="where_query_card">
        where
         tc.yx_bz='1'
         <!--三代离线卡-->
        <choose>
            <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(cardTypeList)">
                and tc.card_type in
                <foreach collection="cardTypeList" separator="," open="(" close=")" item="item" index="index">
                    #{item}
                </foreach>
            </when>
            <otherwise>
                and tc.card_type=#{cardType}
            </otherwise>
        </choose>

        <if test="commIds!=null and commIds.size()>0">
            and tc.comm_id in
            <foreach collection="commIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="commId!=null and commId>0">
            and tc.comm_id = #{commId}
        </if>
        <if test="deposit != null">
            and tc.deposit = #{deposit}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and comm.idChain like CONCAT(#{commIdChain}, '%')
        </if>
        <if test="keyWord!=null and ''!=keyWord">
            AND (tc.card_chip_no LIKE concat('%',#{keyWord},'%') OR tc.card_name LIKE concat('%',#{keyWord},'%'))
        </if>
        <if test="corpName!=null and ''!=corpName">
            AND tbu.bloc_user_name LIKE concat('%',#{corpName},'%')
        </if>
        <if test="cardStatus!=null and cardStatus!='' and cardStatus!=-1">
            AND tc.card_status=#{cardStatus}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(stationName )">
            AND site.name LIKE concat('%',#{stationName},'%')
            AND auth.enable = true
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( carNo )">
            AND tc.`carNo` LIKE concat('%', #{carNo}, '%')
        </if>
        <if test="begintime!=null and ''!=begintime">
            <![CDATA[   and DATE_FORMAT(tc.card_create_date, '%Y-%m-%d %H:%i:%s')>=  FROM_UNIXTIME(LEFT(#{begintime},10),'%Y-%m-%d %H:%i:%s')   ]]>
        </if>
        <if test="endtime!=null and ''!=endtime">
            <![CDATA[   and DATE_FORMAT(tc.card_create_date, '%Y-%m-%d %H:%i:%s')<=  FROM_UNIXTIME(LEFT(#{endtime},10),'%Y-%m-%d %H:%i:%s')   ]]>
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(cardNoList)">
            and tc.card_no in
            <foreach collection="cardNoList" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
    </sql>
    <!--查询已激活 配置下发成功卡片-->
    <select id="getCardByCardNo" resultMap="CARD_RESULT_WITH_AUTH_SITE_LIST">
        select card.id,
        card.card_type cardType,
        card.card_no cardNo,
        card.card_name cardName,
        card.carNo carNo,
        card.car_num carNum,
        card.lineNum,
        card.car_depart carDepart,
        card.card_chip_no cardChipNo,
        card.card_status cardStatus,
        card.is_package isPackage,
        card.card_denomination cardDenomination,
        card.corpId,
        card.comm_id commId,
        sa.siteId,
        user.phone mobile,
        card.user_id as userId,
--         card.merchant_id as merchantId,
        user.username as userName,
        rbu.id as rBlocUserId,
        card.deposit as deposit
        from t_card card
        left join t_user user on card.user_id=user.id
        LEFT JOIN t_r_bloc_user rbu ON card.corpId = rbu.bloc_user_id AND card.user_id = rbu.user_id
        left join t_site_auth sa on sa.account = card.card_no
        where
            trim(LEADING '0' from card.card_no) = trim(LEADING '0' from #{cardNo})
            AND card.yx_bz=1
            AND card.card_status in (10001,10007,10008,10009)
            and sa.type = 1 and sa.topCommId = #{topCommId}
            and sa.enable = TRUE
    </select>

    <!--查询已激活 配置下发成功卡片-->
    <select id="getCardByCardNoX" resultMap="CARD_RESULT_WITH_AUTH_SITE_LIST">
        select card.id,
        card.card_type cardType,
        card.card_no cardNo,
        card.card_name cardName,
        card.carNo carNo,
        card.car_num carNum,
        card.lineNum,
        card.car_depart carDepart,
        card.card_chip_no cardChipNo,
        card.card_status cardStatus,
        card.is_package isPackage,
        card.card_denomination cardDenomination,
        card.corpId,
        card.comm_id commId,
        sa.siteId,
        user.phone mobile,
        card.user_id as userId,
--         card.merchant_id as merchantId,
        user.username as userName,
        rbu.id as rBlocUserId,
        card.deposit as deposit
        from t_card card
        left join t_user user on card.user_id=user.id
        LEFT JOIN t_r_bloc_user rbu ON card.corpId = rbu.bloc_user_id AND card.user_id = rbu.user_id
        left join t_site_auth sa on sa.account = card.card_no
        where
            <choose>
                <when test="cardExactMatch">
                    card.card_no = #{cardNo}
                </when>
                <otherwise>
                    trim(LEADING '0' from card.card_no) = trim(LEADING '0' from #{cardNo})
                </otherwise>
            </choose>
            AND card.yx_bz=1
            AND card.card_status in (10001,10007,10008,10009)
            and sa.siteId = #{siteId}
            and sa.type = 1 and sa.topCommId = #{topCommId}
            and sa.enable = TRUE
    </select>

    <select id="checkCardCount" resultType="java.lang.Long">
        select count(1)
        from t_card c
        inner join t_r_commercial comm on
            c.comm_id = comm.id
        where
            c.card_no = #{cardNo}
          and c.yx_bz = 1
          and c.card_status in (10001, 10007, 10008, 10009)
          and comm.topCommId = #{topCommId}
    </select>

    <select id="getCardChipNoByCardNo" resultType="String">
        select card.card_chip_no cardChipNo
        from t_card card
        where trim(LEADING '0' from card.card_no) = trim(LEADING '0' from #{cardNo})
    </select>

    <!--根据物理卡号获取卡片-->
    <select id="getCardByCardChipNo" resultType="com.chargerlinkcar.framework.common.domain.vo.Card">
        select c.id,
        c.card.card_type cardType,
        c.card_no cardNo,
        c.card_status cardStatus,
        c.is_package isPackage,
        c.card_denomination cardDenomination,
        c.comm_id commId,
        (SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE ) as
        stations,
        c.cardKey
        from t_card c
        where trim(LEADING '0' from c.card_chip_no) = trim(LEADING '0' from #{c.cardChipNo})
        AND yx_bz=1
    </select>

    <!--指定场站数量不包括已删除的场站-->
    <select id="queryCards" resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">
        SELECT
        <!--      IFNULL(s.usableStationCount, 0) AS usableStationCount,-->
        tc.id As cardId,
        tc.card_no AS cardNo,
        tc.carNo AS carNo,
        tc.card_type AS cardType,
        tc.car_num AS carNum,
        tc.car_depart AS carDepart,
        tc.lineNum AS lineNum,
        tc.card_name AS cardName,
        tc.card_chip_no AS cardChipNo,
        tc.card_status AS cardStatus,
        tc.card_create_date AS cardCreateDate,
        tc.user_id as userId,
        tc.comm_id as commId,
--         merchant_id AS merchantId,
<!--        (-->
<!--        SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = tc.card_no AND `enable`=TRUE-->
<!--        <if test="topCommId !=null">-->
<!--            and topCommId = #{topCommId}-->
<!--        </if>-->
<!--        ) as stations,-->
        tc.card_denomination AS cardDenomination,
        tu.username AS userName,
        tc.corpId,
        tbu.bloc_user_name AS corpName,
        tc.deposit as deposit
        FROM

       t_site_auth as auth
        LEFT JOIN t_site as site ON site.id = auth.siteId
        LEFT JOIN t_r_commercial comm on comm.id  = site.commId
        left join t_card tc on tc.card_no = auth.account and auth.type =1
        LEFT JOIN t_user AS tu ON tc.user_id = tu.id
        LEFT JOIN t_bloc_user tbu ON tc.corpId = tbu.id
<!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(stationName )">-->

<!--        </if>-->
        <!--    LEFT JOIN (-->
        <!--      SELECT-->
        <!--        COUNT(tc.id) AS usableStationCount,-->
        <!--        tc.id AS cardId-->
        <!--      FROM-->
        <!--        t_card AS tc-->
        <!--      LEFT JOIN t_site s ON s.id IN ( SELECT siteId FROM t_site_auth WHERE `type` = 1 AND account = tc.card_no AND `enable`=TRUE )-->
        <!--      where s.`status` != 0-->
        <!--      GROUP BY tc.id-->
        <!--    ) s ON tc.id = s.cardId-->
        <include refid="where_query_card"/>
        and tc.card_status != 20000
        and auth.enable= true
        group by cardId
        ORDER BY tc.id desc
    </select>

    <!--<select id="getStationsByCardNo" resultType="String">-->
    <!--SELECT stations FROM t_card where card_no=#{cardNo}-->
    <!--</select>-->

  <!--查询当前商户及子商户未删除的在线卡-->
  <!--指定场站数量不包括已删除的场站-->
    <select id="queryOnlineCardsByPage" resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">
        SELECT
        <!--    IFNULL(s.usableStationCount, 0) AS usableStationCount,-->
        c.id cardId,
        c.carNo carNo,
        c.card_type as cardType,
        c.car_num carNum,
        c.car_depart carDepart,
        c.corpId,
        c.mobile,
        c.user_id userId,
        c.card_no cardNo,
    c.card_chip_no cardChipNo,
    c.card_name cardName,
<!--    (-->
<!--      SELECT-->
<!--        group_concat(auth.siteId)-->
<!--      FROM-->
<!--        t_site_auth auth-->
<!--    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">-->
<!--      left join t_site s on-->
<!--        auth.`siteId` = s.id-->
<!--        and s.`status` != 0-->
<!--      left join t_r_commercial comm on-->
<!--        s.`commId` = comm.id-->
<!--    </if>-->
<!--      WHERE-->
<!--        auth.`type` = 1-->
<!--        AND auth.account = c.card_no-->
<!--        AND auth.`enable`=TRUE-->
<!--      <if test="topCommId !=null">-->
<!--        and auth.topCommId = #{topCommId}-->
<!--      </if>-->
<!--      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">-->
<!--        AND comm.`idChain` like CONCAT(#{commIdChain}, '%')-->
<!--      </if>-->
<!--    ) as stations,-->
    c.card_status cardStatus,
    c.card_activation_date activationDate,
    c.comm_id commId,
    c.lineNum,
    tbu.bloc_user_name AS corpName,
        c.deposit
    FROM
      t_site_auth auth
      left join t_site site on auth.siteId = site.id
      left join d_card_manager.t_card AS c on c.card_no = auth.account and auth.type = 1
      LEFT JOIN t_r_commercial comm ON comm.id = site.commId
      LEFT JOIN t_bloc_user tbu ON c.corpId = tbu.id
<!--    LEFT JOIN (-->
<!--    SELECT-->
<!--    COUNT(c.id) AS usableStationCount,-->
<!--    any_value(c.card_no) cardNo-->
<!--    FROM-->
<!--    d_card_manager.t_card c-->
<!--    LEFT JOIN d_card_manager.t_site s ON s.id IN ( SELECT siteId FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE )-->
<!--    WHERE s.`status` != 0-->
<!--    GROUP BY c.id-->
<!--    ) AS s-->
<!--    ON c.card_no = s.cardNo-->
    WHERE
    auth.enable = 1 and
    c.card_status!=20000 and
    c.yx_bz='1' and
    c.user_id=#{userId}

    <!--添加三代离线卡-->
    <choose>
        <when test="cardType!=null">
            and c.card_type= #{cardType}
        </when>
        <otherwise>
            and c.card_type in (0,4)
        </otherwise>
    </choose>

    <if test="commIds!=null and commIds.size()>0">
      AND c.comm_id in
      <foreach collection="commIds" index="index" item="item" open="("
               separator="," close=")">
        #{item}
      </foreach>
    </if>
    <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
      and comm.idChain like CONCAT(#{commIdChain}, '%')
    </if>
    <if test="begintime!=null and ''!=begintime and endtime!=null and ''!=endtime">
      and c.card_activation_date BETWEEN #{begintime} AND #{endtime}
    </if>
    <if test="cardStatus!=null and ''!=cardStatus">
      and c.card_status = #{cardStatus}
    </if>
        <if test="keyWord!=null and ''!=keyWord">
            and (c.card_chip_no like concat('%',#{keyWord},'%') or c.card_name like concat('%',#{keyWord},'%'))
        </if>
        group by cardId
        ORDER BY c.card_activation_date DESC
    </select>

    <!--查询当前商户及子商户未删除的在线卡-->
    <!--指定场站数量不包括已删除的场站-->
    <!--根据条件查询企业在线卡数据-->
    <select id="queryOnlineCardsByPageOnCorp" parameterType="com.cdz360.biz.cus.domain.request.CardRequest"
            resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">
        SELECT
        <!--    IFNULL(s.usableStationCount, 0) AS usableStationCount,-->
        c.id cardId,
        c.card_no cardNo,
        c.card_chip_no cardChipNo,
        c.card_name cardName,
        c.carNo carNo,
        c.card_type as cardType,
        c.lineNum,
        c.car_num carNum,
        c.car_depart carDepart,
        c.corpId,
        c.mobile,
        c.user_id userId,
<!--        (SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE ) as-->
<!--        stations,-->
        c.card_status cardStatus,
        c.card_activation_date activationDate
        FROM
        d_card_manager.t_card AS c
        <if test="orgIds!=null and orgIds.size()>0">
            LEFT JOIN  t_r_bloc_user trbu
            ON c.corpId=trbu.bloc_user_id AND c.user_id=trbu.user_id
        </if>
        <!--    LEFT JOIN (-->
        <!--    SELECT-->
        <!--    COUNT(c.id) AS usableStationCount,-->
        <!--    any_value(c.card_no) cardNo-->
        <!--    FROM-->
        <!--    d_card_manager.t_card c-->
        <!--    LEFT JOIN d_card_manager.t_site s ON s.id IN ( SELECT siteId FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE )-->
        <!--    WHERE s.`status` != 0-->
        <!--    GROUP BY c.id-->
        <!--    ) AS s-->
        <!--    ON c.card_no = s.cardNo-->
        WHERE
        c.yx_bz='1' and
        c.corpId = #{corpId} and
        c.card_status!=20000

        <!--添加三代离线卡-->
        <choose>
            <when test="cardType!= null">
                and c.card_type=#{cardType}
            </when>
            <otherwise>
                and c.card_type in (0,4)
            </otherwise>
        </choose>
        <if test="cardChipNo!=null and ''!=cardChipNo">
            and c.card_chip_no like concat('%',#{cardChipNo},'%')
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(carDepart)">
            and c.car_depart like concat('%',#{carDepart},'%')
        </if>
        <if test="cardName!=null and ''!=cardName">
            and c.card_name like concat('%',#{cardName},'%')
        </if>
        <if test="carNo!=null and ''!=carNo">
            and c.carNo like concat('%',#{carNo},'%')
        </if>
        <if test="lineNum!=null and ''!=lineNum">
            and c.lineNum like concat('%',#{lineNum},'%')
        </if>
        <if test="carNum!=null and ''!=carNum">
            and c.car_num like concat('%',#{carNum},'%')
        </if>
        <if test="mobile!=null and ''!=mobile">
            and c.mobile like concat('%',#{mobile},'%')
        </if>
        <if test="cardStatus!=null and ''!=cardStatus">
            and c.card_status = #{cardStatus}
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(cardNoList)">
            and c.card_no in
            <foreach collection="cardNoList" separator="," open="(" close=")" item="item" index="index">
                #{item}
            </foreach>
        </if>
        <if test="orgIds!=null and orgIds.size()>0">
            and trbu.corpOrgId in
            <foreach collection="orgIds" index="index" item="orgId"
                     open="(" separator="," close=")">
                #{orgId}
            </foreach>
        </if>
        ORDER BY activationDate DESC
    </select>

<!--    <select id="getUsableStationByCardIdList"-->
<!--            resultType="com.chargerlinkcar.core.domain.vo.CardListdetailVO">-->
<!--        SELECT-->
<!--        COUNT(c.id) AS usableStationCount, c.id as cardId-->
<!--        FROM-->
<!--        d_card_manager.t_card c-->
<!--        LEFT JOIN d_card_manager.t_site s ON s.id IN (-->
<!--        SELECT-->
<!--        auth.siteId-->
<!--        FROM-->
<!--        t_site_auth auth-->
<!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">-->
<!--            left join t_site s on-->
<!--            auth.`siteId` = s.id-->
<!--            left join t_r_commercial comm on-->
<!--            s.`commId` = comm.id-->
<!--        </if>-->
<!--        WHERE-->
<!--        auth.`type` = 1-->
<!--        AND auth.account = c.card_no-->
<!--        AND auth.`enable`=TRUE-->
<!--        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank(commIdChain)">-->
<!--            AND comm.`idChain` like CONCAT(#{commIdChain}, '%')-->
<!--        </if>-->
<!--        ) and s.`status` != 0-->
<!--        WHERE c.id in-->
<!--        <foreach collection="cardIdList" index="index" item="item"-->
<!--                 open="(" separator="," close=")">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--        GROUP BY c.id-->
<!--    </select>-->

    <select id="queryUrgencyCardsByCorpId" resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">

    </select>

    <!--时间查询是针对于卡片激活时间-->
    <select id="queryUrgencyCardsByPage" resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">

        SELECT
        c.id cardId,
        c.card_no cardNo,
        c.card_chip_no cardChipNo,
        c.card_name cardName,
        (
        SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE
        <if test="topCommId !=null">
            and topCommId = #{topCommId}
        </if>
        ) as stations,
        c.user_id as userId,
        u.username as userName,
        c.mobile,
        c.card_status cardStatus,
        c.card_create_date cardCreateDate,
        c.card_activation_date activationDate,
        c.comm_id commId,
        c.corpId,
--         c.merchant_id as merchantId,
        site.name as stationName,
        c.card_activation_code as cardActivationCode,
        IFNULL(tbu.bloc_user_name,'无') as corpName,
        tbu.phone as corpPhone,
        c.remark,
        auth.siteId as siteId
        from
<!--          (SELECT * from t_card where card_type=1 and yx_bz='1') c-->
<!--        LEFT JOIN t_site s on s.id IN (-->
<!--        SELECT siteId FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE-->
<!--        <if test="topCommId !=null">-->
<!--            and topCommId = #{topCommId}-->
<!--        </if>-->
<!--        )-->
          t_site_auth auth
        left join t_site site on auth.siteId = site.id
        left join t_card c on c.card_no = auth.account and auth.type = 1
        LEFT JOIN t_user u
        on c.user_id = u.id
        LEFT join t_bloc_user tbu
        on c.corpId = tbu.id
        left join t_r_commercial comm on comm.id = site.commId
        <where>
            1=1
            and auth.enable = 1
            and c.card_type = 1 and c.yx_bz='1'
            <if test="cardNo!=null and ''!=cardNo">
                AND (c.card_no like concat('%',#{cardNo},'%') OR c.card_chip_no like concat('%',#{cardNo},'%'))
            </if>
            <if test="commIds!=null and commIds.size()>0">
                AND c.comm_id in
                <foreach collection="commIds" index="index" item="item" open="("
                         separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
                and comm.idChain like CONCAT(#{commIdChain}, '%')
            </if>
            <if test="commId!=null and commId>0">
                and c.comm_id = #{commId}
            </if>
            <if test="begintime!=null and ''!=begintime and endtime!=null and ''!=endtime">
                and c.card_activation_date BETWEEN #{begintime} AND #{endtime}
            </if>
            <if test="cardStatus!=null and ''!=cardStatus">
                and c.card_status = #{cardStatus}
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(cardStatusList)">
                and c.card_status in
                <foreach collection="cardStatusList" separator="," open="(" close=")" item="item" index="index">
                    #{item}
                </foreach>
            </if>
            <if test="stationName!=null and ''!=stationName">
                and site.`name` like concat('%',#{stationName},'%')
            </if>
            <if test="stations!=null and ''!=stations">
                and site.id = #{stations}
            </if>
            <if test="corpId!=null and corpId>0">
                and c.corpId = #{corpId}
            </if>
            <if test="queryType!=null and queryStr!=null and queryStr!='' and 1==queryType">
                and c.card_chip_no like concat('%',#{queryStr},'%')
            </if>
            <if test="queryType!=null and queryStr!=null and queryStr!='' and 2==queryType">
                and c.card_name like concat('%',#{queryStr},'%')
            </if>
            <if test="queryType!=null and queryStr!=null and queryStr!='' and 3==queryType">
                and u.username like concat('%',#{queryStr},'%')
            </if>
            <if test="queryType!=null and queryStr!=null and queryStr!='' and 4==queryType">
                and c.mobile like concat('%',#{queryStr},'%')
            </if>
            <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(excludeCardStatusList)">
                and c.card_status not in
                <foreach collection="excludeCardStatusList" separator="," open="(" close=")" item="item" index="index">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY c.card_status=20000 ASC,c.card_activation_date DESC

    </select>

    <!--通过物理卡号变更卡片状态-->
    <update id="updateCardStatusByCardChipNo" parameterType="java.util.Map">
        UPDATE t_card
        SET card_status = #{cardStatus}, card_update_date = NOW()
        WHERE card_chip_no=#{cardChipNo} and yx_bz='1'
    </update>

    <update id="updateCardStatus" parameterType="java.util.Map">
        UPDATE t_card SET card_status = #{cardStatus},card_update_date = NOW() WHERE card_no=#{cardNo} and yx_bz='1'
    </update>
    <!--充电管理平台删除卡片的同时，将卡片的状态置为20000 已删除-->
    <update id="updateCardInvalid" parameterType="java.lang.String">
        UPDATE t_card SET yx_bz = '0', card_status = '20000', card_update_date = NOW() WHERE card_no=#{cardNo}
    </update>
    <select id="countByCardNo" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_card
        WHERE card_no=#{cardNo} and yx_bz='1'
    </select>

    <!--更新卡余额-->
    <update id="updateCardBalance">
        update t_card
        set card_denomination = #{cardBalance,jdbcType=BIGINT}
        where card_no = #{cardNo}
    </update>

    <!--根据客户查询该客户下此商户的所有卡片-->
    <select id="findByUserId" resultType="com.chargerlinkcar.framework.common.domain.vo.CardVo">
        select c.id As cardId, c.card_no as cardNo,c.card_activation_date as
        cardActivationDate,c.card_type as type ,0 as status
        from t_card as c,t_balance as b
        where c.user_id=#{userId} and c.id=b.card_id and b.balance_type = 'PERSONAL'
        UNION
        select c.id As cardId, c.card_no as cardNo,c.card_activation_date as
        cardActivationDate,c.card_type as type ,1 as status
        from t_card as c
        where c.user_id=#{userId}
        and not exists (
        select c.card_no
        from t_balance as b
        where b.user_id=#{userId} and c.id=b.card_id and b.balance_type = 'PERSONAL'
        )
    </select>

    <!--删除卡信息-->
    <delete id="deleteCardByCardNo" parameterType="java.lang.String">
        DELETE FROM t_card WHERE card_no = #{cardNo}
    </delete>

    <!--删除卡信息-->
    <delete id="deleteCardsByIds" parameterType="java.util.List">
        DELETE FROM t_card WHERE id in
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="findListByCardStatusAndId" resultMap="BaseResultMap"
            parameterType="java.util.List">
        SELECT
        <include refid="Base_Column_List"/>
        FROM t_card WHERE 1=1
        <if test="cardStatus!=null and ''!=cardStatus">
            AND card_status = #{cardStatus}
        </if>
        AND id IN
        <foreach collection="ids" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <!--查询当前商户及子商户下的在线卡列表-->
    <select id="queryAllCardList" resultType="com.chargerlinkcar.framework.common.domain.vo.CardVo">
        SELECT
        c.id As cardId,
        c.card_chip_no cardChipNo,
        c.card_no as cardNo,
        c.card_name as cardName,
        c.card_activation_date as cardActivationDate,
        c.card_type as type ,
        c.card_status as status
        FROM t_card as c
        WHERE
        c.card_type = 0 AND c.user_id=#{userId}
        <if test="commIds!=null and commIds.size()>0">
            AND c.comm_id in
            <foreach collection="commIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY c.card_create_date;
    </select>

    <sql id="where_query_mgm_card">
        <where>
            <if test="keyWord!=null and ''!=keyWord">
                AND (tc.card_no LIKE concat('%',#{keyWord},'%') or tc.card_chip_no LIKE concat('%',#{keyWord},'%'))
            </if>
            <if test="cardStatus!=null and cardStatus!='' and cardStatus!=-1">
                AND tc.card_status=#{cardStatus}
            </if>
            <if test="cardType!=null and cardType!=''">
                AND tc.card_type=#{cardType}
            </if>
            <if test="commId != null">
                AND tc.comm_id = #{commId}
            </if>
            <if test="begintime!=null and ''!=begintime">
                <![CDATA[   and DATE_FORMAT(tc.card_create_date, '%Y-%m-%d %H:%i:%s')>=  FROM_UNIXTIME(LEFT(#{begintime},10),'%Y-%m-%d %H:%i:%s')   ]]>
            </if>
            <if test="endtime!=null and ''!=endtime">
                <![CDATA[   and DATE_FORMAT(tc.card_create_date, '%Y-%m-%d %H:%i:%s')<=  FROM_UNIXTIME(LEFT(#{endtime},10),'%Y-%m-%d %H:%i:%s')   ]]>
            </if>
        </where>
    </sql>
    <!-- 卡信息列表-->
    <select id="queryMgmCards" resultType="com.chargerlinkcar.framework.common.domain.vo.CardMgnVo">
        SELECT
        tc.id As id,tc.card_no AS cardNo,tc.card_chip_no AS cardChipNo,tc.card_type AS cardType, tc.card_status AS
        cardStatus,
        tc.card_create_date AS cardCreateDate,tc.user_id as userId,
        tc.comm_id as commId,tc.mobile AS mobile,
        <if test="cardStatus != 10000">
        trc.comm_name as commName,
        </if>
        tc.yx_bz as yxBz, tc.cardKey as cardKey
        FROM t_card as tc
        <if test="cardStatus != 10000">
        left join t_r_commercial trc on tc.comm_id = trc.id
        </if>
        <include refid="where_query_mgm_card"/>
        ORDER BY tc.id desc
    </select>

    <!--校验卡片有效性-->
    <select id="checkCard" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_card
        WHERE card_no=#{cardNo} or card_chip_no=#{cardChipNo}
    </select>

    <!--批量添加卡片-->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO t_card
        (card_no, card_chip_no, card_type, card_status, card_create_date, card_update_date, cardKey, car_depart, remark)
        VALUES
        <foreach collection="cards" item="item" index="index" separator=",">
            (#{item.cardNo}, #{item.cardChipNo}, #{item.cardType}, #{item.cardStatus}, #{item.cardCreateDate},
            #{item.cardUpdateDate}, #{item.cardKey}, #{item.carDepart}, #{item.remark})
        </foreach>
    </insert>

    <update id="updateUrgencyCard" parameterType="java.util.List">
        update t_card set card_status=#{status} where card_no in
        <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        and card_type = #{cardType}
        and card_status in ('10001', '10007', '10008', '10009')
        <if test="excludeStatus != null">
            and card_status != #{excludeStatus}
        </if>
    </update>

    <select id="queryCardNoListByBlocUserId" resultType="string">
        SELECT
        card_no
        FROM t_card
        WHERE 1 = 1 AND corpId =#{blocUserId}
    </select>

    <!-- 订单筛选-根据逻辑卡号查询物理卡号-->
    <select id="selectShipCardNoByCardNos" resultMap="BaseResultMap">
        select card_no, card_chip_no
        from t_card
        where 1=1
        <if test="cardNos!=null and cardNos.size()>0">
            AND card_no in
            <foreach collection="cardNos" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <resultMap id="BlocUserNameResultMap" type="com.chargerlinkcar.framework.common.domain.vo.Card">
        <result column="bloc_user_name" jdbcType="VARCHAR" property="blocUserName"/>
        <result column="card_no" jdbcType="VARCHAR" property="cardNo"/>
    </resultMap>

    <select id="selectBlocUserNameByCardNos" resultMap="BlocUserNameResultMap">
        select C.card_no, B.bloc_user_name
        from t_card C left join t_bloc_user B ON C.corpId = B.id
        where 1=1
        <if test="cardNos!=null and cardNos.size()>0">
            AND C.card_no in
            <foreach collection="cardNos" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <resultMap type="com.cdz360.biz.model.cus.user.dto.WhiteCardDto" id="whiteCardDtoMap">
        <result column="stations" jdbcType="VARCHAR" property="siteId"/>
        <collection property="whiteCardList" ofType="com.cdz360.biz.model.cus.user.dto.WhiteCard">
            <result column="cardNo" property="cardNumber"/>
            <result column="card_chip_no" property="cardChipNo"/>
            <result column="card_activation_code" property="passcode"/>
        </collection>
    </resultMap>

    <!--查询站点下的紧急充电卡列表 (弃用紧急充电卡)-->
    <select id="queryWhiteCardDtoMapList" resultMap="whiteCardDtoMap">
        SELECT
        c.card_no cardNo,
        c.card_chip_no,
        c.card_activation_code,
        (
        SELECT group_concat(siteId) FROM t_site_auth WHERE `type` = 1 AND account = c.card_no AND `enable`=TRUE
        <if test="topCommId !=null">
            and topCommId = #{topCommId}
        </if>
        ) as stations
        FROM t_card as c
        <!-- 逻辑卡号唯一 -->
        left join t_site_auth sa on sa.type=1 and sa.account=c.card_no
        and sa.enable = true
        WHERE
        c.card_type = 1
        AND
        c.card_status NOT IN
        <foreach collection="excludeCardStatusList" index="index" item="item" open="("
                 separator="," close=")">
            #{item}
        </foreach>
        AND
        c.yx_bz = 1
        <!-- 紧急卡只能绑定一个场站 - 暂时处理逻辑 -->
        AND
        sa.siteId is not null
        AND
        sa.siteId != ''
        <if test="siteId != null and siteId != ''">
            AND sa.siteId = #{siteId}
        </if>
        <if test="siteList !=null and siteList.size() > 0">
            AND sa.siteId in
            <foreach collection="siteList" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <!-- ===================================== -->
        <if test="cardChipNo != null and cardChipNo != ''">
            AND c.card_chip_no != #{cardChipNo}
        </if>
        <if test="commIds != null and commIds.size() > 0">
            AND c.comm_id in
            <foreach collection="commIds" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

    <!--获取用户充电卡列表-->
    <select id="findByUserIdAndStatus" resultType="com.chargerlinkcar.framework.common.domain.vo.CardDetailVO">
        SELECT c.id as cardId, c.user_id as userId, c.card_chip_no as cardChipNo, c.card_no as cardNo,
        c.card_status as status, c.comm_id as commId, c.card_name as cardName, comm.comm_name as commName
        from t_card c left join t_r_commercial comm on c.comm_id=comm.id
        WHERE c.card_type = 0 AND c.user_id = #{userId}
        <if test="statusList != null and statusList.size() > 0">
            AND c.card_status in
            <foreach collection="statusList" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty(commIdList)">
            AND c.comm_id in
            <foreach collection="commIdList" index="index" item="item" open="("
                     separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY c.card_create_date DESC
        limit #{offset}, #{limit}
    </select>

    <!--用户是否拥有VIN码-->
    <select id="findOneByUserId" resultType="com.chargerlinkcar.framework.common.domain.vo.CardVo">
        select *
        from t_card
        left join t_r_commercial comm on t_card.comm_id = comm.id
        where yx_bz = 1
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and comm.idChain like CONCAT(#{commIdChain}, '%')
        </if>
        and card_status in (10002, 10001)
        and user_id = #{userId} limit 1
    </select>
    <update id="resetCardById" parameterType="java.util.List">
        update t_card set
        card_status='10000',
        card_activation_date = null,
        card_activation_code = null,
        carNo = '',
        car_num = null,
        lineNum = null,
        card_update_date = now(),
        corpId = null,
        card_type = 2,
        stations = null,
        card_name = null,
        car_depart = null,
        remark = null,
        mobile = null,
        user_id = null,
        is_package = null,
--         merchant_id = null,
        yx_bz = 1,
        comm_id = null
        where id in
        <foreach collection="ids" item="item" index="index" separator="," open="(" close=")">
            #{item}
        </foreach>
        and card_status = 20000
    </update>

    <select id="getCorpOrgName" resultType="com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo">
        SELECT
        rbu.id,
        rbu.`name`,
        rbu.corpOrgId,
        co.orgName as corpOrgName
        FROM
        t_r_bloc_user rbu
        LEFT JOIN t_corp_org co ON rbu.corpOrgId = co.id
        where
        co.`enable` = 1
        AND rbu.`status` = 1
        AND rbu.bloc_user_id = #{blocUserId}
        AND rbu.user_id = #{userId}
        AND rbu.phone = #{phone}
    </select>
    <select id="getCardsByUserIdAndCorpIds" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from t_card
        WHERE user_id = #{userId}
        and card_status <![CDATA[ <> ]]> '20000'
        and corpId in
        <foreach item="item" index="index" collection="corpIds"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        ORDER BY card_update_date DESC
    </select>

    <!--  得到需要被清洗的在线卡数据(获得关键字段即可)-->
    <!-- 备份 c.card_no AS cardNo,c.card_chip_no AS cardChipNo,c.card_status AS cardStatus,c.user_id AS userId,-->
    <!--  c.corpId,c.card_type AS cardType,u.defaultPayType,u.balanceId,rbu.id AS rBlocUserId,bu.id AS blocUserId-->
    <select id="getCardCleanData" resultType="com.chargerlinkcar.framework.common.domain.vo.Card">
        SELECT
        c.card_no AS cardNo,
        bu.id AS blocUserId
        FROM
        t_card c
        INNER JOIN t_user u ON c.user_id = u.id AND u.defaultPayType = 2
        INNER JOIN t_r_bloc_user rbu ON u.balanceId = rbu.id
        INNER JOIN t_bloc_user bu ON rbu.bloc_user_id = bu.id
        WHERE
        c.card_type = 0
        AND c.card_status != 20000
        AND c.corpId IS NULL
    </select>

    <select id="findAll" resultType="com.chargerlinkcar.framework.common.domain.vo.Card">
        SELECT
        c.card_no AS cardNo,
        c.stations AS stations,
        c.comm_id AS commId
        FROM
        t_card c
        WHERE
        c.stations IS NOT NULL
        LIMIT #{start}, #{size}
    </select>

    <select id="getCreditAccountCardCount" resultType="java.lang.Integer">
        select
        count(*)
        from
        d_card_manager.t_r_bloc_user rbu
        inner join d_card_manager.t_card card on
        rbu.bloc_user_id = card.`corpId`
        and rbu.phone = card.mobile
        and rbu.user_id = card.user_id
        and card.card_status != 20000
        and card.yx_bz != 0
        where
        rbu.id = #{rBlocUserId}
    </select>

    <select id="getSiteNameListByCardId"
            resultType="java.lang.String">
        select s.name
        from t_card as card
        left join t_site_auth sa on sa.account = card.card_no
        left join t_site s on s.id = sa.siteId
        where card.id = #{cardId}
        and sa.enable = true and sa.`type` = 1
        and s.status in (2, 3)
    </select>
    <select id="getEmergencyCardBySiteId" resultType="com.chargerlinkcar.framework.common.domain.vo.Card">
        SELECT
            card.comm_id AS commId,
            card.card_chip_no AS cardChipNo,
            trc.idChain
        FROM
            d_card_manager.t_site_auth auth
            LEFT JOIN d_card_manager.t_card card ON auth.account = card.card_no
            LEFT JOIN d_card_manager.t_r_commercial trc ON trc.id = card.comm_id
        WHERE
            siteId = #{siteId}
            AND auth.`enable` = 1
            AND card.card_type = 1
            AND card.yx_bz = 1
            and card.card_status in ('10001','10007','10008','10009')
    </select>
    <select id="getOnlineCardBySiteId" resultType="com.chargerlinkcar.framework.common.domain.vo.Card">
        SELECT
            card.comm_id AS commId,
            card.card_chip_no AS cardChipNo,
            card.corpId,
            trc.idChain
        FROM
            d_card_manager.t_site_auth auth
            LEFT JOIN d_card_manager.t_card card ON auth.account = card.card_no
            LEFT JOIN d_card_manager.t_r_commercial trc ON  trc.id = card.comm_id
        WHERE
            siteId = #{siteId}
            AND auth.`enable` = 1
            AND card.card_type = 0
            AND card.yx_bz = 1
    </select>

    <update id="swapCreditAccountCard">
        update
            d_card_manager.t_card card inner join
            d_card_manager.t_r_bloc_user rbu on
            rbu.bloc_user_id = card.`corpId`
            and rbu.phone = card.mobile
            and rbu.user_id = card.user_id
            and card.card_status != 20000
            and card.yx_bz != 0
        set
            card.mobile = #{mobile}, card.user_id = #{userId}
        where
            rbu.id = #{rBlocUserId}
    </update>
    <update id="updateEnableBySiteId">
        update t_site_auth auth , t_card card  set
        auth.enable = 0
        where
        auth.account =card.card_no
        and auth.type = 1
        and auth.siteId = #{siteId}
        and card.comm_id in
        <foreach collection="commIdList" item="item" open="(" separator=","
                 close=")" index="index">
            #{item}
        </foreach>

    </update>

    <update id="moveCorp">
        update t_card set
            card_update_date = now(),
            comm_id = #{commId}
        where corpId = #{corpId}
    </update>

    <update id="batchUpdateCardDeposit">
        update t_card set
            deposit = #{deposit},
            card_update_date = now()
        <where>
            corpId is null
            <![CDATA[ and deposit <> #{deposit}]]>
            and trim(LEADING '0' from card_no) in
            <foreach collection="cardNos" item="item" open="(" separator="," close=")">
                trim(LEADING '0' from #{item})
            </foreach>
        </where>
    </update>

    <select id="getCardByCorIdAndUserId" resultMap="CARD_RESULT_WITH_AUTH_SITE_LIST">
        select card.id,
        card.card_type cardType,
        card.card_no cardNo,
        card.card_name cardName,
        card.carNo carNo,
        card.car_num carNum,
        card.lineNum,
        card.car_depart carDepart,
        card.card_chip_no cardChipNo,
        card.card_status cardStatus,
        card.is_package isPackage,
        card.card_denomination cardDenomination,
        card.corpId,
        card.comm_id commId,
        sa.siteId,
        user.phone mobile,
        card.user_id as userId,
--         card.merchant_id as merchantId,
        user.username as userName,
        rbu.id as rBlocUserId,
        card.deposit as deposit
        from t_card card
        left join t_user user on card.user_id=user.id
        LEFT JOIN t_r_bloc_user rbu ON card.corpId = rbu.bloc_user_id AND card.user_id = rbu.user_id
        left join t_site_auth sa on sa.account = card.card_no
        where
        card.yx_bz=1
        and card.user_id = #{userId}
        and card.corpId = #{corpId}
        AND card.card_status in (10001,10007,10008,10009)
        and sa.type = 1
        and sa.enable = TRUE
        limit 999
    </select>

    <!--海外版，查询当前商户及子商户未删除的在线卡-->
    <!--不包含site信息-->
    <select id="queryEssOnlineCardsByPage" resultType="com.cdz360.biz.cus.domain.vo.CardListdetailVO">
        SELECT
        c.id cardId,
        c.carNo carNo,
        c.card_type AS cardType,
        c.car_num carNum,
        c.car_depart carDepart,
        c.corpId,
        c.mobile,
        c.user_id userId,
        c.card_no cardNo,
        c.card_chip_no cardChipNo,
        c.card_name cardName,
        c.card_status cardStatus,
        c.card_activation_date activationDate,
        c.comm_id commId,
        c.lineNum,
        c.deposit
        FROM
        t_card c
        LEFT JOIN t_r_commercial comm ON comm.id = c.comm_id
        WHERE
        c.card_status !=20000
        AND c.yx_bz='1'
        AND c.user_id=#{userId}
        <if test="cardType!=null">
            AND c.card_type= #{cardType}
        </if>
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            AND comm.idChain LIKE CONCAT(#{commIdChain}, '%')
        </if>
        <if test="startTime!=null and ''!=startTime and endTime!=null and ''!=endTime">
            AND c.card_activation_date BETWEEN #{startTime} AND #{endTime}
        </if>
        <if test="cardStatus!=null and ''!=cardStatus">
            AND c.card_status = #{cardStatus}
        </if>
        <if test="keywords!=null and ''!=keywords">
            AND (c.card_chip_no LIKE CONCAT('%',#{keywords},'%') OR c.card_name LIKE CONCAT('%',#{keywords},'%'))
        </if>
        GROUP BY cardId
        ORDER BY c.card_activation_date DESC
    </select>
</mapper>