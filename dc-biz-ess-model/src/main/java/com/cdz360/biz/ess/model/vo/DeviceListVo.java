package com.cdz360.biz.ess.model.vo;

import com.cdz360.biz.model.ess.vo.BatteryInfo;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "设备概要信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class DeviceListVo extends DeviceBase {

    @Schema(description = "DTU设备序列号")
    @JsonInclude(Include.NON_NULL)
    private String dtuSerialNo;

    @Schema(description = "当前SOC(%)")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal soc;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 电池
    // 1: 逆变器 <-- 电池
    // 2. 逆变器 --> 电池
    @Schema(description = "充放电状态: 0(不充不放);1(放电);2(充电)")
    @JsonInclude(Include.NON_NULL)
    private Integer batteryStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 光伏
    // 1: 逆变器 <-- 光伏
    // 2. 逆变器 --> 光伏
    @Schema(description = "光伏状态: 0(待机);1(发电)")
    @JsonInclude(Include.NON_NULL)
    private Integer pvStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 负载
    // 1: 逆变器 <-- 负载
    // 2. 逆变器 --> 负载
    @Schema(description = "负载状态: 0(不耗不供);1(供电);2(耗电)")
    @JsonInclude(Include.NON_NULL)
    private Integer loadStatus;

    // 相对逆变器来讨论电流流向
    // 0: 逆变器 --- 电网
    // 1: 逆变器 <-- 电网
    // 2. 逆变器 --> 电网
    @Schema(description = "电网状态: 0(不购不馈);1(购电);2(馈电)")
    @JsonInclude(Include.NON_NULL)
    private Integer gridStatus;

    @JsonIgnore
    @Schema(description = "电池概要信息")
    @JsonInclude(Include.NON_NULL)
    private List<BatteryInfo> batInfoList;

    @Schema(description = "设备图片URL列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> imgList;
}
