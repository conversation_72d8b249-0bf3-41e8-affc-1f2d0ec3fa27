package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "光伏信息")
@Data
@Accessors(chain = true)
public class PvInfoVo {

    @Schema(description = "标号")
    private int idx;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    private EquipStatus status;

    @Schema(description = "电池电压,单位: V")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal voltage;

    @Schema(description = "电池电流,单位: A")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal current;

    @Schema(description = "电池功率,单位: kW")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal power;

    @Schema(description = "光伏负载率 %")
    private BigDecimal loadRate;
}
