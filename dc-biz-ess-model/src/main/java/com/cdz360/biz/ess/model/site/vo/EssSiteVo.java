package com.cdz360.biz.ess.model.site.vo;

import com.cdz360.biz.model.site.param.AddSiteParam.ImageVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "ESS场站信息")
@Data
@Accessors(chain = true)
public class EssSiteVo {

    @Schema(title = "场站ID")
    private String id;

    @Schema(title = "站点名称")
    private String siteName;

    @Schema(title = "站点状态")
    private Long status;

    @Schema(description = "国家名称")
    private String countryName;

    @Schema(description = "国家名称翻译")
    private String countryTranslations;
    
    @Schema(description = "州/省名称")
    private String stateName;

    @Schema(description = "州/省名称翻译")
    private String stateTranslations;

    @Schema(description = "城市名称")
    private String cityName;

    @Schema(description = "城市名称翻译")
    private String cityTranslations;

    @Schema(description = "关联城市ID(t_geo_cities.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long cityId;

    @Schema(description = "关联国家ID(t_geo_countries.id)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long countryId;
    @Schema(description = "场站地址")
    private String address;

    @Schema(description = "储能装机总功率, 单位: kW")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essPower;

    @Schema(description = "储能装机容量, 单位: kW·h")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal essCapacity;

    @Schema(description = "ess设备数量")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long essCount;

    @Schema(description = "站点地图")
    private List<ImageVo> images;

    @Schema(description = "经度")
    private BigDecimal longitude;

    @Schema(description = "纬度")
    private BigDecimal latitude;

    @Schema(description = "投运时间")
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
    private LocalDateTime onlineTime;
}
