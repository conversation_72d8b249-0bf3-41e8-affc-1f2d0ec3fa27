package com.cdz360.biz.model.tj.kc.vo;


import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "勘察站-充电区域")
public class TjSurveyChargeAreaVo {

    @Schema(description = "主键id")
    private Long id;

    @Schema(description = "勘察站的no")
    private String surveyNo;

    @Schema(description = "变压器到充电区域电缆类型（1：使用分支箱，2：无分支箱，3：不需要）")
    private Integer transformerToChargeCable;

    @Schema(description = "主电缆距离")
    private Integer transformerToChargeCableDistance;

    @Schema(description = "变压器块的电缆类型（2001001：铜缆，2001002：铝缆）")
    private Integer transformerToChargeCableType;

    @Schema(description = "充电桩到分支箱或变压器的电缆距离")
    private Integer chargeCableDistance;

    @Schema(description = "主机到终端的距离")
    private Integer masterToTerminalDistance;

    @Schema(description = "充电块的电缆类型（2001001：铜缆，2001002：铝缆）")
    private Integer chargeCableType;

    @Schema(description = "架桥距离")
    private Integer galvanizedCableTrayDistance;

    @Schema(description = "普通土开挖距离")
    private Integer ordinarySoilExcavationDistance;

    @Schema(description = "人行道开挖")
    private Integer pedestrianWalkwayExcavationDistance;

    @Schema(description = "混泥土开挖距离")
    private Integer concreteExcavationDistance;

    @Schema(description = "是否需要电桩基础（水泥浇筑台等）")
    private Boolean evseBaseNeed;

    @Schema(description = "是否删除")
    private Boolean enable;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

    @Schema(description = "设备集合")
    private List<TjSurveyChargeAreaPilePo> tjSurveyChargeAreaPilePoList;
}

