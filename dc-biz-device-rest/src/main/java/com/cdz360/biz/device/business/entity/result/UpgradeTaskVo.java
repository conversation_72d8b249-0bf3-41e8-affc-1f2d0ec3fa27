package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.model.evse.po.PackageInfoItem;
import io.swagger.annotations.ApiModel;
import java.util.List;
import lombok.Data;

import java.util.Date;

/**
 * @Classname UpdateTaskVo
 * @Description TODO
 * @Date 9/11/2019 2:08 PM
 * @Created by Rafael
 */
@Data
@ApiModel("桩升级历史")
public class UpgradeTaskVo {
    private Long id;
    private String siteId;
    private Long bundleId;
    private int evseCount;//升级设备总数
    private Long opId;
    private String opName;
    private Date createTime;//升级时间

    //out of columns
    private String bundleName;
    private String updateProgress;//升级进度
    private Long bundleVersion;

    // 升级包信息-海外版使用
    private List<PackageInfoItem> packageInfo;
    // 升级包版本-海外版使用，由于海外版的升级包对应的t_evse_package表，里边的version字段数据类型是varchar(255)，因此此处单独处理
    private String packageVersion;
}