package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.model.base.vo.BaseObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel("充电桩密钥")
public class EvsePasscodePo extends BaseObject {

    private Long id;

    @ApiModelProperty("充电桩编号")
    private String evseNo;


    @ApiModelProperty("密钥版本号")
    private Long ver;


    @ApiModelProperty("HEX编码的密钥")
    private String passcode;


    @ApiModelProperty("是否有效")
    private Boolean enable;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("最后更新时间")
    private Date updateTime;


}
