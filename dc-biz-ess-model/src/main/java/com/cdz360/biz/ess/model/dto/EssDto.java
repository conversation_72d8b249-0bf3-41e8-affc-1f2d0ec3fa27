package com.cdz360.biz.ess.model.dto;

import com.cdz360.biz.model.ess.po.EssPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssDto extends EssPo {

    @Schema(description = "上次成功下发配置模板名称")
    private String cfgSuccessName;
}
