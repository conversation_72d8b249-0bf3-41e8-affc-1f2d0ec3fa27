package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskDetailRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskInfoRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskListRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskRequest;
import com.cdz360.biz.device.business.entity.result.EvsePasscodePo;
import com.cdz360.biz.device.business.entity.result.EvseVo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class HystrixIotDeviceMgmFactory implements FallbackFactory<IotDeviceMmgClient> {

    @Override
    public IotDeviceMmgClient create(Throwable throwable) {
        return new IotDeviceMmgClient() {

//            @Override
//            public ObjectResponse<DeviceBi> getDeviceBi(String siteId) {
//                log.error("发生熔断. siteId = {}", siteId);
//
//                ObjectResponse<DeviceBi> res = new ObjectResponse<>();
//                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
//                res.setError("系统繁忙,请稍后重试");
//                return res;
//            }

//            @Override
//            public ObjectResponse<Long> uploadEvseBundle(MultipartFile file, String token) {
//                ObjectResponse<Long> res = new ObjectResponse<>();
//                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
//                res.setError("系统繁忙,请稍后重试");
//                return res;
//            }

//            @Override
//            public ListResponse<EvseBundleDto> evseBundlePage(EvseBundleParam evseBundleParam) {
//                ListResponse<EvseBundleDto> res = new ListResponse<>();
//                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
//                res.setError("系统繁忙,请稍后重试");
//                return res;
//            }

//            @Override
//            public BaseResponse deleteEvseBundle(Long id) {
//                BaseResponse res = new BaseResponse();
//                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
//                res.setError("系统繁忙,请稍后重试");
//                return res;
//            }

            @Override
            public ListResponse<EvseVo> selectByEvseIds(List<String> evseIds) {
                ListResponse res = new ListResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public ObjectResponse<Long> updateEvsePasscode(String evseNo, String passcode) {
                ObjectResponse res = new ObjectResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public ObjectResponse<EvsePasscodePo> getEvsePasscode(String evseNo, Long ver) {
                ObjectResponse res = new ObjectResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public ListResponse getUpgradeTaskListBySite(
                UpgradeTaskListRequest updateTaskListRequest) {
                ListResponse res = new ListResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public ListResponse getUpgradeTaskDetailListByTaskId(
                UpgradeTaskDetailRequest updateTaskDetailRequest) {
                ListResponse res = new ListResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public BaseResponse startTask(UpgradeTaskRequest upgradeTaskRequest) {
                BaseResponse res = new BaseResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

            @Override
            public ObjectResponse getUpgradeTaskInfo(
                UpgradeTaskInfoRequest upgradeTaskInfoRequest) {
                ObjectResponse res = new ObjectResponse();
                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
                res.setError("系统繁忙,请稍后重试");
                return res;
            }

//            @Override
//            public ListResponse<String> getIdleSiteIdList(@RequestParam("topCommId") Long topCommId) {
//                ListResponse res = new ListResponse();
//                res.setStatus(DcConstants.KEY_RES_CODE_SERVER_ERROR);
//                res.setError("系统繁忙,请稍后重试");
//                return res;
//            }
        };
    }
}
