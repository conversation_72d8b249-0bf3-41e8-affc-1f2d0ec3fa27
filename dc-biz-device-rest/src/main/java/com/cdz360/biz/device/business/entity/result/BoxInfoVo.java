package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.biz.device.business.constant.ChargingWayEnum;
import com.cdz360.biz.device.common.constant.DeviceStatusEnum;
import lombok.Data;

import java.io.Serializable;

/**
 * 设备简单信息
 *
 * <AUTHOR>
 * @date Create on 2018/7/5 23:12
 */
@Data
public class BoxInfoVo implements Serializable {

    /**
     * 设备ID
     */
    private String deviceId;
    /**
     * 设备序列号/桩号
     */
    private String serialNumber;
    /**
     * 设备类型
     */
    private Integer deviceType;
    /**
     * 设备名称
     */
    private String boxName;
    /**
     * 代理商ID
     */
    private String businessId;
    /**
     * 站点编号
     */
    private String siteId;

    /**
     * 桩状态
     */
    private EvseStatus evseStatus;
    /**
     * 设备状态{@link DeviceStatusEnum}
     */
    private Integer status;
    /**
     * 有效枪头数量
     */
    private Integer validateConnectorCount;
    /**
     * 交直流类型**0-交流 1-直流 2-交直流**
     */
    private Integer currentType;
    /**
     * 所属产品系列名称
     */
    private String productName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 最后心跳时间**毫秒时间戳**
     */
    private Long lastHeartbeatTime;

    /**
     * 是否关联场站默认计费(1：是 0：否)
     */
    private Integer isAssociateSiteTemplate;

    /**
     * 最新下发计费模板Id
     */
    private Long templateId;

    /**
     * 最新下发计费模板名称
     */
    private String templateName;

    /**
     * 当前生效的计费模板Id
     */
    private Long activeTemplateId;

    /**
     * 当前生效的计费模板名称
     */
    private String activeTemplateName;

    /**
     * 站点名称(暂时仅计费模板下发时用到)
     */
    private String siteName;

    /**
     * 收费方式{@link ChargingWayEnum}
     */
    private Integer chargingWay;

    /**
     * 收费方式名称{@link ChargingWayEnum}
     */
    private String chargingWayName;

    /**
     * 是否使用场站默认桩配置 1是0否
     */
    private Integer isUseSiteDefaultSetting;

    /**
     * 桩的额定功率
     */
    private Integer ratedPower;

    /**
     * 配置发送状态: 1下发成功2失败3下发中
     */
    private Integer sendStatus;

    /**
     * 是否支持服务费分时
     */
    private Boolean servFeeTimeDivision;
}
