package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyRwDs;
import com.cdz360.biz.model.sys.constant.SiteGroupOwnType;
import com.cdz360.biz.model.sys.param.ListSiteGroupParam;
import com.cdz360.biz.model.sys.vo.UserGroupVo;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.cdz360.biz.utils.feign.auth.AuthSiteGroupFeignClient;
import com.chargerlinkcar.framework.common.service.RedisNoGen;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjSurveyService {

    @Autowired
    private RedisNoGen redisNoGen;

    @Autowired
    private TjSurveyRoDs tjSurveyRoDs;

    @Autowired
    private TjSurveyRwDs tjSurveyRwDs;

    @Autowired
    private AuthSiteGroupFeignClient authSiteGroupFeignClient;


    public Mono<List<UserGroupVo>> getUserList(List<String> gidList, String userNameLike) {
        if (CollectionUtils.isNotEmpty(gidList) || StringUtils.isNotEmpty(
            userNameLike)) {
            ListSiteGroupParam search = new ListSiteGroupParam();
            search.setNameLike("投建")
                .setOwnTypeList(List.of(SiteGroupOwnType.ZY))
                .setGidList(gidList)
                .setUserNameLike(userNameLike);
            return authSiteGroupFeignClient.findUserGroup(search)
                .doOnNext(FeignResponseValidate::check).map(ListResponse::getData);
        } else {
            return Mono.just(new ArrayList<UserGroupVo>());
        }
    }

    public Mono<ListResponse<TjSurveyPo>> findTjSurvey(ListTjSurveyParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return this.getUserList(param.getGidList(), param.getUserNameLike())
            .flatMap(userList -> {
                if (CollectionUtils.isNotEmpty(userList)) {
                    param.setUidList(
                        userList.stream().map(UserGroupVo::getId).distinct().collect(Collectors.toList()));
                }
                // 存在人员查询条件
                if (StringUtils.isNotEmpty(param.getUserNameLike()) && CollectionUtils.isEmpty(userList)) {
                    return Mono.just(new ArrayList<TjSurveyPo>());
                }
                List<TjSurveyPo> list = tjSurveyRoDs.findTjSurvey(param);
                // 获取用户ID
                if (CollectionUtils.isNotEmpty(list)) {
                    if (CollectionUtils.isNotEmpty(userList)) {
                        Map<Long, String> userMap = userList.stream()
                            .collect(Collectors.toMap(UserGroupVo::getId, UserGroupVo::getName));
                        list.forEach(e -> e.setUsername(userMap.get(e.getUid())));
                        return Mono.just(list);
                    } else {
                        ListSiteGroupParam search = new ListSiteGroupParam();
                        List<Long> uidList = list.stream().map(TjSurveyPo::getUid).distinct().collect(
                            Collectors.toList());

//                        search.setNameLike("投建")
//                            .setOwnTypeList(List.of(SiteGroupOwnType.ZY))
//                            .setUidList(uidList);
                        search.setUidList(uidList);
                        return authSiteGroupFeignClient.findUserGroup(search)
                            .doOnNext(FeignResponseValidate::check).map(ListResponse::getData)
                            .flatMap(x1 -> {
                                Map<Long, String> userMap1 = x1.stream()
                                    .collect(
                                        Collectors.toMap(UserGroupVo::getId, UserGroupVo::getName));
                                list.forEach(e -> e.setUsername(userMap1.get(e.getUid())));
                                return Mono.just(list);
                            });
                    }
                } else {
                    return Mono.just(list);
                }
            }).map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    ((StringUtils.isNotEmpty(param.getUserNameLike()) && CollectionUtils.isEmpty(
                        param.getUidList())) ? 0L : tjSurveyRoDs.countTjSurvey(param)) : 0L));
    }

    public Mono<TjSurveyBiVo> tjSurveyBi(TjSurveyBiParam param) {
        return Mono.just(tjSurveyRoDs.tjSurveyBi(param));
    }

    public Mono<List<TjSurveyVo>> repeatSurvey(RepeatSurveyParam param) {
        return Mono.just(tjSurveyRoDs.repeatSurvey(param));
    }

    public Mono<TjSurveyVo> saveTjSurvey(TjSurveyVo survey) {
        val dto = new TjSurveyPo();
        BeanUtils.copyProperties(survey, dto);
        if (StringUtils.isBlank(dto.getNo())) {
            dto.setNo(redisNoGen.tjSurveyNo());
            tjSurveyRwDs.insertTjSurvey(dto);
        } else {
            tjSurveyRwDs.updateTjSurvey(dto);
        }
        return Mono.just(survey.setNo(dto.getNo()));
    }

}
