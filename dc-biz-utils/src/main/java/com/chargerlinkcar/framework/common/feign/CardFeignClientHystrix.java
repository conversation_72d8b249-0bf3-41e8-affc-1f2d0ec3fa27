package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CardFeignClientHystrix implements FallbackFactory<CardFeignClient> {
    @Override
    public CardFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER, throwable.getStackTrace());

        return new CardFeignClient() {
            /**
             * 有固定条件card_status =10001
             * @param cardNo
             * @return
             */
            @Override
            public ObjectResponse<Card> getCardByCardNo(String cardNo, Long topCommId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            /**
             * 无固定条件
             * @param cardNo
             * @return
             */
            @Override
            public ObjectResponse<Card> queryCardByCardNo(String cardNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }



            /**
             * 批量更新卡
             * by card_no
             * @param cardList 卡集合
             * @return
             */
            @Override
            public BaseResponse updateBatchCard(List<Card> cardList) {
                return RestUtils.serverBusy();
            }

            /**
             * 根据条件批量查询场站下的卡数量
             * @param siteIdList
             * @return
             */
            @Override
            public ListResponse<SiteCardCount> findUrgencyCardNumCount(List<String> siteIdList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getStationsOfCard(String cardNo, Integer type,String idChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<String> getEmergencyCardBySiteId(String siteId, Long commId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<MoveCorpCardList> getEmergencyCardByCorpId(Long corpId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpCardAuthByCorpId(Long corpId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse addEssCard(AddEssCardParam param) {
                return RestUtils.serverBusy();
            }

//            @Override
//            public ObjectResponse<Boolean> syncAmount(String cardNo, BigDecimal amount, Integer result) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public ListResponse<CardAmountSyncVo> getSyncCardAmount(CardAmountSyncParam param) {
//                return RestUtils.serverBusy4ListResponse();
//            }
        };
    }
}
