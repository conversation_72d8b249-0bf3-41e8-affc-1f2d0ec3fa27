package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.model.es.type.hi.EssDtuCommunicationWay;
import com.cdz360.base.model.es.type.hi.EssDtuType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import javax.validation.constraints.NotBlank;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备绑定参数")
@Data
@Accessors(chain = true)
public class BindDeviceDto {

    @Schema(description = "用户ID", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Schema(description = "国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"), hidden = true)
    private String countryCode;

    @Schema(description = "设备所在时区", example = "GMT+08:00/UTC+08:00")
    private String timeZone;

    @NotBlank
    @Schema(description = "序列号", example = "E47F23500003", requiredMode = RequiredMode.REQUIRED)
    private String serialNo;

    //    @NotNull
    @Schema(description = "设备类型")
    private EssDtuType essDtuType;

    //    @NotNull
    @Schema(description = "通讯方式")
    private EssDtuCommunicationWay communicationWay;

    //    @NotBlank
    @Schema(description = "设备名称")
    private String deviceName;

    //    @NotBlank
    @Schema(description = "设备型号")
    private String deviceModel;

    //    @NotBlank
    @Schema(description = "硬件版本")
    private String hardwareVer;

    //    @NotBlank
    @Schema(description = "软件版本")
    private String softwareVer;

    //    @NotBlank
    @Schema(description = "ICCID")
    private String iccid;

//    @NotBlank
//    @Schema(description = "设备编号", required = true)
//    @JsonInclude(Include.NON_EMPTY)
//    private String dno;
//
//    // basic setting
//    @NotNull
//    @Schema(description = "设备组网类型")
//    @JsonInclude(Include.NON_NULL)
//    private Integer plantType;
//
//    @NotBlank
//    @Schema(description = "平台授权码")
//    @JsonInclude(Include.NON_EMPTY)
//    private String authCode;
//
//    @Schema(description = "入网价格(后续逻辑没有想好)")
//    @JsonInclude(Include.NON_NULL)
//    private BigDecimal gridPrice;
}
