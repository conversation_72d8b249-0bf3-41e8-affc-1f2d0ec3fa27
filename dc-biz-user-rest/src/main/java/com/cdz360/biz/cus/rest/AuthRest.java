package com.cdz360.biz.cus.rest;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.LogHelper;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.cus.service.IAuthService;
import com.cdz360.biz.model.cus.auth.dto.CusAuthReqEx;
import com.cdz360.biz.model.cus.auth.dto.CusAuthRes;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Tag(name = "鉴权接口", description = "鉴权")
public class AuthRest {


    @Autowired
    private IAuthService authService;


    @PostMapping(value = "/api/cus/auth")
    public Mono<ObjectResponse<CusAuthRes>> auth(
        @Parameter(name = "密码") @RequestParam(name = "passcode", required = false) String passcode,
        @Parameter(name = "在线鉴权请求") @RequestBody CusAuthReqEx cusAuthReq) {

        long startTime = System.nanoTime();    // debug 性能问题
        log.info("在线卡或者vin码鉴权。 cusAuthReq = {}", JsonUtils.toJsonString(cusAuthReq));
        return authService.authentication(cusAuthReq, passcode)
            .map(RestUtils::buildObjectResponse)
            .doOnNext(res -> {
                LogHelper.logLatency(log, AuthRest.class.getSimpleName(),
                    "auth", "卡/VIN鉴权", startTime);
            });
    }

    /**
     * 从redis中获取鉴权结果
     *
     */
    @ResponseBody
    @PostMapping("/api/cus/getAuthResultAndDel")
    public ObjectResponse<String> getAuthResultAndDel(@Parameter(name = "获取鉴权结果") @RequestBody CusAuthReqEx cusAuthReq) {
//        long startTime = System.nanoTime();    // debug 性能问题
        log.info("鉴权结果请求。 cusAuthReq = {}", JsonUtils.toJsonString(cusAuthReq));
        String authResult = authService.getAndDelAuthResult(cusAuthReq);
        return new ObjectResponse<>(authResult);

    }

}
