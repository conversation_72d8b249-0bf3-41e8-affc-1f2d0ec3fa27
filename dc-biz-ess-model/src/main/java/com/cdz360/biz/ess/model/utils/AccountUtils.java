package com.cdz360.biz.ess.model.utils;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AccountUtils {

    private static final String emailReg = "";
    private static final String phoneReg = "";

    public static boolean email() {
        return true;
    }

    public static boolean phone() {
        return true;
    }

    public static String hashPassw(Long topCommId, String account, String passw) {
        final String tmp = topCommId + ":" + account + ":" + passw;
        log.info("hash password: {}", tmp);
        try {
            final MessageDigest instance = MessageDigest.getInstance("SHA-256");
            instance.update(tmp.getBytes(StandardCharsets.UTF_8));
            final byte[] digest = instance.digest();
            final StringBuilder builder = new StringBuilder();
            for (byte b : digest) {
                builder.append(String.format("%02x", b));
            }
            return builder.toString();
        } catch (Exception ex) {
            // nothing to do
            log.warn("密码加密异常: {}", ex.getMessage(), ex);
        }
        return tmp;
    }
}
