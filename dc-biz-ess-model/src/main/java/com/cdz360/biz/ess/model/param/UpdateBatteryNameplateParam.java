package com.cdz360.biz.ess.model.param;

import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.ess.vo.BatteryInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "更新电池铭牌信息")
@Data
@Accessors(chain = true)
public class UpdateBatteryNameplateParam {

    @Schema(description = "ESS设备编号 平台分配唯一", requiredMode = RequiredMode.REQUIRED)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "电池新的铭牌信息")
    private List<BatteryInfo> batteryInfoList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
