package com.cdz360.biz.device.business.entity.result;

import lombok.Data;

import java.io.Serializable;

/**
 * @author: KT
 * @description: 下发计费模板配置实体类
 * @date: 2019-05-29 11:11
 */
@Data
public class TemplateSendConfig implements Serializable {

    /**
     * id,唯一标识
     */
    private Long id;

    /**
     * 第一阶段下发最大次数
     */
    private Integer firstMaxFailNum;

    /**
     * 第一阶段下发时间间隔
     */
    private Integer firstInterval;

    /**
     * 第二阶段下发最大次数
     */
    private Integer secondMaxFailNum;

    /**
     * 第二阶段下发时间间隔
     */
    private Integer secondInterval;
}
