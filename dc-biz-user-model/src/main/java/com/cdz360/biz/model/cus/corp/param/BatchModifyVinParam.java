package com.cdz360.biz.model.cus.corp.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "批量修改卡/VIN绑定的场站")
public class BatchModifyVinParam {

    private Long topCommId;

    private Long commId;

    private Long corpId;

    private Long opUid;

    /**
     * @link com.cdz360.model.cus.site.type.AuthMediaType
     */
    @Schema(description = "鉴权介质类型. 1, 卡; 2, VIN")
    private Integer type;

    @Schema(description = "卡/VIN")
    private List<String> accountList;

    @Schema(description = "物理卡号 用于记录日志")
    private List<String> cardChipNoList;

    @Schema(description = "场站ID列表")
    private List<String> siteIdList;
}
