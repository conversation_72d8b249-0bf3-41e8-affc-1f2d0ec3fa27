package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.device.business.entity.po.SiteVo;
import com.cdz360.biz.device.business.entity.po.WhiteCardDto;
import com.cdz360.biz.device.business.entity.po.WhiteCardRequest;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Classname HystrixUserClientFactory
 * @Description TODO
 * @Date 2019/7/12
 * @Created by wangzheng
 */
@Slf4j
@Component
public class HystrixUserClientFactory implements FallbackFactory<UserFeignClient> {
    @Override
    public UserFeignClient create(Throwable cause) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, cause.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_DATA_CORE, cause.getStackTrace());

        return new UserFeignClient() {
            @Override
            public ListResponse<WhiteCardDto> queryWhiteCardDtoBySiteList(WhiteCardRequest whiteCardRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> updateUrgencyCardStatus(String evseNo, String cardIds, int status) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> updBatchCardStatusByNos(List<String> cardNos, int status) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ListResponse<SiteVo> findUrgencyCardNumCount(List<String> siteIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }
        };
    }
}