package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjCompetitorSiteRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjCompetitorRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjCompetitorSiteRwDs;
import com.cdz360.biz.model.gaode.GaoDeSearchParam;
import com.cdz360.biz.model.gaode.PlaceGaoDe;
import com.cdz360.biz.model.geo.po.CityPo;
import com.cdz360.biz.model.geo.po.DistrictPo;
import com.cdz360.biz.model.tj.kc.dto.TjCompetitorSiteDto;
import com.cdz360.biz.model.tj.kc.param.ListTjCompetitorSiteParam;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import com.cdz360.biz.model.tj.kc.po.TjCompetitorSitePo;
import com.cdz360.biz.model.tj.kc.vo.TjCompetitorSiteVo;
import com.cdz360.biz.tj.utils.RedisUtil;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.time.Duration;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjCompetitorSiteService {

    private static final int SYNC_PET_SITE_REDIS_KEY = 24 * 30; // 30 天

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private TjCompetitorRwDs tjCompetitorRwDs;

    @Autowired
    private TjCompetitorSiteRwDs tjCompetitorSiteRwDs;

    @Autowired
    private TjCompetitorSiteRoDs tjCompetitorSiteRo;

//    @Autowired
//    private RedisIotReadService redisIotReadService;

    public int fetchSiteByCompetitorDistrict(
        List<DistrictPo> districtList, TjCompetitorPo competitor) {
        GaoDeSearchParam searchParam = new GaoDeSearchParam()
            .setKeywords(competitor.getName())
            .setPageSize(25)
            .setPageNum(1);

        boolean hasSite = false;
        for (DistrictPo ad : districtList) {
            searchParam.setRegion(ad.getCode()); // 仅支持
            while (searchParam.getPageNum() < 10000) {
                List<PlaceGaoDe> list = null;
                try {
                    list = openHlhtFeignClient.searchChargingStation(searchParam)
                        .map(FeignResponseValidate::checkReturn)
                        .block(Duration.ofSeconds(50L));
                } catch (Exception e) {
                    log.info("同步竞争者场站列表: {}", searchParam.getPageNum());
//                    throw new DcServiceException("今日免费查询次数已用光");
                    return 1;
                }

                if (CollectionUtils.isNotEmpty(list)) {
                    hasSite = true;
                    List<TjCompetitorSitePo> poList = list.stream()
                        .map(p -> this.gaodeSite2CompetitorSite(competitor.getId(), p))
                        .collect(Collectors.toList());
                    int i = tjCompetitorSiteRwDs.batchUpset(poList);
                }

                if (list.size() < searchParam.getPageSize()) {
                    break;
                }

                searchParam.setPageNum(searchParam.getPageNum() + 1);
            }
        }

        if (!hasSite) {
//            throw new DcArgumentException("未查询到相关竞争对手场站信息。");
            return 2;
        }

        return 0;
    }

    private TjCompetitorSitePo gaodeSite2CompetitorSite(Long competitorId, PlaceGaoDe p) {
        return new TjCompetitorSitePo()
            .setCompetitorId(competitorId)
            .setGdId(p.getId())
            .setName(p.getName())
            .setPCode(p.getPCode())
            .setPName(p.getPName())
            .setCityCode(p.getCityCode())
            .setCityName(p.getCityName())
            .setAdCode(p.getAdCode())
            .setAdName(p.getAdName())
            .setAddress(p.getAddress())
            .setLocation(p.getLocation());
    }

    @Async
    public void fetchSiteByCompetitor(List<CityPo> cityList, TjCompetitorPo competitor) {
        GaoDeSearchParam searchParam = new GaoDeSearchParam()
            .setKeywords(competitor.getName())
            .setPageSize(25)
            .setPageNum(1);

        if (CollectionUtils.isNotEmpty(cityList)) {
            cityList.forEach(city -> {
                searchParam.setPageNum(1); // 重新开始查询

                // 7天内是否已经完整获取过
                // key: gaode:city:competitorId value:-1--表示已经添加完成;>=0--表示加载一部分，可以继续加载
                String redisKey = RedisUtil.competitorSiteKey(city.getCode(), competitor.getId());
                String tmp = redisUtil.get(redisKey);
                if (StringUtils.isNotBlank(tmp)) {
                    int pageNum = Integer.parseInt(tmp);
                    if (pageNum < 0) {
                        return;
                    } else {
                        searchParam.setPageNum(pageNum);
                    }
                }

                searchParam.setRegion(city.getFullname());

                while (searchParam.getPageNum() < 10000) {
                    List<PlaceGaoDe> list = null;
                    try {
                        list = openHlhtFeignClient.searchChargingStation(searchParam)
                            .map(FeignResponseValidate::checkReturn)
                            .block(Duration.ofSeconds(50L));
                    } catch (Exception e) {
                        log.info("同步竞争者场站列表: {}, {}", redisKey, searchParam.getPageNum());
                        redisUtil.set(redisKey,
                            searchParam.getPageNum() + "", SYNC_PET_SITE_REDIS_KEY);
                        throw e;
                    }

                    if (CollectionUtils.isNotEmpty(list)) {
                        List<TjCompetitorSitePo> poList = list.stream()
                            .map(p -> this.gaodeSite2CompetitorSite(competitor.getId(), p))
                            .collect(Collectors.toList());
                        int i = tjCompetitorSiteRwDs.batchUpset(poList);
                    }

                    if (list.size() < searchParam.getPageSize()) {
                        break;
                    }

                    searchParam.setPageNum(searchParam.getPageNum() + 1);
                }

                redisUtil.set(redisKey, -1 + "", SYNC_PET_SITE_REDIS_KEY);
            });

            if (null == competitor.getFirstPullFinishTime()) {
                tjCompetitorRwDs.updateTjCompetitor(competitor.setFirstPullFinishTime(new Date()));
            }
        }
    }

    public void batchFetchSiteByCompetitor(
        List<CityPo> cityList, List<TjCompetitorPo> competitors) {
        if (null == competitors) {
            return;
        }

        competitors.forEach(competitor ->
            this.fetchSiteByCompetitor(cityList, competitor));
    }

    public Mono<ListResponse<TjCompetitorSiteVo>> findTjCompetitorSite(
        ListTjCompetitorSiteParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjCompetitorSiteRo::findTjCompetitorSite)
//            .map(list -> list.stream().map(x -> {
//                    TjCompetitorSiteVo result = new TjCompetitorSiteVo();
//                    BeanUtils.copyProperties(x, result);
//                    return result;
//                })
//                .collect(Collectors.toList()))
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjCompetitorSiteRo.countTjCompetitorSite(param) : 0L));
    }

    public Mono<TjCompetitorSiteVo> saveTjCompetitorSiteAttachInfo(
        TjCompetitorSiteDto dto) {
        IotAssert.isNotNull(dto.getId(), "场站ID不能为空");
        TjCompetitorSitePo site = tjCompetitorSiteRo.getById(dto.getId());
        IotAssert.isNotNull(site, "场站ID无效");

        Date date = new Date();
        boolean b = tjCompetitorSiteRwDs.updateTjCompetitorSite(
            new TjCompetitorSitePo()
                .setId(dto.getId())
                .setDcEvseNum(dto.getDcEvseNum())
                .setAcEvseNum(dto.getAcEvseNum())
                .setDcPlugNum(dto.getDcPlugNum())
                .setAcPlugNum(dto.getAcPlugNum())
                .setPower(dto.getPower())
                .setMinElecFee(dto.getMinElecFee())
                .setMaxElecFee(dto.getMaxElecFee())
                .setMinServFee(dto.getMinServFee())
                .setMaxServFee(dto.getMaxServFee())
                .setAttachUpdateTime(date)
        );
        if (!b) {
            log.warn("更新场站附加信息失败: {}", dto);
        }

        return Mono.just(new TjCompetitorSiteVo()
            .setId(dto.getId()).setAttachUpdateTime(date));
    }
}
