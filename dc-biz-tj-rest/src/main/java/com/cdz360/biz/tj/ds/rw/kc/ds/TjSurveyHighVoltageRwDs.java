package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyHighVoltageRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationIncomeRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyHighVoltageRwDs {


    @Autowired

    private TjSurveyHighVoltageRwMapper tjSurveyHighVoltageRwMapper;


    public TjSurveyHighVoltagePo getById(Long id, boolean lock) {

        return this.tjSurveyHighVoltageRwMapper.getById(id, lock);

    }


    public boolean insertTjSurveyHighVoltage(TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        return this.tjSurveyHighVoltageRwMapper.insertTjSurveyHighVoltage(tjSurveyHighVoltagePo) > 0;
    }


    public boolean updateTjSurveyHighVoltage(TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        return this.tjSurveyHighVoltageRwMapper.updateTjSurveyHighVoltage(tjSurveyHighVoltagePo) > 0;
    }

    public boolean disableTjSurveyHighVoltageBySurveyNo(String surveyNo) {
        return this.tjSurveyHighVoltageRwMapper.disableTjSurveyHighVoltageBySurveyNo(surveyNo) > 0;
    }

}

