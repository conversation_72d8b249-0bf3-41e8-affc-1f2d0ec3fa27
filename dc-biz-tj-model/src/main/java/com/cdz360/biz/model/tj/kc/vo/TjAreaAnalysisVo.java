package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建分析区域")
public class TjAreaAnalysisVo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "投建区域ID(t_tj_area.id)")
    @NotNull(message = "aid 不能为 null")
    private Long aid;

    @Schema(description = "竞争对手名称")
    @NotNull(message = "name 不能为 null")
    @Size(max = 128, message = "name 长度不能超过 128")
    private String name;

    @Schema(description = "圆半径，shape为圆赋值，其他情况为0(单位: 米)")
    private Integer radius;

    @Schema(description = "中心点经纬度(地图中心点)")
    @NotNull(message = "location 不能为 null")
    private String location;

    @Schema(description = "静态地图")
    private byte[] staticmap;

}

