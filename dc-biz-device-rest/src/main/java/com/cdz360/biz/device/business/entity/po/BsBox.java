package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.biz.device.business.constant.ChargingWayEnum;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 设备管理：盒子
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
@Data
@TableName("bs_box")
public class BsBox {

    /**
     * 设备ID
     */
    @TableId("box_code")
    private String boxCode;
    /**
     * 设备序列号/桩号
     */
    @TableField("box_out_factory_code")
    private String boxOutFactoryCode;
    /**
     * 设备类型
     */
    @TableField("box_charger_type")
    private Integer boxChargerType;
    /**
     * 设备名称
     */
    @TableField("box_name")
    private String boxName;
    /**
     * 代理商ID
     */
    @TableField("business_id")
    private String businessId;
    /**
     * 站点编号
     */
    @TableField("station_code")
    private String stationCode;
    /**
     * 设备状态**0-离线 100-在线**
     */
    @TableField("status")
    private Integer status;
    /**
     * 有效枪头数量
     */
    @TableField("charger_num")
    private Integer chargerNum;
    /**
     * 交直流类型**0-交流 1-直流 2-交直流**
     */
    @TableField("current_type")
    private Integer currentType;
    /**
     * 【新增】所属产品系列名称
     */
    @TableField("product_name")
    private String productName;
    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 最后修改时间
     */
    @TableField("last_modify_time")
    private Date lastModifyTime;

    /**
     * 是否关联场站默认计费(1：是 0：否)
     */
    @TableField("is_associate_site_template")
    private Integer isAssociateSiteTemplate;

    /**
     * 计费模板Id
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 计费模板名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 收费方式{@link ChargingWayEnum}
     */
    @TableField("charging_way")
    private Integer chargingWay;

    /**
     * 是否使用场站默认桩配置 1是0否
     */
    @TableField("isUseSiteDefaultSetting")
    private Integer isUseSiteDefaultSetting;

    /**
     * 桩的额定功率
     */
    @TableField("rated_power")
    private Integer ratedPower;
}
