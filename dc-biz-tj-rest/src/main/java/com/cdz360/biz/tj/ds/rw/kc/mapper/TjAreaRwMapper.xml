<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaRwMapper">

  <resultMap id="RESULT_TJ_AREA_PO" type="com.cdz360.biz.model.tj.kc.po.TjAreaPo">
    <id column="aid" jdbcType="BIGINT" property="aid"/>
    <result column="gid" jdbcType="VARCHAR" property="gid"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="color" jdbcType="VARCHAR" property="color"/>
    <result column="shape" jdbcType="INTEGER" property="shape"/>
    <result column="radius" jdbcType="BIGINT" property="radius"/>
    <result column="geoText" property="paths"
      typeHandler="com.cdz360.biz.ds.trading.PolygonTypeHandler"/>
    <result column="adcode" jdbcType="VARCHAR" property="adcode"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getByAid"
    resultMap="RESULT_TJ_AREA_PO">
    select *, ST_AsText(`geo`) geoText from t_tj_area where aid = #{aid}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjArea" useGeneratedKeys="true" keyProperty="aid"
    keyColumn="aid" parameterType="com.cdz360.biz.model.tj.kc.po.TjAreaPo">
    insert into t_tj_area (`gid`,`uid`,
    `name`,
    `color`,
    `shape`,
    <if test="null != radius">
      `radius`,
    </if>
    `geo`,
    `adcode`,
    `createTime`,
    `updateTime`)
    values (#{gid},#{uid},
    #{name},
    #{color},
    #{shape},
    <if test="null != radius">
      #{radius},
    </if>
    ST_GeomFromText(#{paths, jdbcType=JAVA_OBJECT, typeHandler=com.cdz360.biz.ds.trading.PolygonTypeHandler}),
    #{adcode},
    now(),
    now())
  </insert>

  <update id="updateTjArea" parameterType="com.cdz360.biz.model.tj.kc.po.TjAreaPo">
    update t_tj_area set
    <if test="gid != null">
      gid = #{gid},
    </if>
    <if test="uid != null">
      uid = #{uid},
    </if>
    <if test="name != null">
      name = #{name},
    </if>
    <if test="color != null">
      color = #{color},
    </if>
    <if test="shape != null">
      shape = #{shape},
    </if>
    <if test="radius != null">
      radius = #{radius},
    </if>
    <if test="paths != null">
      geo =
      ST_GeomFromText(#{paths, jdbcType=JAVA_OBJECT, typeHandler=com.cdz360.biz.ds.trading.PolygonTypeHandler}),
    </if>
    <if test="adcode != null">
      adcode = #{adcode},
    </if>
    updateTime = now()
    where aid = #{aid}
  </update>
  <update id="disableTjArea">
    update t_tj_area set
    enable = false, updateTime = now()
    where aid = #{aid}
  </update>

</mapper>

