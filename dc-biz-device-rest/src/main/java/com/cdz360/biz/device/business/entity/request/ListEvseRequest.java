package com.cdz360.biz.device.business.entity.request;

import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.base.utils.JsonUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class ListEvseRequest {
    @ApiModelProperty(value = "桩名称或桩编号")
    private String keywords;
    @ApiModelProperty(value = "桩状态列表")
    private List<EvseStatus> evseStatusList;
    @ApiModelProperty(value = "桩类型")
    private SupplyType supplyType;
    @ApiModelProperty(value = "场站ID")
    private String siteId;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
