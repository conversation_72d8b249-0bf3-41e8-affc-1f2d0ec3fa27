package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站关联投建区域信息")
@Data
@Accessors(chain = true)
public class SiteWithinTjVo {

    @Schema(description = "场站ID")
    private String id;

    @Schema(description = "场站名称")
    private String name;

    @Schema(description = "位置经纬度")
    private String location;

    @Schema(description = "场站地址")
    private String address;

    @Schema(title = "交流桩数量")
    private Integer acEvseNum;

    @Schema(title = "直流桩数量")
    private Integer dcEvseNum;

    @Schema(title = "交流枪头数量")
    private Integer acPlugNum;

    @Schema(title = "直流枪头数量")
    private Integer dcPlugNum;

    @Schema(title = "交流总功率,单位kw")
    private Integer acPower;

    @Schema(title = "直流总功率,单位kw")
    private Integer dcPower;

}
