package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-运营收入")
public class TjSurveyOperationIncomePo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "建站补贴金额")
    private BigDecimal stationSubsidy;

    @ApiModelProperty(value = "建站补贴到账年份")
    private Integer stationSubsidyYear;

    @ApiModelProperty(value = "运营补贴单价（元kW·h）")
    private BigDecimal operationSubsidyPrice;

    @ApiModelProperty(value = "电费单价")
    private BigDecimal elecPrice;

    @ApiModelProperty(value = "第一年前三个月服务费单价")
    private BigDecimal firstYearThirdMonthServicePrice;

    @ApiModelProperty(value = "第一年后九个月服务费单价")
    private BigDecimal firstYearLastNinthMonthServicePrice;

    @ApiModelProperty(value = "服务费每年增长率")
    private BigDecimal serviceIncrease;

    @ApiModelProperty(value = "最大服务费增长额")
    private BigDecimal serviceIncreaseMaxPrice;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

