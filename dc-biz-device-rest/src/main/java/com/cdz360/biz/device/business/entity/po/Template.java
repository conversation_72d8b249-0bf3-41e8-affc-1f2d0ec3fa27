package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 * 计费模板**主模板**
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Data
@TableName("t_template")
public class Template {

    private static final long serialVersionUID = 1L;

    /**
     * 计费模板ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 计费模板编号**唯一标识**
     */
    private String code;
    /**
     * 版本号
     */
    private Integer version;
    /**
     * 模板名称
     */
    private String name;
    /**
     * 代理商ID
     */
    @TableField("commercial_id")
    private Long commercialId;
    /**
     * 充电计费单位**1-按充电时长计费 2-按充电度数计费**
     */
    @TableField("calculate_unit")
    private Integer calculateUnit;
    /**
     * 充电计费方式**10-所有功率统一计费 11-按不同功率段分别计费 20-所有时段统一计费 21-按不同时段分别计费**
     */
    @TableField("calculate_type")
    private Integer calculateType;
    /**
     * 充电收费方式**0-固定收费 1-实时收费**
     */
    @TableField("charging_type")
    private Integer chargingType;
    /**
     * 充电费备注
     */
    @TableField("remark_charge")
    private String remarkCharge;
    /**
     * 服务费备注
     */
    @TableField("remark_service")
    private String remarkService;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 免费充电标识**0-收费 1-免费**
     */
    @TableField("free_charge_flag")
    private Integer freeChargeFlag;

    /**
     * 删除标识**0-正常 1-已删除**
     */
    @TableField("delete_flag")
    private Integer deleteFlag;

}
