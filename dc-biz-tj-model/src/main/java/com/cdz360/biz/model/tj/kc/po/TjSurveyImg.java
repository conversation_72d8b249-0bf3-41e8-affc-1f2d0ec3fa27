package com.cdz360.biz.model.tj.kc.po;

import com.cdz360.biz.model.tj.kc.type.TjSurveyImgFlag;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "勘察场站图片信息")
@Data
@Accessors(chain = true)
public class TjSurveyImg {

    @Schema(description = "场站位置标识")
    @JsonInclude(Include.NON_NULL)
    private TjSurveyImgFlag flag;

    @Schema(description = "场站位置图片")
    @JsonInclude(Include.NON_NULL)
    private List<String> images;
}
