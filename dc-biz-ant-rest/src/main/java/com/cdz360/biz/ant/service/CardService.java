package com.cdz360.biz.ant.service;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ant.constant.BindType;
import com.cdz360.biz.ant.constant.CardStatus;
import com.cdz360.biz.ant.constant.Constant;
import com.cdz360.biz.ant.domain.ListResponseEvseList;
import com.cdz360.biz.ant.domain.User;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.feign.AntUserFeignClient;
import com.cdz360.biz.ant.feign.BizBiFeignClient;
import com.cdz360.biz.ant.feign.SiteFeignClient;
import com.cdz360.biz.ant.feign.TradingFeignClient;
import com.cdz360.biz.ant.utils.ExcelBatchUtil;
import com.cdz360.biz.ant.utils.RedisUtil;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.utils.feign.user.UserFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.feign.CardFeignClient;
import com.chargerlinkcar.framework.common.feign.CommercialFeignClient;
import com.chargerlinkcar.framework.common.feign.UserCommercialFeignClient;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.pagehelper.Page;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.codec.multipart.FilePart;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

/**
 * 卡片功能
 * <p>
 * CardServiceImpl
 *
 * <AUTHOR> 卡片功能
 * @since 2018.11.22
 */
@Slf4j
@Service
public class CardService //
// implements ICardService
{

    public final static String CODE = "0";
    private static final String BLANK_CARD_KEY = "123456";
    private static final String CARD_MAKER_MARK = "CARD_MAKER_MARK";
    // 逻辑卡号最大获取个数
    private static final int MAX_LOGICAL_CARD_NUM_COUNT = 10;
    private static final String KEY_REDIS_CARD_NO_IDX = "sys:card:idx";
    private static final String CARD_MARKER_PREFIX = "300";
    //    /**
//     * 商户服务
//     */
//    @Autowired
//    private MerchantService merchantService;
    @Autowired
    private UserService userService;
    @Autowired
    private CorpService corpService;
    @Autowired
    private UserCommercialFeignClient userCommercialFeignClient;
    @Autowired
    private CommercialFeignClient commercialFeignClient;
    /**
     * 卡片
     */
    @Autowired
    private AntUserFeignClient userFeignClient;
    @Autowired
    private CardFeignClient cardFeignClient;
    @Autowired
    private SiteFeignClient siteFeignClient;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserFeignClient userMonoFeignClient;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    public ObjectResponse<Integer> bindLogicalAndPhysics(List<Card> list) {

        IotAssert.isTrue(CollectionUtils.isNotEmpty(list), "请传入卡片列表");

        list.stream().forEach(e -> {
            if (org.apache.commons.lang3.StringUtils.isBlank(e.getCardKey())) {
                e.setCardKey(BLANK_CARD_KEY);
            }
            e.setRemark(CARD_MAKER_MARK);
        });

        ObjectResponse<Integer> result = userFeignClient.batchAddCard(list);
        return result;
    }

    public ListResponse<String> getLogicalCardNum(String cardNo, Integer count) {

        ObjectResponse<Card> chipCardNo = userFeignClient.queryCardByCardChipNo(cardNo);
        if (chipCardNo.getData() != null) {
            throw new DcArgumentException("卡片已制作完成: " +
                chipCardNo.getData().getCardChipNo() + ", " + chipCardNo.getData().getCardNo());
        }

        if (count > MAX_LOGICAL_CARD_NUM_COUNT) {
            count = MAX_LOGICAL_CARD_NUM_COUNT;
        }
        log.info("获取可用的逻辑卡号:，获取个数: {}", count);

        List<String> ret = new ArrayList<>();
        while (count > 0) {
            ret.add(this.getNextLogicalCardNum());
            count--;
        }

        return new ListResponse<>(ret);

//        Random random = new Random();
//        int i = random.nextInt(10) % 10;
//        return new ListResponse<>(List.of("428030100" + i));
    }

    private String getNextLogicalCardNum() {

        Calendar currDate = Calendar.getInstance();
        currDate.setTime(new Date());
        int yyyy = currDate.get(Calendar.YEAR);

        Long val = stringRedisTemplate.opsForValue().increment(KEY_REDIS_CARD_NO_IDX, 1L);
        String ret = String.format("%s%s%05d", CARD_MARKER_PREFIX, yyyy % 100, val % 100000);
        return ret;

    }

    /**
     * 根据条件查询卡片列表
     *
     * @param token
     * @param page  分页
     * @return
     */

    public ListResponse<CardListdetailVO> queryCardsByPage(String token,
        OldPageParam page, CardSearchParam cardSearchParam) {

        // 将leaderCommId转换成idChain
        if (cardSearchParam.getLeaderCommId() != null) {
            ObjectResponse<Commercial> commercial = commercialFeignClient.getCommercial(
                cardSearchParam.getLeaderCommId());
            FeignResponseValidate.check(commercial);
            cardSearchParam.setCommIdChain(commercial.getData().getIdChain());
            log.debug("online card leaderCommId -> idChain: {} -> {}",
                cardSearchParam.getLeaderCommId(), commercial.getData().getIdChain());
        }

        log.info("根据条件查询卡片列表查询条件是token{}--page{}--cardSearchParam{}",
            token, JsonUtils.toJsonString(page), JsonUtils.toJsonString(cardSearchParam));
        //调用服务
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryCardsByPage(token,
            page.getPageNum(), page.getPageSize(), cardSearchParam);
        //log.info("根据条件查询卡片列表调用服务完成结果是{}", jsonResult);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
//        if (jsonResult == null) {
//            throw new DcServiceException("数据获取失败");
//        } else if (jsonResult.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException(jsonResult.toJSONString());
//        } else {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        }
    }

    public ObjectResponse<ExcelPosition> exportOnlineCardList(String token,
        Page<CardListdetailVO> page, CardSearchParam cardSearchParam) {
        //调用服务
        return bizBiFeignClient.exportOnlineCardList(token, cardSearchParam);
    }

    /**
     * 根据提交查询在线卡列表
     *
     * @param token
     * @param page
     * @param cardStatus
     * @param beginTime
     * @param endTime
     * @param cardNo
     * @param commId
     * @param userId
     * @return
     */
    public ListResponse<CardListdetailVO> queryOnlineCardsByPage(String token,
        OldPageParam page, String cardStatus, String beginTime,
        String endTime, String cardNo, Long cardType, Long commId, Long userId,
        Long topCommId, String commIdChain) {
        log.info(
            "根据条件查询卡片列表查询条件是token = {}--page = {}--cardStatus = {}--cardNo = {}--commId = {}--userId = {}"
            , token, JsonUtils.toJsonString(page), cardStatus, cardNo, commId, userId);
        //调用服务
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryOnlineCardsByPage(token,
            page.getPageNum(), page.getPageSize()
            , cardStatus, beginTime, endTime, cardNo, cardType, commId, userId, topCommId,
            commIdChain);
//        log.info("根据条件查询卡片列表调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
    }

    /**
     * 根据提交查询紧急充电卡列表 用于充电管理平台
     *
     * @param token
     * @param page
     * @return
     */
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPage(String token,
        OldPageParam page, CardRequest cardRequest) {

        log.info("根据条件查询卡片列表查询条件是token{}--page{}--cardRequest{}"
            , token, JsonUtils.toJsonString(page), JsonUtils.toJsonString(cardRequest));

        if (cardRequest.getLeaderCommId() != null) {
            ObjectResponse<Commercial> commRes = this.userCommercialFeignClient.getCommercialByCommId(
                cardRequest.getLeaderCommId());
            FeignResponseValidate.check(commRes);
            log.debug("offline card leaderCommId -> idChain: {} -> {}",
                cardRequest.getLeaderCommId(), commRes.getData().getIdChain());
            cardRequest.setCommIdChain(commRes.getData().getIdChain());
        } else if (cardRequest.getCommId() != null && cardRequest.getCommId().longValue() > 0L) {
            ObjectResponse<Commercial> commRes = this.userCommercialFeignClient.getCommercialByCommId(
                cardRequest.getCommId());
            FeignResponseValidate.check(commRes);
            cardRequest.setCommIdChain(commRes.getData().getIdChain());
            log.info("commIdChain变更为 {}", cardRequest.getCommIdChain());
        }

        //调用服务
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryUrgencyCardsByPage(token,
            page.getPageNum(), page.getPageSize()
            , cardRequest);
        log.info("根据条件查询卡片列表调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        FeignResponseValidate.check(jsonResult);
        return jsonResult;

    }

    /**
     * 根据提交查询紧急充电卡列表 用于运营支撑平台(无需考虑权限)
     *
     * @param token
     * @param page
     * @return
     */
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(String token,
        OldPageParam page, CardRequest cardRequest) {
        log.info("根据条件查询卡片列表查询条件是token{}--page{}--cardRequest{}"
            , token, JsonUtils.toJsonString(page), JsonUtils.toJsonString(cardRequest));
        //调用服务
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryUrgencyCardsByPageOnOperate(
            token, page.getPageNum(), page.getPageSize()
            , cardRequest);
        log.info("根据条件查询卡片列表调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        FeignResponseValidate.check(jsonResult);
        return jsonResult;

    }


    public ObjectResponse urgencyCardsDetail(String token, Long cardId) {
        //查询是否有商户

        return userFeignClient.urgencyCardsDetail(token, cardId);
    }

    /**
     * 根据离线卡cardId获取相关场站的信息
     *
     * @param token
     * @param cardId
     * @return
     */

    public ListResponseEvseList urgencyCardsDetailEvseList(String token, Long cardId, String evse,
        Integer page, Integer rows) {
        return userFeignClient.urgencyCardsDetailEvseList(token, cardId, evse, page, rows);
    }

    /**
     * 新增在线卡片（发卡给客户）
     *
     * @param carNo    车牌号
     * @param carNum   车辆自编号
     * @param lineNum  线路
     * @param cardNo
     * @param cardName
     * @param userId
     * @param commId
     * @param stations
     * @return
     */
    public BaseResponse grantCard(
        String carNo, String carNum, String lineNum, String cardNo, String cardName,
        Long userId, Long commId, String stations, String carDepart) {
        ObjectResponse<Card> resp = cardFeignClient.queryCardByCardNo(cardNo);
        FeignResponseValidate.check(resp);
        Card respCard = resp.getData();
        if (respCard.getCardType() != null && respCard.getCardStatus() != null) {
            if (respCard.getCardType().equals(CardType.ONLINE.getCode()) ||
                respCard.getCardType().equals(CardType.EMERGENCY.getCode()) ||
                respCard.getCardStatus().equals(CardStatus.ACTIVE.getCode())) {
                throw new DcServiceException("该卡已被激活");
            }
        }

        //车队名称验证
        if (carDepart != null && StringUtils.isNotEmpty(carDepart) && carDepart.length() > 20) {
            throw new DcServiceException("请检查车队名称是否有误");
        }
        Date currDate = new Date();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String currDate = dateFormat.format(date);
        Card card = new Card();
        card.setCarNo(carNo);
        card.setCarNum(carNum);
        card.setCarDepart(carDepart); //添加车队名称
        card.setLineNum(lineNum);
        card.setCardNo(cardNo);
        card.setCardName(cardName);
        card.setUserId(userId);
        card.setCommId(commId);
        card.setStations(stations);
//        card.setCardStatus("10001");//新增卡成功后, 卡的状态为'已激活
        card.setCardStatus(CardStatus.ACTIVE.getCode());//新增卡成功后, 卡的状态为'已激活
        card.setCardType(CardType.ONLINE.getCode());//新增卡成功后, 卡的类型为在线卡
        card.setCardActivationDate(currDate);//新增卡成功后, 当前时间为卡片激活时间
        card.setCardUpdateDate(currDate);//更新修改时间
        log.info("新增卡片（发卡给客户）Card：{}", card.toString());
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);

        log.info("新增卡片（发卡给客户）调用服务完成结果是{}", jsonResult);
        return jsonResult;

    }

    /**
     * 新增紧急充电卡片（发卡给客户）
     *
     * @return
     */
    @Transactional
    public BaseResponse grantUrgencyCard(CardRequest cardRequest, List<Long> commIdList)
        throws ParseException {
        log.info("cardRequest：{},commIdList：{}", JsonUtils.toJsonString(cardRequest),
            JsonUtils.toJsonString(commIdList));
        AssertUtil.notEmpty(cardRequest.getCardNo(), "卡号为空");
        AssertUtil.notNull(cardRequest.getBindType(), "绑定客户类为空");
        AssertUtil.notNull(cardRequest.getCommId(), "归属商户为空");
        AssertUtil.notEmpty(cardRequest.getStations(), "站点为空");
        if (cardRequest.getRemark() != null && cardRequest.getRemark().trim().length() > 30) {
            throw new DcServiceException("备注30 个字以内");
        }
        ObjectResponse<Card> resp = cardFeignClient.queryCardByCardNo(cardRequest.getCardNo());
        try {
            FeignResponseValidate.check(resp);
        } catch (DcException e) {
            log.warn("error: {}", e.getMessage(), e);
            throw new DcServiceException("该卡不存在");
        }
        Card respCard = resp.getData();
        if (respCard.getCardType() != null && respCard.getCardStatus() != null) {
            if (respCard.getCardType().equals(CardType.ONLINE.getCode()) ||
                respCard.getCardType().equals(CardType.EMERGENCY.getCode()) ||
                (!respCard.getCardStatus().equals(CardStatus.INACTIVE.getCode()))) {
                throw new DcServiceException("该卡已被激活");
            }
        }
        Date currDate = new Date();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String currDate = dateFormat.format(date);
        Card card = new Card();
        card.setCardNo(cardRequest.getCardNo());
        if (cardRequest.getCardName() != null && cardRequest.getCardName() != "") {
            card.setCardName(cardRequest.getCardName().trim());
        }
        if (cardRequest.getRemark() != null && cardRequest.getRemark() != "") {
            card.setRemark(cardRequest.getRemark().trim());
        }
        if (commIdList != null && commIdList.contains(cardRequest.getCommId())) {
            //确认此commId是否在范围(当前商户及其下级商户)内
            card.setCommId(cardRequest.getCommId());
        } else {
            throw new DcServiceException("归属商户不正确");
        }

        card.setStations(cardRequest.getStations());
//        Random random=new Random();
        card.setCardActivationCode(UUIDUtils.getRandom(true, 6));//6位随机密码
        card.setCardStatus(CardStatus.ACTIVE.getCode());//新增卡成功后, 卡的状态为'已激活
        card.setCardType(CardType.EMERGENCY.getCode());//新增卡成功后, 卡的类型为紧急卡
        card.setCardActivationDate(currDate);//新增卡成功后, 当前时间为卡片激活时间
        card.setCardUpdateDate(currDate);//更新修改时间
        if (cardRequest.getBindType()
            .equals(Integer.parseInt(BindType.PERSONAL_CUSTOMER.getCode()))) {
            throw new DcArgumentException("紧急卡不支持绑定到个人");

        } else if (cardRequest.getBindType()
            .equals(Integer.parseInt(BindType.COMMERCIAL_CUSTOMER.getCode()))) {
            // 商户会员
            AssertUtil.notEmpty(cardRequest.getMobile(), "手机号为空");
            try {
                ObjectResponse<CommCusRef> commCusRef = userFeignClient.findByCommIdAndPhone(
                    cardRequest.getCommId(), cardRequest.getMobile());
                FeignResponseValidate.check(commCusRef);

                CommCusRef vo = commCusRef.getData();
                card.setUserId(vo.getUserId());
                card.setMobile(cardRequest.getMobile());
            } catch (DcException e) {
                log.warn("紧急卡绑定商户会员异常：{}", e.getMessage(), e);
                throw new DcServiceException("手机号不存在");
            }
        } else if (cardRequest.getBindType()
            .equals(Integer.parseInt(BindType.GROUP_CUSTOMER.getCode()))) {
            //如果绑定为集团客户，需新建一个默认个人客户
            AssertUtil.notNull(cardRequest.getCorpId(), "集团客户为空");
            ObjectResponse<BlocUserDto> dtoObjectResponse = userFeignClient.findById(
                cardRequest.getCorpId());
            try {
                FeignResponseValidate.check(dtoObjectResponse);
            } catch (DcException e) {
                log.warn("error: {}", e.getMessage(), e);
                throw new DcServiceException("该集团客户不存在");
            }
            BlocUserDto blocUserDto = dtoObjectResponse.getData();
            //查询是否已存在集团客户默认个人客户(以"2"开头的手机号),没有则新增一个默认个人用户
            ListResponse<RBlocUser> rBlocUserListResponse = userFeignClient.selectRBlocUserByStartWithPhone(
                cardRequest.getCorpId(), "2");
            try {
                FeignResponseValidate.check(rBlocUserListResponse);
            } catch (DcException e) {
                throw new DcServiceException("查询集团客户的个人客户失败");
            }
            RBlocUser rBlocUserFromDB = null;
            if (rBlocUserListResponse.getData().size() > 0) {
                //已存在默认个人客户
                rBlocUserFromDB = rBlocUserListResponse.getData().get(0);
                card.setMobile(rBlocUserFromDB.getPhone());
            } else {
                //需新建默认个人客户
                RBlocUser rBlocUser = new RBlocUser();
                rBlocUser.setBlocUserId(blocUserDto.getId());
                rBlocUser.setCorpOrgId(blocUserDto.getL1OrgId());//默认在一级组织下
                //生成特殊的临时手机号
                StringBuilder tmpPhone = new StringBuilder("2");
                tmpPhone.append(UUIDUtils.getRandom(true, 10));
                rBlocUser.setPhone(tmpPhone.toString());
                rBlocUser.setName(blocUserDto.getBlocUserName());//集团客户名称
                rBlocUser.setCommId(blocUserDto.getCommId());//归属商户：同集团商户
                rBlocUser.setLimitCycle(LimitCycle.UNLIMITED);//因紧急卡新增的授信客户，限额周期为无限制
                //新增客户t_user记录和集团客户t_r_bloc_user记录,并进行授信关系的绑定
                BaseResponse baseResponse = userFeignClient.insertRBlocUser(rBlocUser);
                try {
                    FeignResponseValidate.check(baseResponse);
                } catch (DcException e) {
                    log.warn("error: {}", e.getMessage(), e);
                    throw new DcServiceException("新增默认个人客户失败");
                }
                //从数据库中用blocUserId和commId和phone找出新建的个人客户
                ListResponse<RBlocUser> rBlocUserListResponse2 = userFeignClient.findByCondition(
                    rBlocUser);
                try {
                    FeignResponseValidate.check(rBlocUserListResponse2);
                } catch (DcException e) {
                    log.warn("error: {}", e.getMessage(), e);
                    throw new DcServiceException("查询新增的授信关系失败");
                }
                AssertUtil.isTrue(rBlocUserListResponse2.getData().size() == 1,
                    "查询新建的授信关系失败");
                rBlocUserFromDB = rBlocUserListResponse2.getData().get(0);
                //将手机号改为[2+“UID” +“随机数补全11位”]
                StringBuilder newPhone = new StringBuilder("2");
                newPhone.append(rBlocUserFromDB.getUserId());
                int tmp = 11 - newPhone.length();
                newPhone.append(UUIDUtils.getRandom(true, tmp));
                User reqUser = new User();
                reqUser.setId(rBlocUserFromDB.getUserId());
                reqUser.setPhone(newPhone.toString());
                reqUser.setUsername(blocUserDto.getBlocUserName());
                reqUser.setDefaultPayType(Constant.PAY_BLOC_TYPE);//改为授信账户结算
                reqUser.setBalanceId(rBlocUserFromDB.getId());//改为t_r_bloc_user的id字段
                ObjectResponse<User> userObjectResponse = userFeignClient.setBasicInfo(
                    reqUser);//修改刚新建的个人客户信息(t_user)
                try {
                    FeignResponseValidate.check(userObjectResponse);
                } catch (DcException e) {
                    log.warn("error: {}", e.getMessage(), e);
                    throw new DcServiceException("修改新增的个人客户失败");
                }
                RBlocUser modifyRBlocUser = new RBlocUser();
                modifyRBlocUser.setId(rBlocUserFromDB.getId());
                modifyRBlocUser.setPhone(newPhone.toString());
                BaseResponse modifyByCondition = userFeignClient.modifyByCondition(
                    modifyRBlocUser);//将新手机号传给刚新建的授信关系(t_r_bloc_user.phone)
                try {
                    FeignResponseValidate.check(modifyByCondition);
                } catch (DcException e) {
                    log.warn("error: {}", e.getMessage(), e);
                    throw new DcServiceException("修改新增的授信关系失败");
                }
                card.setMobile(newPhone.toString());
            }
            card.setUserId(rBlocUserFromDB.getUserId());
            card.setCorpId(rBlocUserFromDB.getBlocUserId());


        } else {
            throw new DcServiceException("绑定客户类型错误");
        }
        log.info("新增紧急充电卡片（发卡给客户）Card：{}", JsonUtils.toJsonString(card));
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);
        log.info("新增紧急充电卡片（发卡给客户）调用服务完成结果是{}",
            JsonUtils.toJsonString(jsonResult));
        return jsonResult;

    }

    /**
     * 在线卡挂失
     *
     * @param cardNo
     * @param commIdList
     * @return
     */
    public BaseResponse reportLossCard(String cardNo, List<Long> commIdList) {
        log.info("挂失卡片cardNo：{}", cardNo);
        //调用服务
        ObjectResponse<Card> jsonResult = cardFeignClient.queryCardByCardNo(cardNo);
        FeignResponseValidate.check(jsonResult);
//        if (jsonResult == null || ResultConstant.RES_SUCCESS_CODE != jsonResult.getInteger("status") ||
//                jsonResult.get("data") == null) {

//        }
        Card card = jsonResult.getData();
        if (card == null) {
            log.info("挂失卡片时，查询该卡片详情失败{}", JsonUtils.toJsonString(jsonResult));
            throw new DcServiceException("查询该卡片详情错误");
        }
        if (card.getCardStatus() != null && card.getCardStatus()
            .equals(CardStatus.ACTIVE.getCode())) {

            BaseResponse jsonResult2 = userFeignClient.updateCardStatus(cardNo,
                CardStatus.LOCK.getCode());
            FeignResponseValidate.check(jsonResult2);
            return jsonResult2;

        } else {
            throw new DcServiceException("挂失操作仅可用于'已激活'的卡");
        }
    }

    /**
     * 在线卡激活
     *
     * @param cardNo
     * @return
     */
    public BaseResponse reportActivateCard(String cardNo) {
        log.info("激活卡片cardNo：{}", cardNo);
        //调用服务
        ObjectResponse<Card> jsonResult = cardFeignClient.queryCardByCardNo(cardNo);
        FeignResponseValidate.check(jsonResult);
        Card card = jsonResult.getData();
        if (card == null) {
            log.info("激活卡片时，查询该卡片详情失败{}", jsonResult);
            throw new DcServiceException("查询该卡片详情错误");
        }

        //Card card = JSON.parseObject(JsonUtils.toJsonString(jsonResult.get("data")),new TypeReference<Card>(){});
        if (card.getCardStatus() != null && card.getCardStatus()
            .equals(CardStatus.LOCK.getCode())) {
            if (card.getCardActivationDate() == null) {
//                Date currDate = new Date();
//                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//                String currDate = dateFormat.format(date);
                Card c = new Card();
                c.setCardNo(card.getCardNo());
                c.setCardActivationDate(new Date());
                BaseResponse result = userFeignClient.updateCard(c);
                log.info("激活卡片时，发现卡无激活时间，将以当前时间为激活时间，更新后结果是{}",
                    result);

            }
            BaseResponse jsonResult2 = userFeignClient.updateCardStatus(cardNo,
                CardStatus.ACTIVE.getCode());
            return jsonResult2;

        } else {
            throw new DcServiceException("激活操作仅用于'挂失'状态的卡");
        }
    }

    /**
     * 在线卡修改 紧急卡修改备注
     *
     * @param carNo    车牌号
     * @param carNum   车辆自编号
     * @param lineNum  线路
     * @param cardNo
     * @param commId
     * @param cardName
     * @param stations
     * @param remark
     * @param locale   语言环境，null表示非国际版
     * @return
     */
    public BaseResponse modifyCard(String carDepart, String carNo, String carNum, String lineNum,
        String cardNo, Long commId, String cardName, String stations, String remark,
        Integer deposit, Boolean isLocalAuth, List<String> localAuthSiteList, Locale locale) {
        List<String> noSettlementCardList = corpService.getNoSettlementCard(
            Collections.singletonList(cardNo), null);
        if (CollectionUtils.isNotEmpty(noSettlementCardList)) {
            throw new DcServiceException("卡片存在订单尚未结算，请处理后再进行操作");
        }
        Card card = new Card();

        if (null == locale) {
            if (StringUtils.isNotBlank(carDepart) && carDepart.length() > 30) {
                throw new DcServiceException("请检查车队名称是否有误");
            }
        } else {
            if (StringUtils.isNotBlank(carDepart) && carDepart.length() > 50) {
                throw new DcServiceException("车队名称最多50位");
            }
        }

        if (carDepart != null) {
            card.setCarDepart(carDepart);
        }
        if (carNo != null) {
            card.setCarNo(carNo);
        }

        if (carNum != null) {
            card.setCarNum(carNum);
        }

        if (lineNum != null) {
            card.setLineNum(lineNum);
        }

        if (StringUtils.isNotBlank(cardNo)) {
            card.setCardNo(cardNo);
        }

        if (commId != null && commId > 0) {
            card.setCommId(commId);
        }

        if (cardName != null) {
            card.setCardName(cardName.trim());
        }

        if (StringUtils.isNotBlank(stations)) {
            card.setStations(stations);
        }

        if (deposit != null) {
            card.setDeposit(deposit);
        }

        if (remark != null) {
            if (remark.trim().length() > 30) {
                throw new DcServiceException("备注30 个字以内");
            }
            card.setRemark(remark.trim());
        }

        card.setIsLocalAuth(isLocalAuth);
        if (Boolean.TRUE.equals(isLocalAuth)) {
            if (CollectionUtils.isEmpty(localAuthSiteList)) {
                throw new DcServiceException("本地鉴权开启时，本地鉴权可用场站列表不能为空");
            }
            card.setLocalAuthSiteList(localAuthSiteList);
        }

        if (null != locale) {
            card.setLocale(locale);
        }

        card.setCardUpdateDate(new Date());//更新修改时间
        log.info("修改卡片 Card：{}", JsonUtils.toJsonString(card));
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);
        log.info("修改卡片调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        return jsonResult;
//        if (jsonResult == null) {
//            throw new DcServiceException("操作失败");
//        } else if (jsonResult.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException(jsonResult.toJSONString());
//        } else {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        }
    }

    /**
     * 紧急充电卡修改(该功能需求已删除) (绑定集团客户逻辑需调整)
     *
     * @return
     */
    @Deprecated
    public BaseResponse modifyUrgencyCard(CardRequest cardRequest) throws ParseException {
        log.info("cardRequest：{}", JsonUtils.toJsonString(cardRequest));
        AssertUtil.notEmpty(cardRequest.getCardNo(), "cardNo为空");
        AssertUtil.notEmpty(cardRequest.getMobile(), "mobile为空");
        AssertUtil.notNull(cardRequest.getBindType(), "bindType为空");
        AssertUtil.notNull(cardRequest.getCommId(), "commId为空");
        AssertUtil.notEmpty(cardRequest.getStations(), "stations为空");
//        Date date = new Date();
//        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//        String currDate = dateFormat.format(date);
        Card card = new Card();
//        card.setId(cardRequest.getId());
        card.setCardNo(cardRequest.getCardNo());
        card.setCardName(cardRequest.getCardName().trim());
        if (cardRequest.getBindType()
            .equals(Integer.parseInt(BindType.PERSONAL_CUSTOMER.getCode()))) {
            //如果绑定为个人客户，通过手机得到userID
            ObjectResponse<UserPropVO> response = userFeignClient.findByPhone(
                cardRequest.getMobile(), cardRequest.getCommId());
            FeignResponseValidate.check(response);
            UserPropVO vo = response.getData();
            card.setUserId(vo.getUserId());
            card.setMobile(cardRequest.getMobile());
        } else if (cardRequest.getBindType()
            .equals(Integer.parseInt(BindType.GROUP_CUSTOMER.getCode()))) {
            //如果绑定为集团客户，通过phone和bloc_user_id验证是否存在
            RBlocUser req = new RBlocUser();
            req.setPhone(cardRequest.getMobile());
            AssertUtil.notNull(cardRequest.getCorpId(), "corpId为空");
            req.setBlocUserId(cardRequest.getCorpId());
            req.setStatus(1);
            ObjectResponse<RBlocUser> response = userFeignClient.selectRBlocUserByPhone(req);
            FeignResponseValidate.check(response);
            RBlocUser res = response.getData();
            card.setUserId(res.getUserId());
            card.setMobile(res.getPhone());
            card.setCorpId(res.getBlocUserId());
        } else {
            throw new DcServiceException("绑定客户类型错误");
        }
        card.setCommId(cardRequest.getCommId());
//        //确认stations只有一个
//        ObjectResponse<SiteSimpleInfoVo> res = siteFeignClient.getSiteSimpleInfoById(cardRequest.getStations());
//        FeignResponseValidate.check(res);
        card.setStations(cardRequest.getStations());

        card.setCardUpdateDate(new Date());//更新修改时间
        log.info("修改卡片 Card：{}", JsonUtils.toJsonString(card));
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCard(card);
        log.info("修改卡片调用服务完成结果是{}", JsonUtils.toJsonString(jsonResult));
        return jsonResult;
//        if (jsonResult == null) {
//            throw new DcServiceException("操作失败");
//        } else if (jsonResult.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException(jsonResult.toJSONString());
//        } else {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        }
    }

    /**
     * 在线卡删除
     *
     * @param cardNo
     * @return
     */
    public BaseResponse logicDelCard(String cardNo) {
        log.info("删除卡片 cardNo：{}", cardNo);
        //调用服务
        BaseResponse jsonResult = userFeignClient.updateCardStatus(cardNo,
            CardStatus.DELETED.getCode());
        log.info("修改卡片调用服务完成结果是{}", jsonResult);
        return jsonResult;
//        if (jsonResult == null) {
//            throw new DcServiceException("操作失败");
//        } else if (jsonResult.getInteger("status") != ResultConstant.RES_SUCCESS_CODE) {
//            throw new DcServiceException(jsonResult.toJSONString());
//        } else {
//            return JSONObject.parseObject(jsonResult.toString(), ObjectResponse.class);
//        }
    }

    /**
     * 根据卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     *                   卡有效标志1有效0无效
     * @return
     */

    public BaseResponse updateCardStatus(String cardNo, String cardStatus) {
        BaseResponse jsonResult = userFeignClient.updateCardStatus(cardNo, cardStatus);
        return jsonResult;

    }


    /**
     * 根据卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     *                   卡有效标志1有效0无效
     * @return
     */

    public BaseResponse updateCardStatusAndCheck(String cardNo, String cardStatus,
        List<Long> commIdList) {
        BaseResponse jsonResult = userFeignClient.updateCardStatus(cardNo, cardStatus);
        return jsonResult;

    }

    /**
     * 添加卡片
     *
     * @param commId     所属商户
     * @param cardNo     卡号
     * @param token      用户token
     * @param stations   鉴权卡所属站点（多个站点用‘，’分割）
     * @param isPackaged 卡类型：4 鉴权卡
     * @return
     */

    public BaseResponse addCard(Long commId, String cardNo, String token, String stations,
        Integer isPackaged) {
        //查询是否有商户
        ObjectResponse<Commercial> jsonObject = userCommercialFeignClient.getCommercialByToken(
            token);
        if (jsonObject == null || ResultConstant.RES_SUCCESS_CODE != jsonObject.getStatus()) {
            throw new DcServiceException("商家不存在");
        }
        //封装数据
        Card card = new Card();
        card.setCardNo(cardNo);
        card.setCommId(commId);
        card.setIsPackage(isPackaged);
        card.setCardType(Constant.CARD_ENTITY);
        card.setCardStatus(Constant.CARD_STATUS_NORMAL);
        card.setCardCreateDate(new Date());
        card.setCardChannel(Constant.CARD_CHANNAL_MANAGER);
        card.setStations(stations);
        //添加卡
        BaseResponse jsonResult = userFeignClient.addCard(card);
        return jsonResult;
        //调用成功

    }

    /**
     * 鉴权卡更新功能
     *
     * @param cardNo     卡号
     * @param commId     所属商户
     * @param stations   鉴权卡所属站点（多个站点用‘，’分割）
     * @param isPackaged 卡类型：4 鉴权卡
     * @return
     */

    public BaseResponse updateAuthCard(Long commId, String cardNo, String token, String stations,
        Integer isPackaged) {
        //查询是否有商户
        ObjectResponse<Commercial> jsonObject = userCommercialFeignClient.getCommercialByToken(
            token);
        if (jsonObject == null || ResultConstant.RES_SUCCESS_CODE != jsonObject.getStatus()
            || jsonObject.getData() == null) {
            throw new DcServiceException("商家不存在");
        }
        //封装数据
        Card card = new Card();
        card.setCardNo(cardNo);
        card.setCommId(commId);
        card.setIsPackage(isPackaged);
        card.setCardUpdateDate(new Date());
        card.setStations(stations);
        //更新卡
        BaseResponse jsonResult = userFeignClient.updateCard(card);
        return jsonResult;
        //调用成功

    }

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */

    public ObjectResponse<Card> queryCardById(String id) {
        //更新卡
        ObjectResponse<Card> jsonResult = userFeignClient.queryCardById(id);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
        //调用成功

    }

    /**
     * 根据cardNo查询卡
     *
     * @param cardNo
     * @return
     */

    public ObjectResponse<Card> queryCardByCardNo(String cardNo) {
        //更新卡
        ObjectResponse<Card> jsonResult = cardFeignClient.queryCardByCardNo(cardNo);
        return jsonResult;


    }

    /**
     * 根据cardChipNo查询卡
     *
     * @param cardChipNo
     * @return
     */

    public ObjectResponse<Card> queryCardByCardChipNo(String cardChipNo) {
        //更新卡
        ObjectResponse<Card> jsonResult = userFeignClient.queryCardByCardChipNo(cardChipNo);
        return jsonResult;

    }


    public ListResponse<CardVo> queryAllCardList(String token, String userId) {
        return userFeignClient.queryAllCardList(token, userId);
    }


    public ListResponse<CardMgnVo> queryAllCardsByPage(String token, OldPageParam page,
        String cardStatus, String cardType,
        Long commId, String beginTime, String endTime, String keyWord) {
//        log.info("根据条件查询运营支撑平台卡片列表查询条件是--page{}--cardStatus{}--cardType{}--beginTime{}--endTime{}--keyWord{}"
//                , JsonUtils.toJsonString(page), cardStatus, cardType, beginTime, endTime, keyWord);
        //调用服务
        ListResponse<CardMgnVo> jsonResult = userFeignClient.queryAllCardsByPage(token,
            page.getPageNum(), page.getPageSize()
            , cardStatus, cardType, commId, beginTime, endTime, keyWord);
        log.info("根据条件查询运营支撑平台卡片列表调用服务完成结果是{}", jsonResult);

        return jsonResult;
    }


    public BaseResponse deleteCardsByIds(List<Long> ids) {
        log.info("卡片id集合 ids{}", ids);
//        if(ids.size()==0)
//            throw new DcArgumentException("未选择删除的卡号");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(ids), "未选择需要删除的卡号");
        //调用服务
        BaseResponse jsonResult = userFeignClient.deleteCardsById(ids);
        log.info("删除卡片完成结果是{}", jsonResult);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;

    }


    public BaseResponse resetCardsByIds(List<Long> ids) {
        log.info("激活卡片id集合 ids{}", ids);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(ids), "未选择需要激活的卡号");
        BaseResponse jsonResult = userFeignClient.resetCardsByIds(ids);
        log.info("激活卡片完成结果是{}", jsonResult);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
    }

    /**
     * excel 文件解析
     *
     * @param file
     * @return
     */

    public Mono<ObjectResponse<JsonNode>> parseExcel(FilePart file) {
        log.info(">> Feign 远程调用解析 excel 文件: {}.", file.filename());

        Mono<ObjectResponse<JsonNode>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {

                        InputStream inputStream = new FileInputStream(f);
                        List<List<Object>> list = ExcelBatchUtil.getCardListByExcel(inputStream,
                            file.filename());
                        log.info("list.size = {}", list == null ? null : list.size());
                        if (list == null || list.size() <= 0) {
                            return null;
                        }
                        list = list.stream().map(e -> {

                            return e.stream().map(ie -> {

                                if (ie == null) {
                                    String s = "";
                                    return s;
                                }
                                return (Object) ie.toString();
                            }).collect(Collectors.toList());

                        }).collect(Collectors.toList());

                        ObjectResponse<JsonNode> result = userFeignClient.parseExcel(list);
//                            log.info("result = {}", result);
                        FeignResponseValidate.check(result);
                        return result;

                    } catch (DcServiceException e) {
                        throw new DcServiceException(e.getMessage(), e);
                    } catch (Exception e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
//                    .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;
    }

    /**
     * 解析企业平台在线卡excel文件
     *
     * @param file
     * @return
     */

    public Mono<ObjectResponse<JsonNode>> parseCorpCardExcel(FilePart file, Long corpId) {
        log.info(">> Feign 远程调用解析 excel 文件: {}.", file.filename());

        Mono<ObjectResponse<JsonNode>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {

                        InputStream inputStream = new FileInputStream(f);
//                            log.info("inputStream: {}", inputStream);
                        List<List<Object>> list = ExcelBatchUtil.getCardListByExcelOnCorp(
                            inputStream, file.filename());
                        log.info("list.size = {}", list == null ? null : list.size());
                        if (list == null || list.size() <= 0) {
                            return null;
                        }
                        list = list.stream().map(e -> {

                            return e.stream().map(ie -> {

                                if (ie == null) {
                                    String s = "";
                                    return s;
                                }
                                return (Object) ie.toString();
                            }).collect(Collectors.toList());

                        }).collect(Collectors.toList());
                        ObjectResponse<JsonNode> result = userFeignClient.parseCorpCardExcel(list,
                            corpId);
                        log.info("result = {}", result);
                        FeignResponseValidate.check(result);

                        return result;
                    } catch (DcServiceException e) {
                        throw new DcServiceException(e.getMessage(), e);
                    } catch (Exception e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
//                    .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;

//        return result;
//        log.info(">> Feign 远程调用解析 excel 文件: {}.", file.filename());
//        ObjectResponse<JSONObject> result = userFeignClient.parseCorpCardExcel(file, corpId);
//        FeignResponseValidate.check(result);
//        return result;
    }

    /**
     * 解析企业平台VIN码excel文件
     *
     * @param file
     * @return
     */

    public Mono<ObjectResponse<JsonNode>> parseCorpVinExcel(FilePart file, Long corpId,
        Long corpTopCommId) {
        log.info(">> Feign 远程调用解析 excel 文件: {}, corpId: {}, corpTopCommId: {}",
            file.filename(), corpId, corpTopCommId);

        Mono<ObjectResponse<JsonNode>> m = null;

        try {
            File f = new File("/tmp/" + file.filename());
            m = file.transferTo(f)
                .then(Mono.just("文件上传成功"))
                .map(a -> {
                    try {

                        InputStream inputStream = new FileInputStream(f);
//                            log.info("inputStream: {}", inputStream);
                        List<List<String>> list = com.cdz360.biz.ant.utils.ExcelUtils.getVinListByExcelOnCorp(
                            inputStream, file.filename());
                        log.info("list.size = {}", list == null ? null : list.size());
                        ObjectResponse<JsonNode> result = userFeignClient.parseCorpVinExcel(list,
                            corpId, corpTopCommId);
                        log.info("result = {}", result);
                        FeignResponseValidate.check(result);

                        return result;
                    } catch (DcServiceException e) {
                        throw new DcServiceException(e.getMessage(), e);
                    } catch (Exception e) {
                        log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
                        throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
                    }
                });
//                    .map(a -> RestUtils.buildObjectResponse(a));
        } catch (Exception e) {
            log.warn("msg: {}", e.getMessage(), e);
            throw new DcServiceException("文件上传失败");
        }
        return m;

//        return result;
    }

    /**
     * 卡片批量添加
     *
     * @param cards
     * @return
     */

    public ObjectResponse<Integer> batchAddCard(List<Card> cards) {
        log.info(">> Feign 远程调用批量添加卡片: {}.", cards);
        ObjectResponse<Integer> result = userFeignClient.batchAddCard(cards);
        return result;

    }

    /**
     * 单场站下发紧急充电卡
     *
     * @param cardChipNo
     * @param token
     * @return
     */

    public ObjectResponse<List<String>> sendWhiteCard(String cardChipNo, String token) {
        ObjectResponse<List<String>> objectResponse = userFeignClient.sendWhiteCard(cardChipNo,
            token);
        return objectResponse;
    }

    /**
     * 多场站下发紧急充电卡
     *
     * @param token
     * @return
     */

    public ObjectResponse<Map<String, Object>> sendBatchWhiteCards(String token) {
        ObjectResponse<Map<String, Object>> objectResponse = userFeignClient.sendBatchWhiteCards(
            token);
        return objectResponse;
    }

    /**
     * 下发紧急充电卡 弃用cardChipNo
     *
     * @param cardChipNo
     * @param token
     * @return
     */

    public ObjectResponse<List<String>> abandonWhiteCard(String cardChipNo, String token) {
        ObjectResponse<List<String>> objectResponse = userFeignClient.abandonWhiteCard(cardChipNo,
            token);
        return objectResponse;
    }

    /**
     * 下发紧急充电卡 重置密码
     *
     * @param cardChipNo
     * @param token
     * @return
     */

    public ObjectResponse<List<String>> resetWhiteCardPwd(String cardChipNo, String token) {
        ObjectResponse<List<String>> objectResponse = userFeignClient.resetWhiteCardPwd(cardChipNo,
            token);
        return objectResponse;
    }

    public ListResponse<String> getStationsOfCard(String cardNo, Integer type, String idChain) {
        return cardFeignClient.getStationsOfCard(cardNo, type, idChain);

    }

    public ListResponse<String> getEmergencyCardBySiteId(String siteId, Long commId) {
        return cardFeignClient.getEmergencyCardBySiteId(siteId, commId);

    }

    public ObjectResponse<MoveCorpCardList> getEmergencyCardByCorpId(Long corpId, Long commId) {
        return cardFeignClient.getEmergencyCardByCorpId(corpId, commId);
    }

    public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpCardByCorpId(Long corpId, Long commId) {
        return cardFeignClient.getMoveCorpCardAuthByCorpId(corpId, commId);
    }

    public Mono<ObjectResponse<Boolean>> syncAmount(CardRequest card) {
        return userMonoFeignClient.syncAmount(card.getCardNo(),
            card.getSyncAmount(),
            0,
            card.getTopCommId(),
            card.getUserId());
    }

    public Mono<ListResponse<CardAmountSyncVo>> getSyncCardAmount(CardAmountSyncParam param) {
        return userMonoFeignClient.getSyncCardAmount(param);
    }

    public Mono<ObjectResponse<BigDecimal>> cardAmount(CardRequest param) {
        return userMonoFeignClient.getCardAmount(param.getCardNo(), param.getTopCommId(),
            param.getCardMaker());
    }

    public ObjectResponse<Integer> siteAuthCard(SiteAuthCardParam param) {
        // 按场站下发，下发到场站下所有桩
        // 只需要提供场站列表
        return userFeignClient.siteAuthCard(param);
    }

    public ListResponse<SitePo> getCommonSiteList(CardSearchParam param) {
        return userFeignClient.getCardCommonSiteList(param);
    }

    public ListResponse<SiteAuthCardPo> getCardListBySiteId(String siteId) {
        return userFeignClient.getCardListBySiteId(siteId);
    }

    public BaseResponse addEssCard(AddEssCardParam param) {
        BaseResponse jsonResult = cardFeignClient.addEssCard(param);
        log.info("海外版新增卡片，同时发卡给客户，调用服务完成结果是{}", jsonResult);
        return jsonResult;
    }

    /**
     * 海外版，查询卡片列表（海外版添加卡片后就是已分配给用户的在线卡，因此只查询cardType是0的）
     *
     * @param param
     * @return
     */
    public ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(CardRequest param) {
        ListResponse<CardListdetailVO> jsonResult = userFeignClient.queryEssOnlineCardsByPage(
            param);
        FeignResponseValidate.check(jsonResult);
        return jsonResult;
    }
}
