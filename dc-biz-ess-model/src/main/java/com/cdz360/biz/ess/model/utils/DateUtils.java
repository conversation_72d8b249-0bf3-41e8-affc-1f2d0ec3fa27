package com.cdz360.biz.ess.model.utils;


import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.param.TimeFilter;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.NonNull;

public class DateUtils {

    private static final Logger log = LoggerFactory.getLogger(DateUtils.class);

    /**
     * 一天的秒表示
     */
    public static final int DAY_SECOND = 86400;
    /**
     * 一天的毫秒表示
     */
    public static final long DAY_MILLIONS = 86400000;
    /**
     * 一小时的毫秒表示
     */
    public static final long HOUR_MILLIONS = 3600000;
    /**
     * 一分钟的毫秒表示
     */
    public static final long MINUTE_MILLIONS = 60000;

    /**
     * 一分钟的秒表示,一小时分钟数
     */
    public static final int MINUTE = 60;
    /**
     * 一秒的毫秒表示
     */
    public static final int MILLIONS = 1000;
    /**
     * yyyy-MM-dd HH:mm:ss
     */
    public static final SimpleDateFormat YMDHMS_FORMAT = new SimpleDateFormat(
        "yyyy-MM-dd HH:mm:ss");
    /**
     * yyyy-MM-dd HH:mm:ss SSS
     */
    public static final SimpleDateFormat YMDHMSS_FORMAT = new SimpleDateFormat(
        "yyyy-MM-dd HH:mm:ss SSS");
    /**
     * yyyy-MM-dd
     */
    public static final SimpleDateFormat YMD_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    private static final int[] WEEKDAYS = {7, 1, 2, 3, 4, 5, 6};

    public static final String FULL_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * 按照yyyy-MM-dd HH:mm:ss的格式，日期转字符串
     *
     * @param date
     * @return yyyy-MM-dd HH:mm:ss
     */
    public static String date2Str(Date date) {
        return date2Str(date, FULL_TIME_FORMAT);
    }

    /**
     * 按照参数format的格式，日期转字符串
     *
     * @param date
     * @param format
     * @return
     */
    public static String date2Str(Date date, String format) {
        if (date != null) {
            SimpleDateFormat sdf = new SimpleDateFormat(format);
            return sdf.format(date);
        } else {
            return "";
        }
    }

    public static Date str2Date(String str) {
        return str2Date(str, "yyyy-MM-dd");
    }

    public static Date str2Minute(String str) {
        return str2Date(str, "yyyy-MM-dd HH:mm");
    }

    public static Date timeStr2Date(String str) {
        return str2Date(str, YMDHMS_FORMAT);
    }

    public static Date str2Date(String str, String format) {
        return str2Date(str, new SimpleDateFormat(format));
    }

    public static Date str2Date(String str, SimpleDateFormat pattern) {
        if (StringUtils.isNotBlank(str) && pattern != null) {
            Date parseDate;
            try {
                parseDate = pattern.parse(str);
            } catch (Exception e) {
                parseDate = new Date();
            }
            return parseDate;
        }
        return new Date();
    }

    /**
     * 根据用户生日计算年龄
     */
    public static int getAgeByBirthday(Date birthday) {
        if (birthday == null) {
            return 0;
        }
        Calendar cal = Calendar.getInstance();

        if (cal.before(birthday)) {
            return 0;
        }

        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);

        cal.setTime(birthday);
        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH) + 1;
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;

        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) {
                    age--;
                }
            } else {
                age--;
            }
        }
        return age;
    }

    public static long nowSeconds() {
        return (long) (System.currentTimeMillis() / 1000);
    }

    /**
     * 获得当前日期的前一天
     *
     * @param date
     * @return
     */
    public static Date getSpecifiedDayBefore(Date date) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DATE);
        cal.set(Calendar.DATE, day - 1);
        return cal.getTime();
    }

    /**
     * 获得当前日期的前n天
     *
     * @param date
     * @return
     */
    public static Date getSpecifiedDayBefore(Date date, int difference) {
        if (date == null) {
            return null;
        }
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int day = cal.get(Calendar.DATE);
        cal.set(Calendar.DATE, day - difference);
        return cal.getTime();
    }

    /**
     * 获取时间年月日时00,30
     *
     * @return 201609210900, 201609210930
     */
    public static Long getDateMinute() {
        SimpleDateFormat sdf = null;
        Calendar c = Calendar.getInstance();
        int minute = c.get(Calendar.MINUTE);
        if (minute >= 30) {
            sdf = new SimpleDateFormat("yyyyMMddHH30");
        } else {
            sdf = new SimpleDateFormat("yyyyMMddHH00");
        }
        String ss = sdf.format(c.getTime());
        return Long.valueOf(ss);
    }

    /**
     * 获取时间年月日时00,30
     *
     * @param date
     * @return 201609210900, 201609210930
     */
    public static Long getDateMinute(Date date) {
        SimpleDateFormat sdf = null;
        Calendar c = Calendar.getInstance();
        int minute = c.get(Calendar.MINUTE);
        if (minute >= 30) {
            sdf = new SimpleDateFormat("yyyyMMddHH30");
        } else {
            sdf = new SimpleDateFormat("yyyyMMddHH00");
        }
        String ss = sdf.format(date);
        return Long.valueOf(ss);
    }

    public static int toSecond(long millis) {
        long second = millis / 1000L;
        return (int) second;
    }

    /**
     * 返回当前时间的秒数，单位：秒
     *
     * @return
     */
    public static int getCurrentSecond() {
        return toSecond(System.currentTimeMillis());
    }

    public static Date getCurrentDate() {
        return new Date();
    }

    public static String date2String(Date theDate, String datePattern) {
        if (theDate == null) {
            return "";
        }

        DateFormat format = new SimpleDateFormat(datePattern);
        try {
            return format.format(theDate);
        } catch (Exception e) {
        }
        return "";
    }

    public static String date2String(Date theDate, SimpleDateFormat pattern) {
        if (theDate == null) {
            return "";
        }
        try {
            return pattern.format(theDate);
        } catch (Exception e) {
        }
        return "";
    }

    public static Date string2Date(String dateString, SimpleDateFormat pattern) {
        if ((dateString == null) || (dateString.trim().isEmpty())) {
            return null;
        }
        try {
            return pattern.parse(dateString);
        } catch (ParseException e) {
            log.error("ParseException in Converting String to date: " + e.getMessage());
        }
        return null;
    }

    public static Date string2Date(String dateString, String datePattern) {
        if ((dateString == null) || (dateString.trim().isEmpty())) {
            return null;
        }

        DateFormat format = new SimpleDateFormat(datePattern);
        try {
            return format.parse(dateString);
        } catch (ParseException e) {
            log.error("ParseException in Converting String to date: " + e.getMessage());
        }

        return null;
    }

    public static Date string2Date(String ymd) {
        if ((ymd == null) || (ymd.trim().isEmpty())) {
            return null;
        }
        try {
            return YMD_FORMAT.parse(ymd);
        } catch (ParseException e) {
            log.error("ParseException in Converting String to date: " + e.getMessage());
        }

        return null;
    }

    /**
     * 将时间戳转换成date
     *
     * @param timestamp
     * @return
     */
    public static Date getTimestamp2Date(Long timestamp) {
        Date date = null;
        if (timestamp == null) {
            return date;
        }
        try {
            date = new Date(timestamp);
            return date;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据当前日期返回 yyyy-MM-dd HH:mm:ss 格式字符串
     *
     * @return
     */
    public static final String getCurrentDateTimeString() {
        return YMDHMS_FORMAT.format(Calendar.getInstance().getTime());
    }


    /**
     * 返回日时分秒
     *
     * @param second
     * @return
     */
    public static String secondToTime(Long second) {
        long days = second / 86400;//转换天数
        second = second % 86400;//剩余秒数
        long hours = second / 3600;//转换小时数
        second = second % 3600;//剩余秒数
        long minutes = second / 60;//转换分钟
        second = second % 60;//剩余秒数

        String secondStr = dateFormate(second);
        String minutesStr = dateFormate(minutes);
        String hoursStr = dateFormate(hours);

        if (0 < days) {
            return days + "天 " + hoursStr + ":" + minutesStr + ":" + secondStr + "";
        } else {
            return hoursStr + ":" + minutesStr + ":" + secondStr + "";
        }
    }

    public static String dateFormate(long second) {
        String secondStr = String.valueOf(second);
        if (second == 0) {
            secondStr = "00";
        } else if (second < 10) {
            secondStr = "0" + String.valueOf(second);
        }
        return secondStr;
    }

    /**
     * 获取当前小时的0秒时刻
     *
     * @param date
     * @return
     */
    public static Date getThisHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        return calendar.getTime();
    }

    /**
     * 获取入参天的下一天0秒时刻
     *
     * @param date
     * @return
     */
    public static Date getNextHour(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.add(Calendar.HOUR, 1);
        return calendar.getTime();
    }

    /**
     * 获取入参天的0秒时刻
     *
     * @param date
     * @return
     */
    public static Date getThisDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        return calendar.getTime();
    }

    public static Date getNextDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.add(Calendar.DATE, 1);
        return calendar.getTime();
    }

    public static Date getPrevDate(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.add(Calendar.DATE, -1);
        return calendar.getTime();
    }

    /**
     * 获取时间范围内的每一天，不包括入参天
     *
     * @param start
     * @param end
     * @return
     */
    public static List<Date> getDatesBetween(Date start, Date end) {
        Date curDate = getThisDate(start);
        final Date to = getThisDate(end);

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(curDate);
        calendar.add(Calendar.DATE, 1);
        curDate = calendar.getTime();

        List<Date> dates = new ArrayList<>();
        while (curDate.getTime() < to.getTime()) {
            dates.add(curDate);
            calendar.add(Calendar.DATE, 1);
            curDate = calendar.getTime();
        }

        return dates;
    }

    public static LocalDateTime date2LocalDateTime(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
    }

    public static LocalDateTime dateTimeToDate(Date date) {
        return LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault())
            .withHour(0).withMinute(0).withSecond(0).withNano(0);
    }

    public static Optional<Duration> calcDuration(Date from, Date to) {
        if (from == null || to == null) {
            return Optional.empty();
        }

        return Optional.ofNullable(
            Duration.between(LocalDateTime.ofInstant(from.toInstant(), ZoneId.systemDefault()),
                LocalDateTime.ofInstant(to.toInstant(), ZoneId.systemDefault())));
    }

    public static Date timeToDate(LocalDateTime time) {
        return Date.from(time.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate dateToLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static Date localDateToDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    public static Date addDate(Date date, int dateOffset) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.DATE, dateOffset);
        return cal.getTime();
    }

    /**
     * @param time 小时:分钟 XX:XX
     * @return 当前天的第几分钟
     */
    public static Integer timeToInt(String time) {
        if (com.cdz360.base.utils.StringUtils.isBlank(time)) {
            return null;
        }
        try {
            String[] startTimeArray = time.split(":");
            return Integer.valueOf(startTimeArray[0]) * 60 + Integer.valueOf(startTimeArray[1]);
        } catch (Exception e) {
            return null;
        }
    }

    public static String intToTime(Integer time) {
        if (time == null) {
            return null;
        }
        try {
            String ret = String.format("%02d:%02d", time / 60, time % 60);
            return ret;
        } catch (Exception e) {
            return null;
        }
    }

    public static Date getThisMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.clear(Calendar.MILLISECOND);
        calendar.clear(Calendar.SECOND);
        calendar.clear(Calendar.MINUTE);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return calendar.getTime();
    }

    public static Date getPrevMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, -1);
        return cal.getTime();
    }

    public static Date getNextMonth(Date date) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        cal.add(Calendar.MONTH, 1);
        return cal.getTime();
    }

    /**
     * 计算这个月有几天
     *
     * @param date
     * @return
     */
    public static int getDayCountInMonth(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(Calendar.DATE, 1);
        calendar.add(Calendar.MONTH, 1);
        calendar.add(Calendar.DATE, -1);
        return calendar.get(Calendar.DATE);
    }

    /**
     * 获取今日的分钟数
     *
     * @param date
     * @return [0, 1440]
     */
    public static int getMinutesByDateTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.MINUTE) + calendar.get(Calendar.HOUR_OF_DAY) * 60;
    }

    /**
     * 获取今日的秒数
     *
     * @param date
     * @return [0, 86400)
     */
    public static int getSecondsByDateTime(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        return calendar.get(Calendar.SECOND) + calendar.get(Calendar.MINUTE) * 60
            + calendar.get(Calendar.HOUR_OF_DAY) * 3600;
    }

    public static List<LocalDate> rangeDate(int year, int month) {
        LocalDate now = LocalDate.now().withYear(year).withMonth(month);
        LocalDate start = now.with(TemporalAdjusters.firstDayOfMonth());
        LocalDate monthEnd = now.plusMonths(1).with(TemporalAdjusters.firstDayOfMonth());
        return rangeDate(start, monthEnd);
    }

    public static List<LocalDate> rangeMonthDate(LocalDate fromDate, LocalDate toDate) {
        if (toDate.isBefore(fromDate)) {
            log.error("时间范围无效: fromDate = {}, toDate = {}", fromDate, toDate);
            throw new DcArgumentException("时间范围无效");
        }

        List<LocalDate> dateList = new ArrayList<>();
        LocalDate startDate = fromDate.withDayOfMonth(1);
        while (startDate.isBefore(toDate)) {
            dateList.add(startDate);
            startDate = startDate.plusMonths(1).withDayOfMonth(1);
        }
        return dateList;
    }

    public static List<LocalDate> rangeDate(LocalDate fromDate, LocalDate toDate) {
        if (toDate.isBefore(fromDate)) {
            log.error("时间范围无效: fromDate = {}, toDate = {}", fromDate, toDate);
            throw new DcArgumentException("时间范围无效");
        }

        List<LocalDate> dateList = new ArrayList<>();
        LocalDate startDate = fromDate;
        while (startDate.isBefore(toDate)) {
            dateList.add(startDate);
            startDate = startDate.plusDays(1);
        }
        return dateList;
    }

    /**
     * 判断多个时间段是否有重叠（交集）
     *
     * @param timeList 时间段列表
     * @param strict   是否严格重叠; true严格，没有任何相交或相等；
     *                 false不严格，可以首尾相等，比如2021-05-29到2021-05-31和2021-05-31到2021-06-01，不重叠。
     * @return 返回是否重叠
     */
    public static boolean overlap(@NonNull List<TimeFilter> timeList, boolean strict) {
        timeList.sort(Comparator.comparing(TimeFilter::getStartTime));

        for (int i = 1; i < timeList.size(); i++) {
            if (strict) {
                if (!(timeList.get(i - 1).getEndTime().getTime()
                    < timeList.get(i).getStartTime().getTime())) {
                    return true;
                }
            } else {
                if (!(timeList.get(i - 1).getEndTime().getTime()
                    <= timeList.get(i).getStartTime().getTime())) {
                    return true;
                }
            }
        }
        return false;
    }

}
