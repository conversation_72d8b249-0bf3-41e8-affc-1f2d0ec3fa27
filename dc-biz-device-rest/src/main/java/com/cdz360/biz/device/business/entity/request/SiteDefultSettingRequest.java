package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * @Classname SiteDefultSettingRequest
 * @Description TODO
 * @Date 2019/6/11
 * @Created by wangzheng
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SiteDefultSettingRequest extends BaseRequest {
    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 场站编号
     */
    private String siteId;
    /**
     * 计费模板id
     */
    private Long chargeId;
    /**
     * 管理员密码
     */
    private String adminPassword;
    /**
     * 二级管理员密码
     */
    private String level2Password;
    /**
     * 白天音量
     */
    private Integer dayVolume;
    /**
     * 夜晚音量
     */
    private Integer nightVolume;
    /**
     * 二维码url
     */
    private String url;
    /**
     * 是否支持充电记录查询 （1是0否）
     */
    private Boolean isQueryChargeRecord;
    /**
     * 是否支持定时充电 （1是0否）
     */
    private Boolean isTimedCharge;
    /**
     * 是否支持无卡充电 （1是0否）
     */
    private Boolean isNoCardCharge;
    /**
     * 是否支持扫码充电 （1是0否）
     */
    private Boolean isScanCharge;
    /**
     * 是否支持Vin码充电 （1是0否）
     */
    private Boolean isVinCharge;
    /**
     * 是否支持刷卡充电 （1是0否）
     */
    private Boolean isCardCharge;
    /**
     * 是否支持定额电量充电 （1是0否）
     */
    private Boolean isQuotaEleCharge;
    /**
     * 是否支持固定金额充电 （1是0否）
     */
    private Boolean isQuotaMoneyCharge;
    /**
     * 是否支持固定时长充电 （1是0否）
     */
    private Boolean isQuotaTimeCharge;
    /**
     * 国际协议
     */
    private String internationalAgreement;
    /**
     * 自动停充 （1是0否）
     */
    private Integer isAutoStopCharge;
    /**
     * 均/轮充设置 0均充 1轮充
     */
    private Integer avgOrTurnCharge;
    /**
     * 合充开关 （1开0关）
     */
    private Integer isCombineCharge;
    /**
     * 是否支持辅电手动切换
     */
    private Boolean heating;
    /**
     * 辅电电压设置
     */
    private Integer heatingVoltage;
    /**
     * 是否支持电池反接检测
     */
    private Boolean batteryCheck;
    /**
     * 是否支持主动安全检测
     */
    private Boolean securityCheck;
    /**
     * 是否支持不拔枪充电
     */
    private Boolean constantCharge;
    /**
     * 插枪获取VIN开关
     */
    private Boolean vinDiscover;
    /**
     * 订单信息隐私设置开关（null不做更改 true开启 false关闭）
     */
    private Boolean orderPrivacySetting;

    /**
     * 订单账号显示类型
     * null 不做更改
     * 1 鉴权账号，刷卡时为卡号，VIN时为VIN，平台启动为手机号
     * 2 车牌号（没有车牌号时默认显示类型为0x01的内容）
     */
    private Integer accountDisplayType;
    /**
     * 1是0否
     */
    private Integer isUseDefaultSetting;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 操作人id
     */
    private Long updateByUserid;
}