package com.chargerlinkcar.framework.common.domain.vo;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.vo.ChargePriceVo;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.cus.auth.dto.CardDto;
import com.cdz360.biz.model.cus.score.dto.ScoreSettingDiscountDto;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 *  鉴权介质的鉴权结果
 * @since 2019/5/22 9:33
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
public class AuthMediaResult {
    private String mediaNo;//鉴权介质码
    private String mediaStatus;//状态
    private String stations;//允许在这些场站使用
    private Long commId;//商户id
    private BigDecimal frozenAmount;//冻结金额
    private BigDecimal balance;//余额
    private BigDecimal cardMakerBalance;// 用于给到制卡软件余额
    private String siteId;//场站ID

    /**
     * @deprecated  使用 payType 替换
     */
    @Deprecated
    private Integer defaultPayType;//扣款类型
    private Long corpId;// 扣款类型为授信账户时才有值
    private Long payAccountId;//扣款id
    private Long userId;//用户id
    private String carNo;//车牌号

    @Schema(description = "协议价优惠ID 鉴权用户使用协议价则存在协议价策略ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long discountRefId;

//    @Schema(description = "积分体系id")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private Long scoreSettingId;

//    @Schema(description = "积分体系服务费折扣")
//    @JsonInclude(JsonInclude.Include.NON_NULL)
//    private BigDecimal scoreServFeeDiscount;

    @Schema(description = "积分体系")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ScoreSettingDiscountDto scoreSettingDiscountDto;

    @Schema(description = "协议价优惠后的电价信息，下发到桩 鉴权用户使用协议价时存在")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private ChargePriceVo priceVo;

    @Schema(description = "是否为后付费标识: true/false")
    private Boolean postPaid; // 是否为后付费标识: true/false

    @Schema(description = "卡鉴权时返回卡信息")
    private CardDto card;

    @Schema(description = "VIN鉴权时返回VIN信息")
    private com.cdz360.biz.model.cus.auth.dto.VinDto vin;


    private Boolean realTmeFlag;    // 是否支持实时续费的标志位

    @Schema(description = "支付渠道")
    @JsonInclude(Include.NON_NULL)
    private PayAccountType payType;


    @Schema(description = "微信/支付宝openid, 仅用于信用充")
    @JsonInclude(Include.NON_NULL)
    private UserOpenidPo openid;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }

}