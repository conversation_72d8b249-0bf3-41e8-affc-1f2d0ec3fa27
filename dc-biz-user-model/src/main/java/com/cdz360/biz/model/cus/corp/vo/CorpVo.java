package com.cdz360.biz.model.cus.corp.vo;

import com.cdz360.base.model.base.type.PayChannel;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CorpVo extends CorpPo {

    @Schema(description = "所属商户名称")
    private String commName;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "基础账户余额(含冻结)")
    private BigDecimal amount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "基础账户可用余额")
    private BigDecimal availableAmount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "冻结金额")
    private BigDecimal frozenAmount;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "冻结成本(实际金额)")
    private BigDecimal frozenCost;

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Schema(description = "成本(实际金额) 不含冻结金额")
    private BigDecimal cost;

    @Schema(description = "企业在线充值 true -- 开启, 直属商户配置")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableCorpDeposit;

    @Schema(description = "企业在线退款 true -- 开启")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableCorpRefund;

    @Schema(description = "会员在线退款 true -- 开启")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enableCommRefund;

    @Schema(description = "支持在线充值类型 支付通模式才支持")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<PayChannel> supportPayChannel;
}
