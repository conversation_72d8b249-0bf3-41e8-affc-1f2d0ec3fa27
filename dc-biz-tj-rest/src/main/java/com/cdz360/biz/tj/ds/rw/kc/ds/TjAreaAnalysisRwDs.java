package com.cdz360.biz.tj.ds.rw.kc.ds;

import com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaAnalysisRwMapper;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaAnalysisRwDs {


    @Autowired
    private TjAreaAnalysisRwMapper tjAreaAnalysisRwMapper;

    public TjAreaAnalysisPo getById(Long id, boolean lock) {
        return this.tjAreaAnalysisRwMapper.getById(id, lock);
    }


    public boolean insertTjAreaAnalysis(TjAreaAnalysisPo tjAreaAnalysisPo) {
        return this.tjAreaAnalysisRwMapper.insertTjAreaAnalysis(tjAreaAnalysisPo) > 0;
    }


    public boolean updateTjAreaAnalysis(TjAreaAnalysisPo tjAreaAnalysisPo) {
        return this.tjAreaAnalysisRwMapper.updateTjAreaAnalysis(tjAreaAnalysisPo) > 0;
    }

    public boolean disableTjAreaAnalysis(Long analysisId) {
        return this.tjAreaAnalysisRwMapper.disableTjAreaAnalysis(analysisId) > 0;
    }
}

