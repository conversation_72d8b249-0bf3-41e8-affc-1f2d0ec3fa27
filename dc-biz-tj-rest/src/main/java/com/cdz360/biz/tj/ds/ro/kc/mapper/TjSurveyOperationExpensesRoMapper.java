package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyOperationExpensesRoMapper {

    TjSurveyOperationExpensesPo getById(@Param("id") Long id);

    TjSurveyOperationExpensesPo findTjSurveyOperationExpensesBySurveyNo(@Param("surveyNo") String surveyNo);

}

