package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveySupportingFacilitiesOtherRwMapper {

    TjSurveySupportingFacilitiesOtherPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveySupportingFacilitiesOther(TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo);

    int updateTjSurveySupportingFacilitiesOther(TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo);

    int disableTjSurveySupportingFacilitiesOtherBySurveyNo(String surveyNo);

}

