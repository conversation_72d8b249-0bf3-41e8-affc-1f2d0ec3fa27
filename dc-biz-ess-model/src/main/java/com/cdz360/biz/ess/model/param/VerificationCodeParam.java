package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.ess.model.type.UserAccountType;
import com.cdz360.biz.ess.model.utils.AccountUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Locale;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "验证码请求参数")
@Data
@Accessors(chain = true)
public class VerificationCodeParam {

    @NotNull
    @Schema(description = "用户账户类型", required = true)
    private UserAccountType type;

    @NotBlank
    @Schema(description = "用户账户", required = true)
    @Email // 支持email
    private String account;

    @Schema(description = "国家电话代码(账户类型是手机号必填)", format = "中国: 86",
        externalDocs = @ExternalDocumentation(
            description = "国家代码(Country Code)",
            url = "https://www.countrycode.org/"))
    private String nationalCode;

    @Schema(hidden = true)
    @JsonIgnore
    private Locale locale;

    public static void checkField(VerificationCodeParam data) {
        if (null == data.getType()) {
            throw new DcArgumentException("account.type.not.null");
        }
        switch (data.getType()) {
            case EMAIL:
                if (!AccountUtils.email()) {
                    throw new DcArgumentException("account.email.invalid");
                }
                break;
            case MOBILE:
                if (StringUtils.isBlank(data.getNationalCode())) {
                    throw new DcArgumentException("country.national.code.not.null");
                }
                if (!AccountUtils.phone()) {
                    throw new DcArgumentException("account.phone.invalid");
                }
                break;
            default:
                throw new DcArgumentException("account.type.invalid");
        }
    }
}
