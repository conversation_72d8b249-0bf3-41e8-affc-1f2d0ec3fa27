package com.cdz360.biz.ess.model.dto;

import com.cdz360.biz.ess.model.type.UserAccountType;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@Schema(description = "用户账户基础信息")
@Data
@Accessors(chain = true)
public class BaseUserAccount {

    @Schema(description = "顶级商户ID", example = "123", hidden = true)
    private Long topCommId;

    @NotNull
    @Schema(description = "用户账户类型", requiredMode = RequiredMode.REQUIRED)
    private UserAccountType type;

    //    @Email // 目前仅支持email
    @NotBlank
    @Schema(description = "用户账户", requiredMode = RequiredMode.REQUIRED)
    private String account;

    @NotBlank
    @Schema(description = "用户账户密码", requiredMode = RequiredMode.REQUIRED)
    private String passw;

    @Schema(description = "BCP 47 语言标签(注册时可填写移动端设备默认语言)",
        externalDocs = @ExternalDocumentation(
            description = "BCP 47 Language Tags",
            url = "https://www.rfc-editor.org/info/bcp47<br />"
                + "https://www.countrycode.org/"))
    private String language;

    @Schema(description = "国家地区代码(Alpha-3 code)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    private String countryCode;

    @JsonIgnore
    @Schema(hidden = true)
    // 密码hash化
    public void hashPassw() {
        this.hashPassw(account, passw);
    }

    @JsonIgnore
    @Schema(hidden = true)
    public void hashPassw(String account, String passw) {
        this.passw = this.getHashPassw(account, passw);
    }

    @JsonIgnore
    @Schema(hidden = true)
    public void hashPassw(String passw) {
        this.hashPassw(account, passw);
    }

    @JsonIgnore
    @Schema(hidden = true)
    public String getHashPassw() {
        return this.getHashPassw(account, passw);
    }

    @JsonIgnore
    @Schema(hidden = true)
    public String getHashPassw(String account, String passw) {
        final String tmp = topCommId + ":" + account + ":" + passw;
        log.info("hash password: {}", tmp);
        try {
            final MessageDigest instance = MessageDigest.getInstance("SHA-256");
            instance.update(tmp.getBytes(StandardCharsets.UTF_8));
            final byte[] digest = instance.digest();
            final StringBuilder builder = new StringBuilder();
            for (byte b : digest) {
                builder.append(String.format("%02x", b));
            }
            return builder.toString();
        } catch (Exception ex) {
            // nothing to do
            log.warn("密码加密异常: {}", ex.getMessage(), ex);
        }
        return tmp;
    }
}
