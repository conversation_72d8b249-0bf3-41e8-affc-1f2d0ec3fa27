package com.cdz360.biz.model.cus.post.po;


import com.cdz360.biz.model.cus.post.type.CusPostLevel;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;


@Data

@Accessors(chain = true)

@Schema(description = "用户评论")

public class CusPostPo {



	@Schema(description = "主键ID")

	@NotNull(message = "id 不能为 null")

	private Long id;



	@Schema(description = "订单号")

	@NotNull(message = "orderNo 不能为 null")

	@Size(max = 32, message = "orderNo 长度不能超过 32")

	private String orderNo;



	@Schema(description = "站点编号")

	@NotNull(message = "siteId 不能为 null")

	@Size(max = 32, message = "siteId 长度不能超过 32")

	private String siteId;



	@Schema(description = "用户ID")

	@NotNull(message = "uid 不能为 null")

	private Long uid;



	@Schema(description = "评论状态: SUBMITTED(待回复)), REPLIED(已回复)")

	@NotNull(message = "status 不能为 null")

	private CusPostStatus status;



	@Schema(description = "评论等级,, 范围(1~5)", format = "java.lang.Integer")

	@NotNull(message = "level 不能为 null")

	private CusPostLevel level;



	@Schema(description = "评论内容")

	private String cusContent;



	@Schema(description = "true公开，false不公开")

	@NotNull(message = "open 不能为 null")

	private Boolean open;



	@Schema(description = "回复内容")

	private String replyContent;


	@Schema(description = "标签")

	private List<String> tags;



	@Schema(description = "操作人ID. sys_user.id")

	@NotNull(message = "opUid 不能为 null")

	private Long opUid;

	@Schema(description = "操作人姓名")
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private String opName;

	@Schema(description = "创建时间")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date createTime;


	@Schema(description = "更新时间")
	@JsonFormat(shape = JsonFormat.Shape.NUMBER, pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date updateTime;

}

