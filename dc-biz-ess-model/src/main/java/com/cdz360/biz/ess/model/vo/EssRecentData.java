package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(name = "近期数据")
public class EssRecentData {
    @Schema(name = "当日数据")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal today;

    @Schema(name = "昨日数据")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal yesterday;

    @Schema(name = "本月数据")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal thisMonth;

    @Schema(name = "上月数据")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal lastMonth;

    @Schema(name = "总数")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal total;
}
