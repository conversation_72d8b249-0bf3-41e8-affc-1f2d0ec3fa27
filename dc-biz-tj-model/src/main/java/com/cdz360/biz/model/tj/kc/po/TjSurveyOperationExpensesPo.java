package com.cdz360.biz.model.tj.kc.po;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-运营支出")
public class TjSurveyOperationExpensesPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "勘察站的no")
    private String surveyNo;

    @ApiModelProperty(value = "服务费分成比例")
    private BigDecimal serviceFeeSharing;

    @ApiModelProperty(value = "服务费分成是否减引流费用")
    private Boolean serviceFeeExcludeAttract;

    @ApiModelProperty(value = "是否固定租金")
    private Boolean fixedRent;

    @ApiModelProperty(value = "每月租金费用")
    private BigDecimal rentFee;

    @ApiModelProperty(value = "每几年（增长）不固定租金必填")
    private BigDecimal everyFewYears;

    @ApiModelProperty(value = "（每几年）增长比例不固定租金必填")
    private BigDecimal increase;

    @ApiModelProperty(value = "运营其他支出费用")
    private BigDecimal otherFee;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

