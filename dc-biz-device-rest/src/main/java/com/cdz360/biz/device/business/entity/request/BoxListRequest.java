package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 设备列表查询
 *
 * <AUTHOR>
 * @since 2018年7月24日
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BoxListRequest extends BaseRequest {

    private static final long serialVersionUID = 1L;

    /**
     * 站点id
     */
    private String siteId;

    /**
     *
     */
    private String lastQueryTime;
    /**
     * 代理商ID
     */
    private Long commercialId;
    /**
     * 按设备id列表查询
     */
    private List<String> deviceIdList;


    /**
     * 商户ID集合
     */
    private List<Long> commercialIds;

    /**
     * 是否使用场站默认桩配置 1是0否
     */
    private Integer isUseSiteDefaultSetting;

}
