package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取投建分析列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjAreaAnalysisParam extends BaseListParam {

    @Schema(description = "场站组唯一ID列表")
    @JsonInclude(Include.NON_EMPTY)
    private List<String> gidList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
