package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskDetailRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskInfoRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskListRequest;
import com.cdz360.biz.device.business.entity.request.UpgradeTaskRequest;
import com.cdz360.biz.device.business.entity.result.EvsePasscodePo;
import com.cdz360.biz.device.business.entity.result.EvseVo;
import feign.codec.Encoder;
import feign.form.spring.SpringFormEncoder;
import java.util.List;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.cloud.openfeign.support.SpringEncoder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_DEVICE_MGM, fallbackFactory = HystrixIotDeviceMgmFactory.class, configuration = IotDeviceMmgClient.FeignMultipartSupportConfig.class)
public interface IotDeviceMmgClient {
//    @GetMapping(value = "/iot/bi/device")
//    ObjectResponse<DeviceBi> getDeviceBi(@RequestParam(value = "siteId", required = false) String siteId);

//    /**
//     * @Description: 桩升级包上传
//     * @Author: JLei
//     * @CreateDate: 10:12 2019/9/20
//     */
//    @PostMapping(value = "/device/mgm/evsebundle/uploadEvseBundle", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
//    ObjectResponse<Long> uploadEvseBundle(
//            @RequestPart("file") MultipartFile file,
//            @RequestParam("token") String token);

//    /**
//     * @Description: 桩升级包分页查询
//     * @Author: JLei
//     * @CreateDate: 10:11 2019/9/20
//     */
//    @PostMapping("/device/mgm/evsebundle/listEvseBundlePage")
//    ListResponse<EvseBundleDto> evseBundlePage(@RequestBody EvseBundleParam evseBundleParam);

//    /**
//     * @Description: 桩升级包删除
//     * @Author: JLei
//     * @CreateDate: 10:10 2019/9/20
//     */
//    @GetMapping("/device/mgm/evsebundle/deleteEvseBundle")
//    BaseResponse deleteEvseBundle(@RequestParam("id") Long id);

    @PostMapping("/device/mgm/evse/selectByEvseIds")
    ListResponse<EvseVo> selectByEvseIds(@RequestBody List<String> evseIds);

    /**
     * @Description: 更新长效秘钥，更新成功返回最新版本号,为空表示最新版本秘钥未更改
     * @Author: JLei
     * @CreateDate: 14:29 2019/11/13
     */
    @GetMapping(value = "/device/mgm/evse/updateEvsePasscode")
    ObjectResponse<Long> updateEvsePasscode(@RequestParam(value = "evseNo") String evseNo,
        @RequestParam(value = "passcode") String passcode);

    /**
     * @Description: 获取长效秘钥，版本号为空获取最新
     * @Author: JLei
     * @CreateDate: 14:29 2019/11/13
     */
    @GetMapping(value = "/device/mgm/evse/getEvsePasscode")
    ObjectResponse<EvsePasscodePo> getEvsePasscode(@RequestParam(value = "evseNo") String evseNo,
        @RequestParam(value = "ver", required = false) Long ver);

    /**
     * 桩升级记录相关
     */
    /**
     * 分页获取 获取场站下升级记录列表
     *
     * @return
     */
    @PostMapping("/device/upgrade/getUpgradeTaskListBySite")
    ListResponse getUpgradeTaskListBySite(
        @RequestBody UpgradeTaskListRequest updateTaskListRequest);

    /**
     * 获取升级记录详情列表，不分页
     *
     * @return
     */
    @PostMapping("/device/upgrade/getUpgradeTaskDetailListByTaskId")
    ListResponse getUpgradeTaskDetailListByTaskId(
        @RequestBody UpgradeTaskDetailRequest updateTaskDetailRequest);

    @PostMapping("/device/upgrade/startTask")
    BaseResponse startTask(@RequestBody UpgradeTaskRequest upgradeTaskRequest);

    @PostMapping("/device/upgrade/getUpgradeTaskInfo")
    ObjectResponse getUpgradeTaskInfo(@RequestBody UpgradeTaskInfoRequest upgradeTaskInfoRequest);


    //文件上传编码器
    class FeignMultipartSupportConfig {

        @Autowired
        private ObjectFactory<HttpMessageConverters> messageConverters;

        @Bean
        public Encoder feignFormEncoder() {
            return new SpringFormEncoder(new SpringEncoder(messageConverters));
        }
    }

}
