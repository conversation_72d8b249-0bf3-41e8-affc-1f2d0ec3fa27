package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "物料成本类型")
@Getter
public enum CashOutflowType implements DcEnum {

    UNKNOWN(999, "未知"),

    CASH_MAINT(1, "运维费用"),
    CASH_FLOW(2, "引流费用"),
    CASH_PLATFORM(3, "平台托管费用"),
    CASH_ELE_LOSS(4, "电损支出"),
    CASH_CHANNEL(5, "通道费"),
    CASH_FINANCE(6, "财务费用"),
    CASH_MANAGE(7, "管理费用"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    CashOutflowType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static CashOutflowType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (CashOutflowType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return CashOutflowType.UNKNOWN;
    }

}
