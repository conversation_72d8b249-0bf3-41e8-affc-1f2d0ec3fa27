package com.cdz360.biz.model.cus.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充电限制，默认策略")
public class SiteSocStrategyPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "开始时间,闭区间,最小0,单位分钟")
	@NotNull(message = "startTime 不能为 null")
	private Integer startTime;

	@Schema(description = "开始时间,闭区间,00:00")
	@NotNull(message = "startTime 不能为 null")
	private String startTimeStr;

	@Schema(description = "结束时间,开区间,最大1440,单位分钟")
	@NotNull(message = "endTime 不能为 null")
	private Integer endTime;

	@Schema(description = "结束时间,开区间,24:00")
	@NotNull(message = "endTime 不能为 null")
	private String endTimeStr;

	@Schema(description = "允许充电")
	@NotNull(message = "allow 不能为 null")
	private Boolean allow;

	@Schema(description = "限制的最大SOC")
	private Integer soc;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;


}
