package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 仅充电相关的桩相关数据接口
 */
@Component
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_TRADING, fallbackFactory = BoxTradingFeignClientHystrix.class)
public interface BoxTradingFeignClient {

    @GetMapping("/api/box/getBoxSetting")
    ObjectResponse<BsBoxSettingPo> getBoxSetting(@RequestParam("evseNo") String evseNo);

}
