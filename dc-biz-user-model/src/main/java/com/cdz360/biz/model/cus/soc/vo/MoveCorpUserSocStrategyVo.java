//package com.cdz360.biz.model.cus.soc.vo;
//
//import com.cdz360.biz.model.trading.soc.po.UserSocStrategyPo;
//import lombok.Data;
//import lombok.EqualsAndHashCode;
//import lombok.experimental.Accessors;
//
///**
// * MoveCorpUserSocStrategyVo
// *
// * @since 10/28/2020 3:24 PM
// * <AUTHOR>
// */
//@Data
//@EqualsAndHashCode(callSuper = true)
//@Accessors(chain = true)
//public class MoveCorpUserSocStrategyVo extends UserSocStrategyPo {
//    private String idChain;
//    private String siteName;
//    private String siteId;
//}