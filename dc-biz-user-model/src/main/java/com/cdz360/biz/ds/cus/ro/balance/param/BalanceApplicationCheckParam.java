package com.cdz360.biz.ds.cus.ro.balance.param;

import com.cdz360.biz.model.FileItem;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * BalanceApplicationCheckParam
 *
 * @since 7/2/2021 2:39 PM
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BalanceApplicationCheckParam extends BalanceApplicationCheckPo {

    @Schema(description = "申请id列表")
    private List<Long> applicationIds;

    @Schema(description = "附件链接列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<FileItem> attachmentList;
}