package com.cdz360.biz.device.common.constant;

import com.cdz360.base.model.base.type.PlugStatus;
import lombok.Getter;

/**
 * 充电接口状态枚举
 *
 * <AUTHOR>
 * @date Create on 2018/7/5 23:01
 */
@Getter
public enum ConnectorStatusEnum {

    /**
     * 设备在线-充电接口空闲
     */
    STATUS_ONLINE_IDLE(10, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备在线-充电接口充前占用
     */
    STATUS_ONLINE_OCCUPY(12, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备在线-充电接口充完占用
     */
    STATUS_ONLINE_OCCUPY_COMPLETE(31, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备在线-充电接口充电中
     */
    STATUS_ONLINE_CHARGING(100, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备在线-充电接口充电中(辅枪)
     */
    STATUS_ONLINE_CHARGING_JOIN(101, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备在线-充电接口故障
     */
    STATUS_ONLINE_BREAK_DOWN(-100, DeviceStatusEnum.STATUS_ONLINE),
    /**
     * 设备离线-充电接口离线<br>
     * 枪头没有自己的离线状态，离线状态是从充电桩映射过来的
     */
    @Deprecated
    STATUS_OFFLINE(0, DeviceStatusEnum.STATUS_OFFLINE),
    //
    ;

    /**
     * 充电接口状态对应的状态码
     */
    private int code;
    /**
     * 充电接口状态对应的设备状态的状态码
     */
    private int deviceStatusCode;

    ConnectorStatusEnum(int code, DeviceStatusEnum deviceStatusEnum) {
        this.code = code;
        this.deviceStatusCode = deviceStatusEnum.getCode();
    }

    public static ConnectorStatusEnum convertFromPlugStatus(PlugStatus plugStatus) {
        if(plugStatus == PlugStatus.IDLE) {
            return ConnectorStatusEnum.STATUS_ONLINE_IDLE;
        }else if(plugStatus == PlugStatus.BUSY) {
            return ConnectorStatusEnum.STATUS_ONLINE_CHARGING;
        }else if(plugStatus == PlugStatus.CONNECT) {
            return ConnectorStatusEnum.STATUS_ONLINE_OCCUPY;
        }
        else if(plugStatus == PlugStatus.RECHARGE_END) {
            return ConnectorStatusEnum.STATUS_ONLINE_OCCUPY_COMPLETE;
        }else if(plugStatus == PlugStatus.ERROR) {
            return ConnectorStatusEnum.STATUS_ONLINE_BREAK_DOWN;
        }else {
            return ConnectorStatusEnum.STATUS_OFFLINE;
        }
    }
}
