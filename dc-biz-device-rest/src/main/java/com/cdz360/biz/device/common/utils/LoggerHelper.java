package com.cdz360.biz.device.common.utils;

import jakarta.servlet.http.HttpServletRequest;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Set;

public class LoggerHelper {
    private static final Set<String> IGNORE_PARAMS = new HashSet<>();

    private LoggerHelper() {

    }

    public static String formatEnterLog(HttpServletRequest request) {
        return ">> " + LoggerHelper.getUserLog(request, true);
    }

//    public static String formatEnterLog(HttpServletRequest request, boolean logParams) {
//        return ">> " + LoggerHelper.getUserLog(request, logParams);
//    }
//
//    public static String formatLeaveLog(HttpServletRequest request) {
//        return "<< " + LoggerHelper.getUserLog(request, false);
//    }

    private static String getUserLog(HttpServletRequest request, boolean logParams) {
        String uid = request.getParameter("u");
        //String channel = request.getParameter("c");
        String versionCode = request.getParameter("v");
        //String osType = request.getParameter("o");

        StringBuilder sb = new StringBuilder();
        sb//.append("( channel = ")
                //.append(channel)
                .append("( uid = ").append(uid).append(", ver = ").append(versionCode).append(" ) ");

        if (!logParams) {
            return sb.toString();
        }
        sb.append("params = { ");
        boolean firstKey = true;
        Iterator<String> iter = request.getParameterMap().keySet().iterator();
        while (iter.hasNext()) {
            String k = iter.next();
            if (IGNORE_PARAMS.contains(k)) {
                continue;
            }

            if (firstKey) {
                firstKey = false;
            } else {
                sb.append(", ");
            }

            sb.append(k).append("=[");
            boolean firstValue = true;
            for (String v : request.getParameterMap().get(k)) {
                if (firstValue) {
                    firstValue = false;
                } else {
                    sb.append(",");
                }
                sb.append(v);
            }
            sb.append("]");
        }
        sb.append(" } ");

        return sb.toString();

    }
}
