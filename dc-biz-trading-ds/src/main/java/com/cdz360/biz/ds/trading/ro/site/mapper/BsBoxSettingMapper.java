package com.cdz360.biz.ds.trading.ro.site.mapper;

import com.cdz360.biz.model.trading.site.po.BsBoxSettingPo;
import com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo;
import com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface BsBoxSettingMapper {

    BsBoxSettingPo getOneByEvseNo(@Param(value = "evseNo") String evseNo);

    List<BsBoxSettingPo> getByEvseNo(
            @Param(value = "evseNoList") List<String> evseNoList,
            @Param(value = "statusList") List<Integer> statusList,
            @Param("lock") boolean lock);

    List<PriceSchemeSiteVo> getSchemeSiteUsed(ListPriceSchemeSiteUseParam param);

    int insertOrUpdate(BsBoxSettingPo po);

    int batchUpdate(@Param(value = "poList") List<BsBoxSettingPo> poList);

    int batchInsert(@Param(value = "poList") List<BsBoxSettingPo> poList);

    int delBoxSetting(@Param("evseNo") String evseNo);
}