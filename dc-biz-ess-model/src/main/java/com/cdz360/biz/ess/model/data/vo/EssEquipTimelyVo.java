package com.cdz360.biz.ess.model.data.vo;

import com.cdz360.base.model.es.type.ChargeFlowType;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能设备分时段明细数据")
@Data
@Accessors(chain = true)
public class EssEquipTimelyVo {

    @Schema(description = "储能设备名称")
    private String essName;

    @Schema(description = "对象设备名称")
    private String equipName;

    @ApiModelProperty(value = "时段开始时间")
    @NotNull(message = "startTime 不能为 null")
    private LocalDateTime startTime;

    @ApiModelProperty(value = "时段结束时间")
    @NotNull(message = "endTime 不能为 null")
    private LocalDateTime endTime;

    @ApiModelProperty(value = "储能设备dno")
    @NotNull(message = "essDno 不能为 null")
    @Size(max = 32, message = "essDno 长度不能超过 32")
    private String essDno;

    @ApiModelProperty(value = "设备序号,一般为pcs或电表dno")
    @NotNull(message = "dno 不能为 null")
    @Size(max = 32, message = "dno 长度不能超过 32")
    private String dno;

    @ApiModelProperty(value = "场站ID，记录数据时储能设备对应的场站ID")
    @Size(max = 32, message = "siteId 长度不能超过 32")
    private String siteId;

    @ApiModelProperty(value = "时段电价名称,如: 尖、锋、平、谷")
    @Size(max = 32, message = "piName 长度不能超过 32")
    private String piName;

    @ApiModelProperty(value = "时段充放电设定. 0未知,1充电,2放电,3待机")
    private ChargeFlowType flowType;

    @ApiModelProperty(value = "时段内正向电量, 单位kwh")
    private BigDecimal inKwh;

    @ApiModelProperty(value = "时段内反向电量, 单位kwh")
    private BigDecimal outKwh;

    @ApiModelProperty(value = "正向电量计费金额,单位: 元")
    private BigDecimal inFee;

    @ApiModelProperty(value = "反向电量计费金额,单位: 元")
    private BigDecimal outFee;

    @ApiModelProperty(value = "正向电量计费模板ID(t_template.id)")
    private Long inPriceId;

    @ApiModelProperty(value = "反向电量计费模板ID(t_template.id)")
    private Long outPriceId;

    @ApiModelProperty(value = "总正向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalInKwh;

    @ApiModelProperty(value = "总反向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalOutKwh;

}
