package com.cdz360.biz.model.tj.kc.vo;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import io.swagger.annotations.ApiModel;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站智能测算结果")
public class TjSurveyCalculationResultVo {

    @Schema(description = "预测方法")
    String calculationMethod;

    @Schema(description = "预测结果")
    String calculationResult;
}

