<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationCoefficientRwMapper">

  <resultMap id="RESULT_TJ_DAILY_CHARGING_DURATION_COEFFICIENT_PO" type="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="optimisticCoefficient" jdbcType="DECIMAL" property="optimisticCoefficient"/>
    <result column="pessimisticCoefficient" jdbcType="DECIMAL" property="pessimisticCoefficient"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_DAILY_CHARGING_DURATION_COEFFICIENT_PO">
    select * from t_tj_daily_charging_duration_coefficient where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <update id="updateTjDailyChargingDurationCoefficient" parameterType="com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo">
    update t_tj_daily_charging_duration_coefficient set
    <if test="optimisticCoefficient != null">
      optimisticCoefficient = #{optimisticCoefficient},
    </if>
    <if test="pessimisticCoefficient != null">
      pessimisticCoefficient = #{pessimisticCoefficient},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

</mapper>

