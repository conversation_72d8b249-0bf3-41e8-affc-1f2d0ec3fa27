package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.po.Commercial;
import org.springframework.cloud.openfeign.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Classname HystrixMerchantClientFactory
 * @Description TODO
 * @Date 2019/6/17 15:21
 * @Created by Rafael
 */
@Slf4j
@Component
public class HystrixMerchantClientFactory implements FallbackFactory<MerchantFeignClient> {

    @Override
    public MerchantFeignClient create(Throwable throwable) {
        return new MerchantFeignClient() {
//            @Override
//            public ListResponse<Long> getCommIdListByToken(String token) {
//                return null;
//            }

//            @Override
//            public ListResponse<Long> getCommIdListByCommId(Long commId) {
//                return null;
//            }

            @Override
            public ObjectResponse<Commercial> getCommercialByToken(String token) {
                return null;
            }
        };
    }
}