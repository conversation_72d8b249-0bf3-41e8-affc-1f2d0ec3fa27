package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjCashOutflowRwMapper {

    TjCashOutflowPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjCashOutflow(TjCashOutflowPo tjCashOutflowPo);

    int updateTjCashOutflow(TjCashOutflowPo tjCashOutflowPo);

    int disableTjCashOutflow(@Param("id") Long id);

}

