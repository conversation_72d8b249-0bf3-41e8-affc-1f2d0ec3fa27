package com.cdz360.biz.model.cus.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户充电限制策略,用户关系")
public class UserSocStrategyPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	/**
	 * 关联: d_card_manager.t_r_bloc_user.id
	 */
	@NotNull(message = "userId 不能为 null")
	private Long userId;

	@Schema(description = "用户充电限制策略id,对应t_soc_main_strategy的id")
	@NotNull(message = "strategyId 不能为 null")
	private Long strategyId;

	@Schema(description = "vin码id，对应d_card_manager.t_vin.id")
	private Long vinId;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;


}
