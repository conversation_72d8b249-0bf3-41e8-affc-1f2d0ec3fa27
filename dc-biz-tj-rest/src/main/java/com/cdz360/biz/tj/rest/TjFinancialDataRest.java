package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.sim.vo.SimImportParam;
import com.cdz360.biz.model.sim.vo.SimImportVo;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.param.TjDailyChargingDurationImportParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.vo.ImportTjDailyChargingDurationVo;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationImportItem;
import com.cdz360.biz.model.tj.kc.vo.TjDailyChargingDurationVo;
import com.cdz360.biz.tj.service.kc.TjCashOutflowService;
import com.cdz360.biz.tj.service.kc.TjDailyChargingDurationService;
import io.swagger.annotations.ApiParam;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "财务数据相关操作接口", description = "财务数据相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/financialData")
public class TjFinancialDataRest {

    @Autowired
    private TjDailyChargingDurationService tjDailyChargingDurationService;

    @Autowired
    private TjCashOutflowService tjCashOutflowService;

    @Operation(summary = "获取日充电时长")
    @PostMapping(value = "/findTjDailyChargingDuration")
    public Mono<ListResponse<TjDailyChargingDurationPo>> findTjDailyChargingDuration(@RequestBody ListTjDailyChargingDurationParam param) {
        log.info("获取日充电时长: param = {}", JsonUtils.toJsonString(param));
        return tjDailyChargingDurationService.findTjDailyChargingDuration(param);
    }

    @Operation(summary = "获取日充电时长")
    @GetMapping(value = "/getTjDailyChargingDurationById")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> getTjDailyChargingDurationById(
        @ApiParam("日充电时长唯一ID") @RequestParam("id") Long id) {
        log.info("获取日充电时长: {}", id);
        return tjDailyChargingDurationService.getTjDailyChargingDurationById(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新建或编辑日充电时长")
    @PostMapping(value = "/saveTjDailyChargingDuration")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> saveTjDailyChargingDuration(@RequestBody TjDailyChargingDurationPo tjDailyChargingDurationPo) {
        log.info("保存日充电时长: param = {}", JsonUtils.toJsonString(tjDailyChargingDurationPo));
        return tjDailyChargingDurationService.saveTjDailyChargingDuration(tjDailyChargingDurationPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除日充电时长")
    @GetMapping(value = "/disableTjDailyChargingDuration")
    public Mono<ObjectResponse<TjDailyChargingDurationPo>> disableTjDailyChargingDuration(
        @ApiParam("日充电时长唯一ID") @RequestParam("id") Long id) {
        log.info("删除日充电时长: {}", id);
        return tjDailyChargingDurationService.disableTjDailyChargingDuration(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取日充电时长乐悲观指数")
    @GetMapping(value = "/findTjDailyChargingDurationCoefficient")
    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> findTjDailyChargingDurationCoefficient() {
        return tjDailyChargingDurationService.findTjDailyChargingDurationCoefficient()
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改乐悲观指数")
    @PostMapping(value = "/saveTjDailyChargingDurationCoefficient")
    public Mono<ObjectResponse<TjDailyChargingDurationCoefficientPo>> saveTjDailyChargingDurationCoefficient(@RequestBody TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo) {
        log.info("保存修改乐悲观指数: param = {}", JsonUtils.toJsonString(tjDailyChargingDurationCoefficientPo));
        return tjDailyChargingDurationService.saveTjDailyChargingDurationCoefficient(tjDailyChargingDurationCoefficientPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "导入日充电时长(EXCEL)")
    @PostMapping("/importTjDailyChargingDurationExcel")
    public Mono<ObjectResponse<ImportTjDailyChargingDurationVo<TjDailyChargingDurationVo>>> importTjDailyChargingDurationExcel(@RequestBody List<TjDailyChargingDurationImportItem> dataList) {
        log.info("导入日充电时长: size = {}", dataList.size());
        return tjDailyChargingDurationService.importTjDailyChargingDurationExcel(dataList);
    }


    @Operation(summary = "获取设备折旧配置")
    @GetMapping(value = "/findTjDepreciation")
    public Mono<ObjectResponse<TjDepreciationPo>> findTjDepreciation() {
        return tjCashOutflowService.findTjDepreciation()
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "修改设备折旧配置")
    @PostMapping(value = "/saveTjDepreciation")
    public Mono<ObjectResponse<TjDepreciationPo>> saveTjDepreciation(@RequestBody TjDepreciationPo tjDepreciationPo) {
        log.info("修改设备折旧配置: param = {}", JsonUtils.toJsonString(tjDepreciationPo));
        return tjCashOutflowService.saveTjDepreciation(tjDepreciationPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "获取通用现金流出配置")
    @PostMapping(value = "/findTjCashOutflow")
    public Mono<ListResponse<TjCashOutflowPo>> findTjCashOutflow(@RequestParam("type") Integer type) {
        log.info("获取通用现金流出配置: type = {}", type);
        return tjCashOutflowService.findTjCashOutflow(type);
    }

    @Operation(summary = "获取通用现金流出配置")
    @GetMapping(value = "/getTjCashOutflowById")
    public Mono<ObjectResponse<TjCashOutflowPo>> getTjCashOutflowById(
        @ApiParam("通用现金流出配置唯一ID") @RequestParam("id") Long id) {
        log.info("获取通用现金流出配置: id = {}", id);
        return tjCashOutflowService.getTjCashOutflowById(id)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新建或编辑通用现金流出配置")
    @PostMapping(value = "/saveTjCashOutflow")
    public Mono<ObjectResponse<TjCashOutflowPo>> saveTjCashOutflow(@RequestBody TjCashOutflowPo tjCashOutflowPo) {
        log.info("新建或编辑通用现金流出配置: param = {}", JsonUtils.toJsonString(tjCashOutflowPo));
        return tjCashOutflowService.saveTjCashOutflow(tjCashOutflowPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "删除通用现金流出配置")
    @GetMapping(value = "/disableTjCashOutflow")
    public Mono<ObjectResponse<TjCashOutflowPo>> disableTjCashOutflow(
        @ApiParam("通用现金流出配置唯一ID") @RequestParam("id") Long id) {
        log.info("删除通用现金流出配置: {}", id);
        return tjCashOutflowService.disableTjCashOutflow(id)
            .map(RestUtils::buildObjectResponse);
    }
}
