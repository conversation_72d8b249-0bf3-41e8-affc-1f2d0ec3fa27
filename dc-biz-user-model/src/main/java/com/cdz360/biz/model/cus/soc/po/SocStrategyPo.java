package com.cdz360.biz.model.cus.soc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "用户充电限制策略(模板)")
public class SocStrategyPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	private Long mainId;

	private List<Integer> enableDays;

	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	private Long corpId;

	private String name;

	@Schema(description = "允许充电")
	@NotNull(message = "allow 不能为 null")
	private Boolean allow;

	@Schema(description = "限制的最大SOC")
	@NotNull(message = "soc 不能为 null")
	private Integer soc;

	@NotNull(message = "createTime 不能为 null")
	private Date createTime;

	private Date updateTime;


}
