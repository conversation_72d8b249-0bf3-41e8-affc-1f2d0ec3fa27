package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "竞争者")
public class TjCompetitorVo {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "竞争对手名称")
    @NotNull(message = "name 不能为 null")
    private String name;

    @Schema(description = "标记图标")
    private String logo;

}
