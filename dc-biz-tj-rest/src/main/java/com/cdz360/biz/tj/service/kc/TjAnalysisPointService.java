package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaAnalysisPointRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaAnalysisRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaAnalysisPointRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaAnalysisRwDs;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAnalysisPointService {

    @Autowired
    private TjAreaRoDs tjAreaRoDs;

    @Autowired
    private TjAreaAnalysisRoDs tjAreaAnalysisRoDs;

    @Autowired
    private TjAreaAnalysisRwDs tjAreaAnalysisRwDs;

    @Autowired
    private TjAreaAnalysisPointRwDs tjAreaAnalysisPointRwDs;

    @Autowired
    private TjAreaAnalysisPointRoDs tjAreaAnalysisPointRoDs;

    public Mono<ListResponse<TjAreaAnalysisPointVo>> findTjAnalysisPoint(
        ListTjAreaAnalysisPointParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjAreaAnalysisPointRoDs::findTjAnalysisPoint)
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjAreaAnalysisPointRoDs.countTjAnalysisPoint(param) : 0L));
    }

    public Mono<ListResponse<TjAreaAnalysisPointWithSiteVo>> findTjAnalysisPointWithSite(
        ListTjAreaAnalysisPointParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjAreaAnalysisPointRoDs::findTjAnalysisPointWithSite)
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjAreaAnalysisPointRoDs.countTjAnalysisPoint(param) : 0L));
    }

    public Mono<Long> updateTjAnalysisPointStatus(
        UpdateAnalysisPointStatusParam param) {
        IotAssert.isNotNull(param.getAnalysisId(), "投建区域ID不能为空");
        IotAssert.isNotNull(param.getStatus(), "点位辐射区状态不能为空");
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getNumList()),
            "需求变更标号列表不能为空");
        TjAreaAnalysisPo analysisPo = tjAreaAnalysisRoDs.getById(param.getAnalysisId());
        IotAssert.isNotNull(analysisPo, "投建区域ID无效");
        return Mono.just(param)
            .map(tjAreaAnalysisPointRwDs::updateTjAnalysisPointStatus);
    }
}
