package com.cdz360.biz.tj.ds.ro.kc.mapper;


import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjAreaAnalysisPointRoMapper {

    List<TjAreaAnalysisPointPo> selectByAnalysisId(@Param("analysisId") Long analysisId);

    List<TjAreaAnalysisPointVo> findTjAnalysisPoint(ListTjAreaAnalysisPointParam param);

    List<TjAreaAnalysisPointWithSiteVo> findTjAnalysisPointWithSite(
        ListTjAreaAnalysisPointParam param);

    long countTjAnalysisPoint(ListTjAreaAnalysisPointParam param);
}

