package com.chargerlinkcar.framework.common.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.param.AddCommercialParam;
import com.cdz360.biz.model.merchant.param.ConvertCommPayParam;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.param.UpdateCommercialParam;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.domain.CommercialManage;
import com.chargerlinkcar.framework.common.domain.TCommercialUser;
import com.chargerlinkcar.framework.common.domain.vo.NotifyPay;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_AUTH, fallbackFactory = CommercialFeignClientHystrix.class)
public interface CommercialFeignClient {


    @GetMapping("/api/comm/getCommercial")
    @Operation(summary = "获取商户信息")
    ObjectResponse<Commercial> getCommercial(@RequestParam(value = "commId") Long commId);

    /**
     * 切换企业在线充值
     *
     * @param commId
     * @return
     */
    @GetMapping("/api/comm/convertCommCorpDeposit")
    ObjectResponse<Commercial> convertCommCorpDeposit(@RequestParam(value = "commId") Long commId);

    /**
     * 切换商户在线充值，在线退款
     *
     * @param param
     * @return
     */
    @PostMapping("/api/comm/convertCommPayAndRefund")
    ObjectResponse<Commercial> convertCommPayAndRefund(@RequestBody ConvertCommPayParam param);

    /**
     * 切换企业在线退款
     *
     * @param commId
     * @return
     */
    @GetMapping("/api/comm/convertCommCorpRefund")
    ObjectResponse<Commercial> convertCommCorpRefund(@RequestParam(value = "commId") Long commId);

    /**
     * 切换企业在线退款
     *
     * @param commId
     * @return
     */
    @GetMapping("/api/comm/convertCommRefund")
    ObjectResponse<Commercial> convertCommRefund(@RequestParam(value = "commId") Long commId);


    @GetMapping("/api/comm/getCommTree")
    ObjectResponse<CommercialTreeNode> getCommTree(@RequestParam(value = "commId", required = false) Long commId);

    /**
     * 新增商户
     */
    @PostMapping("/api/comm/addComm")
    ObjectResponse<Long> addComm(@RequestBody AddCommercialParam param);

    /**
     * 海外版新增商户
     */
    @PostMapping("/api/comm/commercial/addComm")
    ObjectResponse<Long> addEssComm(@RequestBody AddCommercialParam param);

    @GetMapping("/api/comm/getPlatInfo")
    ObjectResponse<Commercial> getPlatInfo(@RequestParam(value = "merchants") String merchants);

    /**
     * 更新商户信息
     */
    @PostMapping("/api/comm/updateCommInfo")
    BaseResponse updateCommInfo(@RequestBody UpdateCommercialParam param);

    /**
     * 海外版更新商户信息
     */
    @PostMapping("/api/comm/commercial/updateCommInfo")
    BaseResponse updateEssCommInfo(@RequestBody UpdateCommercialParam param);

    /**
     * 获取下属（含自己）商户ID列表
     *
     * @param commIdChain
     * @return 下属（含自己）商户ID列表
     */
    @GetMapping("/api/comm/getSubCommIdList")
    ListResponse<Long> getSubCommIdList(@RequestParam(value = "commIdChain") String commIdChain);


    @GetMapping("/api/commercialManage/getCommercialManage")
    ObjectResponse<CommercialManage> getCommercialManage(@RequestParam(value = "topCommId") Long topCommId);

    /**
     * 同步图片修改
     *
     * @param merchants 商户号
     * @param logo      商户logo
     * @return
     */
    @PostMapping("/api/comm/updateLogoByMerchants")
    BaseResponse updateLogoByMerchants(@RequestParam(value = "merchants") String merchants,
                                       @RequestParam(value = "logo") String logo,
                                       @RequestParam(value = "platformName") String platformName);


    @PostMapping("/api/comm/getCommList")
        // @Operation(summary = "获取商户信息列表")
    ListResponse<CommercialDto> getCommList(@RequestBody ListCommercialParam param);

    @PostMapping("/api/commercials/user/add")
    BaseResponse addCommercialsUser(@RequestHeader("token") String token, @RequestBody TCommercialUser tcu);

    @PostMapping("/api/sub/chargeNotify")
    BaseResponse chargeNotify(@RequestBody NotifyPay params);
}
