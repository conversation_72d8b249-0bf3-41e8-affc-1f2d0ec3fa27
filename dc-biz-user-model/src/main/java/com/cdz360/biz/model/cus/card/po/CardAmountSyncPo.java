package com.cdz360.biz.model.cus.card.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "充值余额同步记录")
public class CardAmountSyncPo {

	@NotNull(message = "id 不能为 null")
	private Long id;

	@Schema(description = "逻辑卡号")
	@NotNull(message = "cardNo 不能为 null")
	@Size(max = 16, message = "cardNo 长度不能超过 16")
	private String cardNo;

	@Schema(description = "同步金额")
	@NotNull(message = "amount 不能为 null")
	private BigDecimal amount;

	@Schema(description = "可用金额")
	@NotNull(message = "available 不能为 null")
	private BigDecimal available;

	@Schema(description = "操作者userId")
	@NotNull(message = "opUserId 不能为 null")
	private Long opUserId;

	@Schema(description = "同步结果，成功0，失败1")
	private Integer result;

	@NotNull(message = "createTime 不能为 null")
	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date createTime;

	@JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", locale = "zh", timezone = "GMT+8")
	private Date updateTime;


}
