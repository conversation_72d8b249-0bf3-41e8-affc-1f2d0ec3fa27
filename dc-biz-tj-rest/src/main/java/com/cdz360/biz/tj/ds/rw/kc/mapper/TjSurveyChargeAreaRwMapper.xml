<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyChargeAreaRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_CHARGE_AREA_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="transformerToChargeCable" jdbcType="INTEGER"
      property="transformerToChargeCable"/>
    <result column="transformerToChargeCableDistance" jdbcType="INTEGER"
      property="transformerToChargeCableDistance"/>
    <result column="transformerToChargeCableType" jdbcType="INTEGER"
      property="transformerToChargeCableType"/>
    <result column="chargeCableDistance" jdbcType="INTEGER" property="chargeCableDistance"/>
    <result column="masterToTerminalDistance" jdbcType="INTEGER"
      property="masterToTerminalDistance"/>
    <result column="chargeCableType" jdbcType="INTEGER" property="chargeCableType"/>
    <result column="galvanizedCableTrayDistance" jdbcType="INTEGER"
      property="galvanizedCableTrayDistance"/>
    <result column="ordinarySoilExcavationDistance" jdbcType="INTEGER"
      property="ordinarySoilExcavationDistance"/>
    <result column="pedestrianWalkwayExcavationDistance" jdbcType="INTEGER"
      property="pedestrianWalkwayExcavationDistance"/>
    <result column="concreteExcavationDistance" jdbcType="INTEGER"
      property="concreteExcavationDistance"/>
    <result column="evseBaseNeed" jdbcType="BOOLEAN" property="evseBaseNeed"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PO">
    select * from t_tj_survey_charge_area where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveyChargeArea" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo">
    insert into t_tj_survey_charge_area (`surveyNo`,
    <if test="null != transformerToChargeCable">
      `transformerToChargeCable`,
    </if>
    <if test="null != transformerToChargeCableDistance">
      `transformerToChargeCableDistance`,
    </if>
    <if test="null != transformerToChargeCableType">
      `transformerToChargeCableType`,
    </if>
    <if test="null != chargeCableDistance">
      `chargeCableDistance`,
    </if>
    <if test="null != masterToTerminalDistance">
      `masterToTerminalDistance`,
    </if>
    <if test="null != chargeCableType">
      `chargeCableType`,
    </if>
    <if test="null != galvanizedCableTrayDistance">
      `galvanizedCableTrayDistance`,
    </if>
    <if test="null != ordinarySoilExcavationDistance">
      `ordinarySoilExcavationDistance`,
    </if>
    <if test="null != pedestrianWalkwayExcavationDistance">
      `pedestrianWalkwayExcavationDistance`,
    </if>
    <if test="null != concreteExcavationDistance">
      `concreteExcavationDistance`,
    </if>
    <if test="null != evseBaseNeed">
      `evseBaseNeed`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != transformerToChargeCable">
      #{transformerToChargeCable},
    </if>
    <if test="null != transformerToChargeCableDistance">
      #{transformerToChargeCableDistance},
    </if>
    <if test="null != transformerToChargeCableType">
      #{transformerToChargeCableType},
    </if>
    <if test="null != chargeCableDistance">
      #{chargeCableDistance},
    </if>
    <if test="null != masterToTerminalDistance">
      #{masterToTerminalDistance},
    </if>
    <if test="null != chargeCableType">
      #{chargeCableType},
    </if>
    <if test="null != galvanizedCableTrayDistance">
      #{galvanizedCableTrayDistance},
    </if>
    <if test="null != ordinarySoilExcavationDistance">
      #{ordinarySoilExcavationDistance},
    </if>
    <if test="null != pedestrianWalkwayExcavationDistance">
      #{pedestrianWalkwayExcavationDistance},
    </if>
    <if test="null != concreteExcavationDistance">
      #{concreteExcavationDistance},
    </if>
    <if test="null != evseBaseNeed">
      #{evseBaseNeed},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveyChargeArea"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo">
    update t_tj_survey_charge_area set
    <if test="transformerToChargeCable != null">
      transformerToChargeCable = #{transformerToChargeCable},
    </if>
    <if test="transformerToChargeCableDistance != null">
      transformerToChargeCableDistance = #{transformerToChargeCableDistance},
    </if>
    <if test="transformerToChargeCableType != null">
      transformerToChargeCableType = #{transformerToChargeCableType},
    </if>
    <if test="chargeCableDistance != null">
      chargeCableDistance = #{chargeCableDistance},
    </if>
    <if test="masterToTerminalDistance != null">
      masterToTerminalDistance = #{masterToTerminalDistance},
    </if>
    <if test="chargeCableType != null">
      chargeCableType = #{chargeCableType},
    </if>
    <if test="galvanizedCableTrayDistance != null">
      galvanizedCableTrayDistance = #{galvanizedCableTrayDistance},
    </if>
    <if test="ordinarySoilExcavationDistance != null">
      ordinarySoilExcavationDistance = #{ordinarySoilExcavationDistance},
    </if>
    <if test="pedestrianWalkwayExcavationDistance != null">
      pedestrianWalkwayExcavationDistance = #{pedestrianWalkwayExcavationDistance},
    </if>
    <if test="concreteExcavationDistance != null">
      concreteExcavationDistance = #{concreteExcavationDistance},
    </if>
    <if test="evseBaseNeed != null">
      evseBaseNeed = #{evseBaseNeed},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveyChargeAreaBySurveyNo">
    update t_tj_survey_charge_area set enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>


</mapper>

