package com.cdz360.biz.device.business.entity.request;

import lombok.Data;

/**
 * @ProjectName chargerlink-device
 * <AUTHOR>
 * @CreateDate 2019/9/24 15:10
 */

/**
 * @ClassName： OrderMsg
 * @Description: 定制信息
 * @Email: <EMAIL>
 * @Author: JLEI
 * @CreateDate: 2019/9/24 15:32
 */
@Data
public class OrderMsg {
    /**
     * 适配机型
     */
    private String chargingPoleType;
    /**
     * 功率分配方案定制
     */
    private PowerDstbScheme powerDstbScheme;

    @Data
    public class PowerDstbScheme {
        /**
         * 方案名
         */
        private String name;
        /**
         * 方案定制号
         */
        private int sn;
    }
}
