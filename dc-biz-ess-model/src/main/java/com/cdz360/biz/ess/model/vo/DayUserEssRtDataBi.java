package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "场站某天的数据")
@Accessors(chain = true)
@Data
public class DayUserEssRtDataBi {

    @Schema(description = "日期")
    @JsonInclude(Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "GMT+8")
    private LocalDate date;

    @Schema(description = "光伏发电总量,单位: kW.h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal selfElectricityTotal;

    @Schema(description = "电池充电总量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batChargeElectricityTotal;

    @Schema(description = "电池放电总量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batDischargeElectricityTotal;

    @Schema(description = "负载用电总量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadUsingElectricityTotal;

    @Schema(description = "光伏当天自发电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal selfElectricityToday;

    @Schema(description = "当天购电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal purchaseElectricityToday;

    @Schema(description = "当天馈电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal offerElectricityToday;

    @Schema(description = "当天电池充量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batChargeElectricityToday;

    @Schema(description = "当天电池放量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal batDischargeElectricityToday;

    @Schema(description = "当天负载用电量,单位: kW·h")
    @JsonInclude(Include.NON_NULL)
    private BigDecimal loadUsingElectricityToday;
}
