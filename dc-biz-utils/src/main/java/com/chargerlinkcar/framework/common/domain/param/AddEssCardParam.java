package com.chargerlinkcar.framework.common.domain.param;

import io.swagger.v3.oas.annotations.Parameter;
import lombok.Data;

@Data
public class AddEssCardParam {

    @Parameter(name = "用户id")
    private Long userId;
    @Parameter(name = "鉴权卡号，即逻辑卡号")
    private String cardNo;
    @Parameter(name = "卡面编号，即物理卡号")
    private String cardChipNo;
    @Parameter(name = "卡名称")
    private String cardName;
}
