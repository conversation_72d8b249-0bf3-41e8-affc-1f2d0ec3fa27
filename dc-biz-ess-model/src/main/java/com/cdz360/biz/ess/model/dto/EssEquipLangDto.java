package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.model.iot.type.EssEquipType;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import com.fasterxml.jackson.databind.JsonNode;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;

@Data
public class EssEquipLangDto {

    @NotNull(message = "vendor 不能为 null")
    @Size(max = 16, message = "vendor 长度不能超过 16")
    @JsonInclude(Include.NON_EMPTY)
    private String vendor;

//	@ApiModelProperty(value = "设备类型 EssEquipType, 如 PCS BMS")

    @NotNull(message = "equipType 不能为 null")
    @Size(max = 16, message = "equipType 长度不能超过 16")
    @JsonInclude(Include.NON_NULL)
    private EssEquipType equipType;

//	@ApiModelProperty(value = "语言,使用缩写如cn,en")

    @NotNull(message = "lang 不能为 null")
    @Size(max = 6, message = "lang 长度不能超过 6")
    @JsonInclude(Include.NON_EMPTY)
    private String lang;

//	@ApiModelProperty(value = "字段编码")

    @NotNull(message = "code 不能为 null")
    @Size(max = 32, message = "code 长度不能超过 32")
    @JsonInclude(Include.NON_EMPTY)
    private String code;

//	@ApiModelProperty(value = "显示名称")

    @Size(max = 32, message = "name 长度不能超过 32")
    @JsonInclude(Include.NON_EMPTY)
    private String name;

//	@ApiModelProperty(value = "单位")

    @Size(max = 8, message = "unit 长度不能超过 8")
    @JsonInclude(Include.NON_EMPTY)
    private String unit;


    //	@ApiModelProperty(value = "字段值翻译")
    @JsonInclude(Include.NON_NULL)
    private JsonNode values;

//	@ApiModelProperty(value = "更多说明文案")

    @Size(max = 128, message = "desc 长度不能超过 128")
    @JsonInclude(Include.NON_EMPTY)
    private String desc;

}
