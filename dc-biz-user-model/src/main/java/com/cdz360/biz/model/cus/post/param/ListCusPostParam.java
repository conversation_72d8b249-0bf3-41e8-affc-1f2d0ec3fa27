package com.cdz360.biz.model.cus.post.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.biz.model.cus.post.type.CusPostLevel;
import com.cdz360.biz.model.cus.type.CusPostStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户评论列表查询参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListCusPostParam extends BaseListParam {
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    @Schema(description = "商户ID链", example = "34474,34475", hidden = true)
    private String commIdChain;

    @Schema(description = "充电订单号 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String orderNo;

    @Schema(description = "评论用户手机号 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusPhone;

    @Schema(description = "场站名称 支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> siteIdList;

    @Schema(description = "场站组列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<String> gidList;

    @Schema(description = "场站ID 不支持模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "公开状态 true公开，false不公开")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean open;

    @Schema(description = "评论等级 复选")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<CusPostLevel> levelList;

    @Schema(description = "评论状态 SUBMITTED(待回复); REPLIED(已回复)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private CusPostStatus status;

    @Schema(description = "评论时间查询 支持范围查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter createTimeFilter;

    @Schema(description = "更新时间查询 支持范围查询")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private TimeFilter updateTimeFilter;

    @Schema(description = "标签内容 模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String tagContent;

    @Schema(description = "评论内容 模糊查询")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String cusContent;
}
