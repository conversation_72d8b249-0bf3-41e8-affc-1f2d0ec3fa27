package com.cdz360.biz.cus.service.impl;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.cus.client.DataCoreFeignClient;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.domain.VinOrderCount;
import com.cdz360.biz.cus.domain.vo.Vin4ManagerVo;
import com.cdz360.biz.cus.domain.vo.VinMgnListVo;
import com.cdz360.biz.cus.domain.vo.VinSearchParamComm;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.CommercialService;
import com.cdz360.biz.cus.service.IVinService;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.ds.cus.ro.SiteAuthVin.ds.SiteAuthVinLogRoDs;
import com.cdz360.biz.ds.cus.ro.SiteAuthVin.ds.SiteAuthVinRoDs;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserOpenidRoDs;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserRoDs;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.ds.cus.ro.site.ds.SiteAuthRoDs;
import com.cdz360.biz.ds.cus.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.cus.rw.SiteAuthVin.ds.SiteAuthVinLogRwDs;
import com.cdz360.biz.ds.cus.rw.SiteAuthVin.ds.SiteAuthVinRwDs;
import com.cdz360.biz.ds.cus.rw.basic.ds.UserRwDs;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpRwDs;
import com.cdz360.biz.ds.cus.rw.site.ds.SiteAuthDs;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteAuthVinVo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteListVo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.site.po.SiteAuthPo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.type.AuthMediaType;
import com.cdz360.biz.model.cus.site.vo.BatchUpdateSiteAuthVo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.site.vo.SiteAuthMoveCorpVo;
import com.cdz360.biz.model.cus.site.vo.SiteAuthVo;
import com.cdz360.biz.model.cus.user.po.UserOpenidPo;
import com.cdz360.biz.model.cus.user.po.UserPo;
import com.cdz360.biz.model.cus.user.type.UserOpenidType;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.cdz360.biz.model.cus.vin.param.VinSearchParam;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.trading.order.param.VinOrderCountParam;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.cdz360.biz.utils.feign.pcp.PcpAsyncFeignClient;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.domain.pay.PcpCloseVinParam;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinCarNoParam;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.service.DcCusBalanceService;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * VinServiceImpl
 *  vin码服务类
 * @since 2019/5/15 11:06
 * <AUTHOR>
 */

@Slf4j
@Service
public class VinServiceImpl implements IVinService {

    private static final Integer VIN_STATUS_ACTIVE = 1;
    private static final Integer VIN_STATUS_INACTIVE = 0;
    private static final Integer VIN_ENABLE = 1;
    private static final Integer VIN_DISENABLE = 0;
    private static String LogOpTypeModify = "MODIFY";
    private static String LogOpTypeModifyStatus = "MODIFY_STATUS";
    @Autowired
    private VinMapper vinMapper;
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private UserMapper userMapper;
    //    @Autowired
//    private SiteFeignClient siteFeignClient;
    @Autowired
    private RBlocUserMapper rBlocUserMapper;
    @Autowired
    private MerchantService merchantFeignClient;
    @Autowired
    private CommercialService commercialService;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private CommercialRoDs commercialRoDs;
    @Autowired
    private SiteAuthDs siteAuthDs;
    @Autowired
    private SiteAuthRoDs siteAuthRoDs;
    @Autowired
    private DcCusBalanceService dcCusBalanceService;
    @Autowired
    private DataCoreFeignClient dataCoreFeignClient;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private IotBizClient iotBizClient;
    @Autowired
    private SiteAuthVinLogRoDs siteAuthVinLogRoDs;
    @Autowired
    private SiteAuthVinRoDs siteAuthVinRoDs;
    @Autowired
    private CorpRwDs corpRwDs;
    @Autowired
    private SiteAuthVinLogRwDs siteAuthVinLogRwDs;
    @Autowired
    private SiteAuthVinRwDs siteAuthVinRwDs;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private UserRoDs userRoDs;
    @Autowired
    private UserRwDs userRwDs;
    @Autowired
    private UserOpenidRoDs userOpenidRoDs;
    @Autowired
    private PcpAsyncFeignClient pcpAsyncFeignClient;

    @Override
    public ObjectResponse create(VinParam vinParam) {

        log.info("创建vin:{}", JsonUtils.toJsonString(vinParam));

        if (null == vinParam.getLocale()) {
            vinParam.paramCheck();
        } else {
            vinParam.essParamCheck();
        }

        if (vinParam.getSubCommId() == null) {
            throw new DcArgumentException("subCommId不能为空");
        }
        if (vinParam.getUserId() == null) {
            throw new DcArgumentException("userId不能为空");
        }
        if (TextUtils.isBlank(vinParam.getVin())) {
            throw new DcArgumentException("vin不能为空");
        }

        // 企业母账号不允许绑定VIN
        UserPo cusInfo = userRoDs.getCusById(vinParam.getUserId());
        if (cusInfo != null && StringUtils.isNotEmpty(cusInfo.getPhone()) && cusInfo.getPhone()
            .startsWith("B")) {
            throw new DcArgumentException("企业母账号不能绑定VIN");
        }

//        if (vinParam.getCarDepart() != null && StringUtils.isNotEmpty(vinParam.getCarDepart()) && vinParam.getCarDepart().length() > 30) {
//            throw new DcArgumentException("请检查车队名称是否有误");
//        }
        IotAssert.isTrue(!vinParam.getVin().startsWith("0"), "VIN码第一位不能为0");

        //获取该商户的顶级商户与所属商户，填入数据库表的commid和subcommid
        //因为前端传来的commid为子商户id，这里做个swap
        Long subCommId = vinParam.getSubCommId();
        Long commId = subCommId;
        Long topCommId = this.commercialRoDs.getTopCommId(subCommId);
        //CommercialSimpleVo topCommercial = this.commercialRoDs.getTopCommercial(subCommId, 10);
        if (topCommId != null) {
            commId = topCommId;
        }
        vinParam.setCommId(commId);

        //新增时, 如果该VIN码已经绑定在当前商户的其他客户下且该VIN码有效时，提示失败
        List<VinDto> vinSinUserId = vinMapper.selectCountByCommIdSinUserId(
            vinParam.getCommId(),
            vinParam.getUserId(),
            vinParam.getVin());
        Integer ret;
        if (vinSinUserId.size() != 0) {
            //存在同一个商户下绑定到不同userid的vin
            if (vinSinUserId.stream()
                .anyMatch(item -> VIN_STATUS_ACTIVE.equals(item.getStatus()))) { // VIN码被其他用户占用
                log.info("<< 该VIN码已关联在其他客户");
                throw new DcServiceException("新增失败，该VIN码已关联在其他客户。");
            }
            //        } else {
        }
        List<VinDto> vins = vinMapper.selectCountByCommIdConUserId(
            vinParam.getCommId(),
            vinParam.getUserId(),
            vinParam.getVin());

        // 之前的默认
        final List<VinDto> originalPrimaryList = getUserPrimaryVin(vinParam.getUserId());
        boolean isPrimary = false;
        if (CollectionUtils.isEmpty(originalPrimaryList) &&
            StringUtils.isNotBlank(vinParam.getCarNo())) {
            // 设置当前新增的为默认
            isPrimary = true;
        }

        if (!vins.isEmpty()) {
//            if (NumberUtils.equals(VIN_ENABLE, vins.get(0).getEnable())) {
//                log.info("<< 该VIN码已存在");
//                throw new DcServiceException("新增失败，该VIN码已存在");
//            }
            //该客户已有相同的VIN码记录, 应修改该记录
            //修改状态为'有效'并关联到当前商户.
            VinParam vinParamUpdate = new VinParam();
            vinParamUpdate.setId(vins.get(0).getId());
            vinParamUpdate.setStatus(VIN_STATUS_ACTIVE);
            vinParamUpdate.setEnable(VIN_ENABLE);
            vinParamUpdate.setIsPrimary(isPrimary);

            //                vinParamUpdate.setCommId(vinParam.getCommId());
            vinParamUpdate.setSubCommId(vinParam.getSubCommId());
            vinParamUpdate.setStation(vinParam.getStation());

            if (null != vinParam.getName()) {
                vinParamUpdate.setName(vinParam.getName());
            }

            if (null != vinParam.getCarNo()) {
                vinParamUpdate.setCarNo(vinParam.getCarNo());
            }

            if (null != vinParam.getCarNum()) {
                vinParamUpdate.setCarNum(vinParam.getCarNum());
            }

            if (null != vinParam.getCarDepart()) { //添加车队名称
                vinParamUpdate.setCarDepart(vinParam.getCarDepart());
            }

            if (null != vinParam.getBrand()) {
                vinParamUpdate.setBrand(vinParam.getBrand());
            }

            if (null != vinParam.getModel()) {
                vinParamUpdate.setModel(vinParam.getModel());
            }

            if (vinParam.getCarLength() != null) {
                vinParamUpdate.setCarLength(vinParam.getCarLength());
            }

            if (StringUtils.isNotBlank(vinParam.getYear())) {
                vinParamUpdate.setYear(vinParam.getYear());
            }

            if (vinParam.getLineNum() != null) {
                vinParamUpdate.setLineNum(vinParam.getLineNum());
            }

            ret = vinMapper.update(vinParamUpdate);
        } else {
            //该商户下不存在这个vin，新增一个
            vinParam.setIsPrimary(isPrimary);
            ret = vinMapper.insert(vinParam);
        }
        //        }

        // 更新使用范围
        this.updateSiteAuth(vinParam);

        AssertUtil.isTrue(ret == 1, "创建VIN码失败");
        ObjectResponse res = new ObjectResponse<>("创建成功");
        return res;
    }

    /**
     * 获取用户默认车牌号列表
     *
     * @param userId
     */
    private List<VinDto> getUserPrimaryVin(long userId) {
        VinParam vinPrimaryParam = new VinParam();
        vinPrimaryParam.setIsPrimary(Boolean.TRUE);
        vinPrimaryParam.setEnable(VIN_ENABLE);
        vinPrimaryParam.setStatus(VIN_STATUS_ACTIVE);
        vinPrimaryParam.setUserId(userId);
        vinPrimaryParam.setSize(999);
        // 之前的默认
        return vinMapper.getCarNoListByParam(vinPrimaryParam);
    }

    /**
     * 重新选择一个车牌号为默认
     *
     * @param userId
     * @param excludeVinId
     */
    private void setNewVinAsPrimary(long userId, long excludeVinId) {
        log.debug("原先车牌号是默认车牌号，尝试指定新的");
        VinParam vinNewPrimaryParam = new VinParam();
        vinNewPrimaryParam.setUserId(userId);
        vinNewPrimaryParam.setEnable(VIN_ENABLE);
        vinNewPrimaryParam.setIsPrimary(Boolean.FALSE);
        vinNewPrimaryParam.setExcludeIds(List.of(excludeVinId));
        vinNewPrimaryParam.setStatus(1);
        vinNewPrimaryParam.setSize(999);

        final List<VinDto> carNoListNoDefault = vinMapper.getCarNoListByParam(vinNewPrimaryParam);
        if (CollectionUtils.isNotEmpty(carNoListNoDefault)) {
            log.debug("取出第一个，作为默认");//id最大的一个
            final VinDto vinDtoNewDefault = carNoListNoDefault.get(0);
            VinParam vinNewPrimary = new VinParam();
            vinNewPrimary.setId(vinDtoNewDefault.getId());
            vinNewPrimary.setIsPrimary(Boolean.TRUE);
            Integer retNewPrimary = vinMapper.update(vinNewPrimary);
            IotAssert.isTrue(retNewPrimary == 1, "修改新的默认VIN码失败");
        } else {
            log.info("没有可供设置为默认的车牌号");
        }
    }

    /**
     * 更新场站使用范围
     *
     * @param param
     */
    private void updateSiteAuth(VinParam param) {
        String stations = param.getStation();
        if (null == stations) {
            return;
        }
        List<String> siteIdList = List.of(stations.split(","));

        // 查找原来的数据，对比更新数据，如果不存在则disable
        List<SiteAuthVo> oldSiteAuthList = this.siteAuthDs.findByTypeAndAccountAndTopCommId(
            AuthMediaType.VIN, param.getVin(), param.getCommId(), null, null, null);

        List<String> disableList = new ArrayList<>();
        oldSiteAuthList.forEach(o -> {
            if (!siteIdList.contains(o.getSiteId())) {
                disableList.add(o.getSiteId());
            }
        });

        if (siteIdList.isEmpty()) {
            this.siteAuthDs.disableAll(AuthMediaType.VIN, param.getVin(), null, param.getCommId());
        } else {
            this.siteAuthDs.insertOrUpdate(siteIdList.stream().map(siteId ->
                new SiteAuthPo()
                    .setTopCommId(param.getCommId())
                    .setAccount(param.getVin())
                    .setSiteId(siteId)
                    .setType(AuthMediaType.VIN)
                    .setEnable(true)
            ).collect(Collectors.toList()));

            this.siteAuthDs.insertOrUpdate(disableList.stream().map(siteId ->
                new SiteAuthPo()
                    .setTopCommId(param.getCommId())
                    .setAccount(param.getVin())
                    .setSiteId(siteId)
                    .setType(AuthMediaType.VIN)
                    .setEnable(false)
            ).collect(Collectors.toList()));
        }
    }

    /**
     * VIN本地鉴权
     *
     * @param param
     */
    private void updateVinSiteAuth(VinParam param) {
        BatchUpdateSiteAuthVo vo = new BatchUpdateSiteAuthVo();
        vo.setAccountList(List.of(param.getVin()));
        vo.setCommId(param.getCommId());
        if (param.getVinAuthSiteIdList() != null) {
            siteAuthVinRwDs.disableAll(vo);
            if (!param.getVinAuthSiteIdList().isEmpty()) {
                List<SiteAuthVinPo> list = new ArrayList<>();
                param.getVinAuthSiteIdList().forEach(e -> {
                    SiteAuthVinPo siteAuthVinPo = new SiteAuthVinPo();
                    siteAuthVinPo.setEnable(Boolean.TRUE)
                        .setSiteId(e)
                        .setVin(param.getVin())
                        .setCommId(param.getCommId());
                    list.add(siteAuthVinPo);
                });
                siteAuthVinRwDs.insertOrUpdate(list);
            }
        }
    }

    private void updateSiteAuth(VinDto dto) {
        VinParam param = new VinParam();
        param.setVin(dto.getVin())
            .setCommId(dto.getCommId())
            .setStation(dto.getStation());
        this.updateSiteAuth(param);
    }

    @Override
    public ObjectResponse createOnCorp(VinParam vinParam) {
        log.info("创建vin:{}", JsonUtils.toJsonString(vinParam));

        if (vinParam.getSubCommId() == null) {
            throw new DcArgumentException("subCommId不能为空");
        }
        if (vinParam.getCommId() == null) {
            throw new DcArgumentException("commId不能为空");
        }
        if (vinParam.getUserId() == null) {
            throw new DcArgumentException("userId不能为空");
        }
        if (TextUtils.isBlank(vinParam.getVin())) {
            throw new DcArgumentException("vin不能为空");
        }

        //车队名称验证
        if (vinParam.getCarDepart() != null && vinParam.getCarDepart().length() > 20) {
            throw new DcArgumentException("请检查车队名称是否有误");
        }

        //新增时, 如果该VIN码已经绑定在当前商户的其他客户下且该VIN码有效时，提示失败
        List<VinDto> vinSinUserId = vinMapper.selectCountByCommIdSinUserId(
            vinParam.getCommId(),
            vinParam.getUserId(),
            vinParam.getVin());

        Integer ret;
        if (vinSinUserId.size() != 0) {
            //存在同一个商户下绑定到不同userid的vin
            if (vinSinUserId.stream()
                .anyMatch(item -> VIN_ENABLE.equals(item.getEnable()))) { // VIN码被其他用户占用
                log.info("<< 该VIN码已关联在其他客户");
                throw new DcServiceException("新增失败，该VIN码已关联在其他客户。");
            }
        }
        List<VinDto> vins = vinMapper.selectCountByCommIdConUserId(
            vinParam.getCommId(),
            vinParam.getUserId(),
            vinParam.getVin());

        if (!vins.isEmpty()) {
            //该客户已有相同的VIN码记录, 应修改该记录
            //修改状态为'有效'并关联到当前商户.
            VinParam vinParamUpdate = new VinParam();
            vinParamUpdate.setId(vins.get(0).getId());
            vinParamUpdate.setStatus(VIN_STATUS_ACTIVE);
            vinParamUpdate.setEnable(VIN_ENABLE);
            vinParamUpdate.setCorpId(vinParam.getCorpId());
            //                vinParamUpdate.setCommId(vinParam.getCommId());
            vinParamUpdate.setSubCommId(vinParam.getSubCommId());
            vinParamUpdate.setStation(vinParam.getStation());
            vinParamUpdate.setUserId(vinParam.getUserId());
            if (null != vinParam.getName()) {
                vinParamUpdate.setName(vinParam.getName());
            }

            if (null != vinParam.getLineNum()) {
                vinParamUpdate.setLineNum(vinParam.getLineNum());
            }

            if (null != vinParam.getCarNo()) {
                vinParamUpdate.setCarNo(vinParam.getCarNo());
            }

            if (StringUtils.isNotBlank(vinParam.getCarNum())) {
                vinParamUpdate.setCarNum(vinParam.getCarNum());
            }

            if (StringUtils.isNotBlank(vinParam.getCarDepart())) {
                vinParamUpdate.setCarDepart(vinParam.getCarDepart());
            }

            if (StringUtils.isNotBlank(vinParam.getBrand())) {
                vinParamUpdate.setBrand(vinParam.getBrand());
            }

            if (StringUtils.isNotBlank(vinParam.getModel())) {
                vinParamUpdate.setModel(vinParam.getModel());
            }

            if (vinParam.getCarLength() != null) {
                vinParamUpdate.setCarLength(vinParam.getCarLength());
            }

            if (StringUtils.isNotBlank(vinParam.getYear())) {
                vinParamUpdate.setYear(vinParam.getYear());
            }

            ret = vinMapper.update(vinParamUpdate);
        } else {
            //该商户下不存在这个vin，新增一个
            ret = vinMapper.insert(vinParam);
        }

        // 更新适用范围
        this.updateSiteAuth(vinParam);

        AssertUtil.isTrue(ret == 1, "创建VIN码失败");
        ObjectResponse res = new ObjectResponse<>("创建成功");
        return res;
    }

    @Override
    public ObjectResponse batchCreateOnCorp(List<VinParam> vinParamList) {
        List<String> phoneList = vinParamList.stream().map(VinParam::getMobile)
            .collect(Collectors.toList());
        List<RBlocUser> rBlocUsers = rBlocUserMapper.findByPhoneListAndBlocUserId(phoneList,
            vinParamList.get(0).getCorpId());
        Map<String, Long> userIdMap = rBlocUsers.stream()
            .collect(Collectors.toMap(RBlocUser::getPhone, RBlocUser::getUserId));

        List<VinParam> vinList = new ArrayList<>();
        vinParamList.forEach(vinParam -> {
            log.info("创建vin:{}", JsonUtils.toJsonString(vinParam));
            vinParam.setUserId(userIdMap.get(vinParam.getMobile()));
            if (vinParam.getSubCommId() == null) {
                throw new DcArgumentException("subCommId不能为空");
            }
            if (vinParam.getCommId() == null) {
                throw new DcArgumentException("commId不能为空");
            }
            if (vinParam.getUserId() == null) {
                throw new DcArgumentException("userId不能为空");
            }
            if (TextUtils.isBlank(vinParam.getVin())) {
                throw new DcArgumentException("vin不能为空");
            }

            //新增时, 如果该VIN码已经绑定在当前商户的其他客户下且该VIN码有效时，提示失败
//            List<VinDto> vinSinUserId = vinMapper.selectCountByCommIdSinUserId(
//                    vinParam.getCommId(),
//                    vinParam.getUserId(),
//                    vinParam.getVin());
//            if (vinSinUserId.size() != 0) {
//                //存在同一个商户下绑定到不同userid的vin
//                if (vinSinUserId.stream()
//                        .anyMatch(item -> VIN_ENABLE.equals(item.getEnable()))) { // VIN码被其他用户占用
//                    log.info("<< 该VIN码已关联在其他客户");
//                    throw new DcServiceException("新增失败，该VIN码已关联在其他客户。");
//                }
//            }
//            List<VinDto> vins = vinMapper.selectCountByCommIdConUserId(
//                    vinParam.getCommId(),
//                    vinParam.getUserId(),
//                    vinParam.getVin());
            List<VinDto> vinSinUserId = vinMapper.selectVinListByCommId(vinParam.getCommId(),
                vinParam.getVin());

            VinDto vins = new VinDto();
            if (vinSinUserId.size() != 0) {
//                //存在同一个商户下绑定到不同userid的vin
                if (vinSinUserId.stream()
                    .anyMatch(item -> VIN_ENABLE.equals(item.getEnable()))) { // VIN码被其他用户占用
                    log.info("<< 该VIN码已关联在其他客户");
                    throw new DcServiceException("新增失败，该VIN码已关联在其他客户。");
                }

                vinSinUserId.forEach(e -> {
                    if (e.getUserId().equals(vinParam.getUserId())) {
                        vins.setId(e.getId());
                    }
                });
            }

            if (vins.getId() != null) {
                //该客户已有相同的VIN码记录, 应修改该记录
                //修改状态为'有效'并关联到当前商户.
                VinParam vinParamUpdate = new VinParam();
                vinParamUpdate.setId(vins.getId());
                vinParamUpdate.setStatus(VIN_STATUS_ACTIVE);
                vinParamUpdate.setEnable(VIN_ENABLE);
                vinParamUpdate.setCorpId(vinParam.getCorpId());
                //                vinParamUpdate.setCommId(vinParam.getCommId());
                vinParamUpdate.setSubCommId(vinParam.getSubCommId());
                vinParamUpdate.setStation(vinParam.getStation());
                vinParamUpdate.setUserId(vinParam.getUserId());
                if (null != vinParam.getName()) {
                    vinParamUpdate.setName(vinParam.getName());
                }

                if (null != vinParam.getLineNum()) {
                    vinParamUpdate.setLineNum(vinParam.getLineNum());
                }

                if (null != vinParam.getCarNo()) {
                    vinParamUpdate.setCarNo(vinParam.getCarNo());
                }
                //添加车队信息
                if (null != vinParam.getCarDepart()) {
                    vinParamUpdate.setCarDepart(vinParam.getCarDepart());
                }

                if (StringUtils.isNotBlank(vinParam.getCarNum())) {
                    vinParamUpdate.setCarNum(vinParam.getCarNum());
                }

                if (StringUtils.isNotBlank(vinParam.getBrand())) {
                    vinParamUpdate.setBrand(vinParam.getBrand());
                }

                if (StringUtils.isNotBlank(vinParam.getModel())) {
                    vinParamUpdate.setModel(vinParam.getModel());
                }

                if (vinParam.getCarLength() != null) {
                    vinParamUpdate.setCarLength(vinParam.getCarLength());
                }

                if (StringUtils.isNotBlank(vinParam.getYear())) {
                    vinParamUpdate.setYear(vinParam.getYear());
                }
                vinMapper.update(vinParamUpdate);
            } else {
                //该商户下不存在这个vin，新增一个
//                vinMapper.insert(vinParam);
                vinList.add(vinParam);
            }

            // 更新适用范围
            this.updateSiteAuth(vinParam);
        });
        //批量插入VIN
        if (vinList.size() > 0) {
            vinMapper.insertBatch(vinList);
        }
        ObjectResponse res = new ObjectResponse<>("创建成功");
        return res;
    }

    public ObjectResponse<VinDto> getById(Long id) {
        return RestUtils.buildObjectResponse(vinMapper.getById(id));
    }

    @Override
    public ObjectResponse delete(Long id) {
        // 通过Id获取
        VinDto vin = vinMapper.getById(id);
        if (null == vin) {
            throw new DcArgumentException("Id 对应的VIN记录不存在，请提供有效的Id值");
        }

        Integer ret = vinMapper.delete(id, 0L);//TODO 操作者得获得
        AssertUtil.isTrue(ret == 1, "删除VIN码失败");

        // 更新场站适用范围
        this.siteAuthDs.disableAll(AuthMediaType.VIN, vin.getVin(), null, vin.getCommId());

        ObjectResponse res = new ObjectResponse<>("删除成功");
        return res;
    }

    @Override
    public ObjectResponse update(VinParam vinParam) {

        if (null == vinParam.getLocale()) {
            vinParam.paramCheck();
        } else {
            vinParam.essParamCheck();
        }

        //获取该商户的顶级商户与所属商户，填入数据库表的commid和subcommid
        //因为前端传来的commid为子商户id，这里做个swap
        Long subCommId = vinParam.getSubCommId();
        Long commId = subCommId;
        Long topCommId = this.commercialRoDs.getTopCommId(subCommId);
        // CommercialSimpleVo topCommercial = this.commercialRoDs.getTopCommercial(subCommId, 10);
        if (topCommId != null) {
            commId = topCommId;
        }
        vinParam.setCommId(commId);
        vinParam.setSubCommId(subCommId);

        final VinDto byId = vinMapper.getById(vinParam.getId());

        if (LogOpTypeModify.equals(vinParam.getType())) {
            log.info("修改vin信息");
            if (Boolean.TRUE.equals(byId.getIsPrimary())) {
                IotAssert.isNotBlank(vinParam.getCarNo(), "该记录为用户默认车牌号，请填写车牌号");
            }
        } else if (LogOpTypeModifyStatus.equals(vinParam.getType())) {
            log.info("修改vin状态: {}", vinParam.getStatus());
            if (vinParam.getStatus() != null) {

                if (VIN_STATUS_INACTIVE.equals(vinParam.getStatus())) {
                    // 期望vin设置成禁用
                    vinParam.setIsPrimary(false);

                    setNewVinAsPrimary(byId.getUserId(), vinParam.getId());
                } else if (VIN_STATUS_ACTIVE.equals(vinParam.getStatus())) {

                    final List<VinDto> originalPrimaryList = getUserPrimaryVin(byId.getUserId());
                    final boolean hasPrimary = CollectionUtils.isNotEmpty(originalPrimaryList);

                    // 期望vin设置成启用
                    if (!hasPrimary && StringUtils.isNotBlank(byId.getCarNo())) {
                        log.info("设置该vin为默认: {}", vinParam.getId());
                        vinParam.setIsPrimary(true);
                    }
                }
            }
        }

        Integer ret = vinMapper.update(vinParam);//TODO 操作者得获得
        AssertUtil.isTrue(ret == 1, "修改VIN码失败");

        // 更新使用范围
        this.updateSiteAuth(vinParam);
        //更新本地鉴权vin场站配置
        vinParam.setCommId(subCommId);// 确保VinSiteAuth表的commId为直属商户，而非顶级商户
        if (NumberUtils.equals(vinParam.getStatus(), 0)) {

            // 获取原先的记录，以便下发到受影响的场站
            List<SiteListVo> siteListByVin = siteAuthVinRoDs.getSiteListByVin(vinParam.getVin(), 0L,
                999L);

            log.info("删除VIN时，设置本地VIN鉴权siteId列表为[]");
            vinParam.setVinAuthSiteIdList(List.of());
            this.updateVinSiteAuth(vinParam);

            // 根据原先的记录下发到受影响的场站
            if (CollectionUtils.isNotEmpty(siteListByVin)) {
                List<String> siteIds = siteListByVin.stream()
                    .map(SiteListVo::getSiteId)
                    .collect(Collectors.toList());
                log.info("原先地VIN鉴权siteId列表: {}，从各个场站剔除目标VIN: {}，开始下发",
                    siteIds.size(), vinParam.getVin());
                SiteAuthVinParam param = new SiteAuthVinParam();
                param.setVinList(List.of(vinParam.getVin()))
                    .setSiteIdList(siteIds);
                this.siteAuthVin(param);
            }
        } else {
            this.updateVinSiteAuth(vinParam);
        }

        ObjectResponse res = new ObjectResponse<>("修改成功");
        return res;
    }

    /**
     * 仅更新 t_vin.vin 字段
     */
    public void updateVin(Long id, String vin) {
        log.info("更新t_vin记录. id= {}, vin= {}", id, vin);
        VinParam param = new VinParam();
        param.setId(id).setVin(vin);
        this.vinMapper.update(param);
    }

    @Override
    @Transactional
    public ObjectResponse updateByVinAndCommAndCorpOnCorp(List<VinParam> vinParamList) {
        log.info("修改vin:{}", JsonUtils.toJsonString(vinParamList));

        if (CollectionUtils.isNotEmpty(vinParamList)) {
            List<VinDto> byIdList = vinMapper.getByIdList(vinParamList.stream()
                .map(VinParam::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

            Map<Long, VinParam> paramMap = vinParamList.stream()
                .collect(Collectors.toMap(VinParam::getId, o -> o));

            byIdList.stream()
                .filter(e -> !e.getVin().equalsIgnoreCase(paramMap.get(e.getId()).getVin()))
                .collect(Collectors.groupingBy(VinDto::getCommId))
                .entrySet()
                .stream()
                .forEach(e -> {
                    Long commId = e.getKey();
                    List<String> vinList = e.getValue().stream().map(VinDto::getVin)
                        .collect(Collectors.toList());
                    log.debug(
                        ">> 修改的vin和最初vin不一致，将最初vin的使用范围全部禁用commId: {}, vinList: {}",
                        commId, vinList);
                    int count = this.siteAuthDs.disableAll(AuthMediaType.VIN, null, vinList,
                        commId);
                    log.debug("<< 禁用了: {}", count);
                });
        }

        vinParamList.forEach(e -> {
            if (e.getId() == null) {
                throw new DcArgumentException("id不能为空");
            }
            if (e.getVin() == null) {
                throw new DcArgumentException("vin不能为空");
            }
            if (e.getCommId() == null) {
                throw new DcArgumentException("commId不能为空");
            }
            if (e.getCorpId() == null) {
                throw new DcArgumentException("corpId不能为空");
            }
            Integer ret = vinMapper.updateByVinAndCommAndCorpOnCorp(e);//TODO 操作者得获得
            AssertUtil.isTrue(ret == 1, "修改VIN码失败");

            // 更新使用范围
            this.updateSiteAuth(e);
        });
        ObjectResponse res = new ObjectResponse<>("修改成功");
        return res;
    }

    public ListResponse<SitePo> getCommonSiteList(VinParam vinParam) {
        if (CollectionUtils.isEmpty(vinParam.getVinList()) || vinParam.getCommId() == null) {
            throw new DcArgumentException("请求参数不完整");
        }
        //获取绑定的场站
        List<SiteAuthVinVo> list = siteAuthVinRoDs.getListByVin(vinParam.getVinList(),
            vinParam.getCommId(), null);
        List<String> commonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<SiteAuthVinPo>> map = list.stream()
                .collect(Collectors.groupingBy(SiteAuthVinPo::getVin));
            for (String key : map.keySet()) {
                List<String> siteIdList = map.get(key).stream().map(SiteAuthVinPo::getSiteId)
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(commonList)) {
                    commonList.addAll(siteIdList);
                    continue;
                }
                commonList.retainAll(siteIdList);
            }
        }
        if (CollectionUtils.isNotEmpty(commonList)) {
            List<SitePo> sitePoList = siteRoDs.getSiteBySiteIdList(commonList);
            return RestUtils.buildListResponse(sitePoList);
        }
        return RestUtils.buildListResponse(null);
    }

    @Override
    public ListResponse<VinDto> select(String token, VinSearchParam vinSearchParam) {
        log.info("查找vin: {}", JsonUtils.toJsonString(vinSearchParam));
        //List<Long> ret = merchantFeignClient.getCommIdListByToken(token);
        //FeignResponseValidate.check(ret);
        //        JSONObject jsonObjectCommIdList = merchantFeignClient.getCommIdListByToken(token);

        //        if (jsonObjectCommIdList == null || ResultConstant.RES_SUCCESS_CODE != jsonObjectCommIdList.getInteger("status") ||
        //                jsonObjectCommIdList.get("data") ==null) {
        //            log.info("商户查询失败或为空{}",jsonObjectCommIdList);
        //            throw new DcServiceException("商户错误");
        //        }

//        Page<Object> pageResult = PageHelper.startPage(
//                vinSearchParam.getPageNum(), vinSearchParam.getPageSize(), true, false, null);

        Page<VinDto> pageResult = PageHelper.offsetPage(
            vinSearchParam.getStart().intValue(), vinSearchParam.getSize(), true);

        List<VinDto> vinList;
        if (vinSearchParam.getUserId() == null) {
            // List<Long> subCommIds = ret;

            VinSearchParamComm vinSearchParamComm = new VinSearchParamComm();
            BeanUtils.copyProperties(vinSearchParam, vinSearchParamComm);
            //vinSearchParamComm.setSubCommIds(subCommIds);

            if (com.cdz360.base.utils.StringUtils.isBlank(vinSearchParam.getCommIdChain())) {
                vinList = new ArrayList<>();
            } else {
                vinList = vinMapper.selectComm(vinSearchParamComm);
            }
        } else {
            VinSearchParamComm vinSearchParamComm = new VinSearchParamComm();
            BeanUtils.copyProperties(vinSearchParam, vinSearchParamComm);
            //vinSearchParamComm.setSubCommIds(ret);
            vinList = vinMapper.select(vinSearchParamComm);
        }
        //log.info("vinList = {}", JsonUtils.toJsonString( vinList));

        //列表为空时，下面的feign就没必要调用了
        if (vinList.isEmpty()) {
            ListResponse<VinDto> res = new ListResponse<>(vinList, pageResult.getTotal());
            return res;
        }

        if (CollectionUtils.isNotEmpty(vinList)) {

            // 根据鉴权介质，得到绑定的有效场站ID集合,返回值为<account, List<siteId>>
            Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
                AuthMediaType.VIN,
                vinList.stream().map(VinDto::getVin).collect(Collectors.toList()),
                vinList.get(0).getCommId(),
                vinSearchParam.getCommIdChain());
            Map<String, List<String>> stationMap = optionalMap.orElse(null);

            //获取vin绑定的场站列表
            List<SiteAuthVinVo> siteAuthVinPoList = siteAuthVinRoDs.getListByVin(
                vinList.stream().map(VinDto::getVin).collect(Collectors.toList()),
                null, vinSearchParam.getCommIdChain());
            Map<String, List<SiteAuthVinVo>> siteMap = siteAuthVinPoList.stream()
                .collect(Collectors.groupingBy(SiteAuthVinPo::getVin));

//            List<VinDto> tempVinDto = vinMapper.getUsableStationByVinIdList(
//                    vinList.stream().map(VinDto::getId).collect(Collectors.toList()), vinSearchParam.getCommIdChain());
//            Map<Long, Integer> stationCountMap = tempVinDto.stream().collect(Collectors.toMap(VinDto::getId, VinDto::getUsableStationCount));

            vinList.forEach(e -> {
                e.setUsableStationCount(0);
//                Integer usableStationCount = stationCountMap.get(e.getId());
//                e.setUsableStationCount(usableStationCount == null ? 0 : usableStationCount);

                if (siteMap != null && siteMap.get(e.getVin()) != null) {
                    e.setAuthSiteAmount(siteMap.get(e.getVin()).size());
                    List<SiteAuthVinVo> siteList = siteMap.get(e.getVin());
                    e.setAuthSiteList(siteList);
                }

                if (stationMap != null && stationMap.get(e.getVin()) != null) {
                    e.setStationList(stationMap.get(e.getVin()));
                    e.setUsableStationCount(stationMap.get(e.getVin()).size());
                }
            });
        }

        //TODO 根据vin列表的comm id和sub comm id，生成 comm_id, comm_name的map
        //TODO 需要多次调接口，之后再优化
//        Map<Long, String> commNameMap = vinList.stream().map(VinDto::getCommId).filter(Objects::nonNull).distinct().map(e -> {
//            ObjectResponse<Commercial> merchant = commercialService.getCommercialByCommId(e);
//            if (merchant == null ||
//                    ResultConstant.RES_SUCCESS_CODE != merchant.getStatus()) {
//                log.warn("查询商户{}失败", e);
//                return null;
//            } else {
//                String commName = merchant.getData().getCommName();
//                Pair<Long, String> pair = Pair.of(e, commName);
//                return pair;
//            }
//        }).filter(Objects::nonNull)
//                .collect(Collectors.toMap(Pair::getFirst, Pair::getSecond, (oldOne, newOne) -> newOne));
        List<Long> commIdList = vinList.stream().map(VinDto::getCommId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());
        List<Long> subCommIdList = vinList.stream().map(VinDto::getSubCommId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        commIdList.addAll(subCommIdList);
        Map<Long, String> commNameMap = new HashMap<>();
        commIdList.forEach(e -> {
            try {
                ObjectResponse<Commercial> merchant = commercialService.getCommercialByCommId(e);
                if (merchant == null ||
                    ResultConstant.RES_SUCCESS_CODE != merchant.getStatus()) {
                    log.warn("查询商户{}失败", e);
                } else {
                    String commName = merchant.getData().getCommName();
                    commNameMap.put(e, commName);
                }
            } catch (Exception exp) {
                log.warn("commId = {}, error = {}", e, exp.getMessage(), exp);
            }
        });

        //获得vin和对应的订单数的HashMap
        //VIN忽略大小写
        VinOrderCountParam param = new VinOrderCountParam().setUserId(vinSearchParam.getUserId())
            .setCommIdChain(vinSearchParam.getCommIdChain())
            .setVinList(vinList.stream().map(VinDto::getVin).collect(Collectors.toList()));
        ListResponse<VinOrderCount> res = tradingFeignClient.queryOrderCountByVins(param);
        if (res != null && res.getStatus() == ResultConstant.RES_SUCCESS_CODE
            && res.getData() != null) {
            // pass
        } else {
            throw new DcServiceException(res.getError());
        }

        List<VinOrderCount> list = res.getData();

        Map<String, Integer> vinMap = new HashMap<>();
        list.forEach(e -> {
            try {
                String vin = e.getVin();
                Integer orderCount = Integer.valueOf(e.getOrderCount());
                vinMap.put(vin, orderCount);
            } catch (Exception exc) {
                log.error(exc.getMessage(), e);
            }
        });

        List<Long> findList = vinList.stream().map(VinDto::getUserId).filter(Objects::nonNull)
            .distinct().collect(Collectors.toList());
        List<UserVo> userVos = userMapper.queryUsersByIds(findList);
        Map<Long, UserVo> userVoMap = userVos.stream()
            .collect(Collectors.toMap(UserVo::getId, e -> e));

        vinList.forEach(e -> {
            String commName = commNameMap.get(e.getCommId());
            if (TextUtils.isBlank(commName)) {
                e.setCommName(null);
            } else {
                e.setCommName(commName);
            }
            String subCommName = commNameMap.get(e.getSubCommId());
            if (TextUtils.isBlank(subCommName)) {
                e.setSubCommName(null);
            } else {
                e.setSubCommName(subCommName);
            }
            //VIN忽略大小写
            if (vinMap.get(e.getVin().toUpperCase()) == null) {
                e.setOrderCount(0);
            } else {
                e.setOrderCount(vinMap.get(e.getVin().toUpperCase()));
            }

            UserVo userVo = userVoMap.get(e.getUserId());
            if (e.getCorpId() != null && e.getCorpId() > 0) {
                e.setDebitAccountName("企业账户-" + e.getCorpName());
            } else if (userVo != null && userVo.getDebitAccountName() != null) {
                e.setDebitAccountName(userVo.getDebitAccountName());
            }

            e.setIsEdit(true);
            if (vinSearchParam.getCommIdChain() != null) {
                String[] strArr = vinSearchParam.getCommIdChain().split(",");
                int length = strArr.length;

                if (Arrays.asList(strArr).contains(e.getSubCommId().toString()) && !strArr[length
                    - 1].equals(e.getSubCommId().toString())) {

                    e.setIsEdit(false);
                }
            }
        });

        //        PaginationEntity<VinDto> paginationEntity = new PaginationEntity<>(vinList ,pageResult.getTotal());
        //        SuccessResultEntityTmpl resFinal = new SuccessResultEntityTmpl(paginationEntity);
        ListResponse<VinDto> result = new ListResponse<>(vinList, pageResult.getTotal());

        return result;
    }

    @Override
    public ListResponse<VinDto> selectVinOnCorp(VinParam vinParam) {
        log.info("vinParam: {}", JsonUtils.toJsonString(vinParam));

        // @Nathan 调整了入参，需要做对应约束
        if (null == vinParam.getPage()) {
            vinParam.setPage(1);
        }

        if (null == vinParam.getRows()) {
            vinParam.setRows(10);
        }
        if (vinParam.getStart() == null) {
            vinParam.setStart(0L);
        }
        if (vinParam.getSize() == null) {
            vinParam.setSize(999);
        }

        Page<Object> pageResult = PageHelper.startPage(vinParam.getPage(), vinParam.getRows(), true,
            false, null);
        IotAssert.isTrue(vinParam.getCorpId() != null && vinParam.getCorpId() > 0,
            "CorpId不能为空");
        List<VinDto> vinList = vinMapper.selectVinOnCorp(vinParam);
        log.info("vinList.size = {}", vinList.size());

        if (CollectionUtils.isNotEmpty(vinList)) {

            // 根据鉴权介质，得到绑定的有效场站ID集合,返回值为<account, List<siteId>>
            Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
                AuthMediaType.VIN,
                vinList.stream().map(VinDto::getVin).collect(Collectors.toList()),
                vinList.get(0).getCommId(),
                vinParam.getCommIdChain());
            Map<String, List<String>> stationMap = optionalMap.orElse(null);

//            List<VinDto> tempVinDto = vinMapper.getUsableStationByVinIdList(
//                    vinList.stream().map(VinDto::getId).collect(Collectors.toList()), vinParam.getCommIdChain());
//            Map<Long, Integer> stationCountMap = tempVinDto.stream().collect(Collectors.toMap(VinDto::getId, VinDto::getUsableStationCount));

            vinList.forEach(e -> {
                e.setUsableStationCount(0);
//                Integer usableStationCount = stationCountMap.get(e.getId());
//                e.setUsableStationCount(usableStationCount == null ? 0 : usableStationCount);

                if (stationMap != null && stationMap.get(e.getVin()) != null) {
                    e.setStationList(stationMap.get(e.getVin()));
                    e.setUsableStationCount(stationMap.get(e.getVin()).size());
                }
            });
        }

        for (VinDto dto : vinList) {
            RBlocUserVo rBlocUserVo = cardMapper.getCorpOrgName(dto.getCorpId(), dto.getUserId(),
                dto.getMobile());
            if (rBlocUserVo != null) {
                dto.setCorpOrgId(rBlocUserVo.getCorpOrgId());
                dto.setCorpOrgName(rBlocUserVo.getCorpOrgName());
            } else {
                log.warn("无法找到用户组织信息, vin = {}", JsonUtils.toJsonString(dto));
            }
        }

        ListResponse<VinDto> result = new ListResponse<>(vinList, pageResult.getTotal());
        return result;
    }

    public ObjectResponse<VinDto2> getVinDto2ById(Long vinId, String commIdChain) {
        VinDto2 vinDto2 = vinMapper.getVinDto2ById(vinId);
        IotAssert.isNotNull(vinDto2, "信息为空");

        // 根据鉴权介质，得到绑定的有效场站ID集合,返回值为<account, List<siteId>>
        Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
            AuthMediaType.VIN,
            List.of(vinDto2.getVin()),
            vinDto2.getCommId(),
            commIdChain);
        Map<String, List<String>> stationMap = optionalMap.orElse(null);
        if (stationMap != null && stationMap.get(vinDto2.getVin()) != null) {
            vinDto2.setSiteIdList(stationMap.get(vinDto2.getVin()));
        }

        ObjectResponse<VinDto2> vinDto2ObjectResponse = dataCoreFeignClient.getVinDto2RankInfo(
            vinDto2.getVin(), commIdChain);
        FeignResponseValidate.check(vinDto2ObjectResponse);
        VinDto2 info = vinDto2ObjectResponse.getData();
        vinDto2.setElec(info.getElec())
            .setT1Elec(info.getT1Elec())
            .setT2Elec(info.getT2Elec())
            .setT3Elec(info.getT3Elec())
            .setT4Elec(info.getT4Elec())
            .setLatestChargingTime(info.getLatestChargingTime())
            .setSiteElecRankList(info.getSiteElecRankList());

        return RestUtils.buildObjectResponse(vinDto2);
    }

    @Override
    public ObjectResponse parseCorpVinExcel(List<List<String>> list, Long corpId,
        Long corpTopCommId) {
        log.info(">> 解析企业平台Vin码 excel文件 corpId: {}, corpTopCommId: {}", corpId,
            corpTopCommId);

        try {
            List<Vin4ManagerVo> vinList = new ArrayList<>(); //excel导入的VIN集合
            List<Vin4ManagerVo> valid = new ArrayList<>(); // 有效
            List<Vin4ManagerVo> invalid = new ArrayList<>(); // 无效
            log.info("从 excel 中获取内容: {}", list);

            list.forEach(card -> {
                Vin4ManagerVo vo = new Vin4ManagerVo();
                vo.setVin(null == card.get(0) || card.get(0) == "" ? null
                    : card.get(0).trim().toUpperCase()); // VIN码
                vo.setCarNo(
                    null == card.get(1) || card.get(1) == "" ? null : card.get(1).trim());    // 车牌号
                vo.setCarDepart(null == card.get(2) || card.get(2) == "" ? null
                    : card.get(2).trim());    // 车队名称
                vo.setLineNum(
                    null == card.get(3) || card.get(3) == "" ? null : card.get(3).trim());    // 线路
                vo.setCarNum(null == card.get(4) || card.get(4) == "" ? null
                    : card.get(4).trim());    // 车辆自编号
                vo.setBrand(
                    null == card.get(5) || card.get(5) == "" ? null : card.get(5).trim());    // 品牌
                vo.setModel(
                    null == card.get(6) || card.get(6) == "" ? null : card.get(6).trim());    // 型号
                vo.setCarLength(null == card.get(7) || card.get(7) == "" ? null
                    : new BigDecimal(card.get(7).trim()));// 车长
                vo.setYear(
                    null == card.get(8) || card.get(8) == "" ? null : card.get(8).trim());    // 年份
                vo.setPhone(
                    null == card.get(9) || card.get(9) == "" ? null : card.get(9).trim());    // 手机号

                ExcelCheckResult checkResult = checkCorpVinFormat(vo);
                if (checkResult.getCode() != ExcelCheckResult.BIND_VIN.getCode()) {
                    vo.setDetail(checkResult.getDesc());
                    invalid.add(vo);
                } else {
                    valid.add(vo);
                }
                vinList.add(vo);
            });
            HashSet<String> vin = new HashSet<>();
            HashSet<String> vinLst = new HashSet<>();
            VinMgnListVo vinMgnListVo = checkCorpVinInExcel(vin, valid, invalid);
            log.info("vinMgnListVo: {}", JsonUtils.toJsonString(vinMgnListVo));
            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
            vinMgnListVo.getInvalid().forEach(vin4ManagerVo -> {
                vinLst.add(vin4ManagerVo.getVin());
            });
            vinMgnListVo = checkCorpVinInExcel(vinLst, vinMgnListVo.getValid(), invalid);
            log.info("vinMgnListVo: {}", JsonUtils.toJsonString(vinMgnListVo));
            vinMgnListVo = checkCorpVinInDatebase(vinMgnListVo.getValid(), invalid, corpId,
                corpTopCommId);
            log.info("vinMgnListVo: {}", JsonUtils.toJsonString(vinMgnListVo));
            List<Vin4ManagerVo> validLst = vinMgnListVo.getValid();
            List<Vin4ManagerVo> invalidLst = vinMgnListVo.getInvalid();
            log.info("<< 解析结果: 有效记录数={}, 无效记录数={}", validLst.size(),
                invalidLst.size());

            return new ObjectResponse<>(new HashMap<String, List<Vin4ManagerVo>>() {{
                put("valid", validLst);
                put("invalid", invalidLst);
            }});
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }

    }

    private ExcelCheckResult checkCorpVinFormat(Vin4ManagerVo vo) {

        // 有效性检测
        if (StringUtils.isEmpty(vo.getVin()) || StringUtils.isEmpty(vo.getPhone())) {
            return ExcelCheckResult.CORP_VIN_FORMAT_INVALID;
        }

        // VIN规则5~17位，只支持数字、字母；
        if (!RegularExpressionUtil.englishNumberAll(vo.getVin(), 5, 17)) {
            return ExcelCheckResult.VIN_FORMAT_INVALID;
        }

        // 车牌号标准输入7或者8位车牌号，不允许存在空格
        if (StringUtils.isNotEmpty(vo.getCarNo())) {
            if (!RegularExpressionUtil.isProductDemandCarNo(vo.getCarNo()) || !(
                vo.getCarNo().length() == 7 || vo.getCarNo().length() == 8)) {
                return ExcelCheckResult.CAR_NO_FORMAT_INVALID;
            }
        }

        // 线路要求输入20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(vo.getLineNum())) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(vo.getLineNum(), 1, 20)) {
                return ExcelCheckResult.LINE_NUM_FORMAT_INVALID;
            }
        }

        // 车队名称要求输入20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(vo.getCarDepart())) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(vo.getCarDepart(), 1, 20)) {
                return ExcelCheckResult.CAR_DEPART_FORMAT_INVALID;
            }
        }

        // 车辆自编号要求输入1~17位数字、字母组成
        if (StringUtils.isNotEmpty(vo.getCarNum())) {
            if (!RegularExpressionUtil.englishNumberAll(vo.getCarNum(), 1, 17)) {
                return ExcelCheckResult.CAR_NUM_FORMAT_INVALID;
            }
        }

        // 品牌 最多20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(vo.getBrand())) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(vo.getBrand(), 1, 20)) {
                return ExcelCheckResult.BRAND_FORMAT_INVALID;
            }
        }

        // 型号 最多20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(vo.getModel())) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(vo.getModel(), 1, 20)) {
                return ExcelCheckResult.MODEL_FORMAT_INVALID;
            }
        }

        // 车长 仅支持输入数字或小数点，0~100
        if (vo.getCarLength() != null) {
            if (!(DecimalUtils.gtZero(vo.getCarLength())
                && DecimalUtils.lt(vo.getCarLength(), BigDecimal.valueOf(100))
                && DecimalUtils.getDecimalPlacesNum(vo.getCarLength()) <= 2)) {
                return ExcelCheckResult.CAR_LENGTH_FORMAT_INVALID;
            }
        }

        // 年份 仅支持输入4位数字
        if (StringUtils.isNotEmpty(vo.getYear())) {
            if (vo.getYear().length() != 4) {
                return ExcelCheckResult.YEAR_FORMAT_INVALID;
            }
        }
        return ExcelCheckResult.BIND_VIN;
    }

    /**
     * 校验数据是否重复
     *
     * @param vinLst  vin的样本数据
     * @param vinList 需要判断是否重复的数据
     * @param invalid 无效的数据
     * @return
     */
    public VinMgnListVo checkCorpVinInExcel(HashSet<String> vinLst, List<Vin4ManagerVo> vinList,
        List<Vin4ManagerVo> invalid) {
        VinMgnListVo vinMgnListVo = new VinMgnListVo();
        List<Vin4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        vinList.forEach(vin -> {
            ExcelCheckResult checkResult = null;
            if (vinLst.add(vin.getVin())) {
                checkResult = ExcelCheckResult.BIND_VIN;
            } else {
                checkResult = ExcelCheckResult.EXCEL_VIN_EXIST;
            }
            vin.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.BIND_VIN.getCode()) {
                invalid.add(vin);
            } else {
                valid.add(vin);
            }
        });
        vinMgnListVo.setValid(valid);
        vinMgnListVo.setInvalid(invalid);
        return vinMgnListVo;
    }

    /**
     * 校验数据
     *
     * @param vinList 需要判断的数据
     * @param invalid 无效的数据
     * @return
     */
    public VinMgnListVo checkCorpVinInDatebase(List<Vin4ManagerVo> vinList,
        List<Vin4ManagerVo> invalid, Long corpId, Long corpTopCommId) {
        VinMgnListVo vinMgnListVo = new VinMgnListVo();
        List<Vin4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(vinList)) {
            vinMgnListVo.setValid(valid);
            vinMgnListVo.setInvalid(invalid);
            return vinMgnListVo;
        }
//        Map<String, Object> params = new HashMap<>();
//        List<CardMgnVo> cList = vinMapper.queryMgmCards(params);
//        Map<String, String> map = cList.stream().collect(Collectors.toMap(CardMgnVo::getCardChipNo, CardMgnVo::getCardStatus));

        List<String> phoneList = vinList.stream().map(Vin4ManagerVo::getPhone)
            .collect(Collectors.toList());
        List<RBlocUser> rBlocUsers = rBlocUserMapper.findByPhoneListAndBlocUserId(phoneList,
            corpId);
        Map<String, Long> stringLongMap = rBlocUsers.stream()
            .collect(Collectors.toMap(RBlocUser::getPhone, RBlocUser::getCorpOrgId));

        // 物理卡号需提前已在系统中导入，且未曾激活
        // 需确保对应的手机号已在人员管理中绑定了相应的组织名称
        vinList.forEach(vin -> {
            ExcelCheckResult checkResult = null;
            VinDto vinDto = vinMapper.selectByVin(vin.getVin(), corpTopCommId);
            Long corpOrgId = stringLongMap.get(vin.getPhone());
            if (vinDto != null) {
                checkResult = ExcelCheckResult.VIN_EXIST;
            } else if (!(corpOrgId != null && corpOrgId > 0)) {
                checkResult = ExcelCheckResult.PHONE_NOT_BIND_ORG;
            } else {
                checkResult = ExcelCheckResult.BIND_VIN;
            }
            vin.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.BIND_VIN.getCode()) {
                invalid.add(vin);
            } else {
                valid.add(vin);
            }
        });
        vinMgnListVo.setValid(valid);
        vinMgnListVo.setInvalid(invalid);
        return vinMgnListVo;
    }

    @Override
    public ListResponse<VinDto> selectAllVinList(String token, Long userId) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        List<Long> ret = merchantFeignClient.getCommIdListByToken(token);
        //FeignResponseValidate.check(ret);
        //JSONObject commIdsJson = merchantFeignClient.getCommIdListByToken(token);
        //        if (commIdsJson == null || commIdsJson.get("status") == null) {
        //            throw new DcServiceException("查询商户及子商户信息失败");
        //        }
        List<Long> commList = ret;
        Map<String, Object> params = new HashMap<>();
        params.put("commIds", commList);
        params.put("userId", userId);
        List<VinDto> vinDtolist = vinMapper.selectAllVinList(params);
        ListResponse res = new ListResponse<>(vinDtolist, (long) vinDtolist.size());
        return res;
    }

    @Override
    public MoveCorpSiteAuthList getMoveCorpVINByCorpId(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        MoveCorpSiteAuthList ret = new MoveCorpSiteAuthList();

        List<SiteAuthMoveCorpVo> vinAuthList = siteAuthRoDs.getMoveCorpVINByCorpId(corpId);
        if (CollectionUtils.isNotEmpty(vinAuthList)) {
            CommercialSimpleVo commercial = commercialRoDs.getCommerial(commId);
            IotAssert.isNotNull(commercial, "找不到对应商户: " + commId);

            List<SiteAuthMoveCorpVo> removeList = vinAuthList.stream()
                .filter(e -> e.getIdChain().indexOf(commercial.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(removeList);

            List<SiteAuthMoveCorpVo> remainList = vinAuthList.stream()
                .filter(e -> e.getIdChain().indexOf(commercial.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(remainList);

            // 位于 remove 列表的相同元素需要从 remain 中剔除
            Set<String> removeSet = ret.getRemoveList().stream().map(SiteAuthMoveCorpVo::getAccount)
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(removeSet) && CollectionUtils.isNotEmpty(
                ret.getRemainList())) {
                List<SiteAuthMoveCorpVo> collect = ret.getRemainList()
                    .stream()
                    .filter(e -> !removeSet.contains(e.getAccount()))
                    .collect(Collectors.toList());
                ret.setRemainList(collect);
            }
        }

        return ret;
    }

    public Long moveCorpVINAuthByCorpId(Long corpId, Long commId) {

        log.info("修改VIN鉴权信息: corpId: {}, corpId: {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");
        CommercialSimpleVo commercial = commercialRoDs.getCommerial(commId);
        IotAssert.isNotNull(commercial, "找不到对应商户: " + commId);

        IotAssert.isNotBlank(commercial.getIdChain(), "找不到该企业idChain");

        IotAssert.isNotNull(commercial.getTopCommId(), "找不到该商户的顶级商户id");

        Long ret = siteAuthDs.moveCorpVINByCorpId(corpId, commercial.getTopCommId(),
            commercial.getIdChain());

        log.info("移除了vin auth记录 {} 条", ret);

        return ret;

    }

    public Long moveCorpVINByCorpId(Long corpId, Long commId) {

        log.info("修改vin所属商户: corpId: {}, corpId: {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        long ret = vinMapper.moveCorp(corpId, commId);

        log.info("修改了t_vin记录 {} 条", ret);

        return ret;
    }

    @Override
    public ObjectResponse selectCarNoByVins(VINCarNoParam param) {
        if (CollectionUtils.isEmpty(param.getVinList())) {
            return RestUtils.buildObjectResponse(List.of());
        }

        List<VinDto> vinDtos = vinMapper.selectCarNoByVins(param);
        Map<String, String> resultMap = new HashMap<>();
        if (vinDtos != null && vinDtos.size() > 0) {
//            vinDtos.stream().forEach(vinDto -> {
//                resultMap.put(vinDto.getVin(), vinDto.getCarNo());
//            });
            resultMap = vinDtos.stream()
                .filter(i -> StringUtils.isNotBlank(i.getCarNo()))
                .collect(Collectors.toMap(v -> v.getCommId() + v.getVin(), VinDto::getCarNo));
        }
//        ObjectResponse res = new ObjectResponse<>(resultMap);
        return RestUtils.buildObjectResponse(resultMap);
    }

    @Override
    public ObjectResponse<VinDto> selectByVin(String vin, Long topCommId) {
        log.info("selectByVin。vin: {}", vin);

        VinDto vinDto = vinMapper.selectByVin(vin, topCommId);

        log.info("selectByVin。vin: {}, vinDto: {}", vin, vinDto);

        ObjectResponse res = new ObjectResponse<>(vinDto);

        return res;
    }

    @Override
    public VinDto getVinByUid(Long topCommId, Long uid,
        String vin) {
        return vinMapper.getVinByUid(topCommId, uid, vin);
    }

    @Override
    public ObjectResponse<VinDto> selectByCarNo(String carNo, Long userId) {
        log.info("selectByCarNo。carNo: {}", carNo);

        VinDto vinDto = vinMapper.selectByCarNo(carNo, userId);

        log.info("selectByCarNo。carNo: {}, vinDto: {}", carNo, vinDto);

        ObjectResponse res = new ObjectResponse<>(vinDto);

        return res;
    }

    public VinDto selectByVinAndCorp(String vin, Long corpId) {
        log.info("selectByVin。vin: {}", vin);

        VinDto vinDto = vinMapper.selectByVinAndCorp(vin, corpId);

        log.info("selectByVin。vin: {}, vinDto: {}", vin, vinDto);

        return vinDto;
    }

    @Override
    public List<VinDto> getActiveListOfUserId(Long index, Integer size,
        Long topCommId,
        Boolean isPrimary, Long userId) {
        List<VinDto> vinList = vinMapper.findByUserIdAndStatus((index - 1) * size, size,
            userId, isPrimary, 1);
        if (CollectionUtils.isEmpty(vinList) || topCommId == null) {
            return vinList;
        }

        List<UserOpenidPo> openids = userOpenidRoDs.getUserOpenidList(topCommId, userId,
            List.of(UserOpenidType.ALIPAY_VIN));
        Set<String> openidVins = openids.stream().map(UserOpenidPo::getOpenid)
            .collect(Collectors.toSet());
        vinList.stream().forEach(v -> {
            if (openidVins.contains(v.getVin())) {
                v.setAlipayVin(true);
            }
        });
        return vinList;
    }

    @Override
    public List<VinDto> getInactiveListOfUserId(Long index, Integer size, Long topCommId,
        Long userId) {
        List<VinDto> vinList = vinMapper.findByUserIdAndStatus((index - 1) * size, size,
            userId, null, 0);
        if (CollectionUtils.isEmpty(vinList) || topCommId == null) {
            return vinList;
        }
        List<UserOpenidPo> openids = userOpenidRoDs.getUserOpenidList(topCommId, userId,
            List.of(UserOpenidType.ALIPAY_VIN));
        Set<String> openidVins = openids.stream().map(UserOpenidPo::getOpenid)
            .collect(Collectors.toSet());
        vinList.stream().forEach(v -> {
            if (openidVins.contains(v.getVin())) {
                v.setAlipayVin(true);
            }
        });
        return vinList;
    }

    @Override
    public int activeVin(VinParam vinParam) {
        log.info(">> 激活使用VIN码: vinParam={}", vinParam);

        // 用户Id
        if (null == vinParam.getUserId()) {
            log.info("<< 用户Id不能为空");
            throw new DcArgumentException("请输入正确的用户Id");
        }

        // VIN码Id
        if (null == vinParam.getId()) {
            log.info("<< VIN码Id不能为空");
            throw new DcArgumentException("请输入正确的VIN码Id");
        }
        if (null == vinParam.getVin()) {
            log.info("<< VIN码不能为空");
            throw new DcArgumentException("请输入正确的VIN码");
        }

        // 顶级商户Id
        if (null == vinParam.getCommId()) {
            log.info("<< 顶级商户Id不能为空");
            throw new DcArgumentException("请输入正确的顶级商户Id");
        }

        // 激活的条件判断
        // (1) 该VIN码是否已经绑定在当前集团商户的其他客户下
        List<VinDto> vinSinUserId = vinMapper.selectCountByCommIdSinUserId(
            vinParam.getCommId(),
            vinParam.getUserId(),
            vinParam.getVin());
        if (vinSinUserId.size() != 0) { // 当前集团商户存在用户的绑定到该VIN码
            if (vinSinUserId.stream()
                .anyMatch(item -> VIN_STATUS_ACTIVE.equals(item.getStatus()))) { // VIN码被其他用户占用
                log.info("<< 该VIN码被其他客户占用");
                throw new DcServiceException("激活失败，该VIN码被其他客户占用");
            } // end else -> 其他已经废弃，可重新激活使用
        } // end else -> 当前商户不存在其他用户使用当前的VIN码

        // (2) 该VIN码已经被集团后台删除
        VinDto dto = vinMapper.getById(vinParam.getId());
        if (null == dto || VIN_DISENABLE.equals(dto.getEnable())) {
            log.info("<< 该VIN码已被后台删除");
            throw new DcServiceException("该VIN码已被后台删除");
        }

        // 已经是激活的状态
        if (VIN_STATUS_ACTIVE.equals(dto.getStatus())) {
            log.info("<< 当前VIN码处于激活状态");
            return 1;
        }

        // 重新绑定使用的更新条件
        VinParam updateParam = new VinParam();
        updateParam.setId(vinParam.getId())
            .setStatus(VIN_STATUS_ACTIVE);
        log.info("update param={}", updateParam);

        final List<VinDto> originalPrimaryList = getUserPrimaryVin(vinParam.getUserId());
        if (CollectionUtils.isEmpty(originalPrimaryList)) {
            // 设置当前新增的为默认
            vinParam.setIsPrimary(Boolean.TRUE);
        }

        Integer result = vinMapper.update(updateParam);
        log.info("<< result={}", result);
        return result;
    }

    @Override
    public ObjectResponse<Integer> inactiveVin(Long vinId) {
        log.info(">> 停用VIN码: vinId={}", vinId);

        if (null == vinId) {
            log.info("<< VIN码Id不能为空");
            throw new DcArgumentException("请输入正确的VIN码Id");
        }

        // 获取VIN码
        VinDto vinDto = vinMapper.getById(vinId);
        if (null == vinDto) {
            log.info("<< 查询没有对应的VIN码");
            throw new DcServiceException("查询没有对应的VIN码");
        }

        IotAssert.isNotNull(vinDto.getUserId(), "vin未绑定到用户");

        // 是否存在正在进行的订单
        String vin = vinDto.getVin(); // vin 码
        if (StringUtils.isNotBlank(vin)) {
            ObjectResponse<Boolean> res = tradingFeignClient.checkOrderOfVin(vinDto.getUserId(),
                vin, vinDto.getCommId());
            log.info("查询结果: result={}", res);
            FeignResponseValidate.check(res);
            if (res.getData()) {
                log.info("<< 车辆正在充电中, 不能进行解绑操作");
                ObjectResponse<Integer> r = new ObjectResponse<>(-1);
                r.setError("车辆正在充电中, 请先结束订单, 再解绑");
                return r;
            }
        }

        // 停止使用的更新条件
        VinParam updateParam = new VinParam();
        updateParam.setId(vinId)
            .setStatus(VIN_STATUS_INACTIVE);

        if (Boolean.TRUE.equals(vinDto.getIsPrimary())) {
            log.info("取消默认车牌号");
            updateParam.setIsPrimary(false);

            setNewVinAsPrimary(vinDto.getUserId(), vinId);
        }

        log.info("update param={}", updateParam);

        Integer result = vinMapper.update(updateParam);

        // 如果这个VIN开通过支付宝即插即充功能,需要通过PCP去做关闭操作
        if (vinDto.getCommId() != null && vinDto.getUserId() != null) {
            UserOpenidPo alipayVin = userOpenidRoDs.getByUid(vinDto.getCommId(),
                UserOpenidType.ALIPAY_VIN, vinDto.getUserId(), null);
            if (alipayVin != null && StringUtils.isNotBlank(alipayVin.getAppId())) {
                UserOpenidPo alipayOpenid = userOpenidRoDs.getByUid(vinDto.getCommId(),
                    UserOpenidType.ALIPAY_OPENID, vinDto.getUserId(), null);
                if (alipayOpenid != null) {
                    PcpCloseVinParam closeVinParam = new PcpCloseVinParam();
                    closeVinParam.setAppId(alipayVin.getAppId())
                        .setOpenid(alipayOpenid.getOpenid())
                        .setVin(vinDto.getVin())
                        .setCarNo(alipayVin.getExtraA());
                    log.info("调用pcp接口,停用支付宝即插即充功能. closeVinParam= {}",
                        closeVinParam);
                    pcpAsyncFeignClient.closeAlipayVin(closeVinParam)
                        .block(Duration.ofSeconds(50L));
                }
            }
        }
        log.info("<< result={}", result);
        return new ObjectResponse<>(result);
    }

    @Override
    public List<String> getStationsOfVin(Long vinId) {
        log.info(">> 获取VIN码适用场站: vinId={}", vinId);

        if (null == vinId) {
            log.info("<< 参数VIN码Id不能为空");
            throw new DcArgumentException("请输入正确的VIN码Id");
        }

        VinDto dto = vinMapper.getById(vinId);
        if (null == dto) {
            log.info("<< 该VIN码Id没有对应信息: vinId={}", vinId);
            throw new DcServiceException("没有该VIN码相关信息");
        }

        List<SiteAuthVo> siteAuthList = this.siteAuthDs.findByTypeAndAccountAndTopCommId(
            AuthMediaType.VIN, dto.getVin(), dto.getCommId(),
            List.of(SiteStatus.ONLINE, SiteStatus.UNAVAILABLE), true, null);
        List<String> result = siteAuthList.stream().map(SiteAuthVo::getSiteId)
            .collect(Collectors.toList());
//        String stations = dto.getStation();
//        if (null == stations) {
//            log.info("<< 无可用场站");
//            return new ArrayList<>();
//        }
//
//        List<String> result = Arrays.asList(stations.split(","));
//        // BUG0919-156 @李楠楠: 无论是场站列表还是充电卡、VIN码使用范围中的场站，已经被删除、被下线的场站，小程序都不显示，只显示正常运营的和维护中的场站
//        ListResponse<SiteInMongo> response = siteFeignClient.findBySiteIds(result);
//        FeignResponseValidate.check(response);
//        List<SiteInMongo> siteInMongoList = response.getData();
//        result = siteInMongoList.stream().filter(e -> e.getStatus() == SiteStatus.ONLINE || e.getStatus() == SiteStatus.UNAVAILABLE)
//                .map(SiteInMongo::getId).collect(Collectors.toList());
        log.info("<< result={},result size={}", result, result.size());
        return result;
    }

    @Override
    public Boolean ownVin(Long userId, Long commId) {
        return null != vinMapper.findOneByUserId(userId, commId);
    }

    @Override
    public ListResponse<VinDto> getByIdList(List<Long> idList) {
        return new ListResponse<VinDto>(vinMapper.getByIdList(idList));
    }

    @Override
    public void syncVinSiteAuth() {
        log.info(">> vin start");
        long start = 0;
        int size = 100;

        List<VinDto> vinList;
        do {
            vinList = this.vinMapper.findAll(start, size);
            start = start + vinList.size();

            vinList.forEach(this::updateSiteAuth);
            log.info("to start = {}", start);
        } while (CollectionUtils.isNotEmpty(vinList));
        log.info("<< card end");
    }

    @Override
    public Integer updateCarNo(VinCarNoParam vinCarNoParam) {
        IotAssert.isNotNull(vinCarNoParam.getId(), "请传入vin id");
        VinDto vin = vinMapper.getById(vinCarNoParam.getId());
        IotAssert.isNotNull(vin, "无法修改，vin不存在");

        vin.setCarNo(vinCarNoParam.getCarNo());

        VinParam vinParam = new VinParam();
        vinParam.setId(vin.getId()).setCarNo(vinCarNoParam.getCarNo());

        if (this.isExistsCarNo(vinCarNoParam)) {
            log.info("车牌号已存在, 不更新");
            IotAssert.isTrue(false, "修改失败，该车牌号已存在");
            return 0;
        } else {
            return vinMapper.update(vinParam);
        }
    }

    @Override
    public Integer createCarNo(VinCarNoParam vinCarNoParam) {

        CommercialSimpleVo commercial = commercialRoDs.getCommerial(vinCarNoParam.getCommId());

        VinParam vinParam = new VinParam();
        vinParam.setCarNo(vinCarNoParam.getCarNo())
            .setUserId(vinCarNoParam.getUserId())
            .setCommId(commercial.getTopCommId())
            .setSubCommId(vinCarNoParam.getCommId())
            .setVin("");

        if (this.isExistsCarNo(vinCarNoParam)) {
            log.info("车牌号已存在, 不创建");
            IotAssert.isTrue(false, "添加失败，该车牌号已存在");
            return 0;
        } else {
            // 之前的默认
            final List<VinDto> originalPrimaryList = getUserPrimaryVin(vinCarNoParam.getUserId());
            if (CollectionUtils.isEmpty(originalPrimaryList) &&
                StringUtils.isNotBlank(vinCarNoParam.getCarNo())) {
                // 设置当前新增的为默认
                vinParam.setIsPrimary(Boolean.TRUE);
            }

            return vinMapper.insert(vinParam);
        }
    }

    /**
     * 按场站下发，传入siteId列表
     *
     * @param param
     * @return
     */
    @Override
    public Integer siteAuthVin(SiteAuthVinParam param) {

        // 获取场站配置组
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()),
            "请传入需要下发的目标场站列表");
        List<SiteAuthVinPo> bySiteIds = siteAuthVinRoDs.getBySiteIds(param.getSiteIdList());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(bySiteIds), "目标场站未配置vin列表");
        Map<String, List<SiteAuthVinPo>> siteVinMap =
            bySiteIds.stream().collect(Collectors.groupingBy(SiteAuthVinPo::getSiteId));

        // 获取待下发的桩列表
        long page = 0L;
        boolean loop = true;
        List<EvseModelVo> evseRepos = new ArrayList<>();
        while (loop) {
            ListEvseParam listEvseParam = new ListEvseParam();
            listEvseParam.setSiteIdList(param.getSiteIdList())
                .setSize(999)
                .setStart(page);
            ListResponse<EvseModelVo> evseModelVoList = iotDeviceMgmFeignClient.getEvseModelVoList(
                listEvseParam);
            FeignResponseValidate.check(evseModelVoList);
            if (CollectionUtils.isEmpty(evseModelVoList.getData())
                || evseModelVoList.getData().size() < 999) {
                loop = false;
            }

            if (CollectionUtils.isNotEmpty(param.getEvseNoList())) {
                // 仅加入关注的桩
                evseRepos.addAll(evseModelVoList.getData()
                    .stream()
                    .filter(e -> param.getEvseNoList().contains(e.getEvseId()))
                    .collect(Collectors.toList()));
            } else {
                evseRepos.addAll(evseModelVoList.getData());
            }
            page++;
        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(evseRepos), "待下发的桩列表为空");

        List<SiteAuthVinLogPo> authVinLogPos = evseRepos.stream()
            .map(e -> {
                List<SiteAuthVinPo> siteAuthVinPos = siteVinMap.get(e.getSiteId());
                if (StringUtils.isBlank(e.getSiteId()) || CollectionUtils.isEmpty(siteAuthVinPos)) {
                    return null;
                } else {
                    SiteAuthVinLogPo ret = new SiteAuthVinLogPo();
                    return ret.setVins(siteAuthVinPos.stream().map(SiteAuthVinPo::getVin)
                            .collect(Collectors.toList()))
                        .setSiteId(e.getSiteId())
                        .setEvseId(e.getEvseId())
                        .setStatus(this.computeAuthVinLogStatus(e));
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(authVinLogPos), "无需要下发的桩列表");

        // 下发记录处理
        log.info("新增下发记录: {}", siteAuthVinLogRwDs.batchInsert(authVinLogPos));

        List<SiteAuthVinLogPo> sSendVinAuthEvseList = authVinLogPos.stream()
            .filter(e -> e.getStatus() == DcBizConstants.EVSE_LOCAL_VIN_AUTH_INIT)
            .collect(Collectors.toList());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(sSendVinAuthEvseList), "无符合下发需求的桩");

        ModifyEvseCfgParam cfgParam = new ModifyEvseCfgParam();
        cfgParam.setSiteAuthVinList(sSendVinAuthEvseList);
        cfgParam.setEvseNoList(sSendVinAuthEvseList.stream()
            .map(SiteAuthVinLogPo::getEvseId)
            .collect(Collectors.toList()));
        BaseResponse baseResponse = this.iotBizClient.modifyEvseCfgV2(cfgParam);
        log.info("下发桩关联的场站配置信息完成");
        if (null == baseResponse || baseResponse.getStatus() != 0) {
            log.warn("下发本地vin鉴权iot返回异常");
            int ret = siteAuthVinLogRwDs.batchSetStatus(sSendVinAuthEvseList.stream()
                .map(SiteAuthVinLogPo::getId)
                .collect(Collectors.toList()), DcBizConstants.EVSE_LOCAL_VIN_AUTH_FAIL);
            log.info("修改 {} 条记录为失败", ret);
        } else {
            log.warn("下发本地vin鉴权iot返回正常: {}", baseResponse);
            int ret = siteAuthVinLogRwDs.batchSetStatus(sSendVinAuthEvseList.stream()
                .map(SiteAuthVinLogPo::getId)
                .collect(Collectors.toList()), DcBizConstants.EVSE_LOCAL_VIN_AUTH_SEND);
            log.info("修改 {} 条记录为下发中", ret);
        }
        return 0;
    }

    private int computeAuthVinLogStatus(EvseModelVo vo) {
        if (!NumberUtils.equals(DcBizConstants.PROTOCOL_VERSION_370, vo.getProtocolVer()) ||
            CollectionUtils.isEmpty(vo.getFlags()) ||
            !vo.getFlags().contains(DcBizConstants.EVSE_LOCAL_VIN_AUTH_FLAG)) {
            return DcBizConstants.EVSE_LOCAL_VIN_AUTH_NA;
        } else {
            return DcBizConstants.EVSE_LOCAL_VIN_AUTH_INIT;
        }
    }

    /**
     * 用户是否已存在此车牌号
     *
     * @param vinCarNoParam
     * @return
     */
    private boolean isExistsCarNo(VinCarNoParam vinCarNoParam) {

        List<VinDto> vinDtos = vinMapper.findByUserIdAndStatus(0L, 999,
            vinCarNoParam.getUserId(), null, 1);

        if (vinCarNoParam.getId() != null) {
            // 修改时，判断除了本身之外是否存在相同的车牌号
            return vinDtos.stream().anyMatch(e ->
                vinCarNoParam.getCarNo() != null &&
                    vinCarNoParam.getCarNo().equals(e.getCarNo()) &&
                    !e.getId().equals(vinCarNoParam.getId()));
        }

        return vinDtos.stream()
            .filter(e -> vinCarNoParam.getCarNo() != null && vinCarNoParam.getCarNo()
                .equals(e.getCarNo()))
            .count() != 0;
//        VinSearchParamComm vinSearchParamComm = new VinSearchParamComm();
//        vinSearchParamComm.setUserId(vinCarNoParam.getUserId())
//                .setCarNo(vinCarNoParam.getCarNo());
//        List<VinDto> vinDtos = vinMapper.selectComm(vinSearchParamComm);

//        return CollectionUtils.isNotEmpty(vinDtos);
    }

    public List<VinDto> getCarNoListByUserIds(List<Long> ids, Long topCommId) {
        return vinMapper.getCarNoListByUserIds(ids, topCommId);
    }

    public List<String> getEvseListByVin(String siteId, String vin) {
        return siteAuthVinLogRoDs.getEvseListByVin(siteId, vin);
    }

    public List<SiteAuthVinLogPo> getSiteAuthVinTime(List<String> evseNoList) {
        return siteAuthVinLogRoDs.getSiteAuthVinTime(evseNoList);
    }

    public SiteAuthVinLogPo getVinListByEvse(String evseId) {
        return siteAuthVinLogRoDs.getVinListByEvse(evseId);
    }

    public List<SiteAuthVinPo> getVinListBySiteId(String siteId) {
        return siteAuthVinRoDs.getBySiteIds(List.of(siteId));
    }

    public ListResponse<SiteListVo> getSiteListByVin(String vin, Long start, Long size) {
        Long total = siteAuthVinRoDs.getSiteAmountByVin(vin);
        List<SiteListVo> siteListVoList = new ArrayList<>();
        if (total == null) {
            return RestUtils.buildListResponse(siteListVoList);
        }
        siteListVoList = siteAuthVinRoDs.getSiteListByVin(vin, start, size);
        List<String> siteIdList = siteListVoList.stream().map(SiteListVo::getSiteId)
            .collect(Collectors.toList());

        //获取场站下所有桩的列表
        long page = 0L;
        boolean loop = true;
        List<EvseModelVo> evseRepos = new ArrayList<>();
        while (loop) {
            ListEvseParam listEvseParam = new ListEvseParam();
            listEvseParam.setSiteIdList(siteIdList)
                .setSize(999)
                .setStart(page);
            ListResponse<EvseModelVo> evseModelVoList = iotDeviceMgmFeignClient.getEvseModelVoList(
                listEvseParam);
            FeignResponseValidate.check(evseModelVoList);
            if (CollectionUtils.isEmpty(evseModelVoList.getData())
                || evseModelVoList.getData().size() < 999) {
                loop = false;
            }
            evseRepos.addAll(evseModelVoList.getData());
            page++;
        }

//        IotAssert.isTrue(CollectionUtils.isNotEmpty(evseRepos), "待下发的桩列表为空");
        List<EvseModelVo> evseList = evseRepos.stream().filter(e ->
            (NumberUtils.equals(DcBizConstants.PROTOCOL_VERSION_370, e.getProtocolVer()) &&
                CollectionUtils.isNotEmpty(e.getFlags()) &&
                e.getFlags().contains(DcBizConstants.EVSE_LOCAL_VIN_AUTH_FLAG))

        ).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(evseList)) {
            Map<String, List<EvseModelVo>> map = evseList.stream()
                .collect(Collectors.groupingBy(EvseModelVo::getSiteId));
            siteListVoList.forEach(e -> {
                if (map.containsKey(e.getSiteId())) {
                    e.setAmount(map.get(e.getSiteId()).size());
                }
            });
        }

        return RestUtils.buildListResponse(siteListVoList, total);
    }

    public ObjectResponse<VinDto> selectByVinWithAuth(String vin, Long topCommId) {
        VinDto dto = siteAuthVinRoDs.selectByVinWithAuth(vin, topCommId);
        if (dto != null) {
            if (dto.getCorpId() != null && dto.getCorpId() > 0) {
                CorpPo corpPo = corpRwDs.getCorp(dto.getCorpId(), false);
                dto.setCorpSettlementType(corpPo.getSettlementType());
            }
        }
        return RestUtils.buildObjectResponse(siteAuthVinRoDs.selectByVinWithAuth(vin, topCommId));
    }

    /**
     * 逻辑删除车牌号，可能选取新的车牌号为默认
     *
     * @param vinId
     * @param userId
     * @return
     */
    @Override
    @Transactional
    public boolean disableCarNoByVinId(Long vinId, Long userId) {
        final UserPo cusById = userRwDs.getCusById(userId, true);
        IotAssert.isNotNull(cusById, "找不到用户");

        VinParam vinParam = new VinParam();
        vinParam.setUserId(userId);
        vinParam.setEnable(VIN_ENABLE);
        vinParam.setId(vinId);
        vinParam.setSize(999);

        final List<VinDto> carNoListByParam = vinMapper.getCarNoListByParam(vinParam);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(carNoListByParam), "找不到车牌号");

        final VinDto vinDto = carNoListByParam.get(0);

        VinParam vinDisableParam = new VinParam();
        vinDisableParam.setId(vinId);
        vinDisableParam.setIsPrimary(Boolean.FALSE);
        vinDisableParam.setEnable(VIN_DISENABLE);
        Integer ret = vinMapper.update(vinDisableParam);
        IotAssert.isTrue(ret == 1, "修改VIN码失败");

        if (Boolean.TRUE.equals(vinDto.getIsPrimary())) {
            setNewVinAsPrimary(userId, vinId);
//            log.debug("原先车牌号是默认车牌号，尝试指定新的");
//            VinParam vinNewPrimaryParam = new VinParam();
//            vinNewPrimaryParam.setUserId(userId);
//            vinNewPrimaryParam.setEnable(VIN_ENABLE);
//            vinNewPrimaryParam.setIsPrimary(Boolean.FALSE);
//            vinNewPrimaryParam.setExcludeIds(List.of(vinId));
//            vinNewPrimaryParam.setSize(999);
//
//            final List<VinDto> carNoListNoDefault = vinMapper.getCarNoListByParam(vinNewPrimaryParam);
//            if(CollectionUtils.isNotEmpty(carNoListNoDefault)) {
//                log.debug("取出第一个，作为默认");//id最大的一个
//                final VinDto vinDtoNewDefault = carNoListNoDefault.get(0);
//                VinParam vinNewPrimary = new VinParam();
//                vinNewPrimary.setId(vinDtoNewDefault.getId());
//                vinNewPrimary.setIsPrimary(Boolean.TRUE);
//                Integer retNewPrimary = vinMapper.update(vinNewPrimary);
//                IotAssert.isTrue(retNewPrimary == 1, "修改新的默认VIN码失败");
//            } else {
//                log.info("没有可供设置为默认的车牌号");
//            }
        }

        return true;
    }

    @Override
    @Transactional
    public boolean setAsPrimaryCarNoByVinId(Long vinId, Long userId) {
        final UserPo cusById = userRwDs.getCusById(userId, true);
        IotAssert.isNotNull(cusById, "找不到用户");

        VinParam vinParam = new VinParam();
        vinParam.setUserId(userId);
        vinParam.setEnable(VIN_ENABLE);
        vinParam.setId(vinId);
        vinParam.setSize(999);

        final List<VinDto> carNoListByParam = vinMapper.getCarNoListByParam(vinParam);
        IotAssert.isTrue(CollectionUtils.isNotEmpty(carNoListByParam), "找不到车牌号");

        final VinDto vinDto = carNoListByParam.get(0);

        if (Boolean.TRUE.equals(vinDto.getIsPrimary())) {
            log.info("该车牌号已设为默认");
            return true;
        }

        // 之前的默认
        final List<VinDto> originalPrimaryList = getUserPrimaryVin(userId);
        if (CollectionUtils.isNotEmpty(originalPrimaryList)) {
            final List<Long> primaryVinIdList = originalPrimaryList.stream()
                .map(VinDto::getId)
                .collect(Collectors.toList());
            log.info(">>取消设为默认： {}", primaryVinIdList);
            final Integer integer = vinMapper.setPrimary(primaryVinIdList, false);
            log.info("<<取消设为默认： {}", integer);
        }

        // 设为默认
        log.info(">>设为默认： {}", vinId);
        final Integer integer = vinMapper.setPrimary(List.of(vinId), true);
        log.info("<<设为默认： {}", integer);

        return true;
    }

    @Override
   public ListResponse<VinDto> selectVinListForCorp(ListChargeOrderBiByVinParam param){
        return RestUtils.buildListResponse(vinMapper.findVinList(param));
   }

}