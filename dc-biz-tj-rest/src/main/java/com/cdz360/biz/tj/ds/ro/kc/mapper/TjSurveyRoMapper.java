package com.cdz360.biz.tj.ds.ro.kc.mapper;


import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface TjSurveyRoMapper {


    TjSurveyPo getByNo(@Param("no") String no);

    List<TjSurveyPo> findTjSurvey(ListTjSurveyParam param);

    long countTjSurvey(ListTjSurveyParam param);

    TjSurveyBiVo tjSurveyBi(TjSurveyBiParam param);

    List<TjSurveyPo> repeatSurvey(RepeatSurveyParam param);
}

