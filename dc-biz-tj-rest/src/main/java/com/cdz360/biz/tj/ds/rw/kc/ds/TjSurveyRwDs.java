package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyRwMapper;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyRwDs {


    @Autowired

    private TjSurveyRwMapper tjSurveyRwMapper;


    public TjSurveyPo getByNo(String no, boolean lock) {

        return this.tjSurveyRwMapper.getByNo(no, lock);

    }


    public boolean insertTjSurvey(TjSurveyPo tjSurveyPo) {
        return this.tjSurveyRwMapper.insertTjSurvey(tjSurveyPo) > 0;
    }


    public boolean updateTjSurvey(TjSurveyPo tjSurveyPo) {
        return this.tjSurveyRwMapper.updateTjSurvey(tjSurveyPo) > 0;
    }


}

