package com.cdz360.biz.model.cus.site.param;

import com.cdz360.biz.model.cus.site.po.SiteWhitelistPo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * SiteWhitelistParam
 *
 * @since 1/13/2021 10:40 AM
 * <AUTHOR>
 */
@Schema(description = "场站白名单")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class SiteWhitelistParam extends SiteWhitelistPo {
    @Schema(description = "顶级商户id，后端填写", hidden = true)
    private Long topCommId;

    private String siteName;

    private String corpName;

}