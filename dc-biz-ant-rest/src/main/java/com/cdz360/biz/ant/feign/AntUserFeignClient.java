package com.cdz360.biz.ant.feign;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.ListResponseEvseList;
import com.cdz360.biz.ant.domain.RelOperator;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.domain.vo.GroupUserListVo;
import com.cdz360.biz.ant.domain.vo.OperatorInfoVo;
import com.cdz360.biz.ant.domain.vo.UserAndDynamicAndCardsVo;
import com.cdz360.biz.ant.domain.vo.UserGroupVo;
import com.cdz360.biz.ant.domain.vo.UserSampleVo;
import com.cdz360.biz.ant.domain.vo.UserVo;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationCheckParam;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.dashboard.CommPropertyCountBiVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.commercialUser.param.UserRegisterParam;
import com.cdz360.biz.model.common.po.CustomerAttractPo;
import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteListVo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.model.cus.corp.dto.CorpInvoiceInfoDto;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.BatchUpdateSiteAuthVo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.cus.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.cus.soc.param.SocStrategyDict;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyVinVo;
import com.cdz360.biz.model.cus.user.dto.UserAndBalanceAndTokenVo;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.cus.wallet.param.ListRefundOrderParam;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountEx;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.Map;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 用户操作的UserFeignClient
 *
 * <AUTHOR>
 * @since 2018.11.23
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER, fallbackFactory = AntUserClientHystrixFactory.class)
public interface AntUserFeignClient {

    /**
     * 新增运营商关系配置
     *
     * @param relOperator
     * @return
     */
    @RequestMapping(value = "/insertBatchRelOperator")
    BaseResponse insertBatchRelOperator(@RequestBody RelOperator relOperator);

    @GetMapping("/api/corp/getOrgById")
    ListResponse<CorpOrgVO> getOrgById(@RequestParam("corpId") Long corpId,
        @RequestParam("id") Long id, @RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size);

    @GetMapping("/api/corp/getCorpOrgByIdAndLevel")
    ObjectResponse<CorpOrgPo> getCorpOrgByIdAndLevel(@RequestParam("corpId") Long corpId,
        @RequestParam("orgLevel") Integer orgLevel);

    @GetMapping("/api/corp/getOrgByAccount")
    ObjectResponse<CorpOrgLoginVo> getOrgByAccount(@RequestParam("account") String account);

//    /**
//     * 新增或者更新企业组织
//     *
//     * @param corpOrgPo
//     * @return
//     */
//    @PostMapping(value = "/api/corp/addOrUpdateCorpOrg")
//    BaseResponse addOrUpdateCorpOrg(@RequestBody CorpOrgPo corpOrgPo);

    @GetMapping(value = "/api/corp/getOrgList")
    ListResponse<CorpOrgVO> getOrgTree(@RequestParam("corpId") Long corpId,
        @RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size);


    /**
     * 获取所有的运营商信息基本信息（客户运营商、设备运营商）
     *
     * @return
     */
    @RequestMapping(value = "/api/hlht/queryAllOperatorInfo")
    ListResponse<OperatorInfoVo> queryAllOperators();


    /**
     * 根据用户ID查询基本资料
     *
     * @param uid         用户ID
     * @param commIdChain
     * @return
     */
    @RequestMapping(value = "/api/user/findUserAndDynamicByCommIdAndUid", method = RequestMethod.POST)
    ObjectResponse<UserAndDynamicAndCardsVo> findUserAndDynamicByCommIdAndUid(
        @RequestParam("uid") Long uid,
        @RequestParam("topCommId") Long topCommId,
        @RequestParam("commIdChain") String commIdChain);


    /**
     * 根据 客户分组名称 查询 客户分组列表
     *
     * @param _index
     * @param _size
     * @param groupName  分组名称
     * @param commIdList 商户ID及子商户ID集合
     * @return
     */
    @RequestMapping(value = "/api/userGroup/queryUserGroupList", method = RequestMethod.GET)
    ListResponse<UserGroupVo> queryUserGroupList(@RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size,
        @RequestParam(value = "groupName") String groupName,
        @RequestParam(value = "commIdList") List<Long> commIdList);


    /**
     * 根据 客户分组Id 查询 客户分组详情
     *
     * @param userGroupId 客户分组Id
     * @param commIdList  商户ID及子商户ID集合
     * @return
     */
    @RequestMapping(value = "/api/userGroup/queryUserGroupById", method = RequestMethod.GET)
    ObjectResponse<GroupUserListVo> queryUserGroupById(
        @RequestParam(value = "userGroupId") Long userGroupId,
        @RequestParam(value = "commIdList") List<Long> commIdList);


    /**
     * 获取能分组的客户信息
     *
     * @param commIdList 商户id列表
     * @return
     */
    @RequestMapping(value = "/api/userGroup/queryNegationUsers", method = RequestMethod.GET)
    ListResponse<UserSampleVo> queryNegationUsers(
        @RequestParam(value = "commIdList") List<Long> commIdList);

    /**
     * 修改客户分组
     *
     * @param commId          商户id
     * @param groupId         分组id
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户列表
     * @return
     */
    @RequestMapping(value = "/api/userGroup/updateUserGroup", method = RequestMethod.POST)
    BaseResponse updateUserGroup(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "groupId") Long groupId,
        @RequestParam(value = "groupName") String groupName,
        @RequestParam(value = "groupDescrition", required = false) String groupDescrition,
        @RequestParam(value = "userIds") List<Long> userIds);

    /**
     * 添加客户分组
     *
     * @param commId          商户id
     * @param groupName       分组名称
     * @param groupDescrition 分组描述
     * @param userIds         客户列表
     * @return
     */
    @RequestMapping(value = "/api/userGroup/addUserGroup", method = RequestMethod.POST)
    BaseResponse addUserGroup(@RequestParam(value = "commId") Long commId,
        @RequestParam(value = "groupName") String groupName,
        @RequestParam(value = "groupDescrition", required = false) String groupDescrition,
        @RequestParam(value = "userIds") List<Long> userIds);

    /**
     * 根据条件查询鉴权卡分页数据
     *
     * @param token
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardsByPage", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryCardsByPage(@RequestParam("token") String token,
        @RequestParam(value = "_index", required = false) int pageNum,
        @RequestParam(value = "_size", required = false) int pageSize,
        @RequestBody CardSearchParam cardSearchParam);

    /**
     * 根据提交查询在线卡列表
     *
     * @param token
     * @param pageNum
     * @param pageSize
     * @param cardStatus
     * @param beginTime
     * @param endTime
     * @param keyWord
     * @param commId
     * @param userId
     * @return
     */
    @RequestMapping(value = "/api/card/queryOnlineCardsByPage", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryOnlineCardsByPage(@RequestParam("token") String token,
        @RequestParam(value = "_index", required = false) int pageNum,
        @RequestParam(value = "_size", required = false) int pageSize,
        @RequestParam(value = "cardStatus", required = false) String cardStatus,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam(value = "cardType", required = false) Long cardType,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "commIdChain") String commIdChain);

    /**
     * 根据提交查询在线卡列表
     *
     * @return
     */
    @RequestMapping(value = "/api/card/queryOnlineCardsByPageOnCorp", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryOnlineCardsByPageOnCorp(
        @RequestBody CardRequest cardRequest);

    @RequestMapping(value = "/api/card/exportOnlineCardsByCorp", method = RequestMethod.POST)
    ObjectResponse<ExcelPosition> exportOnlineCardsByCorp(@RequestBody CardRequest cardRequest);

    /**
     * 根据提交查询紧急充电卡列表 用于充电管理平台
     *
     * @param token
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/api/card/queryUrgencyCardsByPage", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryUrgencyCardsByPage(@RequestParam("token") String token,
        @RequestParam(value = "_index", required = false) int pageNum,
        @RequestParam(value = "_size", required = false) int pageSize,
        @RequestBody CardRequest cardRequest);

    /**
     * 根据提交查询紧急充电卡列表 用于运营支撑平台(无需考虑权限)
     *
     * @param token
     * @param pageNum
     * @param pageSize
     * @return
     */
    @RequestMapping(value = "/api/card/queryUrgencyCardsByPageOnOperate", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(
        @RequestParam("token") String token,
        @RequestParam(value = "_index", required = false) int pageNum,
        @RequestParam(value = "_size", required = false) int pageSize,
        @RequestBody CardRequest cardRequest);

    @Deprecated
    @RequestMapping(value = "/api/card/exportOnlineCardList", method = RequestMethod.POST)
    ObjectResponse<ExcelPosition> exportOnlineCardList(@RequestParam("token") String token,
        @RequestBody CardSearchParam param);

    /**
     * 根据提交查询紧急充电卡列表 用于桩配置
     *
     * @return
     */
    @RequestMapping(value = "/api/card/getCardListByCardNoList", method = RequestMethod.POST)
    ListResponse<WhiteCardCfgVo> getWhiteCardCfgVoByCardNos(@RequestBody CardsParam cardsParam);

    /**
     * 根据离线卡cardId获取相关场站的信息
     *
     * @param token
     * @param cardId
     * @return
     */
    @RequestMapping(value = "/api/card/urgencyCardsDetail", method = RequestMethod.POST)
    ObjectResponse urgencyCardsDetail(@Deprecated @RequestParam("token") String token,
        @RequestParam("cardId") Long cardId);

    /**
     * 根据离线卡cardId获取相关桩列表信息
     *
     * @param token
     * @param cardId
     * @return
     */
    @RequestMapping(value = "/api/card/urgencyCardsDetailEvseList", method = RequestMethod.POST)
    ListResponseEvseList urgencyCardsDetailEvseList(@RequestParam("token") String token,
        @RequestParam("cardId") Long cardId,
        @RequestParam(value = "evse", required = false) String evse,
        @RequestParam(value = "page") Integer page,
        @RequestParam(value = "rows") Integer rows);

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardById", method = RequestMethod.POST)
    ObjectResponse<Card> queryCardById(@RequestParam("id") String id);

    /**
     * 根据卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     *                   卡有效标志1有效0无效
     * @return 分页卡列表数据
     */
    @RequestMapping(value = "/api/card/updateCardStatus", method = RequestMethod.GET)
    BaseResponse updateCardStatus(@RequestParam("cardNo") String cardNo,
        @RequestParam("cardStatus") String cardStatus);

    /**
     * 添加卡片
     *
     * @param card commId 所属商户,cardNo 卡号,isPackaged 卡片类型：3-离线卡 4-鉴权卡 stations 鉴权卡所属站点（多个站点用‘，’分割）
     * @return
     */
    @RequestMapping(value = "/api/card/addCard", method = RequestMethod.POST)
    BaseResponse addCard(@RequestBody Card card);

    /**
     * 更新卡片
     *
     * @param card commId 所属商户,cardNo 卡号,isPackaged 卡片类型：3-离线卡 4-鉴权卡  stations 鉴权卡所属站点（多个站点用‘，’分割）
     * @return
     */
    @RequestMapping(value = "/api/card/updateCard", method = RequestMethod.POST)
    BaseResponse updateCard(@RequestBody Card card);

    /**
     * 企业平台批量新增卡
     *
     * @param cards 卡
     * @return
     */
    @PostMapping("/api/card/batchGrantCard")
    ObjectResponse<Integer> batchGrantCard(@RequestBody List<Card> cards);


    /**
     * 根据手机号和商户id查询客户信息
     *
     * @param phone
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/user/findByPhone", method = RequestMethod.POST)
    ObjectResponse<UserPropVO> findByPhone(@RequestParam(value = "phone") String phone,
        @RequestParam(value = "commId") Long commId);

    /**
     * 根据邮箱和商户id查询客户信息
     *
     * @param email
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/user/findByEmail", method = RequestMethod.POST)
    ObjectResponse<UserPropVO> findByEmail(@RequestParam(value = "email") String email,
        @RequestParam(value = "commId") Long commId);


    /**
     * 用户加入黑名单
     *
     * @param uid         用户id
     * @param commIdChain 商户id列表
     * @return
     */
    @RequestMapping(value = "/api/user/userToBlack", method = RequestMethod.POST)
    BaseResponse userToBlack(
        @RequestParam(value = "uid") Long uid,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "commIdChain") String commIdChain);


    /**
     * 用户解除黑名单
     *
     * @param uid       用户id
     * @param topCommId 集团商户id
     * @return
     */
    @RequestMapping(value = "/api/user/userUnBlack", method = RequestMethod.POST)
    BaseResponse userUnBlack(
        @RequestParam(value = "uid") Long uid,
        @RequestParam(value = "topCommId") Long topCommId);


    /**
     * 根据客户ID查询客户信息
     *
     * @return
     */
    @RequestMapping(value = "/api/user/findInfoByUid", method = RequestMethod.POST)
    ObjectResponse<UserVo> findInfoByUid(@RequestParam(value = "userId") Long userId,
        @RequestParam(value = "topCommId") Long topCommId,
        @RequestParam(value = "commIdChain", required = false) String commIdChain);

    /**
     * 获取用户信息
     *
     * @param phone      用户手机号
     * @param commIdList 商户列表
     * @return
     */
    @RequestMapping(value = "/api/user/findUserByPhone", method = RequestMethod.POST)
    ObjectResponse<UserVo> findUserByPhone(
        @RequestParam(value = "commIdList") List<Long> commIdList,
        @RequestParam(value = "phone") String phone);

    /**
     * 设置用户资料信息
     *
     * @param user 用户信息
     * @return
     */
    @PostMapping(value = "/api/user/setBasicInfo")
    ObjectResponse<User> setBasicInfo(@RequestBody User user);

    /**
     * 根据条件查询
     *
     * @param rBlocUser
     * @return
     */
    @PostMapping("/api/rblocUser/findByCondition")
    ListResponse<RBlocUser> findByCondition(@RequestBody RBlocUser rBlocUser);

    /**
     * 根据条件更新 id必传，其他字段至少传一个
     *
     * @param rBlocUser
     * @return
     */
    @PostMapping("/api/rblocUser/modifyByCondition")
    BaseResponse modifyByCondition(@RequestBody RBlocUser rBlocUser);

    /**
     * 新增集团用户信息
     *
     * @param rBlocUser
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/insertRBlocUser", method = RequestMethod.POST)
    BaseResponse insertRBlocUser(@RequestBody RBlocUser rBlocUser);

    /**
     * 根据集团id查询所属集团的客户列表
     *
     * @param blocUserId
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/findRBlocUserByBlocUserId", method = RequestMethod.GET)
    ListResponse<Long> findRBlocUserByBlocUserId(
        @RequestParam(value = "blocUserId") Long blocUserId);

    /**
     * 更新集团用户信息
     *
     * @param rBlocUser
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/updateRBlocUser", method = RequestMethod.POST)
    BaseResponse updateRBlocUser(@RequestBody RBlocUser rBlocUser);

    /**
     * 禁用集团授信关系
     *
     * @param rBlocUserId
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/deleteRBlocUserById", method = RequestMethod.GET)
    BaseResponse deleteRBlocUserById(@RequestParam(value = "rBlocUserId") Long rBlocUserId);

    /**
     * 集团客户id查询集团客户详情
     *
     * @param rBlocUserId
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/findRBlocUserById", method = RequestMethod.GET)
    ObjectResponse<RBlocUser> findRBlocUserById(
        @RequestParam(value = "rBlocUserId") Long rBlocUserId);

    /**
     * 集团客户id查询集团客户详情
     *
     * @param rBlocUserId
     * @return
     */
    @GetMapping("/api/rblocUser/findRBlocUserVoById")
    ObjectResponse<RBlocUserVo> findRBlocUserVoById(@RequestParam("rBlocUserId") Long rBlocUserId);

    /**
     * 查询集团客户信息(分页查询)
     *
     * @param _num
     * @param _size
     * @param keyWord
     * @param blocUserId
     * @param commIdChain
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/queryRBlocUser", method = RequestMethod.POST)
    ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> queryRBlocUser(
        @RequestParam(value = "_num") Integer _num,
        @RequestParam(value = "_size") Integer _size,
        @RequestParam(value = "keyWord", required = false) String keyWord,
        @RequestParam(value = "blocUserId") Long blocUserId,
        @RequestParam(value = "commIdChain") String commIdChain);


    @RequestMapping(value = "/api/rblocUser/getRBlocUserList", method = RequestMethod.POST)
    ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> getRBlocUserList(
        @RequestParam(value = "_num") Integer _num,
        @RequestParam(value = "_size") Integer _size,
        @RequestParam(value = "userParams", required = false) Integer userParams,
        @RequestParam(value = "keyword", required = false) String keyWord,
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "corpOrgId") Long corpOrgId,
        @RequestParam(value = "corpOrgIds") String corpOrgIds);


    /**
     * 企业管理平台 新增修改删除授信账户统一接口
     *
     * @param corpCreditAccountEx
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/upserdelRBlocUser", method = RequestMethod.POST)
    ObjectResponse<Long> upserdelRBlocUser(@RequestBody CorpCreditAccountEx corpCreditAccountEx);

    /**
     * 批量删除集团中的客户信息
     *
     * @param rBlocUserIds
     * @return
     */
    @RequestMapping(value = "/api/rblocUser/deleteBatchRBlocUser", method = RequestMethod.GET)
    BaseResponse deleteBatchRBlocUser(
        @RequestParam(value = "rBlocUserIds") List<Long> rBlocUserIds);


    /**
     * 是否开启多笔订单
     *
     * @param topCommId
     * @param uid       用户ID
     * @param isMulti   0-未开启;1-已开启
     * @return
     */
    @RequestMapping(value = "/api/user/openMultiOrder", method = RequestMethod.POST)
    BaseResponse openMultiOrder(
        @RequestParam(value = "uid") Long uid,
        @RequestParam(value = "isMulti") boolean isMulti,
        @RequestParam(value = "topCommId") Long topCommId);

    /**
     * 根据phone和blocUserId查询集团用户
     *
     * @param rBlocUser(phone和blocUserId)
     * @return
     */
    @PostMapping("/api/rblocUser/selectRBlocUserByPhone")
    ObjectResponse<RBlocUser> selectRBlocUserByPhone(@RequestBody RBlocUser rBlocUser);

    /**
     * 查询某集团客户是否有xxx开头手机号的集团授信关系
     *
     * @param blocUserId
     * @param phone
     * @return
     */
    @GetMapping("/api/rblocUser/selectRBlocUserByStartWithPhone")
    ListResponse<RBlocUser> selectRBlocUserByStartWithPhone(
        @RequestParam("blocUserId") Long blocUserId, @RequestParam("phone") String phone);

    /**
     * 通过token查询本身及下属商户的集团客户
     *
     * @return
     */
    @GetMapping("/api/blocUser/selectSubBlocUserByCommIdChain")
    ListResponse<BlocUserDto> selectSubBlocUserByCommIdChain(
        @RequestParam(value = "commIdChain", required = false) String commIdChain,
        @RequestParam(value = "includedHlhtCorp", required = false) Boolean includedHlhtCorp);

    /**
     * 查询商户的集团客户 用于运营支撑平台
     */
    @GetMapping("/api/blocUser/selectSubBlocUserByTokenOnOperate")
    ListResponse<BlocUserDto> selectSubBlocUserByTokenOnOperate();

    @GetMapping("/api/blocUser/findById")
    ObjectResponse<BlocUserDto> findById(@RequestParam("id") Long id);

    @PostMapping("/api/blocUser/findByCondition")
    ListResponse<BlocUserDto> blocUserFindByCondition(@RequestBody BlocUser blocUser);


    /**
     * 模糊查询商户及子商户卡列表信息
     *
     * @param token
     * @return
     */
    @RequestMapping(value = "/api/card/queryAllCardList", method = RequestMethod.GET)
    ListResponse<CardVo> queryAllCardList(@RequestParam("token") String token,
        @RequestParam("userId") String userId);


    /**
     * 查询用户下集团客户授权账户
     *
     * @param userId
     * @return
     */
    @GetMapping("/api/rblocUser/findRBlocUserByUserId")
    ListResponse<RBlocUserVo> getAuthAccountByUserId(@RequestParam("userId") Long userId,
        @RequestParam("commIdChain") String commIdChain);

    /**
     * 修改默认扣款账户
     *
     * @param balanceId
     * @param defaultPayType
     * @return
     */
    @PostMapping("/api/user/setDefaultAccount")
    ObjectResponse<User> setDefaultAccount(@RequestParam("id") Long id,
        @RequestParam("balanceId") Long balanceId,
        @RequestParam("defaultPayType") Integer defaultPayType);

    /**
     * 根据条件查询卡分页数据
     *
     * @param pageNum    第几页
     * @param pageSize   每页数量
     * @param cardStatus 卡状态
     * @param cardType   卡类型
     * @param beginTime  查询开始时间
     * @param endTime    查询结束时间
     * @param keyWord    物理卡号/逻辑卡号
     * @return
     */
    @RequestMapping(value = "/api/cardMgm/queryAllCardsByPage", method = RequestMethod.GET)
    ListResponse<CardMgnVo> queryAllCardsByPage(
        @RequestParam("token") String token,
        @RequestParam(value = "_index", required = false) int pageNum,
        @RequestParam(value = "_size", required = false) int pageSize,
        @RequestParam(value = "cardStatus", required = false) String cardStatus,
        @RequestParam(value = "cardType", required = false) String cardType,
        @RequestParam(value = "commId", required = false) Long commId,
        @RequestParam(value = "beginTime", required = false) String beginTime,
        @RequestParam(value = "endTime", required = false) String endTime,
        @RequestParam(value = "keyWord", required = false) String keyWord);

    /**
     * 根据cardNo查询卡
     *
     * @param cardChipNo
     * @return
     */
    @RequestMapping(value = "/api/card/queryCardByCardChipNo", method = RequestMethod.POST)
    ObjectResponse<Card> queryCardByCardChipNo(
        @RequestParam(value = "cardChipNo") String cardChipNo);

    @RequestMapping(value = "/api/card/sendWhiteCard", method = RequestMethod.POST)
    ObjectResponse<List<String>> sendWhiteCard(
        @RequestParam(value = "cardChipNo") String cardChipNo,
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/card/abandonWhiteCard", method = RequestMethod.POST)
    ObjectResponse<List<String>> abandonWhiteCard(
        @RequestParam(value = "cardChipNo") String cardChipNo,
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/card/resetWhiteCardPwd", method = RequestMethod.POST)
    ObjectResponse<List<String>> resetWhiteCardPwd(
        @RequestParam(value = "cardChipNo") String cardChipNo,
        @RequestParam(value = "token") String token);

    @RequestMapping(value = "/api/card/sendBatchWhiteCards", method = RequestMethod.POST)
    ObjectResponse<Map<String, Object>> sendBatchWhiteCards(
        @RequestParam(value = "token") String token);

    /**
     * 查询站点下的紧急充电卡 用于多场站下多卡弃用查询DTO时，条件必须为(cardChipNoList必传，siteList必传,isAbandon必传为true)
     * 用于单场站下发查询DTO时，条件为(cardChipNoList不传，site必传,isAbandon为false) 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    @PostMapping("/api/card/queryWhiteCardDtoBySiteList")
    ListResponse<WhiteCardDto> queryWhiteCardDtoBySiteList(
        @RequestBody WhiteCardRequest whiteCardRequest);

    /**
     * 删除未激活卡片
     *
     * @param ids 卡片id数组集
     * @return
     */
    @RequestMapping(value = "/api/cardMgm/deleteCardsById", method = RequestMethod.DELETE)
    BaseResponse deleteCardsById(@RequestBody List<Long> ids);

    /**
     * 重置（恢复初始）卡片
     *
     * @param ids 卡片id数组集
     * @return
     */
    @PostMapping(value = "/api/cardMgm/resetCardsByIds")
    BaseResponse resetCardsByIds(@RequestBody List<Long> ids);

    /**
     * excel 文件解析
     *
     * @return
     */
    @PostMapping(value = "/api/excel/parse")
    ObjectResponse<JsonNode> parseExcel(@RequestBody List<List<Object>> list);

    /**
     * excel 文件解析
     *
     * @return
     */
    @PostMapping(value = "/api/excel/parseCorpCardExcel")
    ObjectResponse<JsonNode> parseCorpCardExcel(@RequestBody List<List<Object>> list,
        @RequestParam("corpId") Long corpId);

    /**
     * excel 文件解析
     *
     * @return
     */
    @PostMapping(value = "/api/excel/parseCorpVinExcel")
    ObjectResponse<JsonNode> parseCorpVinExcel(@RequestBody List<List<String>> list,
        @RequestParam("corpId") Long corpId,
        @RequestParam("corpTopCommId") Long corpTopCommId);

    /**
     * 卡片批量添加
     *
     * @param cards
     * @return
     */
    @PostMapping("/api/card/batchAddCard")
    ObjectResponse<Integer> batchAddCard(@RequestBody List<Card> cards);

    /**
     * 本地鉴权下发
     *
     * @param param
     * @return
     */
    @PostMapping(value = "/api/card/siteAuthCard")
    ObjectResponse<Integer> siteAuthCard(SiteAuthCardParam param);

    /**
     * 查询商户及子商户VIN码列表信息
     *
     * @param token
     * @return
     */
    @RequestMapping(value = "/api/vin/selectAllVinList", method = RequestMethod.GET)
    ListResponse<VinDto> selectAllVinList(@RequestParam("token") String token,
        @RequestParam("userId") Long userId);

    /**
     * 企业切换商户时，获取VIN保留/移除授权场站记录
     *
     * @param corpId
     * @param commId
     * @return
     */
    @RequestMapping(value = "/api/vin/getMoveCorpVINByCorpId", method = RequestMethod.GET)
    ObjectResponse<MoveCorpSiteAuthList> getMoveCorpVINByCorpId(
        @RequestParam(value = "corpId") Long corpId,
        @RequestParam(value = "commId") Long commId);

    /**
     * 获取使用开票模板的企业
     *
     * @param productId
     * @return
     */
    @RequestMapping(value = "/api/invoiced/getCorpInfoByProduct", method = RequestMethod.GET)
    ObjectResponse getCorpInfoByProduct(@RequestParam("productId") Long productId);


    @RequestMapping(value = "/api/site/getChangeInfo")
    ObjectResponse<ChangeInfo> getChangeInfo(@RequestParam("siteId") String siteId,
        @RequestParam("commId") Long commId);

    @RequestMapping(value = "/api/site/queryChangeCommIdBySiteId")
    ObjectResponse<Integer> queryChangeCommIdBySiteId(@RequestParam("siteId") String siteId,
        @RequestParam("commId") Long commId);


    /**
     * 更新用户开票金额、周期、是否自动开票
     *
     * @param userId
     * @param invoicedAmount
     * @param monthDay
     * @param auto
     * @return
     */
    @RequestMapping(value = "/api/invoiced/saveInvoicedUserAutoAmount", method = RequestMethod.POST)
    ObjectResponse saveInvoicedUserAutoAmount(@RequestParam(value = "userId") Long userId,
        @RequestParam(value = "invoicedAmount") long invoicedAmount,
        @RequestParam(value = "monthDay") Integer monthDay,
        @RequestParam(value = "auto") Integer auto);


    /**
     * 查询商户用户关联表
     *
     * @param userPhone   用户手机号
     * @param commId      商户id
     * @param enable      状态（1启用，0禁用）
     * @param commIdChain 当前商户及子商户id列表
     * @return
     */
    @RequestMapping(value = "/api/user/queryCommCusRefs", method = RequestMethod.GET)
    ListResponse<CommCusRef> queryCommCusRefs(@RequestParam("_index") Integer _index,
        @RequestParam("_size") Integer _size,
        @RequestParam(value = "userPhone") String userPhone,
        @RequestParam(value = "enable") Boolean enable,
        @RequestParam(value = "commId") Long commId,
        @RequestParam(value = "userId") Long userId,
        @RequestParam(value = "cusName") String cusName,
        @RequestParam(value = "commIdChain") String commIdChain);

    @RequestMapping(value = "/api/blocWallet/selectBlocPointLogsByOrderNo", method = RequestMethod.GET)
    ListResponse<PointLog> selectBlocPointLogsByOrderNo(
        @RequestParam(value = "orderNo") String orderNo);

    @RequestMapping(value = "/api/wallet/listRefundOrder", method = RequestMethod.POST)
    ListResponse<RefundOrderPo> listRefundOrder(@RequestBody ListRefundOrderParam param);

    @RequestMapping(value = "/api/user/addUser", method = RequestMethod.POST)
    ObjectResponse<Long> addUser(@RequestBody AddUserParam param);

    @PostMapping(value = "/api/user/addUserCommRef")
    BaseResponse addUserCommRef(@RequestBody UserCommRef userCommRef);

    @PostMapping(value = "/api/user/findUserCommRef")
    ObjectResponse<Boolean> findUserCommRef(@RequestParam("uid") Long uid,
        @RequestParam("commId") Long commId);

    @GetMapping(value = "/api/merchantBalance/findById")
    ObjectResponse<CommCusRef> merFindById(@RequestParam("id") Long id);

//    @GetMapping(value = "/api/merchantBalance/findByCommIdAndPhone")
//    ObjectResponse<CommCusRef> findByCommIdAndPhone(@RequestParam("commId") Long commId,
//                                                    @RequestParam("phone") String phone);

    @PostMapping(value = "/api/merchantBalance/findByCondition")
    ListResponse<CommCusRef> findByCondition(@RequestBody CommCusRef ref);

    @GetMapping(value = "/api/commercial/getCommBi")
    ObjectResponse<CommPropertyCountBiVo> getCommBi(@RequestParam("commId") Long commId,
        @RequestParam("idChain") String idChain);

    @PostMapping(value = "/api/siteAuth/batchUpdate")
    BaseResponse batchUpdate(@RequestBody BatchUpdateSiteAuthVo vo);

    @GetMapping(value = "/api/user/setCouponAutoDeduct")
    BaseResponse setCouponAutoDeduct(@RequestParam("userId") Long userId,
        @RequestParam("autoDeduct") Boolean autoDeduct);

    // 获取商户下用户数
    @GetMapping(value = "/api/user/getUserCount")
    ObjectResponse<Integer> getUserCount(
        @RequestParam(value = "idChain", required = false) String idChain,
        @RequestParam(value = "topCommId", required = false) Long topCommId);

    @PostMapping(value = "/api/cusAttract/queryByListParam")
    ListResponse<CustomerAttractPo> queryCusAttractList(
        @RequestBody CustomerAttractListParam param);

    /**
     * 获取企业客户的开票设置
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/api/invoice/getCorpInvoiceInfo")
    ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceInfo(
        @RequestParam(value = "corpId") Long corpId);

    /**
     * 更新企业客户的开票设置
     *
     * @param dto
     * @return
     */
    @PostMapping(value = "/api/invoice/saveCorpInvoiceInfo")
    ObjectResponse<Integer> saveCorpInvoiceInfo(@RequestBody CorpInvoiceInfoDto dto);

    /**
     * 企业客户的开票设置软删除
     *
     * @param corpId
     * @return
     */
    @GetMapping(value = "/api/invoice/disableByCorpId")
    ObjectResponse<Integer> disableByCorpId(@RequestParam(value = "corpId") Long corpId);

    @GetMapping(value = "/api/merchantBalance/findByCommIdAndPhone")
    ObjectResponse<CommCusRef> findByCommIdAndPhone(@RequestParam("commId") Long commId,
        @RequestParam("phone") String phone);

    /**
     * 更新企业开票设置信息
     *
     * @param updateIdDTOList
     * @return
     */
    @PostMapping(value = "/api/invoice/updateCorpInvoiceInfoProTempId")
    ObjectResponse<Integer> updateCorpInvoiceInfoProTempId(
        @RequestBody List<UpdateIdDTO> updateIdDTOList);

    @PostMapping(value = "/soc/site/updateCorpStrategy")
    BaseResponse updateCorpStrategy(@RequestBody SocStrategyDict param);

    @GetMapping(value = "/soc/site/deleteCorpStrategy")
    BaseResponse deleteCorpStrategy(@RequestParam(value = "id") Long id);

    @PostMapping(value = "/soc/site/queryStrategy")
    ListResponse<SocStrategyDict> queryStrategy(@RequestBody QueryStrategyParam param);

    @PostMapping(value = "/soc/site/queryCorpStrategyCreditCus")
    ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(
        @RequestBody QueryStrategyParam param);

    @PostMapping(value = "/soc/site/queryCorpStrategyVin")
    ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(@RequestBody QueryStrategyParam param);

    @PostMapping(value = "/soc/site/addCorpStrategyCreditCus")
    ObjectResponse<Integer> addCorpStrategyCreditCus(@RequestBody List<QueryStrategyParam> params);

    @PostMapping(value = "/soc/site/removeCorpStrategyCreditCus")
    ObjectResponse<Integer> removeCorpStrategyCreditCus(
        @RequestBody List<QueryStrategyParam> params);

    @PostMapping(value = "/soc/site/addCorpStrategyVin")
    ObjectResponse<Integer> addCorpStrategyVin(@RequestBody List<QueryStrategyParam> params);

    @PostMapping(value = "/soc/site/createCorpSocStrategy")
    BaseResponse createCorpSocStrategy(@RequestBody SocStrategyDict param);

    @PostMapping(value = "/balanceApplication/search")
    ListResponse<BalanceApplicationVo> searchBalanceApplication(BalanceApplicationParam po);

    @PostMapping(value = "/balanceApplication/add")
    ObjectResponse<BalanceApplicationPo> addBalanceApplication(BalanceApplicationPo po);

    @PostMapping(value = "/balanceApplication/check")
    ObjectResponse<Boolean> checkBalanceApplication(BalanceApplicationCheckParam param);

    @PostMapping(value = "/balanceApplication/batchCheckReview")
    ObjectResponse<Boolean> batchCheckReviewBalanceApplication(BalanceApplicationCheckParam param);

    @PostMapping(value = "/balanceApplication/getCheckList")
    ListResponse<BalanceApplicationCheckPo> getCheckList(BalanceApplicationCheckParam param);

    @GetMapping(value = "/api/vin/getVinListByEvse")
    ObjectResponse<SiteAuthVinLogPo> getVinListByEvse(@RequestParam("evseId") String evseId);

    @GetMapping(value = "/api/vin/getVinListBySiteId")
    ListResponse<SiteAuthVinPo> getVinListBySiteId(@RequestParam("siteId") String siteId);

    @GetMapping(value = "/api/vin/getSiteListByVin")
    ListResponse<SiteListVo> getSiteListByVin(@RequestParam("vin") String vin,
        @RequestParam("start") Long start,
        @RequestParam("size") Long size);

    @PostMapping(value = "/api/vin/getCommonSiteList")
    ListResponse<SitePo> getCommonSiteList(@RequestBody VinParam param);

    @PostMapping(value = "/api/vin/siteAuthVin")
    ObjectResponse<Integer> siteAuthVin(@RequestBody SiteAuthVinParam param);

    @PostMapping(value = "/api/card/getCardCommonSiteList")
    ListResponse<SitePo> getCardCommonSiteList(CardSearchParam param);

    @GetMapping(value = "/api/card/getCardListBySiteId")
    ListResponse<SiteAuthCardPo> getCardListBySiteId(@RequestParam("siteId") String siteId);

    @PostMapping(value = "/api/CommercialUser/registerByEmail")
    ObjectResponse<UserAndBalanceAndTokenVo> registerByEmail(@RequestBody UserRegisterParam param);

    /**
     * 海外版，条件查询在线卡列表
     *
     * @return
     */
    @RequestMapping(value = "/api/card/commercial/queryOnlineCardsByPage", method = RequestMethod.POST)
    ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(@RequestBody CardRequest param);
}
