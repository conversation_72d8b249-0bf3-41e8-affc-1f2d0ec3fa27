package com.cdz360.biz.model.cus.corp.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "企业简易信息")
public class CorpSimpleVo {

    @Schema(description = "企业所属商户的ID链")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String commIdChain;

    @Schema(description = "企业所属商户顶级商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long topCommId;

    @Schema(description = "企业所属商户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long commId;

    @Schema(description = "企业所属商户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String commName;

    @Schema(description = "企业客户名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String corpName;

    @Schema(description = "企业客户ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long corpId;

    @Schema(description = "企业客户用户ID t_user.id")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long uid;

    @Schema(description = "true-启用，false-禁用")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Boolean enable;
}
