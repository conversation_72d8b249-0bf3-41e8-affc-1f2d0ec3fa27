<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyPo">
    <id column="no" jdbcType="VARCHAR" property="no"/>
    <result column="siteName" jdbcType="VARCHAR" property="siteName"/>
    <result column="uid" jdbcType="BIGINT" property="uid"/>
    <result column="longitude" jdbcType="DECIMAL" property="longitude"/>
    <result column="latitude" jdbcType="DECIMAL" property="latitude"/>
    <result column="province" jdbcType="INTEGER" property="province"/>
    <result column="city" jdbcType="INTEGER" property="city"/>
    <result column="area" jdbcType="INTEGER" property="area"/>
    <result column="address" jdbcType="VARCHAR" property="address"/>
    <result column="stationType" jdbcType="INTEGER" property="stationType"/>
    <result column="stationArea" jdbcType="VARCHAR" property="stationArea"/>
    <result column="contractTerm" jdbcType="DECIMAL" property="contractTerm"/>
    <result column="score" jdbcType="INTEGER" property="score"/>
    <result column="parkNum" jdbcType="INTEGER" property="parkNum"/>
    <result column="power" jdbcType="DECIMAL" property="power"/>
    <result column="images" property="imageInfo"
      typeHandler="com.cdz360.biz.ds.trading.MybatisJsonTypeHandler"/>
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="lastTime" jdbcType="TIMESTAMP" property="lastTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getByNo"
    resultMap="RESULT_TJ_SURVEY_PO">
    select * from t_tj_survey where no = #{no}
  </select>

  <sql id="FIND_CONDITION_SQL">
    <where>
      <if test="null != uid">
        and uid = #{uid}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( uidList )">
        <foreach collection="uidList" index="index" item="item" open=" AND uid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteName )">
        and siteName like CONCAT('%', #{siteName}, '%')
      </if>
      <if test="score !=null">
        and score = #{score}
      </if>
      <if test="null != withinAid">
        AND withinArea.aid = #{withinAid}
        and withinArea.aid is not null
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
        <foreach collection="withinAidList" index="index" item="item"
          open=" AND withinArea.aid in (" separator="," close=")">
          #{item}
        </foreach>
        and withinArea.aid is not null
      </if>
      <if test="null != analysisId and null != analysisPointNum">
        and ST_Distance_Sphere(
        ST_GeomFromText(concat('POINT(', survey.longitude , ' ', survey.latitude, ')')),
        ap.location) <![CDATA[<=]]> (ap.radius * 1000)
      </if>
    </where>
  </sql>

  <select id="findTjSurvey"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam"
    resultMap="RESULT_TJ_SURVEY_PO">
    select survey.*
    from t_tj_survey survey
    <if test="null != withinAid">
      left join t_tj_area withinArea on ST_Within(
      ST_GeomFromText(concat('POINT(', survey.longitude, ' ', survey.latitude, ')')),
      withinArea.geo)
    </if>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
      left join t_tj_area withinArea on ST_Within(
      ST_GeomFromText(concat('POINT(', survey.longitude, ' ', survey.latitude, ')')),
      withinArea.geo)
    </if>
    <if test="null != analysisId and null != analysisPointNum">
      left join t_tj_area_analysis_point ap on
      ap.analysisId = #{analysisId} and ap.num = #{analysisPointNum}
    </if>
    <include refid="FIND_CONDITION_SQL"/>
    <choose>
      <when test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
        <foreach item="sort" collection="sorts"
          open="order by" separator="," close=" ">
          ${sort.columnsString} ${sort.order}
        </foreach>
      </when>
      <otherwise>
        order by `no` desc
      </otherwise>
    </choose>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countTjSurvey"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam"
    resultType="java.lang.Long">
    select count(survey.`no`)
    from t_tj_survey survey
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( withinAidList )">
      left join t_tj_area withinArea on ST_Within(
      ST_GeomFromText(concat('POINT(', survey.longitude, ' ', survey.latitude, ')')),
      withinArea.geo)
    </if>
    <if test="null != analysisId and null != analysisPointNum">
      left join t_tj_area_analysis_point ap on
      ap.analysisId = #{analysisId} and ap.num = #{analysisPointNum}
    </if>
    <include refid="FIND_CONDITION_SQL"/>
  </select>

  <select id="tjSurveyBi"
    parameterType="com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam"
    resultType="com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo">
    select
    count(*) surveyNum,
    sum(case when survey.score = 1 then 1 else 0 end) score1
    from t_tj_survey survey
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( gid )">
        and gid = #{gid}
      </if>
      <if test="null != uid">
        and uid = #{uid}
      </if>
    </where>
  </select>

  <select id="repeatSurvey"
    parameterType="com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam"
    resultMap="RESULT_TJ_SURVEY_PO">
    select * from t_tj_survey
    <where>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( siteName )">
        or siteName = #{siteName}
      </if>
      <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( address )">
        or address = #{address}
      </if>
      <if test="null != longitude and null != latitude">
        or st_distance_sphere(POINT(#{longitude},#{latitude}),POINT(longitude,latitude))
        <![CDATA[ < ]]> 500
      </if>
    </where>
  </select>

</mapper>

