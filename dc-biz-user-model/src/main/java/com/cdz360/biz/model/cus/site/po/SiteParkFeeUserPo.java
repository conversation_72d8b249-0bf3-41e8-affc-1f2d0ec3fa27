package com.cdz360.biz.model.cus.site.po;

import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "停充超时收费用户列表")
public class SiteParkFeeUserPo {

	@Schema(description = "t_user.id")
	@NotNull(message = "uid 不能为 null")
	private Long uid;

	@Schema(description = "站点编号")
	@NotNull(message = "siteId 不能为 null")
	@Size(max = 32, message = "siteId 长度不能超过 32")
	private String siteId;

	@Schema(description = "true,有效; false,无效")
	@NotNull(message = "enable 不能为 null")
	private Boolean enable;


}
