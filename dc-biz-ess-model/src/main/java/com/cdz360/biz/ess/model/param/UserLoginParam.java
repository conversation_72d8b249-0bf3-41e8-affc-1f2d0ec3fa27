package com.cdz360.biz.ess.model.param;

import com.cdz360.biz.ess.model.dto.BaseUserAccount;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户登录请求参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class UserLoginParam extends BaseUserAccount {

//    @Schema(description = "记住我")
//    private Boolean rememberMe;
}
