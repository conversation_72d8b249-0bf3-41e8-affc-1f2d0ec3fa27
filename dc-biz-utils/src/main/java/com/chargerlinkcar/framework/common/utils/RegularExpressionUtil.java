package com.chargerlinkcar.framework.common.utils;

import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.model.common.constant.RegularExpression;
import java.math.BigDecimal;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.http.util.TextUtils;

public class RegularExpressionUtil {

    /**
     * 车辆自编号校验
     * 车辆自编号要求1~17位，数字、字母组成
     * @param str
     */
    public static void carNumCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(englishNumberAll(str, 1, 17),
                    "请输入正确的车辆自编号");
        }
    }

    /**
     * 海外版，车辆自编号校验
     * 车辆自编号要求1~100位，数字、字母、常规字符组成
     * @param str
     */
    public static void carNumEssCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 100),
                    "请输入正确的车辆自编号");
        }
    }

    /**
     * 车队校验
     * 车队要求最大20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void carDepartCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 20),
                    "请输入正确的车队名称");
        }
    }

    /**
     * 海外版，车队校验
     * 车队要求最大50个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void carDepartEssCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 50),
                    "请输入正确的车队名称");
        }
    }

    /**
     * 线路校验
     * 线路要求最大20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void lineNumCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 20),
                    "请输入正确的线路");
        }
    }
    /**
     * 海外版，线路校验
     * 线路要求最大100个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void lineNumEssCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 100),
                    "请输入正确的线路");
        }
    }

    /**
     * 品牌校验
     * 最多20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void brandCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 20),
                    "请输入正确的品牌");
        }
    }

    /**
     * 海外版，品牌校验
     * 最多20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void brandEssCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 100),
                    "请输入正确的品牌");
        }
    }

    /**
     * 型号校验
     * 最多20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void modelCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 20),
                    "请输入正确的型号");
        }
    }

    /**
     * 海外版，型号校验
     * 最多20个字符，只支持字母、数字、汉字、常规字符
     * @param str
     */
    public static void modelEssCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue(chineseEnglishNumberCharacterAll(str, 1, 100),
                    "请输入正确的型号");
        }
    }

    /**
     * 车长校验
     * 仅支持输入数字或小数点，0~100
     * @param decimal
     */
    public static void carLengthCheck(BigDecimal decimal) {
        if (decimal != null) {
            if (DecimalUtils.isZero(decimal)) {
                decimal = null;
                return;
            }
            IotAssert.isTrue(DecimalUtils.gtZero(decimal)
                            && DecimalUtils.lt(decimal, BigDecimal.valueOf(100))
                            && DecimalUtils.getDecimalPlacesNum(decimal) <= 2,
                    "请输入正确的车长");
        }
    }

    /**
     * 年份校验
     * 仅支持输入4位数字
     * @param str
     */
    public static void yearCheck(String str) {
        if (StringUtils.isNotBlank(str)) {
            IotAssert.isTrue( str.length() == 4,
                    "请输入正确的年份");
        }
    }

    /**
     * 字符串是否全为数字
     *
     * @param str 目标字符串
     * @return
     */
    public static boolean digitAll(String str) {
        Pattern pattern = Pattern.compile(RegularExpression.digit);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 字符串是否全为数字、字母
     *
     * @param str 目标字符串,必传
     * @param minLength 最小位数,可选,默认为 1
     * @param maxLength 最大位数,可选
     * @return
     */
    public static boolean englishNumberAll(String str, Integer minLength, Integer maxLength) {
//        StringBuffer sb = new StringBuffer(RegularExpression.englishNumber);
        StringBuffer sb = new StringBuffer("^[A-Za-z0-9]");
        if (minLength != null && maxLength != null) {
            sb.append("{").append(minLength).append(",").append(maxLength).append("}");
        } else if (minLength != null) {
            sb.append("{").append(minLength).append(",").append("}");
        } else if (maxLength != null) {
            sb.append("{").append(1).append(",").append(maxLength).append("}");
        } else {
            sb.append("+");
        }
        sb.append("$");
        Pattern pattern = Pattern.compile(sb.toString());
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 字符串是否为中文、英文、数字
     *
     * @param str 目标字符串
     * @return
     */
    public static boolean chineseEnglishNumberAll(String str) {
        Pattern pattern = Pattern.compile(RegularExpression.chineseEnglishNumber);
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 字符串是否为中文、英文、数字、常规字符
     * 产品需求 限定常规字符为{@code `~!@#$%^&*()_\-+=<>?:"{}|,.\/;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、}
     * @param str 目标字符串,必传
     * @param minLength 最小位数,可选,默认为 1
     * @param maxLength 最大位数,可选
     * @return
     */
    public static boolean chineseEnglishNumberCharacterAll(String str, Integer minLength, Integer maxLength) {
//        StringBuffer sb = new StringBuffer(RegularExpression.chineseEnglishNumber);
        StringBuffer sb = new StringBuffer("^[\\u4E00-\\u9FA5A-Za-z0-9");//中文、英文、数字
        sb.append(RegularExpression.character);//产品需求常规字符
        sb.append("]");
        if (minLength != null && maxLength != null) {
            sb.append("{").append(minLength).append(",").append(maxLength).append("}");
        } else if (minLength != null) {
            sb.append("{").append(minLength).append(",").append("}");
        } else if (maxLength != null) {
            sb.append("{").append(1).append(",").append(maxLength).append("}");
        } else {
            sb.append("+");
        }
        sb.append("$");
        Pattern pattern = Pattern.compile(sb.toString());
        Matcher matcher = pattern.matcher(str);
        return matcher.matches();
    }

    /**
     * 产品需求
     * 车牌号：第一位汉字，第二位字母，其余为字母、数字、汉字
     * @param carNo
     * @return
     */
    public static boolean isProductDemandCarNo(String carNo) {
        Pattern Chinese = Pattern.compile(RegularExpression.chinese);
        Pattern letter = Pattern.compile(RegularExpression.letter);
        Pattern chineseEnglishNumber = Pattern.compile(RegularExpression.chineseEnglishNumber);

        String first = carNo.substring(0,1);
        Matcher matcher1 = Chinese.matcher(first);

        String second = carNo.substring(1,2);
        Matcher matcher2 = letter.matcher(second);

        String surplus = carNo.substring(2);
        Matcher matcher3 = chineseEnglishNumber.matcher(surplus);
        if (matcher1.matches() && matcher2.matches() && matcher3.matches()) {
            return true;
        }
        return false;
    }

    /**
     * 车牌校验
     * @param carnumber
     * @return
     */
    @Deprecated
    public static boolean isCarnumberNO(String carnumber) {
//        1.常规车牌号：仅允许以汉字开头，后面可录入六个字符，由大写英文字母和阿拉伯数字组成。如：粤B12345；
//        2.武警车牌：允许前两位为大写英文字母，后面可录入五个或六个字符，由大写英文字母和阿拉伯数字组成，其中第三位可录汉字也可录大写英文字母及阿拉伯数字，第三位也可空，如：WJ警00081、WJ京1234J、WJ1234X。
//        3.最后一个为汉字的车牌：允许以汉字开头，后面可录入六个字符，前五位字符，由大写英文字母和阿拉伯数字组成，而最后一个字符为汉字，汉字包括“挂”、“学”、“警”、“军”、“港”、“澳”。如：粤Z1234港。
//        4.新军车牌：以两位为大写英文字母开头，后面以5位阿拉伯数字组成。如：BA12345。
//        String carnumRegex = "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[警京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{0,1}[A-Z0-9]{4}[A-Z0-9挂学警港澳]{1}$";
        String carnumRegex = "^(([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z](([0-9]{5}[DF])|([DF]([A-HJ-NP-Z0-9])[0-9]{4})))|([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳使领]))$";
        if (TextUtils.isEmpty(carnumber)) return false;
        else return carnumber.matches(carnumRegex);
    }

    /**
     * 邮箱校验
     * @param email
     * @return
     */
    public static boolean isEmail(String email) {
        Pattern pattern = Pattern.compile(RegularExpression.email);
        Matcher matcher = pattern.matcher(email);
        return matcher.matches();
    }

    /**
     * 通用手机号校验
     * @param phone
     * @return
     */
    public static boolean isPhone(String phone) {
        Pattern pattern = Pattern.compile(RegularExpression.phone);
        Matcher matcher = pattern.matcher(phone);
        return matcher.matches();
    }

    /**
     * 简单日期校验(yyyy-MM-dd)
     * @param val
     * @return
     */
    public static boolean isDate(String val) {
        return isDate(val, RegularExpression.yyyyMMdd);
    }

    /**
     * 简单日期校验
     * @param val
     * @return
     */
    public static boolean isDate(String val, String regex) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(val);
        return matcher.matches();
    }

}
