package com.cdz360.biz.device.business.util;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.StringUtils;

public class DbsAssert {

//    public static void isBlank(String obj, String message) {
//        if (!StringUtils.isBlank(obj)) {
//            throw new DcArgumentException(message);
//        }
//    }

    public static void isNotBlank(String obj, String message) {
        if (StringUtils.isBlank(obj)) {
            throw new DcArgumentException(message);
        }
    }

//    public static void isNull(Object obj, String message) {
//        if (obj != null) {
//            throw new DcArgumentException(message);
//        }
//    }

    public static void isNotNull(Object obj, String message) {
        if (obj == null) {
            throw new DcArgumentException(message);
        }
    }

    public static void isTrue(boolean expression, String message) {
        if (!expression) {
            throw new DcServiceException(message);
        }
    }

//    public static void isTrue(boolean expression, DcException exception) {
//        if (!expression) {
//            throw exception;
//        }
//    }
}
