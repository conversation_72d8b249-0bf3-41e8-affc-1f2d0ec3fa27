package com.cdz360.biz.ess.model.data.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDate;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GetInDaySamplingListParam {

    @Schema(title = "储能站ID", required = false, description = "如果siteId不为空，表示要查询储能站所有BMS的soc曲线数据")
    private String siteId;

    @Schema(title = "emu设备号", required = false, description = "如果emuDno不为空，表示要查询EMU下属BMS的soc曲线数据")
    private String emuDno;

    @Schema(title = "bms设备号", required = false, description = "如果bmsDno不为空，表示要查询对应BMS的soc曲线数据")
    private String bmsDno;

    @Schema(title = "pcs设备号", required = false, description = "如果pcsDno不为空，表示要查询对应PCS的功率/电压/电流曲线数据")
    private String pcsDno;

    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "日期", required = true, description = "格式yyyy-MM-dd")
    private LocalDate date;
}
