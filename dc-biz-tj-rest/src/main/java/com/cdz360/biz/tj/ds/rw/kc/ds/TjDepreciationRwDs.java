package com.cdz360.biz.tj.ds.rw.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDailyChargingDurationCoefficientRwMapper;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjDepreciationRwMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjDepreciationRwDs {


    @Autowired

    private TjDepreciationRwMapper tjDepreciationRwMapper;


    public TjDepreciationPo getById(Long id, boolean lock) {
        return this.tjDepreciationRwMapper.getById(id, lock);
    }

    public boolean updateTjDepreciation(TjDepreciationPo tjDepreciationPo) {
        return this.tjDepreciationRwMapper.updateTjDepreciation(tjDepreciationPo) > 0;
    }

}

