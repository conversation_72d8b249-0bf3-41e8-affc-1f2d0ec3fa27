package com.cdz360.biz.model.tj.kc.po;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ApiModel(value = "勘察站-充电区域设备")
public class TjSurveyChargeAreaPilePo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @ApiModelProperty(value = "充电区域id")
    private Long areaId;

    @ApiModelProperty(value = "充电桩型号ID")
    private Long evseModelId;

    @ApiModelProperty(value = "该型号的充电桩数量")
    private Integer evseCount;

    @ApiModelProperty(value = "是否删除")
    private Boolean enable;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}

