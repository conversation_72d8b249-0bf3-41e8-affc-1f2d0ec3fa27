<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.ds.trading.ro.site.mapper.BsBoxSettingMapper">

    <select id="getOneByEvseNo" resultType="com.cdz360.biz.model.trading.site.po.BsBoxSettingPo">
        select * from t_bs_box_setting
        where boxOutFactoryCode = #{evseNo}
        limit 1
    </select>

    <select id="getByEvseNo" resultType="com.cdz360.biz.model.trading.site.po.BsBoxSettingPo">
        select * from t_bs_box_setting
        where 1=1
        <if test="statusList != null and statusList.size() > 0">
            and `status` IN
            <foreach item="item" index="index" collection="statusList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="evseNoList != null and evseNoList.size() > 0">
            and boxOutFactoryCode IN
            <foreach item="item" index="index" collection="evseNoList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="lock == true">
            for update
        </if>
    </select>

    <select id="getSchemeSiteUsed"
            parameterType="com.chargerlinkcar.framework.common.domain.param.ListPriceSchemeSiteUseParam"
            resultType="com.cdz360.biz.model.trading.site.vo.PriceSchemeSiteVo">
        SELECT
<!--        setting.chargeId,-->
<!--        setting.boxOutFactoryCode,-->
<!--        setting.`status`,-->
        any_value(template.code) AS priceSchemeCode,
        0 AS status,
        any_value(box.station_code) AS siteId,
        any_value(site.`name`) AS siteName
        FROM
        t_bs_box_setting setting
        LEFT JOIN t_template template ON setting.chargeId = template.id
        LEFT JOIN bs_box box ON box.box_code = setting.boxOutFactoryCode
        LEFT JOIN t_site site ON site.id = box.station_code
        left join t_r_commercial comm on comm.id = site.operate_id
        <!-- 成功和下发中-->
        WHERE setting.`status` in (1, 3)
        and template.code in (
            select code from t_template
            where id IN
            <foreach item="item" index="index" collection="priceSchemeIdList"
                     open="(" separator="," close=")">
                #{item}
            </foreach>
        )
<!--        <if test="priceSchemeIdList != null and priceSchemeIdList.size() > 0">-->
<!--            and template.id IN-->
<!--            <foreach item="item" index="index" collection="priceSchemeIdList"-->
<!--                     open="(" separator="," close=")">-->
<!--                #{item}-->
<!--            </foreach>-->
<!--        </if>-->
        <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
            and comm.idChain like CONCAT(#{commIdChain}, '%')
        </if>
        group by site.id
<!--        <if test="lock == true">-->
<!--            for update-->
<!--        </if>-->
    </select>

    <insert id="insertOrUpdate" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.biz.model.trading.site.po.BsBoxSettingPo">
        INSERT INTO t_bs_box_setting (
        id, boxCode, boxOutFactoryCode, `status`,
        dayVolume, nightVolume, isQueryChargeRecord, isTimedCharge, isNoCardCharge,
        isScanCharge, isVinCharge, isCardCharge, isQuotaEleCharge, isQuotaMoneyCharge,
        isQuotaTimeCharge, internationalAgreement, isAutoStopCharge, avgOrTurnCharge,
        isCombineCharge, triggerResult,
        adminPassword, level2Password, adminCodeResult, -- 桩端管理员密码
        url, qrResult, -- 二维码信息
        chargeId, templateCode, chargeResult, -- 计费模板信息
        whiteCardsStatus, whiteCardList, -- 紧急充电卡
        updateByUserid, createTime, updateTime
        )
        VALUES
        (#{id}, #{boxCode}, #{boxOutFactoryCode}, #{status},
        #{dayVolume}, #{nightVolume}, #{isQueryChargeRecord}, #{isTimedCharge}, #{isNoCardCharge},
        #{isScanCharge}, #{isVinCharge}, #{isCardCharge}, #{isQuotaEleCharge}, #{isQuotaMoneyCharge},
        #{isQuotaTimeCharge}, #{internationalAgreement}, #{isAutoStopCharge}, #{avgOrTurnCharge},
        #{isCombineCharge}, #{triggerResult},
        #{adminPassword}, #{level2Password}, #{adminCodeResult},
        #{url}, #{qrResult},
        #{chargeId}, #{templateCode}, #{chargeResult},
        #{whiteCardsStatus}, #{whiteCardList},
        #{updateByUserid}, now(), now())
        on DUPLICATE key UPDATE
        <if test="status != null">
            `status` = #{status},
        </if>
        <if test="dayVolume != null">
            dayVolume = #{dayVolume},
        </if>
        <if test="nightVolume != null">
            nightVolume = #{nightVolume},
        </if>
        <if test="isQueryChargeRecord != null">
            isQueryChargeRecord = #{isQueryChargeRecord},
        </if>
        <if test="isTimedCharge != null">
            isTimedCharge = #{isTimedCharge},
        </if>
        <if test="isNoCardCharge != null">
            isNoCardCharge = #{isNoCardCharge},
        </if>
        <if test="isScanCharge != null">
            isScanCharge = #{isScanCharge},
        </if>
        <if test="isVinCharge != null">
            isVinCharge = #{isVinCharge},
        </if>
        <if test="isCardCharge != null">
            isCardCharge = #{isCardCharge},
        </if>
        <if test="isQuotaEleCharge != null">
            isQuotaEleCharge = #{isQuotaEleCharge},
        </if>
        <if test="isQuotaMoneyCharge != null">
            isQuotaMoneyCharge = #{isQuotaMoneyCharge},
        </if>
        <if test="isQuotaTimeCharge != null">
            isQuotaTimeCharge = #{isQuotaTimeCharge},
        </if>
        <if test="internationalAgreement != null">
            internationalAgreement = #{internationalAgreement},
        </if>
        <if test="isAutoStopCharge != null">
            isAutoStopCharge = #{isAutoStopCharge},
        </if>
        <if test="avgOrTurnCharge != null">
            avgOrTurnCharge = #{avgOrTurnCharge},
        </if>
        <if test="isCombineCharge != null">
            isCombineCharge = #{isCombineCharge},
        </if>
        <if test="triggerResult != null">
            triggerResult = #{triggerResult},
        </if>

        <if test="adminPassword != null">
            adminPassword = #{adminPassword},
        </if>
        <if test="level2Password != null">
            level2Password = #{level2Password},
        </if>
        <if test="adminCodeResult != null">
            adminCodeResult = #{adminCodeResult},
        </if>

        <if test="url != null">
            url = #{url},
        </if>
        <if test="qrResult != null">
            qrResult = #{qrResult},
        </if>

        <if test="chargeId != null">
            chargeId = #{chargeId},
        </if>
        <if test="templateCode != null">
            templateCode = #{templateCode},
        </if>
        <if test="chargeResult != null">
            chargeResult = #{chargeResult},
        </if>

        <if test="whiteCardList != null">
            whiteCardList = #{whiteCardList},
        </if>
        <if test="whiteCardsStatus != null">
            whiteCardsStatus = #{whiteCardsStatus},
        </if>

        <if test="updateByUserid != null">
            updateByUserid = #{updateByUserid},
        </if>
        updateTime = now()
    </insert>

    <update id="batchUpdate"  parameterType="java.util.List">
        update t_bs_box_setting
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="status =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.status!=null">
                        when id=#{i.id} then #{i.status}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" dayVolume =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.dayVolume!=null">
                        when id=#{i.id} then #{i.dayVolume}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" nightVolume =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.nightVolume!=null">
                        when id=#{i.id} then #{i.nightVolume}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isQueryChargeRecord =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isQueryChargeRecord!=null">
                        when id=#{i.id} then #{i.isQueryChargeRecord}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isTimedCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isTimedCharge!=null">
                        when id=#{i.id} then #{i.isTimedCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isNoCardCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isNoCardCharge!=null">
                        when id=#{i.id} then #{i.isNoCardCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isScanCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isScanCharge!=null">
                        when id=#{i.id} then #{i.isScanCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isVinCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isVinCharge!=null">
                        when id=#{i.id} then #{i.isVinCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isCardCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isCardCharge!=null">
                        when id=#{i.id} then #{i.isCardCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isQuotaEleCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isQuotaEleCharge!=null">
                        when id=#{i.id} then #{i.isQuotaEleCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isQuotaMoneyCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isQuotaMoneyCharge!=null">
                        when id=#{i.id} then #{i.isQuotaMoneyCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isQuotaTimeCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isQuotaTimeCharge!=null">
                        when id=#{i.id} then #{i.isQuotaTimeCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" internationalAgreement =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.internationalAgreement!=null">
                        when id=#{i.id} then #{i.internationalAgreement}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isAutoStopCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isAutoStopCharge!=null">
                        when id=#{i.id} then #{i.isAutoStopCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" avgOrTurnCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.avgOrTurnCharge!=null">
                        when id=#{i.id} then #{i.avgOrTurnCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" isCombineCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.isCombineCharge!=null">
                        when id=#{i.id} then #{i.isCombineCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" heating =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.heating!=null">
                        when id=#{i.id} then #{i.heating}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" heatingVoltage =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.heatingVoltage!=null">
                        when id=#{i.id} then #{i.heatingVoltage}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" batteryCheck =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.batteryCheck!=null">
                        when id=#{i.id} then #{i.batteryCheck}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" securityCheck =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.securityCheck!=null">
                        when id=#{i.id} then #{i.securityCheck}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" constantCharge =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.constantCharge!=null">
                        when id=#{i.id} then #{i.constantCharge}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" vinDiscover =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.vinDiscover!=null">
                        when id=#{i.id} then #{i.vinDiscover}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" orderPrivacySetting =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.orderPrivacySetting!=null">
                        when id=#{i.id} then #{i.orderPrivacySetting}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" accountDisplayType =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.accountDisplayType!=null">
                        when id=#{i.id} then #{i.accountDisplayType}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" triggerResult =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.triggerResult!=null">
                        when id=#{i.id} then #{i.triggerResult}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" adminPassword =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.adminPassword!=null">
                        when id=#{i.id} then #{i.adminPassword}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" level2Password =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.level2Password!=null">
                        when id=#{i.id} then #{i.level2Password}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" adminCodeResult =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.adminCodeResult!=null">
                        when id=#{i.id} then #{i.adminCodeResult}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" url =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.url!=null">
                        when id=#{i.id} then #{i.url}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" qrResult =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.qrResult!=null">
                        when id=#{i.id} then #{i.qrResult}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" chargeId =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.chargeId!=null">
                        when id=#{i.id} then #{i.chargeId}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" templateCode =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.templateCode!=null">
                        when id=#{i.id} then #{i.templateCode}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" chargeResult =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.chargeResult!=null">
                        when id=#{i.id} then #{i.chargeResult}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" whiteCardList =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.whiteCardList!=null">
                        when id=#{i.id} then #{i.whiteCardList}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" whiteCardsStatus =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.whiteCardsStatus!=null">
                        when id=#{i.id} then #{i.whiteCardsStatus}
                    </if>
                </foreach>
            </trim>
            <trim prefix=" updateByUserid =case" suffix="end,">
                <foreach collection="poList" item="i" index="index">
                    <if test="i.updateByUserid!=null">
                        when id=#{i.id} then #{i.updateByUserid}
                    </if>
                </foreach>
            </trim>
        </trim>
        where
        <foreach collection="poList" separator="or" item="i" index="index">
            <choose>
                <when test="i.id != null">
                    id=#{i.id}
                </when>
                <when test="i.boxOutFactoryCode != null">
                    boxOutFactoryCode=#{i.boxOutFactoryCode}
                </when>
                <otherwise>
                    1=2
                </otherwise>
            </choose>
        </foreach>
    </update>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO t_bs_box_setting (
        boxCode, boxOutFactoryCode, `status`,
        dayVolume, nightVolume, isQueryChargeRecord, isTimedCharge, isNoCardCharge,
        isScanCharge, isVinCharge, isCardCharge, isQuotaEleCharge, isQuotaMoneyCharge,
        isQuotaTimeCharge, internationalAgreement, isAutoStopCharge, avgOrTurnCharge,
        isCombineCharge,
        heating, heatingVoltage,
        batteryCheck, securityCheck,
        constantCharge, vinDiscover,
        orderPrivacySetting, accountDisplayType,
        triggerResult,
        adminPassword, level2Password, adminCodeResult, -- 桩端管理员密码
        url, qrResult, -- 二维码信息
        chargeId, templateCode, chargeResult, -- 计费模板信息
        whiteCardsStatus, whiteCardList, -- 紧急充电卡
        updateByUserid, createTime, updateTime
        )
        VALUES
        <foreach collection="poList" item="item" index="index" separator=",">
            (#{item.boxCode}, #{item.boxOutFactoryCode}, #{item.status},
            #{item.dayVolume}, #{item.nightVolume}, #{item.isQueryChargeRecord}, #{item.isTimedCharge}, #{item.isNoCardCharge},
            #{item.isScanCharge}, #{item.isVinCharge}, #{item.isCardCharge}, #{item.isQuotaEleCharge}, #{item.isQuotaMoneyCharge},
            #{item.isQuotaTimeCharge}, #{item.internationalAgreement}, #{item.isAutoStopCharge}, #{item.avgOrTurnCharge},
            #{item.isCombineCharge},
            #{item.heating}, #{item.heatingVoltage},
            #{item.batteryCheck}, #{item.securityCheck},
            #{item.constantCharge}, #{item.vinDiscover},
            #{item.orderPrivacySetting}, #{item.accountDisplayType},
            #{item.triggerResult},
            #{item.adminPassword}, #{item.level2Password}, #{item.adminCodeResult},
            #{item.url}, #{item.qrResult},
            #{item.chargeId}, #{item.templateCode}, #{item.chargeResult},
            #{item.whiteCardsStatus}, #{item.whiteCardList},
            #{item.updateByUserid}, now(), now())
        </foreach>
    </insert>

    <delete id="delBoxSetting">
        delete from t_bs_box_setting
        where boxOutFactoryCode=#{evseNo}
    </delete>

</mapper>
