package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "个人场站勘察汇总信息查询参数")
@Data
@Accessors(chain = true)
public class TjSurveyBiParam {

    @Schema(description = "场站组gid")
    @JsonInclude(Include.NON_EMPTY)
    private String gid;

    @Schema(description = "用户ID")
    @JsonInclude(Include.NON_NULL)
    private Long uid;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
