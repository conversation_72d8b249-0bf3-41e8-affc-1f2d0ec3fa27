package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.common.param.BaseListParam;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjDailyChargingDurationRoMapper {

    TjDailyChargingDurationPo getById(@Param("id") Long id);

    List<TjDailyChargingDurationPo> findTjDailyChargingDuration(
        ListTjDailyChargingDurationParam param);

    long countTjDailyChargingDuration(ListTjDailyChargingDurationParam param);

}

