package com.cdz360.biz.ess.model.vo;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "储能地图数据")
@Data
@Accessors(chain = true)
public class EssMapDataVo {

    @Schema(description = "所属国家地区代码",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    private String countryCode;

    @Schema(description = "区域用户数")
    private Long userCnt;

    @Schema(description = "区域设备数")
    private Long deviceCnt;

    @Schema(description = "设备装机容量，单位: Ah")
    private BigDecimal deviceCapacity;
}
