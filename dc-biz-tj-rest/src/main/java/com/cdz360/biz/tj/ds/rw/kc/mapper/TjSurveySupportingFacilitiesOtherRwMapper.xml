<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveySupportingFacilitiesOtherRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_OTHER_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="facilitiesCount" jdbcType="INTEGER" property="facilitiesCount"/>
    <result column="price" jdbcType="DECIMAL" property="price"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_OTHER_PO">
    select * from t_tj_survey_supporting_facilities_other where id = #{id} and enable = true
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveySupportingFacilitiesOther"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo">
    insert into t_tj_survey_supporting_facilities_other (`surveyNo`,
    <if test="null != name">
      `name`,
    </if>
    <if test="null != facilitiesCount">
      `facilitiesCount`,
    </if>
    <if test="null != price">
      `price`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != name">
      #{name},
    </if>
    <if test="null != facilitiesCount">
      #{facilitiesCount},
    </if>
    <if test="null != price">
      #{price},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveySupportingFacilitiesOther" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo">
    update t_tj_survey_supporting_facilities_other set
    <if test="name != null">
      name = #{name},
    </if>
    <if test="facilitiesCount != null">
      facilitiesCount = #{facilitiesCount},
    </if>
    <if test="price != null">
      price = #{price},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveySupportingFacilitiesOtherBySurveyNo">
    update t_tj_survey_supporting_facilities_other set
    enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>


</mapper>

