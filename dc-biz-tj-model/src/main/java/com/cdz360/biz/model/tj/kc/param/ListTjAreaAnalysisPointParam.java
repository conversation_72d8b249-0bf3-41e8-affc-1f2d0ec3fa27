package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.tj.kc.type.PointZoneStatus;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取投建分析划分点列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjAreaAnalysisPointParam extends BaseListParam {

    @Schema(description = "投建分析记录ID")
    @JsonInclude(Include.NON_NULL)
    private Long analysisId;

    @Schema(description = "点位地址(模糊查询)")
    @JsonInclude(Include.NON_EMPTY)
    private String addressLike;

    @Schema(description = "点位辐射区状态列表")
    @JsonInclude(Include.NON_NULL)
    private List<PointZoneStatus> statusList;

    @Schema(description = "投建区域ID列表(t_tj_area.id)-查投建区域内的所有站点")
    @JsonInclude(Include.NON_NULL)
    private List<Long> withinAidList;

    @Schema(description = "辐射区竞争者ID列表", example = "1,2,3")
    @JsonInclude(Include.NON_NULL)
    private String petIdSetString;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
