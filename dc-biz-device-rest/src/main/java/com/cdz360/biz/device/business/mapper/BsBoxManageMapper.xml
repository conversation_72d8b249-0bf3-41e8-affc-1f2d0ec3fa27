<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.BsBoxManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.po.BsBox">
        <id column="box_code" property="boxCode"/>
        <result column="box_out_factory_code" property="boxOutFactoryCode"/>
        <result column="box_charger_type" property="boxChargerType"/>
        <result column="business_id" property="businessId"/>
        <result column="station_code" property="stationCode"/>
        <result column="status" property="status"/>
        <result column="charger_num" property="chargerNum"/>
        <result column="current_type" property="currentType"/>
        <result column="product_name" property="productName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="last_modify_time" property="lastModifyTime"/>
        <result column="charging_way" property="chargingWay"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        box_code AS boxCode,
        box_out_factory_code AS boxOutFactoryCode,
        box_charger_type AS boxChargerType,
        business_id AS businessId,
        station_code AS stationCode,
        status AS status,
        charger_num AS chargerNum,
        current_type AS currentType,
        product_name AS productName,
        remark AS remark,
        create_time AS createTime,
        last_modify_time AS lastModifyTime,
        charging_way AS chargingWay
    </sql>

    <!--批量更新盒子状态-->
<!--    <update id="updateBatchDeviceStatus">-->
<!--        update d_charger.bs_box-->
<!--        <set>-->
<!--            <if test="status != null">-->
<!--                `status` = #{status},-->
<!--            </if>-->
<!--            <if test="lastHeartbeatTime != null">-->
<!--                heart_beat_time = #{lastHeartbeatTime},-->
<!--            </if>-->
<!--        </set>-->
<!--        where box_code in-->
<!--        <foreach collection="deviceIdList" item="item" index="index" separator="," open="(" close=")">-->
<!--            #{item}-->
<!--        </foreach>-->
<!--    </update>-->

    <!--根据站点ID，修改站点下所有设备支持的客户端类型-->
    <update id="updateClientVersionByStationCode">
      UPDATE
        d_charger.bs_box
      SET client_version = #{clientVersion}
      WHERE station_code = #{stationCode}
    </update>

    <!--设备简单信息查询结果-->
    <sql id="select_simple_column_list">
        box.box_code AS deviceId,
        box.box_out_factory_code AS serialNumber,
        box.box_charger_type AS deviceType,
        box.`status` AS `status`,
        box.heart_beat_time AS lastHeartbeatTime,
        box.charger_num AS validateConnectorCount,
        box.client_version AS clientVersion,
        box.station_code AS siteId,
        box.business_id AS businessId,
        box.remark AS remark
    </sql>

    <!--获取设备简单列表-->
    <select id="getBoxSimpleList" resultType="com.cdz360.biz.device.business.entity.result.BoxInfoVo">
        SELECT
        <include refid="select_simple_column_list"/>
        FROM
        d_charger.bs_box box
        LEFT JOIN d_charger.t_site site on CAST(site.id AS CHAR) = box.station_code
        <where>
            <if test="siteId != null">
                AND site.id = #{siteId}
            </if>
        </where>
        ORDER BY box.create_time DESC
    </select>

    <!--更新桩的计费模板信息-->
    <update id="updateEvseTemplateInfoByEvseNo">
        update d_charger.bs_box
        <set>
            <if test="templateId != null">
                template_id = #{templateId},
            </if>
            <if test="templateName != null">
                template_name = #{templateName},
            </if>
        </set>
        where box_out_factory_code=#{evseNo}
    </update>
</mapper>
