package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "箱变容量")
@Getter
public enum MaterialCostBoxCapacity implements DcEnum {

    CAPACITY_315(315, "315kVA"),
    CAPACITY_500(500, "500kVA"),
    CAPACITY_630(630, "630kVA"),
    CAPACITY_800(800, "800kVA"),
    CAPACITY_1000(1000, "1000kVA"),
    CAPACITY_1250(1250, "1250kVA"),
    CAPACITY_1600(1600, "1600kVA"),

    ;
    @JsonValue
    private final int code;
    private final String desc;

    MaterialCostBoxCapacity(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static MaterialCostBoxCapacity valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (MaterialCostBoxCapacity flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return null;
    }

}
