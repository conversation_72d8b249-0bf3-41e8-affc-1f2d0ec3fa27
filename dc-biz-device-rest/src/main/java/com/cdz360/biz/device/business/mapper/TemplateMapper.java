package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.Template;
import com.cdz360.biz.device.business.entity.request.TemplateListRequest;
import com.cdz360.biz.device.business.entity.result.TemplateInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计费模板**主模板** Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Mapper
public interface TemplateMapper extends BaseMapper<Template> {

    /**
     * 获取计费模板列表
     *
     * @param request
     * @return
     */
    List<TemplateInfoVo> getTemplateList(TemplateListRequest request);

    /**
     * 获取本身及下属的计费模板列表(包含上级所指派经营场站的计费模板)
     * @param request
     * @return
     */
    List<TemplateInfoVo> getTemplateListAndBeAuthorized(TemplateListRequest request);

    /**
     * 根据计费模板编号，获取最新的计费模板详情
     *
     * @param code
     * @return
     */
    Template getLatestTemplateDetailByCode(@Param("code") String code);


    /**
     * 获取计费模板列表
     *
     * @param list
     * @return
     */
    List<TemplateInfoVo> getTemplateListById(List<Long> list);

    /**
     * 根据计费模板编号，获取编号下所有的计费模板编号
     *
     * @param code
     * @return
     */
    List<Long> getTemplateIdList(@Param("code") String code);

    /**
     * 通过计费模板Id获取计费模板
     *
     * @param id
     * @param lock
     * @return
     */
    TemplateInfoVo findById(@Param("id") Long id, @Param("lock") boolean lock);

    /**
     * 更新计费模板的使能状态
     *
     * @param id
     * @param enable
     * @return
     */
    int updateEnable(@Param("id") Long id, @Param("enable") boolean enable);

    /**
     * 更新计费模板删除标识
     *
     * @param id
     * @return
     */
    int updateDeleteFlagById(@Param("id") Long id);
}
