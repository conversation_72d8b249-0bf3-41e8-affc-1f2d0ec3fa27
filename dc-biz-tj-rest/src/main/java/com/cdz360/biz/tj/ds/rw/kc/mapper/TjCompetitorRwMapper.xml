<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjCompetitorRwMapper">

  <resultMap id="RESULT_TJ_COMPETITOR_PO" type="com.cdz360.biz.model.tj.kc.po.TjCompetitorPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="logo" jdbcType="VARCHAR" property="logo"/>
    <result column="firstPullFinishTime" jdbcType="TIMESTAMP" property="firstPullFinishTime"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_COMPETITOR_PO">
    select * from t_tj_competitor where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjCompetitor" useGeneratedKeys="true" keyProperty="id"
    keyColumn="id" parameterType="com.cdz360.biz.model.tj.kc.po.TjCompetitorPo">
    insert into t_tj_competitor (`name`,
    `logo`,
    `createTime`,
    `updateTime`)
    values (#{name},
    #{logo},
    now(),
    now())
  </insert>

  <update id="updateTjCompetitor" parameterType="com.cdz360.biz.model.tj.kc.po.TjCompetitorPo">
    update t_tj_competitor set
    <if test="name != null">
      name = #{name},
    </if>
    <if test="logo != null">
      logo = #{logo},
    </if>
    <if test="firstPullFinishTime != null">
      firstPullFinishTime = #{firstPullFinishTime},
    </if>
    updateTime = now()
    where id = #{id}
  </update>
  <update id="disableCompetitor">
    update t_tj_competitor set
    enable = false, updateTime = now()
    where id = #{competitorId}
  </update>

</mapper>

