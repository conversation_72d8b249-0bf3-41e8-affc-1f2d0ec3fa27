package com.cdz360.biz.model.tj.kc.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "个人场站勘察信息")
@Data
@Accessors(chain = true)
public class TjSurveyBiVo {

    @Schema(description = "已勘察场站数量")
    private long surveyNum;

    @Schema(description = "符合要求勘察场站数量")
    private long score1;

    @Schema(description = "在建场站数")
    private long buildingNum;
}
