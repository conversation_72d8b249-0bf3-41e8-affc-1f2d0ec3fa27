package com.cdz360.biz.model.cus.corp.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import java.util.List;
import lombok.Data;

@Data
public class ChangeByGidsParam {

    private Long corpId;

    private List<String> gids;

    private Long topCommId;

    public void fieldsCheck() {
        boolean invalid = corpId == null
            || gids == null
            || topCommId == null;
        if (invalid) {
            throw new DcArgumentException("缺少入参信息");
        }
    }

}
