package com.cdz360.biz.model.tj.kc.po;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "投建-数据字典-财务数据-日充电时长乐观悲观系数")
public class TjDailyChargingDurationCoefficientPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "乐观系数")
    private BigDecimal optimisticCoefficient;

    @Schema(description = "悲观系数")
    private BigDecimal pessimisticCoefficient;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "更新时间")
    private Date updateTime;

}

