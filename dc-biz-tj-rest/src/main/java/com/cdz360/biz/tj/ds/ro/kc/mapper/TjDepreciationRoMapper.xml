<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjDepreciationRoMapper">

  <resultMap id="RESULT_TJ_DEPRECIATION_PO" type="com.cdz360.biz.model.tj.kc.po.TjDepreciationPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="ageLimit" jdbcType="INTEGER" property="ageLimit"/>
    <result column="residualValue" jdbcType="DECIMAL" property="residualValue"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_DEPRECIATION_PO">
    select * from t_tj_depreciation where id = #{id}
  </select>

  <select id="findTjDepreciation"
    resultMap="RESULT_TJ_DEPRECIATION_PO">
    select * from t_tj_depreciation where 1=1 limit 0,1
  </select>

</mapper>

