package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaRoDs;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAreaSiteRelService {

    @Autowired
    private TjAreaRoDs tjAreaRoDs;

    public Mono<ListResponse<SiteWithinTjVo>> findSiteWithinTjArea(
        ListSiteWithinTjAreaParam param) {
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(
//            param.getWithinAidList()), "投建区域ID列表不能为空");
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjAreaRoDs::findSiteWithinTjArea)
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjAreaRoDs.countSiteWithinTjArea(param) : 0L));
    }
}
