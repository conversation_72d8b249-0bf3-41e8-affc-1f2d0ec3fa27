package com.cdz360.biz.model.tj.kc.dto;

import com.cdz360.base.utils.JsonUtils;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@Schema(description = "竞争对手场站")
public class TjCompetitorSiteDto {

    @NotNull(message = "id 不能为 null")
    private Long id;

    @Schema(description = "直流桩数量")
    private Integer dcEvseNum;

    @Schema(description = "直流枪头数量")
    private Integer dcPlugNum;

    @Schema(description = "交流桩数量")
    private Integer acEvseNum;

    @Schema(description = "交流枪头数量")
    private Integer acPlugNum;

    @Schema(description = "总功率,单位kW")
    private BigDecimal power;

    @Schema(description = "最小电费单价，单位：元")
    private BigDecimal minElecFee;

    @Schema(description = "最大电费单价，单位：元")
    private BigDecimal maxElecFee;

    @Schema(description = "最小服务费单价，单位：元")
    private BigDecimal minServFee;

    @Schema(description = "最大服务费单价，单位：元")
    private BigDecimal maxServFee;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}

