package com.cdz360.biz.device.business.entity.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @ProjectName chargerlink-device
 * <AUTHOR>
 * @CreateDate 2019/9/16 13:46
 */

@ApiModel(value = "com.cdz360.biz.device.business.entity.po.UpdatePackage")
@Data
@Accessors(chain = true)
public class UpdatePackage implements Serializable {
    /**
     *
     */
    @ApiModelProperty(value = "主键")
    private Long id;

    /**
     *
     */
    @ApiModelProperty(value = "升级包版本-升级包编码")
    private String version;

    /**
     * 包名字
     */
    @ApiModelProperty(value = "包名字")
    private String name;

    /**
     * 升级要点
     */
    @ApiModelProperty(value = "升级要点")
    private String comment;

    /**
     * 上传时间
     */
    @ApiModelProperty(value = "上传时间")
    private Date uploadtime;

    /**
     * 新增时间
     */
    @ApiModelProperty(value = "新增时间")
    private Date createtime;

    /**
     * 操作人
     */
    @ApiModelProperty(value = "操作人")
    private String operator;

    /**
     * 删除标记,1-删除,0-未删除
     */
    @ApiModelProperty(value = "删除标记,1-删除,0-未删除")
    private Integer delflag;

    private List<UpdatePackageModule> updPkgModuleList;

    private static final long serialVersionUID = 1L;
}