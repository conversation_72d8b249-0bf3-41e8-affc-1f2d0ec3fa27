package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjAreaAnalysisPointRwMapper {

    int batchInsertTjAreaAnalysisPoint(
        @Param(value = "poList") List<TjAreaAnalysisPointPo> tjAreaAnalysisPointPos);

    int insertTjAreaAnalysisPoint(TjAreaAnalysisPointPo tjAreaAnalysisPointPo);

    int deleteByAnalysisId(@Param("analysisId") Long analysisId);

    long updateTjAnalysisPointStatus(UpdateAnalysisPointStatusParam updateParam);
}

