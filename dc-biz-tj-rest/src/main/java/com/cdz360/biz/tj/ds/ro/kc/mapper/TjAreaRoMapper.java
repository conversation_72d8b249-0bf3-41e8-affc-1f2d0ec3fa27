package com.cdz360.biz.tj.ds.ro.kc.mapper;


import com.cdz360.biz.model.tj.kc.param.ListTjAreaParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import com.cdz360.biz.model.tj.kc.vo.SiteWithinTjVo;
import com.cdz360.biz.model.tj.kc.param.ListSiteWithinTjAreaParam;

import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;


@Mapper

public interface TjAreaRoMapper {


    TjAreaPo getByAid(@Param("aid") Long aid);

    List<TjAreaPo> findAllUserArea(ListTjAreaParam param);

    List<TjAreaPo> findArea(ListTjAreaParam param);

    long count(ListTjAreaParam param);

    List<SiteWithinTjVo> findSiteWithinTjArea(ListSiteWithinTjAreaParam param);

    long countSiteWithinTjArea(ListSiteWithinTjAreaParam param);
}

