package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyChargeAreaRwMapper {

    TjSurveyChargeAreaPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjSurveyChargeArea(TjSurveyChargeAreaPo tjSurveyChargeAreaPo);

    int updateTjSurveyChargeArea(TjSurveyChargeAreaPo tjSurveyChargeAreaPo);

    int disableTjSurveyChargeAreaBySurveyNo(String surveyNo);

}

