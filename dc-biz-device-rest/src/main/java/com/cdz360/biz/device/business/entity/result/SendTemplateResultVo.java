package com.cdz360.biz.device.business.entity.result;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @author: KT
 * @description:
 * @date: 2019-03-30 22:10
 */
@Data
public class SendTemplateResultVo implements Serializable{

    /**
     * 与更改计费模板相关联的所有设备数量
     */
    private Integer sendTemlateDeviceCount;

    /**
     * 与更改计费模板相关联的设备下发后失败的设备信息
     */
    private List<BoxInfoVo> boxInfoVoFailList;

    /**
     * 下发成功的数量
     */
    private Integer sendTemlateDeviceSuccessCount;

    /**
     * 下发中的数量
     */
    private Integer sendTemlateDeviceSendingCount;

    /**
     * 下发失败的数量
     */
    private Integer sendTemlateDeviceFailedCount;
}
