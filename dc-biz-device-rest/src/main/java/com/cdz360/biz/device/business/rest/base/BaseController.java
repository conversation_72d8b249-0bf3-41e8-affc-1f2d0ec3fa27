package com.cdz360.biz.device.business.rest.base;

import com.cdz360.biz.device.business.constant.Constant;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * 公共数据获取
 *
 * <AUTHOR>
 * @since 2017年8月7日下午12:13:46
 */
public class BaseController {

    @Autowired
    protected HttpServletRequest request;

    @Autowired
    protected HttpServletResponse response;

    /**
     * 获取当前登录用户的token
     *
     * @return
     */
    protected String getToken() {
        String token = request.getHeader(Constant.CURRENT_USER_TOKEN);
        if(StringUtils.isBlank(token)){
            token = request.getParameter(Constant.CURRENT_USER_TOKEN);
        }
        return token;
    }

    /**
     * 商户端（B端）
     * 获取当前登录用户的所属商户id
     *
     * @return
     */
    protected String getCommId() {
        String commId = request.getHeader(Constant.CURRENT_COMM_ID);
        if(StringUtils.isBlank(commId)){
            commId = request.getParameter(Constant.CURRENT_COMM_ID);
        }
        return commId;
    }
}
