package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.BsBox;
import com.cdz360.biz.device.business.entity.request.BoxListRequest;
import com.cdz360.biz.device.business.entity.result.BoxInfoVo;
import com.cdz360.biz.device.business.entity.result.BoxSettingInfoVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 设备管理：盒子 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-06
 */
@Mapper
public interface BsBoxQueryMapper extends BaseMapper<BsBox> {

    /**
     * 根据桩号获取设备ID
     *
     * @param serialNumber
     * @return
     */
    String getDeviceIdBySerialNumber(@Param("serialNumber") String serialNumber);

    /**
     * 根据站点id获取站点下设备id列表
     *
     * @param siteId
     * @return
     */
    List<String> getBoxCodeListBySiteId(@Param("siteId") String siteId);

    /**
     * 获取设备简单列表
     *
     * @param request
     * @return
     */
    List<BoxInfoVo> getBoxSimpleList(BoxListRequest request);

    /**
     * 获取桩&桩配置简单列表
     *
     * @param request
     * @return
     */
    List<BoxSettingInfoVo> BoxSettingSimpleList(BoxListRequest request);

    /**
     * 临时接口
     *
     * @param siteId
     * @return
     */
    Map<String, Object> getDeviceStatistics(@Param("siteId") String siteId);

    /**
     * 根据计费模板Id获取所有与此计费模板绑定的设备
     *
     * @param
     * @return
     */
    List<BoxInfoVo> getBoxIOnfoListByTemplateId(@Param("siteId") String siteId,
                                                @Param("templateIdList") List<Long> templateIdList);

    /**
     * 根据siteId获取默认采用场站计费模板的设备
     * @param siteId
     * @return
     */
    List<BoxInfoVo> getBoxIOnfoListBySiteId(@Param("siteId") String siteId);

    List<BoxInfoVo> selectEvseNamesByDeviceIds(Map<String, Object> map);

    List<String> getAllBoxBySiteId(@Param("siteId") String siteId);
}
