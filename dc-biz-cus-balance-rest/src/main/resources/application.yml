
server:
  port: 8080
  use-forward-headers: true
  compression.enabled: true

spring:
  application:
    name: dc-biz-cus-balance-dev
  config:
    import: "configserver:http://oam-test.iot.renwochong.com"
  profiles:
    active: common,rabbitmq,jdbc-balance,redis,zipkin
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    config:
      label: test01

hyena:
  idempotent: redis
  cache: redis



logging:
  level:
    com.cdz360: 'DEBUG'
    io.github.alphajiang.hyena: DEBUG
    org.springframework: 'WARN'
    org.springframework.cloud: 'INFO'
    org.springframework.cloud.config: 'INFO'
    org.springframework.cloud.netflix: 'DEBUG'
    org.springframework.amqp: 'DEBUG'


  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} - %logger{36}.%M\\(%line\\) - %msg%n"

#spring-cloud服务配置
eureka:
  instance:
    hostname: localhost
  client:
    registerWithEureka: false
    fetchRegistry: true
    healthcheck:
      enabled: true
    serviceUrl:
      defaultZone: http://aaa:<EMAIL>:7001/eureka/,http://aaa:<EMAIL>:7001/eureka/

feign:
  hystrix:
    enabled: true

hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            timeoutInMilliseconds: 5000
          strategy: SEMAPHORE
##超时时间10000毫秒 = 10秒

ribbon:
  ReadTimeout: 60000
  ConnectTimeout: 60000



