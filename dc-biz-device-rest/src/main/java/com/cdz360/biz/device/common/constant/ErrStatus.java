package com.cdz360.biz.device.common.constant;

import java.util.Arrays;

/**
 * @Enumname ErrStatus
 * @Description TODO
 * @Date 2019/6/5
 * @Created by wangzheng
 */
public enum ErrStatus {

    OK(0, "success"),
    NAME_CONFLICT(4001, "name conflict"),

    REQ_CONFLICT(4002, "request conflict"),
    REQ_PARAM_NOT_VALID(4003, "request param not valid !"),
    BAD_MOBILE(4005, "invalid phone number"),
    OP_TOO_FREQUENT(4006, "you operation are too frequent"),
    INVALID_VERIFY_CODE(4007,"invalid verify code"),
    OUTOFDATE_VERIFY_CODE(4007,"invalid verify code"),
    AUT_WRONG_PASS(4011, "validate username or password fail"),
    AUT_INVALID_TOKEN(4012, "invalid token"),
    USER_NOT_EXIST(4013, "user not exists"),
    FREEZE_ERROR(4014, "由于被其他数据引用, 该记录不能被停用/删除!"),
    UNFREEZE_ERROR(4015, "由于被其他数据引用, 该记录不能被启用!"),
    USER_FREZZE(4019, "账号已被停用"),
    USER_LOCKED(4020,"账号被锁定"),
    ACCESS_FORBIDDEN(4030, "你无权进行访问"),
    RES_NOT_MODIFY(4041, "not change"),
    RES_NOT_FOUND(4040, "not found"),
    COM_NOT_EXIST(4042, "user's com not found"),
    UNKNOWN(9999, "unknown error"),
    REST_ERROR(5001, "inner http interface  call exception"),
    SQL_ERROR(5002, "sql error"),
    REQ_ALREADY_IN_USE(4017, "该资源被其他资源使用,不能修改其关键属性"),
    NOT_AUTH(4043, "not auth"),
    USERNAME_CONFLICT(4044, "登录名重复"),
    PHONE_CONFLICT(4045, "电话号码重复"),
    EMAIL_CONFLICT(4046, "邮箱重复"),
    CREDENTIALS_ERROR(4047,"身份证重复")
    ;

    ErrStatus(int errcode, String errmsg) {
        this.errcode = errcode;
        this.errmsg = errmsg;
    }

    private final int errcode;
    private final String errmsg;

    public final int getErrcode() {
        return errcode;
    }

    public final String getErrmsg() {
        return errmsg;
    }


//    public static void main(String[] args) {
//        Arrays.asList(ErrStatus.values())
//                .forEach(errStatus -> {
//                    System.out.printf("|%s|%s|%s|\n", errStatus.errcode, errStatus.errmsg, "");
//                });
//    }
}
