package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.device.business.entity.param.ModifyEvseCfgParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

/**
 * @Classname HystrixEvseCfgClientFactory
 * @Description TODO
 * @Date 2019/6/18 14:14
 * @Created by Rafael
 */
@Slf4j
@Component
public class HystrixEvseCfgClientFactory implements FallbackFactory<EvseCfgClient> {

    @Override
    public EvseCfgClient create(Throwable throwable) {
        return new EvseCfgClient() {
            @Override
            public BaseResponse modifyEvseCfg(String cfgEvseParam) {
                log.error(throwable.getMessage());
                return null;
            }

            @Override
            public BaseResponse modifyEvseCfgV2(ModifyEvseCfgParam param) {
                return RestUtils.serverBusy();
            }
        };
    }
}