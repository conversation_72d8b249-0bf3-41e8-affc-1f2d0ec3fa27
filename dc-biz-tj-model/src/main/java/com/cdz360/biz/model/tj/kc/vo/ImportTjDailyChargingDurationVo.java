package com.cdz360.biz.model.tj.kc.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "日充电时长导入统计信息")
@Data
@Accessors(chain = true)
public class ImportTjDailyChargingDurationVo<T> {

    @Schema(description = "导入总数")
    @JsonInclude(Include.NON_NULL)
    private int importTotal;

    @Schema(description = "成功总数")
    @JsonInclude(Include.NON_NULL)
    private int successTotal;

    @Schema(description = "失败项")
    @JsonInclude(Include.NON_NULL)
    private List<T> errList;
}
