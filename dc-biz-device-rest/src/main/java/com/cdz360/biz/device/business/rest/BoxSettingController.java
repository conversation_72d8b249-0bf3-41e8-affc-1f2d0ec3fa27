package com.cdz360.biz.device.business.rest;


import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.device.business.client.MerchantFeignClient;
import com.cdz360.biz.device.business.constant.Constant;
import com.cdz360.biz.device.business.constant.DbsUrlConstant;
import com.cdz360.biz.device.business.entity.request.BoxSettingListRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingResultRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingUpsertQRCodeRequest;
import com.cdz360.biz.device.business.entity.request.BoxSettingUpsertRequest;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo;
import com.cdz360.biz.device.business.entity.result.EvsePasscodePo;
import com.cdz360.biz.device.business.entity.result.ListResponseEvseList;
import com.cdz360.biz.device.business.entity.result.UrgencyCardEvse;
import com.cdz360.biz.device.business.rest.base.BaseController;
import com.cdz360.biz.device.business.service.BoxSettingService;
import com.cdz360.biz.device.business.util.DbsAssert;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * @Classname BoxSettingController
 * @Description 桩配置
 * @Date 2019/6/6 13:35
 * @Created by JLei
 * @Email <EMAIL>
 */
@Slf4j
@RestController
@Deprecated
public class BoxSettingController extends BaseController {

    @Autowired
    private BoxSettingService boxSettingService;

    @Autowired
    private MerchantFeignClient merchantFeignClient;

    /**
     * @param request 分页查询对象列表
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SELECT_PAGE)
    public BaseResponse selectPagedBoxSettingList(@RequestBody BoxSettingListRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置分页查询参数不能为空");
        Map<String, Object> resultmap = boxSettingService.selectPagedBoxSettingList(request);
        List<BsBoxSettingBInfoVo> boxSettinglist = (List<BsBoxSettingBInfoVo>) resultmap.get("rows");
        long total = Long.parseLong(resultmap.get("total").toString());
        return new ListResponse<>(boxSettinglist, total);
    }

    /**
     * 根据ID查询对象
     *
     * @param id 主键
     * @return
     */
    @GetMapping(DbsUrlConstant.URL_BOXSETTING_SELECT_BY_ID)
    public BaseResponse selectBoxSettingById(@PathVariable("id") Long id) {
        log.info("param = {}", id);
        DbsAssert.isNotNull(id, "桩配置主键查询主键必填");
        BsBoxSettingBInfoVo bsBoxSettingBInfoVo = boxSettingService.selectBoxSettingById(id);
        return new ObjectResponse<>(bsBoxSettingBInfoVo);
    }

    /**
     * @param request 插入对象
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_INSERT)
    public BaseResponse insertBoxSetting(@RequestBody BoxSettingUpsertRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置插入数据不能为空");
        DbsAssert.isNotNull(request.getBoxCode(), "桩配置插入参数异常");
        Integer res = boxSettingService.insertBoxSetting(request);
        if (res <= 0) {
            throw new DcServiceException("桩配置数据插入失败");
        }
        return new ObjectResponse();
    }

    /**
     * @param request 更新对象
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_UPATTE)
    public BaseResponse updateBoxSetting(@RequestBody BoxSettingUpsertRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置修改请求参数为空");
        DbsAssert.isNotNull(request.getId(), "桩配置修改主键不能为空");
        Integer res = boxSettingService.updateBoxSetting(request);
        if (res <= 0) {
            throw new DcServiceException("桩配置数据修改失败，主键：" + request.getId());
        }
        return new ObjectResponse();
    }

    /**
     * 通过桩号更新对象
     *
     * @param request
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_UPATTE_BY_EVSEID)
    public BaseResponse updateBoxSettingByEvseId(@RequestBody BoxSettingUpsertRequest request) {
        log.info(">> 通过桩号更新对象。request = {}", request);
        DbsAssert.isNotNull(request, "通过桩号修改桩配置请求参数为空");
        DbsAssert.isNotNull(request.getBoxOutFactoryCode(), "通过桩号修改桩配置桩号不能为空");
        Integer res = boxSettingService.updateBoxSettingByEvseId(request);
        if (res <= 0) {
            throw new DcServiceException("桩配置数据修改失败，主键：" + request.getId());
        }
        return new ObjectResponse();
    }

    /**
     * @param id 通过主键删除对象
     * @return
     */
    @GetMapping(DbsUrlConstant.URL_BOXSETTING_DELETE)
    public BaseResponse deleteBoxSetting(@PathVariable("id") Long id) {
        log.info("param = {}", id);
        DbsAssert.isNotNull(id, "桩配置删除主键不能为空");
        Integer res = boxSettingService.deleteBoxSetting(id);
        if (res <= 0) {
            throw new DcServiceException("桩配置数据删除失败，主键：" + id);
        }
        return new ObjectResponse();
    }

    /**
     * @param ids 批量删除对象
     * @return
     */
    @GetMapping(DbsUrlConstant.URL_BOXSETTING_DELETE_BATCH)
    public BaseResponse deleteBatchBoxSetting(@RequestParam(value = "ids") List<Long> ids) {
        log.info("param = {}", ids);
        DbsAssert.isTrue(ids != null && ids.size() > 0, "桩配置批量删除主键集合不能为空");
        Integer res = boxSettingService.deleteBatchBoxSetting(ids);
        return new ObjectResponse<>("桩配置批量删除记录" + res + "条");
    }

    /**
     * 根据请求header的token、状态数组、指定的commid，分页查询出下发结果
     *
     * @param req
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SELECT_BY_TOKEN)
    public ListResponse<BsBoxSettingBInfoVo> getBoxSettingResultByCommId(@RequestBody BoxSettingResultRequest req) {
        log.info("查询下发结果。request: {}", req);
        String token = request.getHeader(Constant.CURRENT_USER_TOKEN);
        ListResponse<BsBoxSettingBInfoVo> result = boxSettingService.getResultByCommIds(req.getCommIdChain(), req);

        return result;
    }

    /**
     * 配置下发
     *
     * @param request
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND)
    public BaseResponse sendBoxSetting(@RequestBody BoxSettingUpsertRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置下发请求参数不能为空");
        DbsAssert.isNotNull(request.getBoxOutFactoryCode(), "桩配置下发桩号不能为空");
        boxSettingService.sendBoxSetting(request);
        return new ObjectResponse();
    }

    /**
     * 批量配置下发
     *
     * @param request
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND_BATCH)
    public BaseResponse sendBatchBoxSetting(@RequestBody BoxSettingUpsertRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置批量修改请求参数为空");
        boxSettingService.sendBatchBoxSetting(request);
        return new ObjectResponse();
    }


    /**
     * @param request 更新桩二维码配置，并下发桩配置（仅二维码）
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_UPATTE_QRCODE)
    public ObjectResponse updateBoxSettingQRCode(@RequestBody BoxSettingUpsertQRCodeRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "桩配置修改请求参数为空");
        DbsAssert.isNotNull(request.getQrCode(), "桩配置二维码url不能为空");
        DbsAssert.isNotNull(request.getIds(), "id不能为空");
        Integer res = boxSettingService.updateBoxSettingQRCode(request.getIds(), request.getQrCode());
        return new ObjectResponse<>(res);
    }

    /**
     * 再次下发
     *
     * @param id
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND_QRCODE_BY_ID)
    public ObjectResponse sendQRCodeById(@RequestHeader("token") String token, @RequestParam("id") Long id) {
        log.info("param = {}", id);
        DbsAssert.isNotNull(token, "token不能为空");
        DbsAssert.isNotNull(id, "id不能为空");
        Integer res = boxSettingService.sendQRCodeById(token, id);
        return new ObjectResponse<>(res);
    }

    /**
     * 失败下发
     *
     * @param token
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND_FAIL_QRCODE)
    public ObjectResponse sendFailQRCode(@RequestHeader("token") String token, @RequestParam("commId") Long commId) {
        log.info("param = {}", commId);
        DbsAssert.isNotNull(token, "token不能为空");
        DbsAssert.isNotNull(commId, "commId不能为空");
        Integer res = boxSettingService.sendFailQRCodeByToken(token, commId);
        return new ObjectResponse<>(res);
    }

    /**
     * 根据下发状态进行Count
     *
     * @param stationCode     必传，站点编号
     * @param whiteCardStatus 可传，紧急充电卡下发状态1下发成功2失败3下发中
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SELECT_BY_STATIONCODE_WHITECARDSTATUS)
    public ObjectResponse<Integer> selectCountByStationCodeAndWhiteCardStatus(
            @RequestParam("stationCode") String stationCode,
            @RequestParam(value = "whiteCardStatus", required = false) Integer whiteCardStatus) {

        log.info("stationCode = {}, whiteCardStatus {}", stationCode, whiteCardStatus);
        DbsAssert.isNotBlank(stationCode, "stationCode为空");
        Integer integer = boxSettingService.selectCountByStationCodeAndWhiteCardStatus(stationCode, whiteCardStatus);
        return new ObjectResponse<>(integer);
    }

    /**
     * 多场站紧急充电卡批量下发
     *
     * @param requestList
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND_BATCH_WHITECARD)
    public BaseResponse sendBatchWhiteCards(@RequestBody List<BoxSettingListRequest> requestList) {
        log.info("requestList = {}", requestList);
        DbsAssert.isTrue(requestList != null && requestList.size() > 0, "请求参数不能为空");
        Map<String, Object> resMap = boxSettingService.sendBatchWhiteCards(requestList);
        return new ObjectResponse<>(resMap);
    }

    /**
     * 更新紧急充电卡-桩关系表
     *
     * @param evseId
     * @param status 2-成功，1-失败
     * @return
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_UPDATE_STATUS_ON_EVSE_ID_AND_STATUS)
    public BaseResponse updateStatusOnEvseIdAndStatus(@RequestParam("evseId") String evseId, @RequestParam("status") int status) {
        log.info("evseId = {}, status = {}", evseId, status);
        int ret = boxSettingService.updateStatusOnEvseIdAndStatus(evseId, status);
        return new ObjectResponse<>(ret);
    }

    /**
     * 单场站紧急充电卡下发
     *
     * @param request
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_SEND_WHITECARD)
    public BaseResponse sendWhiteCard(@RequestBody BoxSettingListRequest request) {
        log.info("param = {}", request);
        DbsAssert.isNotNull(request, "请求参数不能为空");
        DbsAssert.isTrue(StringUtils.isNotBlank(request.getSiteId()), "站点ID不能为空");
        return new ListResponse<>(boxSettingService.sendWhiteCard(request));
    }

    /**
     * 根据stationIds获取相关桩的信息，[,]分割
     *
     * @param stationIds
     */
    @PostMapping(DbsUrlConstant.URL_BOXSETTING_URGENCY_CARD_DETAIL_EVSE_LIST)
    public ListResponseEvseList<UrgencyCardEvse> urgencyCardsDetailEvseList(@RequestParam(value = "stationIds") String stationIds,
                                                                            @RequestParam(value = "cardChipNo") String cardChipNo,
                                                                            @RequestParam(value = "evse", required = false) String evse,
                                                                            @RequestParam(value = "page", required = false) Integer page,
                                                                            @RequestParam(value = "rows", required = false) Integer rows) {
        log.info("stationIds = {}", stationIds);
        if (page == null || page < 1) page = 1;
        if (rows == null || rows < 1) rows = 10;
        return boxSettingService.urgencyCardsDetailEvseList(stationIds, cardChipNo, evse, page, rows);
    }

    /**
     * 获取
     *
     * @param evseNo
     */
    @GetMapping(DbsUrlConstant.URL_BOXSETTING_GET_EVSE_PASSCODE)
    public ObjectResponse<EvsePasscodePo> getEvsePasscode(@PathVariable(value = "evseNo") String evseNo) {
        log.info("evseNo = {}", evseNo);
        return boxSettingService.getEvsePasscode(evseNo);
    }

    /**
     * 给桩下发场站默认配置信息
     *
     * @param evseNo
     * @param siteId
     * @return
     */
    @GetMapping(DbsUrlConstant.URL_BOXSETTING_DOWN_SETTING_TO_EVSE)
    public BaseResponse downSetting2Evse(@RequestParam("evseNo") String evseNo,
                                         @RequestParam("siteId") String siteId) {
        log.info("evseNo = {}", evseNo);
        boxSettingService.downSetting2Evse(evseNo, siteId);
        return RestUtils.success();
    }
}
