package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.WhiteCardEvse;
import com.cdz360.biz.device.business.entity.po.WhiteCardEvseDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname WhiteCardEvseMapper
 * @Description 桩关联紧急充电卡下发情况
 * @Date 2019/7/17 15:45
 * @Created by JLei
 * @Email <EMAIL>
 */
@Mapper
public interface WhiteCardEvseMapper extends BaseMapper<WhiteCardEvse> {

    int STATUS_DELTA_OK = 2;
    int STATUS_DELTA_FAIL = 1;

    /**
     * 修改下发结果返回的，将【下发中】或【废弃中】的记录，修改为终结状态，
     * 修改为【1-下发成功】后，将passwordTmp字段换到password上
     * 成功时delta=2
     * 失败是delta=1
     *
     * @param evseId
     * @param statusDelta
     * @return
     */
    int updateStatusOnEvseIdAndStatus(@Param("evseId") String evseId, @Param("statusDelta") int statusDelta);

    int batchInsert(@Param("whiteCardEvses") List<WhiteCardEvse> whiteCardEvses);

    int updateByEvseIdAndCardNo(@Param("whiteCardEvse") WhiteCardEvse whiteCardEvse);

    int selectByEvseAndCardNo(@Param("evseId") String evseId, @Param("cardNo") String cardNo);

    /**
     * 根据条件查询
     * @param whiteCardEvseDto
     * @return
     */
    List<WhiteCardEvse> queryByCondition(WhiteCardEvseDto whiteCardEvseDto);
}