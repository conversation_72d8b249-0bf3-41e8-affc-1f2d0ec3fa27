package com.cdz360.biz.device.business.entity.po;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.cdz360.base.model.base.type.SupplyType;
import com.cdz360.biz.device.business.constant.SiteStatusEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 站点表
 * </p>
 *
 * <AUTHOR>
 * @since 2018-07-23
 */
@Data
@TableName("t_site")
public class Site {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID
     */
    @TableId(value = "id")
    private String id;
    /**
     * 站点编号
     */
    @TableField("id_no")
    private String idNo;

    /**
     * 充电站的电流形式
     */
    @TableField("supplyType")
    private SupplyType supplyType;

    /**
     * 站点名称
     */
    private String name;


    /**
     * 站点自编号
     */
    @TableField("siteNo")
    private String siteNo;

    /**
     * 站点实时冻结金额
     */
    @TableField("frozenAmount")
    private BigDecimal frozenAmount;
    /**
     * 字典类型
     * 站点类型: 0-未知 1-公共 2-公交 3-物流 4-混合
     */
    private Integer type;
    /**
     * 经度
     */
    private BigDecimal longitude;
    /**
     * 纬度
     */
    private BigDecimal latitude;
    /**
     * 保存经纬度对应的geohash,用于计算附近的站点
     */
    private String geohash;
    /**
     * 站点地址
     */
    @TableField("address")
    private String address;
    /**
     * 省份编码
     */
    private Integer province;
    /**
     * 城市编码
     */
    private Integer city;
    /**
     * 区域编码
     */
    private Integer area;
    /**
     * 服务号码
     */
    private String phone;
    /**
     * 站点状态{@link SiteStatusEnum}
     */
    private Integer status;

    @TableField("topCommId")
    private Long topCommId;
    /**
     * 运营商ID
     */
    @TableField("operate_id")
    private Long operateId;
    /**
     * 运营商名称
     */
    @TableField("operate_name")
    private String operateName;
    /**
     * 物业ID或代理商账号ID
     */
    @TableField("merchant_id")
    private Long merchantId;
    /**
     * 联系人
     */
    private String contacts;
    /**
     * 联系人号码
     */
    @TableField("contacts_phone")
    private String contactsPhone;
    /**
     * 备注
     */
    private String remark;
    /**
     * 工作日服务时间
     */
    @TableField("service_workday_time")
    private String serviceWorkdayTime;
    /**
     * 节假日服务时间
     */
    @TableField("service_holiday_time")
    private String serviceHolidayTime;
    /**
     * 停车是否收费 **0-未知 1-收费 2-免费**
     */
    private Integer park;
    /**
     * 停车费
     */
    @TableField("park_fee")
    private String parkFee;
    /**
     * 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
     */
    private Integer appoint;
    /**
     * 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
     */
    private Integer scope;
    /**
     * 上线时间
     */
    @TableField("online_date")
    private Date onlineDate;
    /**
     * 最后更新时间
     */
    @TableField("update_time")
    private Date updateTime;
    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;
    /**
     * 站点图片
     */
    @TableField("images")
    private String images;
    /**
     * 支持品牌
     */
    @TableField("brand_ids")
    private String brandIds;
    /**
     * 支付方式
     */
    @TableField("app_pay")
    private String appPay;
    /**
     * 收费说明
     */
    @TableField("fee_description")
    private String feeDescription;
    /**
     * 发票描述
     */
    @TableField("invoiceDesc")
    private String invoiceDesc;
    /**
     * 站点收费范围 最低
     */
    @TableField("fee_min")
    private Integer feeMin;
    /**
     * 站点收费范围 最高
     */
    @TableField("fee_max")
    private Integer feeMax;

    /**
     * 站点默认计费模板Id
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 站点默认计费模板名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 禁用的客户端编号,使用","分隔
     */
    @TableField("forbiddenClient")
    private String forbiddenClient;

    @TableField("defaultPayType")
    @ApiModelProperty(value = "默认扣款类型")
    private Integer defaultPayType;

    @TableField("payAccountId")
    @ApiModelProperty(value = "扣款账户ID")
    private Long payAccountId;

    @TableField("bizType")
    @ApiModelProperty(value = "运营属性")
    private Integer bizType;

    @TableField("bizName")
    @ApiModelProperty(value = "运营方名称")
    private String bizName;
}
