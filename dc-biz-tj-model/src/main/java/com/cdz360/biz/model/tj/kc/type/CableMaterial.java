package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "电缆材质")
@Getter
public enum CableMaterial implements DcEnum {

    UNKNOWN(999, "未知"),

    COPPER(1, "铜"),
    ALUMINIUM(2, "铝"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    CableMaterial(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static CableMaterial valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (CableMaterial flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return CableMaterial.UNKNOWN;
    }

}
