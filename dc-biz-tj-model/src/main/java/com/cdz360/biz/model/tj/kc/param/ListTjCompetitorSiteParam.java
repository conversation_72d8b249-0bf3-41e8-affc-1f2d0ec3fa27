package com.cdz360.biz.model.tj.kc.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取竞争者场站列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListTjCompetitorSiteParam extends BaseListParam {

    @Schema(description = "竞争对手ID(t_tj_competitor.id)")
    @JsonInclude(Include.NON_NULL)
    private Long competitorId;

    @Schema(description = "竞争对手场站ID")
    @JsonInclude(Include.NON_NULL)
    private List<Long> siteIdList;

    @Schema(description = "投建区域ID列表(t_tj_area.id)-查投建区域内的所有站点")
    @JsonInclude(Include.NON_NULL)
    private Long withinAid;

    @Schema(description = "投建区域ID列表(t_tj_area.id)-查投建区域内的所有站点")
    @JsonInclude(Include.NON_NULL)
    private List<Long> withinAidList;

    @Schema(description = "投建区域分析ID-查询点位辐射区内的场站")
    @JsonInclude(Include.NON_NULL)
    private Long analysisId;

    @Schema(description = "投建区域分析点位编号-查询点位辐射区内的场站")
    @JsonInclude(Include.NON_NULL)
    private Long analysisPointNum;

    @Schema(description = "辐射区竞争者ID列表", example = "1,2,3")
    @JsonInclude(Include.NON_NULL)
    private String petIdSetString;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
