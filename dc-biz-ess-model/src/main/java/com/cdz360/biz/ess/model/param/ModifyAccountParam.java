package com.cdz360.biz.ess.model.param;

import com.cdz360.biz.ess.model.dto.BaseUserAccount;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import javax.validation.constraints.Email;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "用户账户信息变更参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ModifyAccountParam extends BaseUserAccount {

    @Email
    @Schema(description = "新账户(New Account)")
    private String email;

    //    @NotBlank
    @Schema(description = "新密码")
    private String newPassw;

    //    @NotBlank
//    @Schema(description = "确认密码", required = true)
//    private String confirmPassw;

    @Schema(description = "验证码", required = true)
//    @NotBlank
    private String verificationCode;

    @Schema(description = "是否确认密码: true(要校验密码); false(不需要校验密码)", hidden = true)
    @JsonInclude(Include.NON_NULL)
    private Boolean checkPassw;

}
