package com.cdz360.biz.model.cus.auth.dto;


import com.cdz360.base.model.base.vo.BaseObject;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CusAuthReq extends BaseObject {
    private String seq;
    private String evseId;
    private String plugId;
    private Integer authType;//鉴权类型:0x11: 在线卡, 0x12: 车架号
    private String accountNo;//账号, 此处为卡号 (逻辑卡号) 或 17位 VIN 码
    private Boolean save2Redis; // 是否保存鉴权结果到redis



}
