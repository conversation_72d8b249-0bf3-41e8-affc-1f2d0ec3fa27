<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyChargeAreaPileRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="areaId" jdbcType="BIGINT" property="areaId"/>
    <result column="evseModelId" jdbcType="BIGINT" property="evseModelId"/>
    <result column="evseCount" jdbcType="INTEGER" property="evseCount"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO">
    select * from t_tj_survey_charge_area_pile where id = #{id} and enable = true
  </select>

  <select id="findTjSurveyChargeAreaPileByAreaId"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO">
    select * from t_tj_survey_charge_area_pile where areaId = #{areaId} and enable = true
    order by id desc
  </select>

  <select id="findTjSurveyChargeAreaPileBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_CHARGE_AREA_PILE_PO">
    select * from t_tj_survey_charge_area_pile cap
    left join t_tj_survey_charge_area ca
    on cap.areaId = ca.id
    where ca.surveyNo = #{surveyNo}
    and cap.enable = true
    order by cap.id desc
  </select>


</mapper>

