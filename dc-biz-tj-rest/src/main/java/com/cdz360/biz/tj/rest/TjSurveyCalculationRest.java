package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjSurveyParam;
import com.cdz360.biz.model.tj.kc.param.RepeatSurveyParam;
import com.cdz360.biz.model.tj.kc.param.TjSurveyBiParam;
import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyBiVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyVo;
import com.cdz360.biz.model.trading.hlht.po.SiteOutPo;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.model.trading.yw.param.ListYwOrderParam;
import com.cdz360.biz.model.trading.yw.vo.YwOrderVo;
import com.cdz360.biz.tj.service.kc.TjSurveyCalculationService;
import com.cdz360.biz.tj.service.kc.TjSurveyService;
import com.cdz360.biz.utils.feign.yw.YwOrderClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmEvseModelFeignClient;
import com.chargerlinkcar.framework.common.feign.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.utils.LoggerHelper2;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建勘察智能测算录入相关操作接口", description = "投建勘察智能测算录入相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/survey/calculation")
public class TjSurveyCalculationRest {

    @Autowired
    private TjSurveyCalculationService tjSurveyCalculationService;

    @Autowired
    private IotDeviceMgmEvseModelFeignClient iotDeviceMgmEvseModelFeignClient;


    @Operation(summary = "勘察站充电区域查询")
    @GetMapping(value = "/findTjSurveyChargeAreaBySurveyNo")
    public Mono<ListResponse<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察站充电区域查询: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyChargeAreaBySurveyNo(surveyNo)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "新增或编辑勘察站充电区域")
    @PostMapping(value = "/saveTjSurveyChargeArea")
    public Mono<ObjectResponse<TjSurveyChargeAreaVo>> saveTjSurveyChargeArea(@RequestBody TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
        log.debug("新增或编辑勘察站充电区域: param = {}", JsonUtils.toJsonString(tjSurveyChargeAreaVo));
        return tjSurveyCalculationService.saveTjSurveyChargeArea(tjSurveyChargeAreaVo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "勘察站标准设施查询")
    @GetMapping(value = "/findTjSurveySupportingFacilitiesBySurveyNo")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> findTjSurveySupportingFacilitiesBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察站标准设施查询: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveySupportingFacilitiesBySurveyNo(surveyNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新增或编辑勘察站标准设施")
    @PostMapping(value = "/saveTjSurveySupportingFacilities")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesPo>> saveTjSurveySupportingFacilities(@RequestBody TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        log.debug("新增或编辑勘察站标准设施: param = {}", JsonUtils.toJsonString(tjSurveySupportingFacilitiesPo));
        return tjSurveyCalculationService.saveTjSurveySupportingFacilities(tjSurveySupportingFacilitiesPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "勘察站其他设施查询")
    @GetMapping(value = "/findTjSurveySupportingFacilitiesOtherBySurveyNo")
    public Mono<ListResponse<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察站其他设施查询: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveySupportingFacilitiesOtherBySurveyNo(surveyNo)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "新增或编辑勘察站其他设施")
    @PostMapping(value = "/saveTjSurveySupportingFacilitiesOther")
    public Mono<ObjectResponse<TjSurveySupportingFacilitiesOtherPo>> saveTjSurveySupportingFacilitiesOther(@RequestBody TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        log.debug("新增或编辑勘察站其他设施: param = {}", JsonUtils.toJsonString(tjSurveySupportingFacilitiesOtherPo));
        return tjSurveyCalculationService.saveTjSurveySupportingFacilitiesOther(tjSurveySupportingFacilitiesOtherPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "勘察站高压查询")
    @GetMapping(value = "/findTjSurveyHighVoltageBySurveyNo")
    public Mono<ListResponse<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察站高压查询: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyHighVoltageBySurveyNo(surveyNo)
            .map(RestUtils::buildListResponse);
    }

    @Operation(summary = "新增或编辑勘察站高压信息")
    @PostMapping(value = "/saveTjSurveyHighVoltage")
    public Mono<ObjectResponse<TjSurveyHighVoltagePo>> saveTjSurveyHighVoltage(@RequestBody TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        log.debug("新增或编辑勘察站高压信息: param = {}", JsonUtils.toJsonString(tjSurveyHighVoltagePo));
        return tjSurveyCalculationService.saveTjSurveyHighVoltage(tjSurveyHighVoltagePo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "勘察站运营收入查询")
    @GetMapping(value = "/findTjSurveyOperationIncomeBySurveyNo")
    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> findTjSurveyOperationIncomeBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察场站运营收入: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyOperationIncomeBySurveyNo(surveyNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新增或编辑场站勘察站运营收入")
    @PostMapping(value = "/saveTjSurveyOperationIncome")
    public Mono<ObjectResponse<TjSurveyOperationIncomePo>> saveTjSurveyOperationIncome(@RequestBody TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        log.debug("新增或编辑场站勘察站运营收入: param = {}", JsonUtils.toJsonString(tjSurveyOperationIncomePo));
        return tjSurveyCalculationService.saveTjSurveyOperationIncome(tjSurveyOperationIncomePo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "勘察站运营支出查询")
    @GetMapping(value = "/findTjSurveyOperationExpensesBySurveyNo")
    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> findTjSurveyOperationExpensesBySurveyNo(@RequestParam("surveyNo") String surveyNo) {
        log.debug("获取勘察场站运营支出: param = {}", JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.findTjSurveyOperationExpensesBySurveyNo(surveyNo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新增或编辑场站勘察站运营支出")
    @PostMapping(value = "/saveTjSurveyOperationExpenses")
    public Mono<ObjectResponse<TjSurveyOperationExpensesPo>> saveTjSurveyOperationExpenses(@RequestBody TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        log.debug("新增或编辑场站勘察站运营支出: param = {}", JsonUtils.toJsonString(tjSurveyOperationExpensesPo));
        return tjSurveyCalculationService.saveTjSurveyOperationExpenses(tjSurveyOperationExpensesPo)
            .map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "新增或编辑场站勘察站智能测算信息")
    @PostMapping(value = "/saveTjSurveyCalculationInfo")
    public Mono<ObjectResponse<TjSurveyCalculationInfoVo>> saveTjSurveyCalculationInfo(
        @RequestBody TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo) {
        log.debug("新增或编辑场站勘察站智能测算录入信息: {}", JsonUtils.toJsonString(tjSurveyCalculationInfoVo));
        return tjSurveyCalculationService.saveTjSurveyCalculationInfoVo(tjSurveyCalculationInfoVo).map(RestUtils::buildObjectResponse);
    }

    @Operation(summary = "智能测算")
    @GetMapping(value = "/calculationResult")
    public Mono<ListResponse<TjSurveyCalculationResultVo>> calculationResult(
        @RequestParam("surveyNo") String surveyNo) {
        log.debug("智能测算: param = {}",  JsonUtils.toJsonString(surveyNo));
        return tjSurveyCalculationService.calculationResult(surveyNo).map(RestUtils::buildListResponse);
    }
}
