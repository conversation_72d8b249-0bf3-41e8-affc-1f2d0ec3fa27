<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.BsBoxSettingMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.result.BsBoxSettingVo">
        <id column="id" property="id"/>
        <result column="boxCode" property="boxCode"/>
        <result column="boxOutFactoryCode" property="boxOutFactoryCode"/>
        <result column="chargeId" property="chargeId"/>
        <result column="status" property="status"/>
        <result column="adminPassword" property="adminPassword"/>
        <result column="level2Password" property="level2Password"/>
        <result column="dayVolume" property="dayVolume"/>
        <result column="nightVolume" property="nightVolume"/>
        <result column="url" property="url"/>
        <result column="isQueryChargeRecord" property="isQueryChargeRecord"/>
        <result column="isTimedCharge" property="isTimedCharge"/>
        <result column="isNoCardCharge" property="isNoCardCharge"/>

        <result column="isScanCharge" property="isScanCharge"/>
        <result column="isVinCharge" property="isVinCharge"/>
        <result column="isCardCharge" property="isCardCharge"/>
        <result column="isQuotaEleCharge" property="isQuotaEleCharge"/>
        <result column="isQuotaMoneyCharge" property="isQuotaMoneyCharge"/>
        <result column="isQuotaTimeCharge" property="isQuotaTimeCharge"/>
        <result column="internationalAgreement" property="internationalAgreement"/>
        <result column="isAutoStopCharge" property="isAutoStopCharge"/>
        <result column="avgOrTurnCharge" property="avgOrTurnCharge"/>
        <result column="isCombineCharge" property="isCombineCharge"/>
        <result column="heating" property="heating"/>
        <result column="heatingVoltage" property="heatingVoltage"/>
        <result column="batteryCheck" property="batteryCheck"/>
        <result column="securityCheck" property="securityCheck"/>
        <result column="constantCharge" property="constantCharge"/>
        <result column="vinDiscover" property="vinDiscover"/>
        <result column="orderPrivacySetting" property="orderPrivacySetting"/>
        <result column="accountDisplayType" property="accountDisplayType"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="updateByUserid" property="updateByUserid"/>
    </resultMap>

    <resultMap extends="BaseResultMap" id="ResultMapWithBoxInfos"
               type="com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo">
        <result column="product_name" jdbcType="VARCHAR" property="productName"/>
        <result column="box_name" jdbcType="VARCHAR" property="boxName"/>
        <result column="current_type" jdbcType="INTEGER" property="currentType"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        bbs.id,
        bbs.boxCode,
        bbs.boxOutFactoryCode,
        bbs.chargeId,
        bbs.status,
        bbs.adminPassword,
        bbs.level2Password,
        bbs.dayVolume,
        bbs.nightVolume,
        bbs.url,
        bbs.isQueryChargeRecord,
        bbs.isTimedCharge,
        bbs.isNoCardCharge,
        bbs.isScanCharge,
        bbs.isVinCharge,
        bbs.isCardCharge,
        bbs.isQuotaEleCharge,
        bbs.isQuotaMoneyCharge,
        bbs.isQuotaTimeCharge,
        bbs.internationalAgreement,
        bbs.isAutoStopCharge,
        bbs.avgOrTurnCharge,
        bbs.isCombineCharge,
        bbs.heating,
        bbs.heatingVoltage,
        bbs.batteryCheck,
        bbs.securityCheck,
        bbs.constantCharge,
        bbs.vinDiscover,
        bbs.orderPrivacySetting,
        bbs.accountDisplayType,
        bbs.createTime,
        bbs.updateTime,
        bbs.updateByUserid,
        bbs.templateCode
    </sql>

    <!--获取设备简单列表-->
    <select id="selectBoxSettingList" resultMap="ResultMapWithBoxInfos">
        SELECT
            <include refid="Base_Column_List"/>,
            box.product_name,
            box.box_name,
            box.current_type,
            box.status as evseStatus
        FROM
          bs_box box
        LEFT JOIN t_bs_box_setting bbs ON bbs.boxOutFactoryCode = box.box_out_factory_code
        <where>
            <if test="evse != null and evse != ''">
                AND (box.box_out_factory_code = #{evse} or box.box_name LIKE CONCAT('%', #{evse}, '%'))
            </if>
            <if test="siteId != null">
                AND box.station_code = #{siteId}
            </if>
            <if test="chargeId != null and chargeId != ''">
                AND bbs.chargeId = #{chargeId}
            </if>
            <if test="templateCode != null">
                AND bbs.templateCode = #{templateCode}
            </if>
            <if test="boxCode != null and boxCode != ''">
                AND bbs.boxCode = #{boxCode}
            </if>
            <if test="status != null">
                AND bbs.status = #{status}
            </if>
            <if test="isQueryChargeRecord != null and isQueryChargeRecord != ''">
                AND bbs.isQueryChargeRecord = #{isQueryChargeRecord}
            </if>
            <if test="isTimedCharge != null and isTimedCharge != ''">
                AND bbs.isTimedCharge = #{isTimedCharge}
            </if>
            <if test="isNoCardCharge != null and isNoCardCharge != ''">
                AND bbs.isNoCardCharge = #{isNoCardCharge}
            </if>
            <if test="isScanCharge != null and isScanCharge != ''">
                AND bbs.isScanCharge = #{isScanCharge}
            </if>
            <if test="isVinCharge != null and isVinCharge != ''">
                AND bbs.isVinCharge = #{isVinCharge}
            </if>
            <if test="isCardCharge != null and isCardCharge != ''">
                AND bbs.isCardCharge = #{isCardCharge}
            </if>
            <if test="statusList != null and statusList.size()>0  ">
                and bbs.status IN
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="siteIds != null and siteIds.size() > 0">
                and box.station_code in
                <foreach collection="siteIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectBoxSettingListForUrgencyCard" resultMap="ResultMapWithBoxInfos">
        SELECT
            bbs.boxOutFactoryCode as boxOutFactoryCode,
            box.box_name as box_name,
            rwce.sendStatus as sendStatus,
            rwce.`passWord` as urgencyPassword
        FROM
        bs_box box
        LEFT JOIN t_bs_box_setting bbs ON bbs.boxCode = box.box_code
        LEFT JOIN t_r_white_card_evse rwce ON rwce.evseId = box.box_out_factory_code
        <where>
            rwce.enable = true
            <if test="cardChipNo != null and cardChipNo != ''">
                AND rwce.whiteCardNo = #{cardChipNo}
            </if>
            <if test="cardNo != null and cardNo != ''">
                AND rwce.card_no = #{cardNo}
            </if>
            <if test="evse != null and evse != ''">
                AND (box.box_out_factory_code = #{evse} or box.box_name LIKE CONCAT('%', #{evse}, '%'))
            </if>
            <if test="siteId != null">
                AND box.station_code = #{siteId}
            </if>
            <if test="chargeId != null and chargeId != ''">
                AND bbs.chargeId = #{chargeId}
            </if>
            <if test="boxCode != null and boxCode != ''">
                AND bbs.boxCode = #{boxCode}
            </if>
            <if test="status != null">
                AND bbs.status = #{status}
            </if>
            <if test="isQueryChargeRecord != null and isQueryChargeRecord != ''">
                AND bbs.isQueryChargeRecord = #{isQueryChargeRecord}
            </if>
            <if test="isTimedCharge != null and isTimedCharge != ''">
                AND bbs.isTimedCharge = #{isTimedCharge}
            </if>
            <if test="isNoCardCharge != null and isNoCardCharge != ''">
                AND bbs.isNoCardCharge = #{isNoCardCharge}
            </if>
            <if test="isScanCharge != null and isScanCharge != ''">
                AND bbs.isScanCharge = #{isScanCharge}
            </if>
            <if test="isVinCharge != null and isVinCharge != ''">
                AND bbs.isVinCharge = #{isVinCharge}
            </if>
            <if test="isCardCharge != null and isCardCharge != ''">
                AND bbs.isCardCharge = #{isCardCharge}
            </if>
            <if test="statusList != null and statusList.size()>0  ">
                and bbs.status IN
                <foreach item="item" index="index" collection="statusList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="siteIds != null and siteIds.size() > 0">
                and box.station_code in
                <foreach collection="siteIds" item="item" index="index" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


    <select id="selectBoxSettingById" resultMap="ResultMapWithBoxInfos">
        SELECT
        <include refid="Base_Column_List"/>,
        box.product_name,
        box.box_name,
        box.current_type
        FROM
        t_bs_box_setting bbs
        LEFT JOIN bs_box box ON bbs.boxCode = box.box_code
        WHERE id = #{id}
    </select>

    <select id="selectResultByCommIds" resultMap="ResultMapWithBoxInfos">
        SELECT
        <include refid="Base_Column_List"/>,
        box.product_name,
        box.box_name,
        box.current_type
        FROM
        bs_box box right join
        t_bs_box_setting bbs
        on box.box_code=bbs.boxCode
        left join t_site site on site.id = box.station_code
        left join t_r_commercial comm on comm.id = site.operate_id
        <where>
            1 = 1
            <if test="@com.cdz360.base.utils.StringUtils@isNotBlank( commIdChain )">
                and comm.idChain like CONCAT(#{commIdChain}, '%')
            </if>
            <if test="status!=null and !status.isEmpty()">
                AND bbs.status in
                <foreach collection="status" item="item" index="index" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    <select id="getPagedBoxSettingFailList" resultMap="ResultMapWithBoxInfos" resultType="com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo">
        SELECT
        <include refid="Base_Column_List"/>,
        box.product_name,
        box.box_name,
        box.current_type
        FROM
        bs_box box right join
        t_bs_box_setting bbs
        on box.box_code=bbs.boxCode
        <where>
            bbs.status=2 and
            box.isUseSiteDefaultSetting = 1
            <if test="commIds!=null and !commIds.isEmpty()">
                AND box.business_id in
                <foreach collection="commIds" item="item" index="index" separator=","
                         open="(" close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectCountByStationCodeAndWhiteCardStatus" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM
        t_bs_box_setting bbs
        INNER JOIN (
        SELECT
        *
        FROM
        bs_box
        WHERE
        station_code = #{stationCode}
        ) bb ON bbs.boxCode = bb.box_Code
        <where>
            <if test="status!=null">
                bbs.whiteCardsStatus = #{status}
            </if>
        </where>
    </select>

    <select id="getSettingProgress" resultType="com.cdz360.biz.device.business.entity.result.SettingProgressVo">
        select
            status as `status`,
            count(status) as `count`
        from t_bs_box_setting
        where templateCode=#{templateCode}
        group by status
    </select>

    <delete id="deleteSettingByBoxOutFactoryCode">
        delete from t_bs_box_setting where boxOutFactoryCode=#{boxOutFactoryCode}
    </delete>

    <insert id="insertFillTemplateCode" useGeneratedKeys="true" keyProperty="id"
            keyColumn="id" parameterType="com.cdz360.biz.device.business.entity.po.BsBoxSetting">
        insert into t_bs_box_setting
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="bsBoxSetting.boxCode != null">
                boxCode,
            </if>
            <if test="bsBoxSetting.boxOutFactoryCode != null">
                boxOutFactoryCode,
            </if>
            <if test="bsBoxSetting.chargeId != null">
                chargeId,
            </if>
            <if test="bsBoxSetting.status != null">
                status,
            </if>
            <if test="bsBoxSetting.adminPassword != null">
                adminPassword,
            </if>
            <if test="bsBoxSetting.level2Password != null">
                level2Password,
            </if>
            <if test="bsBoxSetting.dayVolume != null">
                dayVolume,
            </if>
            <if test="bsBoxSetting.nightVolume != null">
                nightVolume,
            </if>
            <if test="bsBoxSetting.url != null">
                url,
            </if>
            <if test="bsBoxSetting.isQueryChargeRecord != null">
                isQueryChargeRecord,
            </if>
            <if test="bsBoxSetting.isTimedCharge != null">
                isTimedCharge,
            </if>
            <if test="bsBoxSetting.isNoCardCharge != null">
                isNoCardCharge,
            </if>
            <if test="bsBoxSetting.isScanCharge != null">
                isScanCharge,
            </if>
            <if test="bsBoxSetting.isVinCharge != null">
                isVinCharge,
            </if>
            <if test="bsBoxSetting.isCardCharge != null">
                isCardCharge,
            </if>
            <if test="bsBoxSetting.isQuotaEleCharge != null">
                isQuotaEleCharge,
            </if>
            <if test="bsBoxSetting.isQuotaMoneyCharge != null">
                isQuotaMoneyCharge,
            </if>
            <if test="bsBoxSetting.isQuotaTimeCharge != null">
                isQuotaTimeCharge,
            </if>
            <if test="bsBoxSetting.internationalAgreement != null">
                internationalAgreement,
            </if>
            <if test="bsBoxSetting.isAutoStopCharge != null">
                isAutoStopCharge,
            </if>
            <if test="bsBoxSetting.avgOrTurnCharge != null">
                avgOrTurnCharge,
            </if>
            <if test="bsBoxSetting.isCombineCharge != null">
                isCombineCharge,
            </if>
            <if test="bsBoxSetting.heating != null">
                heating,
            </if>
            <if test="bsBoxSetting.heatingVoltage != null">
                heatingVoltage,
            </if>
            <if test="bsBoxSetting.batteryCheck != null">
                batteryCheck,
            </if>
            <if test="bsBoxSetting.securityCheck != null">
                securityCheck,
            </if>
            <if test="bsBoxSetting.constantCharge != null">
                constantCharge,
            </if>
            <if test="bsBoxSetting.vinDiscover != null">
                vinDiscover,
            </if>
            <if test="bsBoxSetting.orderPrivacySetting != null">
                orderPrivacySetting,
            </if>
            <if test="bsBoxSetting.accountDisplayType != null">
                accountDisplayType,
            </if>
            <if test="bsBoxSetting.createTime != null">
                createTime,
            </if>
            <if test="bsBoxSetting.updateTime != null">
                updateTime,
            </if>
            <if test="bsBoxSetting.updateByUserid != null">
                updateByUserid,
            </if>
            <if test="bsBoxSetting.whiteCardsStatus != null">
                whiteCardsStatus,
            </if>
            <if test="bsBoxSetting.whiteCardList != null">
                whiteCardList,
            </if>
            <if test="bsBoxSetting.adminCodeResult != null">
                adminCodeResult,
            </if>
            <if test="bsBoxSetting.triggerResult != null">
                triggerResult,
            </if>
            <if test="bsBoxSetting.chargeResult != null">
                chargeResult,
            </if>
            <if test="bsBoxSetting.qrResult != null">
                qrResult,
            </if>
            <if test="bsBoxSetting.chargeId != null">
                templateCode
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="bsBoxSetting.boxCode != null">
                #{bsBoxSetting.boxCode},
            </if>
            <if test="bsBoxSetting.boxOutFactoryCode != null">
                #{bsBoxSetting.boxOutFactoryCode},
            </if>
            <if test="bsBoxSetting.chargeId != null">
                #{bsBoxSetting.chargeId},
            </if>
            <if test="bsBoxSetting.status != null">
                #{bsBoxSetting.status},
            </if>
            <if test="bsBoxSetting.adminPassword != null">
                #{bsBoxSetting.adminPassword},
            </if>
            <if test="bsBoxSetting.level2Password != null">
                #{bsBoxSetting.level2Password},
            </if>
            <if test="bsBoxSetting.dayVolume != null">
                #{bsBoxSetting.dayVolume},
            </if>
            <if test="bsBoxSetting.nightVolume != null">
                #{bsBoxSetting.nightVolume},
            </if>
            <if test="bsBoxSetting.url != null">
                #{bsBoxSetting.url},
            </if>
            <if test="bsBoxSetting.isQueryChargeRecord != null">
                #{bsBoxSetting.isQueryChargeRecord},
            </if>
            <if test="bsBoxSetting.isTimedCharge != null">
                #{bsBoxSetting.isTimedCharge},
            </if>
            <if test="bsBoxSetting.isNoCardCharge != null">
                #{bsBoxSetting.isNoCardCharge},
            </if>
            <if test="bsBoxSetting.isScanCharge != null">
                #{bsBoxSetting.isScanCharge},
            </if>
            <if test="bsBoxSetting.isVinCharge != null">
                #{bsBoxSetting.isVinCharge},
            </if>
            <if test="bsBoxSetting.isCardCharge != null">
                #{bsBoxSetting.isCardCharge},
            </if>
            <if test="bsBoxSetting.isQuotaEleCharge != null">
                #{bsBoxSetting.isQuotaEleCharge},
            </if>
            <if test="bsBoxSetting.isQuotaMoneyCharge != null">
                #{bsBoxSetting.isQuotaMoneyCharge},
            </if>
            <if test="bsBoxSetting.isQuotaTimeCharge != null">
                #{bsBoxSetting.isQuotaTimeCharge},
            </if>
            <if test="bsBoxSetting.internationalAgreement != null">
                #{bsBoxSetting.internationalAgreement},
            </if>
            <if test="bsBoxSetting.isAutoStopCharge != null">
                #{bsBoxSetting.isAutoStopCharge},
            </if>
            <if test="bsBoxSetting.avgOrTurnCharge != null">
                #{bsBoxSetting.avgOrTurnCharge},
            </if>
            <if test="bsBoxSetting.isCombineCharge != null">
                #{bsBoxSetting.isCombineCharge},
            </if>
            <if test="bsBoxSetting.heating != null">
                #{bsBoxSetting.heating},
            </if>
            <if test="bsBoxSetting.heatingVoltage != null">
                #{bsBoxSetting.heatingVoltage},
            </if>
            <if test="bsBoxSetting.batteryCheck != null">
                #{bsBoxSetting.batteryCheck},
            </if>
            <if test="bsBoxSetting.securityCheck != null">
                #{bsBoxSetting.securityCheck},
            </if>
            <if test="bsBoxSetting.constantCharge != null">
                #{bsBoxSetting.constantCharge},
            </if>
            <if test="bsBoxSetting.vinDiscover != null">
                #{bsBoxSetting.vinDiscover},
            </if>
            <if test="bsBoxSetting.orderPrivacySetting != null">
                #{bsBoxSetting.orderPrivacySetting},
            </if>
            <if test="bsBoxSetting.accountDisplayType != null">
                #{bsBoxSetting.accountDisplayType},
            </if>
            <if test="bsBoxSetting.createTime != null">
                #{bsBoxSetting.createTime},
            </if>
            <if test="bsBoxSetting.updateTime != null">
                #{bsBoxSetting.updateTime},
            </if>
            <if test="bsBoxSetting.updateByUserid != null">
                #{bsBoxSetting.updateByUserid},
            </if>
            <if test="bsBoxSetting.whiteCardsStatus != null">
                #{bsBoxSetting.whiteCardsStatus},
            </if>
            <if test="bsBoxSetting.whiteCardList != null">
                #{bsBoxSetting.whiteCardList},
            </if>
            <if test="bsBoxSetting.adminCodeResult != null">
                #{bsBoxSetting.adminCodeResult},
            </if>
            <if test="bsBoxSetting.triggerResult != null">
                #{bsBoxSetting.triggerResult},
            </if>
            <if test="bsBoxSetting.chargeResult != null">
                #{bsBoxSetting.chargeResult},
            </if>
            <if test="bsBoxSetting.qrResult != null">
                #{bsBoxSetting.qrResult},
            </if>
            <if test="bsBoxSetting.chargeId != null">
                (select code from t_template where id=#{bsBoxSetting.chargeId})
            </if>
        </trim>
    </insert>
    <update id="updateByIdFillTemplateCode" parameterType="com.cdz360.biz.device.business.entity.po.BsBoxSetting">
        update t_bs_box_setting
        <set>
            <if test="bsBoxSetting.boxCode != null">
                boxCode=#{bsBoxSetting.boxCode},
            </if>
            <if test="bsBoxSetting.boxOutFactoryCode != null">
                boxOutFactoryCode=#{bsBoxSetting.boxOutFactoryCode},
            </if>
            <if test="bsBoxSetting.chargeId != null">
                chargeId=#{bsBoxSetting.chargeId},
            </if>
            <if test="bsBoxSetting.status != null">
                status=#{bsBoxSetting.status},
            </if>
            <if test="bsBoxSetting.adminPassword != null">
                adminPassword=#{bsBoxSetting.adminPassword},
            </if>
            <if test="bsBoxSetting.level2Password != null">
                level2Password=#{bsBoxSetting.level2Password},
            </if>
            <if test="bsBoxSetting.dayVolume != null">
                dayVolume=#{bsBoxSetting.dayVolume},
            </if>
            <if test="bsBoxSetting.nightVolume != null">
                nightVolume=#{bsBoxSetting.nightVolume},
            </if>
            <if test="bsBoxSetting.url != null">
                url=#{bsBoxSetting.url},
            </if>
            <if test="bsBoxSetting.isQueryChargeRecord != null">
                isQueryChargeRecord=#{bsBoxSetting.isQueryChargeRecord},
            </if>
            <if test="bsBoxSetting.isTimedCharge != null">
                isTimedCharge=#{bsBoxSetting.isTimedCharge},
            </if>
            <if test="bsBoxSetting.isNoCardCharge != null">
                isNoCardCharge=#{bsBoxSetting.isNoCardCharge},
            </if>
            <if test="bsBoxSetting.isScanCharge != null">
                isScanCharge=#{bsBoxSetting.isScanCharge},
            </if>
            <if test="bsBoxSetting.isVinCharge != null">
                isVinCharge=#{bsBoxSetting.isVinCharge},
            </if>
            <if test="bsBoxSetting.isCardCharge != null">
                isCardCharge=#{bsBoxSetting.isCardCharge},
            </if>
            <if test="bsBoxSetting.isQuotaEleCharge != null">
                isQuotaEleCharge=#{bsBoxSetting.isQuotaEleCharge},
            </if>
            <if test="bsBoxSetting.isQuotaMoneyCharge != null">
                isQuotaMoneyCharge=#{bsBoxSetting.isQuotaMoneyCharge},
            </if>
            <if test="bsBoxSetting.isQuotaTimeCharge != null">
                isQuotaTimeCharge=#{bsBoxSetting.isQuotaTimeCharge},
            </if>
            <if test="bsBoxSetting.internationalAgreement != null">
                internationalAgreement=#{bsBoxSetting.internationalAgreement},
            </if>
            <if test="bsBoxSetting.isAutoStopCharge != null">
                isAutoStopCharge=#{bsBoxSetting.isAutoStopCharge},
            </if>
            <if test="bsBoxSetting.avgOrTurnCharge != null">
                avgOrTurnCharge=#{bsBoxSetting.avgOrTurnCharge},
            </if>
            <if test="bsBoxSetting.isCombineCharge != null">
                isCombineCharge=#{bsBoxSetting.isCombineCharge},
            </if>
            <if test="bsBoxSetting.heating != null">
                heating=#{bsBoxSetting.heating},
            </if>
            <if test="bsBoxSetting.heatingVoltage != null">
                heatingVoltage=#{bsBoxSetting.heatingVoltage},
            </if>
            <if test="bsBoxSetting.batteryCheck != null">
                batteryCheck=#{bsBoxSetting.batteryCheck},
            </if>
            <if test="bsBoxSetting.securityCheck != null">
                securityCheck=#{bsBoxSetting.securityCheck},
            </if>
            <if test="bsBoxSetting.constantCharge != null">
                constantCharge=#{bsBoxSetting.constantCharge},
            </if>
            <if test="bsBoxSetting.vinDiscover != null">
                vinDiscover=#{bsBoxSetting.vinDiscover},
            </if>
            <if test="bsBoxSetting.orderPrivacySetting != null">
                orderPrivacySetting=#{bsBoxSetting.orderPrivacySetting},
            </if>
            <if test="bsBoxSetting.accountDisplayType != null">
                accountDisplayType=#{bsBoxSetting.accountDisplayType},
            </if>
            <if test="bsBoxSetting.createTime != null">
                createTime=#{bsBoxSetting.createTime},
            </if>
            <if test="bsBoxSetting.updateTime != null">
                updateTime=#{bsBoxSetting.updateTime},
            </if>
            <if test="bsBoxSetting.updateByUserid != null">
                updateByUserid=#{bsBoxSetting.updateByUserid},
            </if>
            <if test="bsBoxSetting.whiteCardsStatus != null">
                whiteCardsStatus=#{bsBoxSetting.whiteCardsStatus},
            </if>
            <if test="bsBoxSetting.whiteCardList != null">
                whiteCardList=#{bsBoxSetting.whiteCardList},
            </if>
            <if test="bsBoxSetting.adminCodeResult != null">
                adminCodeResult=#{bsBoxSetting.adminCodeResult},
            </if>
            <if test="bsBoxSetting.triggerResult != null">
                triggerResult=#{bsBoxSetting.triggerResult},
            </if>
            <if test="bsBoxSetting.chargeResult != null">
                chargeResult=#{bsBoxSetting.chargeResult},
            </if>
            <if test="bsBoxSetting.qrResult != null">
                qrResult=#{bsBoxSetting.qrResult},
            </if>
            <if test="bsBoxSetting.chargeId != null">
                templateCode=(select code from t_template where id=#{bsBoxSetting.chargeId})
            </if>
        </set>
        where id=#{bsBoxSetting.id}
    </update>
</mapper>
