package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjAreaAnalysisRwMapper {

    TjAreaAnalysisPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjAreaAnalysis(TjAreaAnalysisPo tjAreaAnalysisPo);

    int updateTjAreaAnalysis(TjAreaAnalysisPo tjAreaAnalysisPo);

    int disableTjAreaAnalysis(@Param("id") Long analysisId);
}

