package com.cdz360.biz.tj.rest;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisPointParam;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisPointWithSiteVo;
import com.cdz360.biz.tj.service.kc.TjAnalysisPointService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@Tag(name = "投建竞争对手相关操作接口", description = "投建竞争对手相关操作接口")
@Slf4j
@RestController
@RequestMapping("/tj/area/analysis/point")
public class TjAnalysisPointRest {

    @Autowired
    private TjAnalysisPointService tjAnalysisPointService;

    @Operation(summary = "获取投建分析划分点")
    @PostMapping("/findTjAnalysisPoint")
    public Mono<ListResponse<TjAreaAnalysisPointVo>> findTjAnalysisPoint(
        @RequestBody ListTjAreaAnalysisPointParam param) {
        log.info("获取投建分析划分点: {}", param);
        return tjAnalysisPointService.findTjAnalysisPoint(param);
    }

    @Operation(summary = "获取投建分析划分点(带场站列表信息)")
    @PostMapping("/findTjAnalysisPointWithSite")
    public Mono<ListResponse<TjAreaAnalysisPointWithSiteVo>> findTjAnalysisPointWithSite(
        @RequestBody ListTjAreaAnalysisPointParam param) {
        log.info("获取投建分析划分点(带场站列表信息): {}", param);
        return tjAnalysisPointService.findTjAnalysisPointWithSite(param);
    }

    @Operation(summary = "更新指定投建分析下的划分点状态")
    @PostMapping("/updateAnalysisPointStatus")
    public Mono<ObjectResponse<Long>> updateTjAnalysisPointStatus(
        @RequestBody UpdateAnalysisPointStatusParam param) {
        log.info("更新指定投建分析下的划分点状态: {}", param);
        return tjAnalysisPointService.updateTjAnalysisPointStatus(param)
            .map(RestUtils::buildObjectResponse);
    }
}
