package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.biz.device.business.entity.po.SiteVo;
import com.cdz360.biz.device.business.entity.po.WhiteCardDto;
import com.cdz360.biz.device.business.entity.po.WhiteCardRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Interfacename CarUserFeignClient
 * @Description TODO
 * @Date 2019/7/12
 * @Created by wangzheng
 */
@FeignClient(name = DcConstants.KEY_FEIGN_DC_BIZ_USER,fallbackFactory = HystrixUserClientFactory.class)
public interface UserFeignClient {

    /**
     * 查询站点下的紧急充电卡
     * 用于多场站下多卡弃用时，条件必须为(cardChipNoList必传，siteList必传,isAbandon为true)
     * 用于单场站下发查询DTO时，条件为(cardChipNoList不传，site必传,isAbandon为false)
     * 不支持重置密码
     * 不支持单卡弃用
     * @param whiteCardRequest
     * @return
     */
    @PostMapping("/api/card/queryWhiteCardDtoBySiteList")
    ListResponse<WhiteCardDto> queryWhiteCardDtoBySiteList(@RequestBody WhiteCardRequest whiteCardRequest);

    /**
     * 根据卡片id（用,分割），更新卡片状态
     * 0x00为下发成功，其他为失败
     * @param cardIds
     * @param status
     */
    @PostMapping("/api/card/updateUrgencyCardStatus")
    ObjectResponse<Integer> updateUrgencyCardStatus(@RequestParam("evseNo") String evseNo,
                                                    @RequestParam("cardIds") String cardIds,
                                                    @RequestParam("status") int status);

    /**
     * 更新卡片状态
     *
     * @param cardNos
     * @param status
     */
    @PostMapping("/api/card/updBatchCardStatusByNos")
    ObjectResponse<Integer> updBatchCardStatusByNos(@RequestParam("cardNos") List<String> cardNos,
                                                    @RequestParam("status") int status);

//    /**
//     * 根据条件批量查询场站下的卡数量
//     * @param siteIdList
//     * @return
//     */
//    @PostMapping(value = "/api/card/findUrgencyCardNumCount")
//    ListResponse<SiteVo> findUrgencyCardNumCount(@RequestBody List<String> siteIdList);
}