package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveyChargeAreaRoMapper {

    TjSurveyChargeAreaPo getById(@Param("id") Long id);

    List<TjSurveyChargeAreaPo> findTjSurveyChargeAreaBySurveyNo(@Param("surveyNo") String surveyNo);

}

