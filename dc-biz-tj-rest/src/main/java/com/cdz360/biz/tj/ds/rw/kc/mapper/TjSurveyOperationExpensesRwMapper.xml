<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjSurveyOperationExpensesRwMapper">

  <resultMap id="RESULT_TJ_SURVEY_OPERATION_EXPENSES_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="serviceFeeSharing" jdbcType="DECIMAL" property="serviceFeeSharing"/>
    <result column="serviceFeeExcludeAttract" jdbcType="BOOLEAN" property="serviceFeeExcludeAttract"/>
    <result column="fixedRent" jdbcType="BOOLEAN" property="fixedRent"/>
    <result column="rentFee" jdbcType="DECIMAL" property="rentFee"/>
    <result column="everyFewYears" jdbcType="DECIMAL" property="everyFewYears"/>
    <result column="increase" jdbcType="DECIMAL" property="increase"/>
    <result column="otherFee" jdbcType="DECIMAL" property="otherFee"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_OPERATION_EXPENSES_PO">
    select * from t_tj_survey_operation_expenses where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <insert id="insertTjSurveyOperationExpenses"
    parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo">
    insert into t_tj_survey_operation_expenses (`surveyNo`,
    <if test="null != serviceFeeSharing">
      `serviceFeeSharing`,
    </if>
    <if test="null != serviceFeeExcludeAttract">
      `serviceFeeExcludeAttract`,
    </if>
    <if test="null != fixedRent">
      `fixedRent`,
    </if>
    <if test="null != rentFee">
      `rentFee`,
    </if>
    <if test="null != everyFewYears">
      `everyFewYears`,
    </if>
    <if test="null != increase">
      `increase`,
    </if>
    <if test="null != otherFee">
      `otherFee`,
    </if>
    `enable`,
    `createTime`,
    `updateTime`)
    values (#{surveyNo},
    <if test="null != serviceFeeSharing">
      #{serviceFeeSharing},
    </if>
    <if test="null != serviceFeeExcludeAttract">
      #{serviceFeeExcludeAttract},
    </if>
    <if test="null != fixedRent">
      #{fixedRent},
    </if>
    <if test="null != rentFee">
      #{rentFee},
    </if>
    <if test="null != everyFewYears">
      #{everyFewYears},
    </if>
    <if test="null != increase">
      #{increase},
    </if>
    <if test="null != otherFee">
      #{otherFee},
    </if>
    true,
    now(),
    now())
  </insert>

  <update id="updateTjSurveyOperationExpenses" parameterType="com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo">
    update t_tj_survey_operation_expenses set
    <if test="serviceFeeSharing != null">
      serviceFeeSharing = #{serviceFeeSharing},
    </if>
    <if test="serviceFeeExcludeAttract != null">
      serviceFeeExcludeAttract = #{serviceFeeExcludeAttract},
    </if>
    <if test="fixedRent != null">
      fixedRent = #{fixedRent},
    </if>
    <if test="rentFee != null">
      rentFee = #{rentFee},
    </if>
    <if test="everyFewYears != null">
      everyFewYears = #{everyFewYears},
    </if>
    <if test="increase != null">
      increase = #{increase},
    </if>
    <if test="otherFee != null">
      otherFee = #{otherFee},
    </if>
    <if test="enable != null">
      enable = #{enable},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

  <update id="disableTjSurveyOperationExpensesBySurveyNo">
    update t_tj_survey_operation_expenses set
    enable = false,
    updateTime = now()
    where surveyNo = #{surveyNo}
  </update>


</mapper>

