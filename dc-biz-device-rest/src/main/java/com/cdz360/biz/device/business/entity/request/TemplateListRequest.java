package com.cdz360.biz.device.business.entity.request;

import com.cdz360.biz.device.common.param.BaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 计费模板列表请求
 *
 * <AUTHOR>
 * @date Create on 2018/12/24 11:56
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "计费模板列表请求参数")
public class TemplateListRequest extends BaseRequest {

    @ApiModelProperty(value = "操作人商户ID")
    private Long opCommId;

    /**
     * 代理商ID
     */
    @ApiModelProperty(value = "代理商ID")
    private Long commercialId;
    /**
     * 计费模板编号
     */
    @ApiModelProperty(value = "计费模板编号")
    private String code;
    /**
     * 代理商ID列表
     */
    @Deprecated
    @ApiModelProperty(value = "代理商ID列表")
    private List<Long> commercialIdList;
    @ApiModelProperty(value = "商户ID链")
    private String commIdChain;
    /**
     * 当前登录账号的commId
     */
    @ApiModelProperty(value = "当前登录账号的commId")
    private Long currentCommId;

    @ApiModelProperty(value = "计费模板使能状态值")
    private Boolean enable;
}
