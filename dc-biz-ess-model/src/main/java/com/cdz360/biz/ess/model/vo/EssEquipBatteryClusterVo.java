package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.iot.type.EquipStatus;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.biz.ess.model.dto.BatteryCluster;
import com.cdz360.biz.model.ess.type.EquipAlertStatus;
import com.cdz360.biz.model.ess.vo.EquipNameplateInfo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "电池簇信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class EssEquipBatteryClusterVo extends BatteryCluster {

    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteId;

    @Schema(description = "场站名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String siteName;

    @Schema(description = "微网控制器编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwno;

    @Schema(description = "微网控制器名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String gwName;

    @Schema(description = "储能ESS唯一编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "储能ESS名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essName;

    @Schema(description = "电池堆设备名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stackEquipName;

    @Schema(description = "电池堆设备ID ESS下唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long stackEquipId;

    @Schema(description = "储能ESS设备名称")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String name;

    @Schema(title = "状态", description = "设备状态.0未知;1在线;2离线;3启动中;4告警;5故障;99,下线")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EquipStatus status;

    @Schema(description = "告警状态: 0,未知;1,正常;2,异常")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EquipAlertStatus alertStatus;

    @Schema(description = "设备ID(ess内唯一)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long equipId;

    @Schema(description = "设备类型")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private EssEquipType equipType;

    @Schema(description = "铭牌信息")
    @JsonInclude(Include.NON_NULL)
    private EquipNameplateInfo nameplateInfo;
}
