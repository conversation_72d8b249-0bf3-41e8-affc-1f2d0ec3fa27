#系统繁忙,请稍后重试!
\u7cfb\u7edf\u7e41\u5fd9\u002c\u8bf7\u7a0d\u540e\u91cd\u8bd5\u0021=System busy. Please try again later
#系统繁忙,请稍后重试!
\u7cfb\u7edf\u7e41\u5fd9\u002c\u8bf7\u7a0d\u540e\u91cd\u8bd5=System busy. Please try again later
#系统繁忙，请稍后重试
\u7cfb\u7edf\u7e41\u5fd9\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5=System busy. Please try again later
#系统错误
\u7cfb\u7edf\u9519\u8bef=System error
#用户ID无效
\u7528\u6237\u0049\u0044\u65e0\u6548=Invalid UID
#参数错误
\u53c2\u6570\u9519\u8bef=Parameter error
#参数错误!
\u53c2\u6570\u9519\u8bef\u0021=Parameter error!
#参数出错
\u53c2\u6570\u51fa\u9519=Parameter error
#参数异常
\u53c2\u6570\u5f02\u5e38=Parameter exception
#请求参数不能为空
\u8bf7\u6c42\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a=The query parameter cannot be empty
#后台失败
\u540e\u53f0\u5931\u8d25=Background failure
#操作失败
\u64cd\u4f5c\u5931\u8d25=Operation failure
#数据为空
\u6570\u636e\u4e3a\u7a7a=No data available
#废弃的逻辑
\u5e9f\u5f03\u7684\u903b\u8f91=Obsolete logic
#你无权进行访问
\u4f60\u65e0\u6743\u8fdb\u884c\u8bbf\u95ee=You don't have access
#用户无权访问!
\u7528\u6237\u65e0\u6743\u8bbf\u95ee\u0021=User does not have access!
#用户无权限
\u7528\u6237\u65e0\u6743\u9650=User has no permission
#inner http interface  call exception
\u0069\u006e\u006e\u0065\u0072\u0020\u0068\u0074\u0074\u0070\u0020\u0069\u006e\u0074\u0065\u0072\u0066\u0061\u0063\u0065\u0020\u0020\u0063\u0061\u006c\u006c\u0020\u0065\u0078\u0063\u0065\u0070\u0074\u0069\u006f\u006e=Inner http interface  call exception
#添加失败
\u6dfb\u52a0\u5931\u8d25=Add failure
#获取失败
\u83b7\u53d6\u5931\u8d25=Acquisition failure
#创建成功
\u521b\u5efa\u6210\u529f=Created successfully
#删除成功
\u5220\u9664\u6210\u529f=Successfully deleted
#修改成功
\u4fee\u6539\u6210\u529f=Change successful
#--------------用户相关start
\u0075\u0073\u0065\u0072\u006e\u0061\u006d\u0065\u003a\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0062\u006c\u0061\u006e\u006b=username:must not be blank
\u0070\u0061\u0073\u0073\u0077\u006f\u0072\u0064\u003a\u006d\u0075\u0073\u0074\u0020\u006e\u006f\u0074\u0020\u0062\u0065\u0020\u0062\u006c\u0061\u006e\u006b=password:must not be blank
#帐号不存在
\u5e10\u53f7\u4e0d\u5b58\u5728=Account does not exist
#帐号被锁定,请稍后再重试
\u5e10\u53f7\u88ab\u9501\u5b9a\u002c\u8bf7\u7a0d\u540e\u518d\u91cd\u8bd5=The account is locked. Please try again later
#参数错误,uid不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0075\u0069\u0064\u4e0d\u80fd\u4e3a\u7a7a=Parameter is incorrect. uid cannot be empty
#帐号/密码错误
\u5e10\u53f7\u002f\u5bc6\u7801\u9519\u8bef=The account/password is incorrect
#请重新登录
\u8bf7\u91cd\u65b0\u767b\u5f55=Please login again
#当前操作用户信息不存在，请重新登录
\u5f53\u524d\u64cd\u4f5c\u7528\u6237\u4fe1\u606f\u4e0d\u5b58\u5728\uff0c\u8bf7\u91cd\u65b0\u767b\u5f55=The current user does not exist. Please log in again
#用户登录信息已失效，请刷新后重试
\u7528\u6237\u767b\u5f55\u4fe1\u606f\u5df2\u5931\u6548\uff0c\u8bf7\u5237\u65b0\u540e\u91cd\u8bd5=The user login information is invalid. Please refresh it and try again
#手机号不能为空
\u624b\u673a\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The phone number cannot be empty
#token不能为空，请确认。
\u0074\u006f\u006b\u0065\u006e\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u8bf7\u786e\u8ba4\u3002=token cannot be empty, please confirm.
#当前手机号对应账户不可用
\u5f53\u524d\u624b\u673a\u53f7\u5bf9\u5e94\u8d26\u6237\u4e0d\u53ef\u7528=The account corresponding to the current mobile number is unavailable
#请传入参数
\u8bf7\u4f20\u5165\u53c2\u6570=Please pass in parameters
#请传入uid参数
\u8bf7\u4f20\u5165\u0075\u0069\u0064\u53c2\u6570=Please pass in the uid parameter
#无法获取用户信息，请确认是否登陆
\u65e0\u6cd5\u83b7\u53d6\u7528\u6237\u4fe1\u606f\uff0c\u8bf7\u786e\u8ba4\u662f\u5426\u767b\u9646=Unable to obtain user information, please confirm login
#用户名不能为空
\u7528\u6237\u540d\u4e0d\u80fd\u4e3a\u7a7a=The user name cannot be empty
#该账号已经添加
\u8be5\u8d26\u53f7\u5df2\u7ecf\u6dfb\u52a0=The account has been added
#新增客户引流记录失败。customerAttractPo: {0}
user.add.attractRecordFailed=Failed to add customer traffic diversion records. Procedure customerAttractPo: {0}
#新增客户引流记录失败。customerId: {0}, error: {1}
user.add.attractRecordFailed.msg=Failed to add customer traffic diversion records. Procedure customerId: {0}, error: {1}
#判断失败，用户Id不能为空!
\u5224\u65ad\u5931\u8d25\uff0c\u7528\u6237\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a\u0021=Judgment failed, user Id cannot be empty!
#获取用户信息失败
\u83b7\u53d6\u7528\u6237\u4fe1\u606f\u5931\u8d25=Description Failed to obtain user information
#获取用户信息为空
\u83b7\u53d6\u7528\u6237\u4fe1\u606f\u4e3a\u7a7a=Get user information is empty
#查询用户下商户会员失败
\u67e5\u8be2\u7528\u6237\u4e0b\u5546\u6237\u4f1a\u5458\u5931\u8d25=Failed to query merchant membership under user
#客户名称不能为空
\u5ba2\u6237\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The customer name cannot be empty
#客户手机号不能为空
\u5ba2\u6237\u624b\u673a\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The customer's mobile phone number cannot be empty
#当前操作用户商户信息不明确
\u5f53\u524d\u64cd\u4f5c\u7528\u6237\u5546\u6237\u4fe1\u606f\u4e0d\u660e\u786e=The current user merchant information is not clear
#当前操作用户Id不存在
\u5f53\u524d\u64cd\u4f5c\u7528\u6237\u0049\u0064\u4e0d\u5b58\u5728=The current operation user Id does not exist
#用户ID不能为空
\u7528\u6237\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The user ID cannot be empty
#uid对应用户不存在
\u0075\u0069\u0064\u5bf9\u5e94\u7528\u6237\u4e0d\u5b58\u5728=The uid user does not exist
#当前客户不是当前登录商户及子商户下的客户
\u5f53\u524d\u5ba2\u6237\u4e0d\u662f\u5f53\u524d\u767b\u5f55\u5546\u6237\u53ca\u5b50\u5546\u6237\u4e0b\u7684\u5ba2\u6237=The current customer is not a customer under the currently registered merchant or sub-merchant
#集团默认个人客户不支持拉黑
\u96c6\u56e2\u9ed8\u8ba4\u4e2a\u4eba\u5ba2\u6237\u4e0d\u652f\u6301\u62c9\u9ed1=Group default individual customers do not support blocking
#加入黑名单失败!
\u52a0\u5165\u9ed1\u540d\u5355\u5931\u8d25\u0021=Failed to join the blacklist!
#客户ID错误
\u5ba2\u6237\u0049\u0044\u9519\u8bef=Customer ID error
#userId不可为空
\u0075\u0073\u0065\u0072\u0049\u0064\u4e0d\u53ef\u4e3a\u7a7a=userId cannot be empty
#更新用户资料失败
\u66f4\u65b0\u7528\u6237\u8d44\u6599\u5931\u8d25=Failed to update user information
#请提供有效的用户Id
\u8bf7\u63d0\u4f9b\u6709\u6548\u7684\u7528\u6237\u0049\u0064=Please provide a valid user Id
#请提供有效的集团商户ID
\u8bf7\u63d0\u4f9b\u6709\u6548\u7684\u96c6\u56e2\u5546\u6237\u0049\u0044=Please provide a valid group merchant ID
#请提供有效的客户端类型
\u8bf7\u63d0\u4f9b\u6709\u6548\u7684\u5ba2\u6237\u7aef\u7c7b\u578b=Please provide a valid client type
#用户所属顶级商户不存在
\u7528\u6237\u6240\u5c5e\u9876\u7ea7\u5546\u6237\u4e0d\u5b58\u5728=The top-level merchant to which the user belongs does not exist
#顶级商户密钥没有配置
\u9876\u7ea7\u5546\u6237\u5bc6\u94a5\u6ca1\u6709\u914d\u7f6e=The top-level merchant key is not configured
#申请用户ID无效
\u7533\u8bf7\u7528\u6237\u0049\u0044\u65e0\u6548=The user ID request is invalid
#请传入分页参数
\u8bf7\u4f20\u5165\u5206\u9875\u53c2\u6570=Please pass in paging parameters
#请配置所属商户
\u8bf7\u914d\u7f6e\u6240\u5c5e\u5546\u6237=Please configure your merchant
#账号已被使用
\u8d26\u53f7\u5df2\u88ab\u4f7f\u7528=The account has been used
#邮箱重复
\u90ae\u7bb1\u91cd\u590d=Mailbox duplication
#电话号码重复
\u7535\u8bdd\u53f7\u7801\u91cd\u590d=Phone number duplication
#name conflict
\u006e\u0061\u006d\u0065\u0020\u0063\u006f\u006e\u0066\u006c\u0069\u0063\u0074=name conflict
#not found
\u006e\u006f\u0074\u0020\u0066\u006f\u0075\u006e\u0064=not found
#请输入账号
\u8bf7\u8f93\u5165\u8d26\u53f7=Please enter your account number
#账号最大长度40个字符
\u8d26\u53f7\u6700\u5927\u957f\u5ea6\u0034\u0030\u4e2a\u5b57\u7b26=The maximum length of an account is 40 characters
#账号只支持字母、数字、汉字
\u8d26\u53f7\u53ea\u652f\u6301\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u6c49\u5b57=The account supports only letters, numbers, and Chinese characters
#请输入正确的邮箱
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u90ae\u7bb1=Please enter the correct email address
#未传入状态
\u672a\u4f20\u5165\u72b6\u6001=Unpassed state
#--------------用户相关end
#查询类型不能为空
\u67e5\u8be2\u7c7b\u578b\u4e0d\u80fd\u4e3a\u7a7a=The query type cannot be empty
#参数错误-商户ID错误
\u53c2\u6570\u9519\u8bef\u002d\u5546\u6237\u0049\u0044\u9519\u8bef=Parameter error - Merchant ID error
#参数错误. 商户不存在
\u53c2\u6570\u9519\u8bef\u002e\u0020\u5546\u6237\u4e0d\u5b58\u5728=Parameter error. Merchant does not exist.
#参数错误。商户不存在
\u53c2\u6570\u9519\u8bef\u3002\u5546\u6237\u4e0d\u5b58\u5728=Parameter error. Merchant does not exist.
#参数错误,手机号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u624b\u673a\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error: mobile number cannot be empty.
#联系人电话或联系人邮箱重复
\u8054\u7cfb\u4eba\u7535\u8bdd\u6216\u8054\u7cfb\u4eba\u90ae\u7bb1\u91cd\u590d=Contact phone number or email address already exists.
#商户简称重复
\u5546\u6237\u7b80\u79f0\u91cd\u590d=Merchant short name already exists.
#该手机号已被使用
\u8be5\u624b\u673a\u53f7\u5df2\u88ab\u4f7f\u7528=This phone number is already in use.
#商户ID不能为空
\u5546\u6237\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The merchant ID cannot be empty
#无法获取顶级商户Id，请重试。
\u65e0\u6cd5\u83b7\u53d6\u9876\u7ea7\u5546\u6237\u0049\u0064\uff0c\u8bf7\u91cd\u8bd5\u3002=Unable to get top-level merchant Id, please try again.
#获取商户信息失败
\u83b7\u53d6\u5546\u6237\u4fe1\u606f\u5931\u8d25=Failed to obtain merchant information
#获取商户id失败，请重试。
\u83b7\u53d6\u5546\u6237\u0069\u0064\u5931\u8d25\uff0c\u8bf7\u91cd\u8bd5\u3002=Failed to get the merchant id, please try again.
#商户会员在停用状态下无法设置为默认账户
\u5546\u6237\u4f1a\u5458\u5728\u505c\u7528\u72b6\u6001\u4e0b\u65e0\u6cd5\u8bbe\u7f6e\u4e3a\u9ed8\u8ba4\u8d26\u6237=Merchant members cannot be set as default accounts when disabled
#该接口需要提供商户Id
\u8be5\u63a5\u53e3\u9700\u8981\u63d0\u4f9b\u5546\u6237\u0049\u0064=The interface needs to provide a merchant Id
#请提供操作类型(增加/减少)
\u8bf7\u63d0\u4f9b\u64cd\u4f5c\u7c7b\u578b\u0028\u589e\u52a0\u002f\u51cf\u5c11\u0029=Please provide the type of operation (increase/decrease)
#操作失败：该笔充值已关联了开票申请
\u64cd\u4f5c\u5931\u8d25\uff1a\u8be5\u7b14\u5145\u503c\u5df2\u5173\u8054\u4e86\u5f00\u7968\u7533\u8bf7=Operation failed: The recharge has been associated with the billing application
#账户余额增加实际金额失败
\u8d26\u6237\u4f59\u989d\u589e\u52a0\u5b9e\u9645\u91d1\u989d\u5931\u8d25=The account balance failed to increase the actual amount
#账户余额增加赠送金额失败
\u8d26\u6237\u4f59\u989d\u589e\u52a0\u8d60\u9001\u91d1\u989d\u5931\u8d25=Account balance increase gift amount failed
#请指定需要减少的充值记录订单号
\u8bf7\u6307\u5b9a\u9700\u8981\u51cf\u5c11\u7684\u5145\u503c\u8bb0\u5f55\u8ba2\u5355\u53f7=Please specify the recharge record order number to be reduced
#请提供充值订单号
\u8bf7\u63d0\u4f9b\u5145\u503c\u8ba2\u5355\u53f7=Please provide the top-up order number
#查无改充值记录信息
\u67e5\u65e0\u6539\u5145\u503c\u8bb0\u5f55\u4fe1\u606f=Check no recharging record information
#支付失败的充值，不允许查看
\u652f\u4ed8\u5931\u8d25\u7684\u5145\u503c\uff0c\u4e0d\u5141\u8bb8\u67e5\u770b=Payment failure to recharge, not allowed to view
#查无该充值记录: orderId={0}
recharge.order.notFound=Check whether the recharge record is available: orderId={0}
#订单导出分组不能为空
\u8ba2\u5355\u5bfc\u51fa\u5206\u7ec4\u4e0d\u80fd\u4e3a\u7a7a=The order export group cannot be empty
#该充值记录没有关联的资金块信息: orderId={0}
recharge.order.noFundingBlock=The top-up record has no associated block information: orderId={0}
#充值对应的资金块明细seq不存在: orderId={0}
recharge.order.seqNotFound=The details of the corresponding fund block seq do not exist: orderId={0}
#充值记录已经开票，不能进行减少操作
\u5145\u503c\u8bb0\u5f55\u5df2\u7ecf\u5f00\u7968\uff0c\u4e0d\u80fd\u8fdb\u884c\u51cf\u5c11\u64cd\u4f5c=The top-up record has been invoiced and cannot be reduced
#减少操作引用的充值记录无效
\u51cf\u5c11\u64cd\u4f5c\u5f15\u7528\u7684\u5145\u503c\u8bb0\u5f55\u65e0\u6548=The recharge record referenced by the reduce operation is invalid
#该资金块已消耗完，不支持减少操作
\u8be5\u8d44\u91d1\u5757\u5df2\u6d88\u8017\u5b8c\uff0c\u4e0d\u652f\u6301\u51cf\u5c11\u64cd\u4f5c=The fund block has been used up and cannot be reduced
#减少金额已超过可减少的金额，请调整金额再进行减少操作
\u51cf\u5c11\u91d1\u989d\u5df2\u8d85\u8fc7\u53ef\u51cf\u5c11\u7684\u91d1\u989d\uff0c\u8bf7\u8c03\u6574\u91d1\u989d\u518d\u8fdb\u884c\u51cf\u5c11\u64cd\u4f5c=The amount reduced exceeds the amount that can be reduced. Please adjust the amount before reducing
#账户余额减少金额失败
\u8d26\u6237\u4f59\u989d\u51cf\u5c11\u91d1\u989d\u5931\u8d25=Account balance reduction failed
#商户信息不存在
\u5546\u6237\u4fe1\u606f\u4e0d\u5b58\u5728=Merchant information does not exist
#退款金额发生变更，请刷新页面重试！
\u9000\u6b3e\u91d1\u989d\u53d1\u751f\u53d8\u66f4\uff0c\u8bf7\u5237\u65b0\u9875\u9762\u91cd\u8bd5\uff01=The refund amount has changed, please refresh the page and try again!
#问题描述100字以内
\u95ee\u9898\u63cf\u8ff0\u0031\u0030\u0030\u5b57\u4ee5\u5185=Problem description 100 words or less
#您的充电订单中存在异常订单，暂时无法申请退款，请先联系客服处理，处理完成后再申请。
\u60a8\u7684\u5145\u7535\u8ba2\u5355\u4e2d\u5b58\u5728\u5f02\u5e38\u8ba2\u5355\uff0c\u6682\u65f6\u65e0\u6cd5\u7533\u8bf7\u9000\u6b3e\uff0c\u8bf7\u5148\u8054\u7cfb\u5ba2\u670d\u5904\u7406\uff0c\u5904\u7406\u5b8c\u6210\u540e\u518d\u7533\u8bf7\u3002=There are abnormal orders in your charging order, and you cannot apply for a refund temporarily. Please contact customer service first, and then apply after processing.
#您存在未支付的停充超时订单，暂时无法申请退款，请先完成支付再申请。
\u60a8\u5b58\u5728\u672a\u652f\u4ed8\u7684\u505c\u5145\u8d85\u65f6\u8ba2\u5355\uff0c\u6682\u65f6\u65e0\u6cd5\u7533\u8bf7\u9000\u6b3e\uff0c\u8bf7\u5148\u5b8c\u6210\u652f\u4ed8\u518d\u7533\u8bf7\u3002=You have an unpaid stop-charge timeout order and cannot apply for a refund for the time being. Please complete the payment before applying.
#暂无可退款充值记录
\u6682\u65e0\u53ef\u9000\u6b3e\u5145\u503c\u8bb0\u5f55=There is no refundable top-up record
#序号不能为空
\u5e8f\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The serial number cannot be empty
#退款没有指定使用退款方式
\u9000\u6b3e\u6ca1\u6709\u6307\u5b9a\u4f7f\u7528\u9000\u6b3e\u65b9\u5f0f=Refund No refund method is specified
#资金块解冻失败
\u8d44\u91d1\u5757\u89e3\u51bb\u5931\u8d25=Failed to unfreeze the fund block
#获取失败!失败原因:{0}
failed.msg=Get failed! Failure cause :{0}
#获取卡信息失败，卡号不能为空
\u83b7\u53d6\u5361\u4fe1\u606f\u5931\u8d25\uff0c\u5361\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Failed to obtain the card information. The card number cannot be empty
#当前已存在{0}个下载任务，请稍后再试
download.task.maxAmount=There are {0} download tasks. please try again later
#新增下载任务失败
\u65b0\u589e\u4e0b\u8f7d\u4efb\u52a1\u5931\u8d25=Failed to add a download task. Procedure
#商户不存在
\u5546\u6237\u4e0d\u5b58\u5728=Merchant does not exist
#请分开查询 {0} 前的数据
search.time.separate=Please query data before {0} separately
#暂不支持查询 {0} 前的数据
search.time.notSupport=Data before {0} is not supported
#--------------卡相关start
#该卡已被激活
\u8be5\u5361\u5df2\u88ab\u6fc0\u6d3b=The card has been activated
#卡号不存在
\u5361\u53f7\u4e0d\u5b58\u5728=The card number does not exist
#企业母账号不能绑定卡
\u4f01\u4e1a\u6bcd\u8d26\u53f7\u4e0d\u80fd\u7ed1\u5b9a\u5361=An enterprise parent account cannot be bound to a card
#卡信息变更失败
\u5361\u4fe1\u606f\u53d8\u66f4\u5931\u8d25=Failed to change the card information
#物理卡号列表or逻辑卡号列表不能为空
\u7269\u7406\u5361\u53f7\u5217\u8868\u006f\u0072\u903b\u8f91\u5361\u53f7\u5217\u8868\u4e0d\u80fd\u4e3a\u7a7a=The physical card number list or logical card number list cannot be empty
#参数错误，卡号不能为空
\u53c2\u6570\u9519\u8bef\uff0c\u5361\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, card number cannot be empty
#卡片存在订单尚未结算，请处理后再进行操作
\u5361\u7247\u5b58\u5728\u8ba2\u5355\u5c1a\u672a\u7ed3\u7b97\uff0c\u8bf7\u5904\u7406\u540e\u518d\u8fdb\u884c\u64cd\u4f5c=The order in the card has not been settled, please process it before proceeding
#备注30 个字以内
\u5907\u6ce8\u0033\u0030\u0020\u4e2a\u5b57\u4ee5\u5185=Note no more than 30 words
#查询该卡片详情错误
\u67e5\u8be2\u8be5\u5361\u7247\u8be6\u60c5\u9519\u8bef=Error querying the card details
#挂失操作仅可用于'已激活'的卡
\u6302\u5931\u64cd\u4f5c\u4ec5\u53ef\u7528\u4e8e\u0027\u5df2\u6fc0\u6d3b\u0027\u7684\u5361=Loss reporting is only available for 'activated' cards
#存在订单尚未处理，请处理后再进行操作
\u5b58\u5728\u8ba2\u5355\u5c1a\u672a\u5904\u7406\uff0c\u8bf7\u5904\u7406\u540e\u518d\u8fdb\u884c\u64cd\u4f5c=An order has not yet been processed. Please process it before performing operations
#激活操作仅用于'挂失'状态的卡
\u6fc0\u6d3b\u64cd\u4f5c\u4ec5\u7528\u4e8e\u0027\u6302\u5931\u0027\u72b6\u6001\u7684\u5361=The activation is only available for cards in 'report lost' status
#本地鉴权开启时，本地鉴权可用场站列表不能为空
\u672c\u5730\u9274\u6743\u5f00\u542f\u65f6\uff0c\u672c\u5730\u9274\u6743\u53ef\u7528\u573a\u7ad9\u5217\u8868\u4e0d\u80fd\u4e3a\u7a7a=When local authentication is enabled, the list of locally authenticated stations must not be empty
#poList为空，持久化失败
\u0070\u006f\u004c\u0069\u0073\u0074\u4e3a\u7a7a\uff0c\u6301\u4e45\u5316\u5931\u8d25=The persistent operation has failed because the poList is empty
#桩当前离线，无法获取实时本地CARD。
\u6869\u5f53\u524d\u79bb\u7ebf\uff0c\u65e0\u6cd5\u83b7\u53d6\u5b9e\u65f6\u672c\u5730\u0043\u0041\u0052\u0044\u3002=The pile is currently offline and unable to obtain real-time local card
#卡面编号不能为空
\u5361\u9762\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Card face number cannot be empty
#鉴权卡号不能为空
\u9274\u6743\u5361\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Authentication card number cannot be empty
#卡面编号已存在
\u5361\u9762\u7f16\u53f7\u5df2\u5b58\u5728=Card face number already exists
#鉴权卡号已存在
\u9274\u6743\u5361\u53f7\u5df2\u5b58\u5728=Authentication card number already exists
#用户所属商户信息异常
\u7528\u6237\u6240\u5c5e\u5546\u6237\u4fe1\u606f\u5f02\u5e38=User merchant information is abnormal
#卡面编号最多30位，仅支持大小写英文字母和数字
\u5361\u9762\u7f16\u53f7\u6700\u591a30\u4f4d\uff0c\u4ec5\u652f\u6301\u5927\u5c0f\u5199\u82f1\u6587\u5b57\u6bcd\u548c\u6570\u5b57=Card face number up to 30 characters, uppercase and lowercase English letters and digits only
#鉴权卡号最多16位，仅支持大小写英文字母和数字
\u9274\u6743\u5361\u53f7\u6700\u591a16\u4f4d\uff0c\u4ec5\u652f\u6301\u5927\u5c0f\u5199\u82f1\u6587\u5b57\u6bcd\u548c\u6570\u5b57=Authentication card number up to 16 characters, uppercase and lowercase English letters and digits only
#卡名称最多20位，仅支持字母、数字、汉字
\u5361\u540d\u79f0\u6700\u591a20\u4f4d\uff0c\u4ec5\u652f\u6301\u5b57\u6bcd\u3001\u6570\u5b57\u3001\u6c49\u5b57=Card name up to 20 characters, letters, numbers, and Chinese characters only
#--------------卡相关end
#--------------站点相关start
#站点名称不能为空
\u7ad9\u70b9\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The site name cannot be empty
#站点精度不能为空
\u7ad9\u70b9\u7ecf\u5ea6\u4e0d\u80fd\u4e3a\u7a7a=Site accuracy cannot be empty
#站点纬度不能为空
\u7ad9\u70b9\u7eac\u5ea6\u4e0d\u80fd\u4e3a\u7a7a=The site latitude cannot be empty
#站点地址不能为空
\u7ad9\u70b9\u5730\u5740\u4e0d\u80fd\u4e3a\u7a7a=The station address cannot be empty
#省份编码不能为空
\u7701\u4efd\u7f16\u7801\u4e0d\u80fd\u4e3a\u7a7a=The province code cannot be empty
#城市编码不能为空
\u57ce\u5e02\u7f16\u7801\u4e0d\u80fd\u4e3a\u7a7a=The city code cannot be empty
#区域编码不能为空
\u533a\u57df\u7f16\u7801\u4e0d\u80fd\u4e3a\u7a7a=The region code cannot be empty
#所属运营商ID不能为空
\u6240\u5c5e\u8fd0\u8425\u5546\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The carrier ID cannot be empty
#所属运营商名称不能为空
\u6240\u5c5e\u8fd0\u8425\u5546\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The carrier name cannot be empty
#运营商企业代码不能为空
\u8fd0\u8425\u5546\u4f01\u4e1a\u4ee3\u7801\u4e0d\u80fd\u4e3a\u7a7a=The carrier enterprise code cannot be empty
#发票提供方不能为空
\u53d1\u7968\u63d0\u4f9b\u65b9\u4e0d\u80fd\u4e3a\u7a7a=The invoice provider cannot be empty
#运营属性不能为空
\u8fd0\u8425\u5c5e\u6027\u4e0d\u80fd\u4e3a\u7a7a=The operation property cannot be empty
#运营方名称不能为空
\u8fd0\u8425\u65b9\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The operator name cannot be empty
#停车费不能为空
\u505c\u8f66\u8d39\u4e0d\u80fd\u4e3a\u7a7a=Parking fees cannot be empty
#场站支付渠道不能为空
\u573a\u7ad9\u652f\u4ed8\u6e20\u9053\u4e0d\u80fd\u4e3a\u7a7a=The terminal payment channel cannot be empty
#参数错误,集团商户ID不能为空
\u53c2\u6570\u9519\u8bef\u002c\u96c6\u56e2\u5546\u6237\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, the group merchant ID cannot be empty
#场站编号长度不能超过30个字符
\u573a\u7ad9\u7f16\u53f7\u957f\u5ea6\u4e0d\u80fd\u8d85\u8fc7\u0033\u0030\u4e2a\u5b57\u7b26=The length of the terminal number cannot exceed 30 characters
#不可输入/,-,_以外的其他特殊字符
\u4e0d\u53ef\u8f93\u5165\u002f\u002c\u002d\u002c\u005f\u4ee5\u5916\u7684\u5176\u4ed6\u7279\u6b8a\u5b57\u7b26=Special characters other than /,-, and _ cannot be entered
#站点编号已存在
\u7ad9\u70b9\u7f16\u53f7\u5df2\u5b58\u5728=The site number already exists
#站点名称重复
\u7ad9\u70b9\u540d\u79f0\u91cd\u590d=Site name duplication
#参数错误,siteId不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0073\u0069\u0074\u0065\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a=The parameter is incorrect. The siteId cannot be empty
#参数错误,场站编号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u573a\u7ad9\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, the station number cannot be empty
#未找到站点信息
\u672a\u627e\u5230\u7ad9\u70b9\u4fe1\u606f=No site information found
#您不能修改这个场站的信息
\u60a8\u4e0d\u80fd\u4fee\u6539\u8fd9\u4e2a\u573a\u7ad9\u7684\u4fe1\u606f=You cannot modify the information of this terminal
#站点编号格式不正确
\u7ad9\u70b9\u7f16\u53f7\u683c\u5f0f\u4e0d\u6b63\u786e=The site number format is incorrect
#请输入有效经度
\u8bf7\u8f93\u5165\u6709\u6548\u7ecf\u5ea6=Please enter a valid longitude
#请输入有效纬度
\u8bf7\u8f93\u5165\u6709\u6548\u7eac\u5ea6=Please enter a valid latitude
#未查询到站点信息
\u672a\u67e5\u8be2\u5230\u7ad9\u70b9\u4fe1\u606f=No site information is found
#参数错误, 订单号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0020\u8ba2\u5355\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, order number cannot be empty
#参数错误,场站不存在
\u53c2\u6570\u9519\u8bef\u002c\u573a\u7ad9\u4e0d\u5b58\u5728=Parameter error. The station does not exist
#无效的查询条件
\u65e0\u6548\u7684\u67e5\u8be2\u6761\u4ef6=Invalid search criteria. Procedure
#站点id不能为空
\u7ad9\u70b9\u0069\u0064\u4e0d\u80fd\u4e3a\u7a7a=The site id cannot be empty
#无法找到该场站. 场站ID ={0}
site.notFound=The station could not be located. Station ID = {0}
#场站默认配置修改请求参数为空
\u573a\u7ad9\u9ed8\u8ba4\u914d\u7f6e\u4fee\u6539\u8bf7\u6c42\u53c2\u6570\u4e3a\u7a7a=By default, the modification request parameter is null
#场站默认配置修改主键不能为空
\u573a\u7ad9\u9ed8\u8ba4\u914d\u7f6e\u4fee\u6539\u4e3b\u952e\u4e0d\u80fd\u4e3a\u7a7a=The primary key cannot be empty
#二维码地址配置不规范
\u4e8c\u7ef4\u7801\u5730\u5740\u914d\u7f6e\u4e0d\u89c4\u8303=The two-dimensional code address is not configured properly
#场站 id={0}默认配置记录不存在
site.conf.notFound=Station id={0} The default configuration record does not exist
#参数不能为空
\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a=The parameter cannot be null
#支持无卡充电时，其他类型与模式不可为‘是’
\u652f\u6301\u65e0\u5361\u5145\u7535\u65f6\uff0c\u5176\u4ed6\u7c7b\u578b\u4e0e\u6a21\u5f0f\u4e0d\u53ef\u4e3a\u2018\u662f\u2019=When no card charging is supported, other types and modes cannot be "Yes"
#不支持无卡充电时，扫码充电、VIN码充电、刷卡充电 中至少有一个为‘是’
\u4e0d\u652f\u6301\u65e0\u5361\u5145\u7535\u65f6\uff0c\u626b\u7801\u5145\u7535\u3001\u0056\u0049\u004e\u7801\u5145\u7535\u3001\u5237\u5361\u5145\u7535\u0020\u4e2d\u81f3\u5c11\u6709\u4e00\u4e2a\u4e3a\u2018\u662f\u2019=When no card charging is supported, at least one of the scanning code charging, VIN code charging, and card charging is "Yes"
#场站默认配置数据修改失败，主键：{0}
site.conf.updateFailed=Terminal default configuration data modification failed, primary key: {0}
#场站信息不存在
\u573a\u7ad9\u4fe1\u606f\u4e0d\u5b58\u5728=Terminal information does not exist
#请传入场站id
\u8bf7\u4f20\u5165\u573a\u7ad9\u0069\u0064=Please pass in the station id
#场站计费模板不存在
\u573a\u7ad9\u8ba1\u8d39\u6a21\u677f\u4e0d\u5b58\u5728=The terminal billing template does not exist
#找不到场站信息: {0}
site.info.notFound=Station information not found: {0}
#--------------站点相关end
#组信息不能为空
\u7ec4\u4fe1\u606f\u4e0d\u80fd\u4e3a\u7a7a=The group information cannot be empty
#请传入token
\u8bf7\u4f20\u5165\u0074\u006f\u006b\u0065\u006e=Please pass token
#--------------桩相关start
#桩配置分页查询参数不能为空
\u6869\u914d\u7f6e\u5206\u9875\u67e5\u8be2\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a=The pile configuration paging query parameter cannot be empty
#桩号列表不能为空
\u6869\u53f7\u5217\u8868\u4e0d\u80fd\u4e3a\u7a7a=The list of stakes cannot be empty
#请指定获取的桩编号
\u8bf7\u6307\u5b9a\u83b7\u53d6\u7684\u6869\u7f16\u53f7=Please specify the number of the acquired pile
#该桩还未接入平台, 请确认桩编号是否正确
\u8be5\u6869\u8fd8\u672a\u63a5\u5165\u5e73\u53f0\u002c\u0020\u8bf7\u786e\u8ba4\u6869\u7f16\u53f7\u662f\u5426\u6b63\u786e=The pile has not been connected to the platform, please confirm whether the pile number is correct
#该桩已经离线
\u8be5\u6869\u5df2\u7ecf\u79bb\u7ebf=The stake is offline
#请输入桩列表
\u8bf7\u8f93\u5165\u6869\u5217\u8868=Please enter a list of piles
#超出单次最大桩数
\u8d85\u51fa\u5355\u6b21\u6700\u5927\u6869\u6570=The maximum number of piles exceeded
#存在重复的桩编号: {0}
pile.duplicate=Duplicate pile numbers: {0}
#找不到桩: {0}
pile.notFound=No stake found: {0}
#桩号不能为空
\u6869\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The stake number cannot be empty
#参数错误, evseNo不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0020\u0065\u0076\u0073\u0065\u004e\u006f\u4e0d\u80fd\u4e3a\u7a7a=The parameter is incorrect. evseNo cannot be null
#桩配置获取失败
\u6869\u914d\u7f6e\u83b7\u53d6\u5931\u8d25=Failed to obtain the pile configuration
#充电桩不存在
\u5145\u7535\u6869\u4e0d\u5b58\u5728=Charging pile does not exist
#新增充电桩失败，站点不能为空
\u65b0\u589e\u5145\u7535\u6869\u5931\u8d25\uff0c\u7ad9\u70b9\u4e0d\u80fd\u4e3a\u7a7a=Failed to add charging pile, the site cannot be empty
#新增充电桩失败，桩号不能为空
\u65b0\u589e\u5145\u7535\u6869\u5931\u8d25\uff0c\u6869\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Failed to add a charging pile. The pile number cannot be empty
#新增充电桩失败，桩名称不能为空
\u65b0\u589e\u5145\u7535\u6869\u5931\u8d25\uff0c\u6869\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=Failed to add a charging pile. The pile name cannot be empty
#新增充电桩失败，桩的额定功率不能空
\u65b0\u589e\u5145\u7535\u6869\u5931\u8d25\uff0c\u6869\u7684\u989d\u5b9a\u529f\u7387\u4e0d\u80fd\u7a7a=The new charging pile fails, and the rated power of the pile cannot be empty
#新增充电桩失败，桩的额定功率最大输入设定为99999kw
\u65b0\u589e\u5145\u7535\u6869\u5931\u8d25\uff0c\u6869\u7684\u989d\u5b9a\u529f\u7387\u6700\u5927\u8f93\u5165\u8bbe\u5b9a\u4e3a\u0039\u0039\u0039\u0039\u0039\u006b\u0077=The charging pile failed to be added, and the maximum rated power input of the pile was set to 99999kw
#桩编号不能为空
\u6869\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The stake number cannot be empty
#场站ID不能为空
\u573a\u7ad9\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The site ID cannot be empty
#设备型号不能为空
\u8bbe\u5907\u578b\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The device model cannot be empty
#枪头标识不能重复
\u67aa\u5934\u6807\u8bc6\u4e0d\u80fd\u91cd\u590d=Connector mark must not be repeated
#sim不存在
\u0073\u0069\u006d\u4e0d\u5b58\u5728=The sim does not exist
#SIM卡不存在
\u0053\u0049\u004d\u5361\u4e0d\u5b58\u5728=The SIM card does not exist
#SIM卡已绑定，桩：{0}
sim.isBounded=SIM card has been bound, pile: {0}
#SIM卡已绑定，桩：{0}，场站：{1}
sim.site.isBounded=SIM card bound, pile: {0}, station: {1}
#该桩已绑定在别的场站,请不要使用重复的桩编号
\u8be5\u6869\u5df2\u7ed1\u5b9a\u5728\u522b\u7684\u573a\u7ad9\u002c\u8bf7\u4e0d\u8981\u4f7f\u7528\u91cd\u590d\u7684\u6869\u7f16\u53f7=The pile has been bound to another station, please do not use a duplicate pile number
#无法找到该设备型号. 设备型号 ={0}
device.model.notFound=The device model could not be found. Equipment model = {0}
#必填参数不能为空
\u5fc5\u586b\u53c2\u6570\u4e0d\u80fd\u4e3a\u7a7a=Mandatory parameters cannot be empty
#器件名称不能为空
\u5668\u4ef6\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The device name cannot be empty
#槽位数量不能为空
\u69fd\u4f4d\u6570\u91cf\u4e0d\u80fd\u4e3a\u7a7a=The number of slots cannot be empty
#moduleId不能为空
\u006d\u006f\u0064\u0075\u006c\u0065\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a=The moduleId cannot be empty
#idx不能为空
\u0069\u0064\u0078\u4e0d\u80fd\u4e3a\u7a7a=idx cannot be empty
#桩编号和枪编号不能同时为空
\u6869\u7f16\u53f7\u548c\u67aa\u7f16\u53f7\u4e0d\u80fd\u540c\u65f6\u4e3a\u7a7a=The pile number and connector number cannot be empty at the same time
#桩编号或场站Id没有提供
\u6869\u7f16\u53f7\u6216\u573a\u7ad9\u0049\u0064\u6ca1\u6709\u63d0\u4f9b=Pile number or site Id not provided
#枪头序号错误
\u67aa\u5934\u5e8f\u53f7\u9519\u8bef=Wrong head serial number
#参数错误,桩编号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u6869\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, pile number cannot be empty
#请输入合理的桩功率
\u8bf7\u8f93\u5165\u5408\u7406\u7684\u6869\u529f\u7387=Please input reasonable pile power
#您输入的桩名称太长
\u60a8\u8f93\u5165\u7684\u6869\u540d\u79f0\u592a\u957f=The pile name you entered is too long
#桩名称不能为空
\u6869\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a=The pile name cannot be empty
#请输入正确的桩编号
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u6869\u7f16\u53f7=Please enter the correct pile number
#场站Id无效, 没有默认配置信息
\u573a\u7ad9\u0049\u0064\u65e0\u6548\u002c\u0020\u6ca1\u6709\u9ed8\u8ba4\u914d\u7f6e\u4fe1\u606f=The site Id is invalid. There is no default configuration information
#参数错误, 桩编号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0020\u6869\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Parameter error, pile number cannot be empty
#根据siteId={0}查询场站默认配置,无记录
site.conf.noRecord=The default configuration of the terminal is queried according to siteId={0}. No record is recorded
#根据siteId={0}查询场站默认配置结果不唯一
site.conf.repeat=The result of querying the default configuration of the terminal based on siteId={0} is not unique
#参数错误,桩号不能为空
\u53c2\u6570\u9519\u8bef\u002c\u6869\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The parameter is incorrect. The stake number cannot be empty
#计费模板编号不正确，请重新下发计费模板
\u8ba1\u8d39\u6a21\u677f\u7f16\u53f7\u4e0d\u6b63\u786e\uff0c\u8bf7\u91cd\u65b0\u4e0b\u53d1\u8ba1\u8d39\u6a21\u677f=The accounting template number is incorrect. Resend the accounting template
#计费模板ID不能为空
\u8ba1\u8d39\u6a21\u677f\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The accounting template ID cannot be empty
#未获取到计费模板信息
\u672a\u83b7\u53d6\u5230\u8ba1\u8d39\u6a21\u677f\u4fe1\u606f=Description No accounting template information was obtained
#参数错误，计费模板Id不能为空
\u53c2\u6570\u9519\u8bef\uff0c\u8ba1\u8d39\u6a21\u677f\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a=The parameter is incorrect. The accounting template Id cannot be empty
#该计费模板已被禁用，请选择其他计费模板
\u8be5\u8ba1\u8d39\u6a21\u677f\u5df2\u88ab\u7981\u7528\uff0c\u8bf7\u9009\u62e9\u5176\u4ed6\u8ba1\u8d39\u6a21\u677f=This accounting template has been disabled. Please select another accounting template
#无法获取对应的计费模板
\u65e0\u6cd5\u83b7\u53d6\u5bf9\u5e94\u7684\u8ba1\u8d39\u6a21\u677f=The accounting template cannot be obtained
#桩编号不能为空，请提供桩编号
\u6869\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a\uff0c\u8bf7\u63d0\u4f9b\u6869\u7f16\u53f7=The pile number cannot be empty. Please provide the pile number
#该桩没有配置相关信息，请给桩下发配置信息
\u8be5\u6869\u6ca1\u6709\u914d\u7f6e\u76f8\u5173\u4fe1\u606f\uff0c\u8bf7\u7ed9\u6869\u4e0b\u53d1\u914d\u7f6e\u4fe1\u606f=The pile does not have configuration information. Please deliver configuration information to the pile
#桩列表参数应仅有一个非空元素
\u6869\u5217\u8868\u53c2\u6570\u5e94\u4ec5\u6709\u4e00\u4e2a\u975e\u7a7a\u5143\u7d20=The pile list parameter should have only one non-empty element
#桩信息不存在
\u6869\u4fe1\u606f\u4e0d\u5b58\u5728=The message does not exist
#计费模板不存在
\u8ba1\u8d39\u6a21\u677f\u4e0d\u5b58\u5728=The accounting template does not exist
#桩当前无配置成功的计费.
\u6869\u5f53\u524d\u65e0\u914d\u7f6e\u6210\u529f\u7684\u8ba1\u8d39\u002e=No accounting is successfully configured for the pile
#桩当前无配置成功的计费
\u6869\u5f53\u524d\u65e0\u914d\u7f6e\u6210\u529f\u7684\u8ba1\u8d39=No accounting is successfully configured for the pile
#请传入正确的升级包编码
\u8bf7\u4f20\u5165\u6b63\u786e\u7684\u5347\u7ea7\u5305\u7f16\u7801=Please pass in the correct upgrade package code
#找不到任务: {0}
download.task.notFound=No task found: {0}
#请传入待升级的桩列表。
\u8bf7\u4f20\u5165\u5f85\u5347\u7ea7\u7684\u6869\u5217\u8868\u3002=Please pass in the list of piles to upgrade
#请传入升级包编号。
\u8bf7\u4f20\u5165\u5347\u7ea7\u5305\u7f16\u53f7\u3002=Please pass in the upgrade package number
#请传入siteId
\u8bf7\u4f20\u5165\u0073\u0069\u0074\u0065\u0049\u0064=Please pass in the siteId
#请传入操作者id
\u8bf7\u4f20\u5165\u64cd\u4f5c\u8005\u0069\u0064=Please pass in the operator id
#请传入操作者name
\u8bf7\u4f20\u5165\u64cd\u4f5c\u8005\u006e\u0061\u006d\u0065=Please pass in the operator name
#请传入需要升级的桩列表
\u8bf7\u4f20\u5165\u9700\u8981\u5347\u7ea7\u7684\u6869\u5217\u8868=Please pass in the list of piles that need to be upgraded
#没有找到包详情信息
\u6ca1\u6709\u627e\u5230\u5305\u8be6\u60c5\u4fe1\u606f=No package details found
#没有找到模块详情信息
\u6ca1\u6709\u627e\u5230\u6a21\u5757\u8be6\u60c5\u4fe1\u606f=No module details found
#场站下找不到任何桩
\u573a\u7ad9\u4e0b\u627e\u4e0d\u5230\u4efb\u4f55\u6869=Couldn't find any piles under the site
#升级失败，场站{0}下，找不到桩[{1}]
site.pile.notFound=Upgrade failed, stie {0}, no pile can be found [{1}]
#找不到待符合条件的待升级桩
\u627e\u4e0d\u5230\u5f85\u7b26\u5408\u6761\u4ef6\u7684\u5f85\u5347\u7ea7\u6869=No pile to be upgraded can be found
#设备当前升级中
\u8bbe\u5907\u5f53\u524d\u5347\u7ea7\u4e2d=The device is currently being upgraded
#设备状态未知
\u8bbe\u5907\u72b6\u6001\u672a\u77e5=Unknown device status
#未知原因导致的失败，请联系平台客服
\u672a\u77e5\u539f\u56e0\u5bfc\u81f4\u7684\u5931\u8d25\uff0c\u8bf7\u8054\u7cfb\u5e73\u53f0\u5ba2\u670d=Failure due to unknown reasons, please contact platform customer service
#所选定的桩当前不符合升级条件，请先确认桩的状态是否空闲，且桩没有在升级，然后再试
\u6240\u9009\u5b9a\u7684\u6869\u5f53\u524d\u4e0d\u7b26\u5408\u5347\u7ea7\u6761\u4ef6\uff0c\u8bf7\u5148\u786e\u8ba4\u6869\u7684\u72b6\u6001\u662f\u5426\u7a7a\u95f2\uff0c\u4e14\u6869\u6ca1\u6709\u5728\u5347\u7ea7\uff0c\u7136\u540e\u518d\u8bd5=The selected pile does not meet the upgrade conditions. Check whether the status of the pile is idle and the pile is not being upgraded, and then try again
#所选定的桩当前不符合升级条件，请先确认桩的状态是否是空闲，然后再尝试
\u6240\u9009\u5b9a\u7684\u6869\u5f53\u524d\u4e0d\u7b26\u5408\u5347\u7ea7\u6761\u4ef6\uff0c\u8bf7\u5148\u786e\u8ba4\u6869\u7684\u72b6\u6001\u662f\u5426\u662f\u7a7a\u95f2\uff0c\u7136\u540e\u518d\u5c1d\u8bd5=The selected pile does not meet the upgrade conditions. Please check whether the status of the pile is idle and then try again
#所选定的桩当前不符合升级条件，请先确认桩是否正在升级，升级中不可重复操作
\u6240\u9009\u5b9a\u7684\u6869\u5f53\u524d\u4e0d\u7b26\u5408\u5347\u7ea7\u6761\u4ef6\uff0c\u8bf7\u5148\u786e\u8ba4\u6869\u662f\u5426\u6b63\u5728\u5347\u7ea7\uff0c\u5347\u7ea7\u4e2d\u4e0d\u53ef\u91cd\u590d\u64cd\u4f5c=The selected pile does not meet the upgrade conditions. Check whether the pile is being upgraded. The operation cannot be repeated during upgrading
#所选定的升级包有问题，请先确认升级包是否正常，然后再尝试
\u6240\u9009\u5b9a\u7684\u5347\u7ea7\u5305\u6709\u95ee\u9898\uff0c\u8bf7\u5148\u786e\u8ba4\u5347\u7ea7\u5305\u662f\u5426\u6b63\u5e38\uff0c\u7136\u540e\u518d\u5c1d\u8bd5=The selected update package has issues. Please first confirm whether the update package is normal, and then try again
#缺少入参信息
\u7f3a\u5c11\u5165\u53c2\u4fe1\u606f=Missing input parameter information
#请传入正确的任务id
\u8bf7\u4f20\u5165\u6b63\u786e\u7684\u4efb\u52a1\u0069\u0064=Please provide a valid task ID
#请传入taskId
\u8bf7\u4f20\u5165\u0074\u0061\u0073\u006b\u0049\u0064=Please pass in the taskId
#找不到任何升级详情
\u627e\u4e0d\u5230\u4efb\u4f55\u5347\u7ea7\u8be6\u60c5=No upgrade details found
#没有找到升级包下载链接信息
\u6ca1\u6709\u627e\u5230\u5347\u7ea7\u5305\u4e0b\u8f7d\u94fe\u63a5\u4fe1\u606f=No update package download link information found
#没有找到升级包详细链接等信息
\u6ca1\u6709\u627e\u5230\u5347\u7ea7\u5305\u8be6\u7ec6\u94fe\u63a5\u7b49\u4fe1\u606f=No detailed information about the update package link was found
#请传入升级包详细链接等信息。
\u8bf7\u4f20\u5165\u5347\u7ea7\u5305\u8be6\u7ec6\u94fe\u63a5\u7b49\u4fe1\u606f\u3002=Please provide the detailed upgrade package link and related information
#请传入需要重新升级的桩
\u8bf7\u4f20\u5165\u9700\u8981\u91cd\u65b0\u5347\u7ea7\u7684\u6869=Please provide the pile that needs to be upgraded again
#找不到升级记录id: {0}
site.pile.upgrade.taskIdNotFound=No upgrade record ID found:{0}
#找不到升级记录链接信息: {0}
site.pile.upgrade.taskUrlNotFound=No upgrade record link information found:{0}
#发现重复的桩编号，请检查。桩号: {0}
site.pile.upgrade.duplicatePile=Duplicate pile number detected, please check. pile number:{0}
#升级记录中找不到这些桩: {0}
site.pile.upgrade.pileRecordsNotFound=No records found for these piles in the upgrade log:{0}
#升级状态不正确，可能发生变化，请刷新页面后再试。桩号：{0}
site.pile.upgrade.statusRefreshError=The upgrade status is incorrect and may have changed, please refresh the page and try again. Pile number:{0}
#--------------桩相关end
#--------------VIN相关start
#subCommId不能为空
\u0073\u0075\u0062\u0043\u006f\u006d\u006d\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a=subCommId cannot be empty
#userId不能为空
\u0075\u0073\u0065\u0072\u0049\u0064\u4e0d\u80fd\u4e3a\u7a7a=userId cannot be empty
#vin不能为空
\u0076\u0069\u006e\u4e0d\u80fd\u4e3a\u7a7a=The vin cannot be empty
#企业母账号不能绑定VIN
\u4f01\u4e1a\u6bcd\u8d26\u53f7\u4e0d\u80fd\u7ed1\u5b9a\u0056\u0049\u004e=The enterprise parent account cannot be bound to a VIN
#VIN码第一位不能为0
\u0056\u0049\u004e\u7801\u7b2c\u4e00\u4f4d\u4e0d\u80fd\u4e3a\u0030=The first digit of the VIN code cannot be 0
#新增失败，该VIN码已关联在其他客户。
\u65b0\u589e\u5931\u8d25\uff0c\u8be5\u0056\u0049\u004e\u7801\u5df2\u5173\u8054\u5728\u5176\u4ed6\u5ba2\u6237\u3002=Failed to add the VIN. The VIN code has been associated with another customer.
#创建VIN码失败
\u521b\u5efa\u0056\u0049\u004e\u7801\u5931\u8d25=Failed to create a VIN. Procedure
#请输入正确的车辆自编号
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u8f66\u8f86\u81ea\u7f16\u53f7=Please enter the correct vehicle ID
#请输入正确的车队名称
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u8f66\u961f\u540d\u79f0=Please enter the correct vehicle name
#请输入正确的线路
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u7ebf\u8def=Please enter the correct line
#请输入正确的品牌
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u54c1\u724c=Please enter the correct brand
#请输入正确的型号
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u578b\u53f7=Please enter the correct model number
#请输入正确的车长
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u8f66\u957f=Please enter the correct length
#请输入正确的年份
\u8bf7\u8f93\u5165\u6b63\u786e\u7684\u5e74\u4efd=Please enter the correct year
#请检查车队名称是否有误
\u8bf7\u68c0\u67e5\u8f66\u961f\u540d\u79f0\u662f\u5426\u6709\u8bef=Please check if the fleet name is incorrect
#车队名称最多50位
\u8f66\u961f\u540d\u79f0\u6700\u591a50\u4f4d=Fleet name must be no more than 50 characters
#Id 对应的VIN记录不存在，请提供有效的Id值
\u0049\u0064\u0020\u5bf9\u5e94\u7684\u0056\u0049\u004e\u8bb0\u5f55\u4e0d\u5b58\u5728\uff0c\u8bf7\u63d0\u4f9b\u6709\u6548\u7684\u0049\u0064\u503c=The VIN record corresponding to the Id does not exist. Please provide a valid Id value
#删除VIN码失败
\u5220\u9664\u0056\u0049\u004e\u7801\u5931\u8d25=Failed to delete the VIN code. Procedure
#该记录为用户默认车牌号，请填写车牌号
\u8be5\u8bb0\u5f55\u4e3a\u7528\u6237\u9ed8\u8ba4\u8f66\u724c\u53f7\uff0c\u8bf7\u586b\u5199\u8f66\u724c\u53f7=This record is the user's default license plate number, please fill in the license plate number
#修改VIN码失败
\u4fee\u6539\u0056\u0049\u004e\u7801\u5931\u8d25=Failed to change the VIN code
#请传入需要下发的目标场站列表
\u8bf7\u4f20\u5165\u9700\u8981\u4e0b\u53d1\u7684\u76ee\u6807\u573a\u7ad9\u5217\u8868=Please pass in the list of target terminals to be delivered
#目标场站未配置vin列表
\u76ee\u6807\u573a\u7ad9\u672a\u914d\u7f6e\u0076\u0069\u006e\u5217\u8868=The vin list is not configured at the target terminal
#待下发的桩列表为空
\u5f85\u4e0b\u53d1\u7684\u6869\u5217\u8868\u4e3a\u7a7a=The pile list to be delivered is empty
#无需要下发的桩列表
\u65e0\u9700\u8981\u4e0b\u53d1\u7684\u6869\u5217\u8868=There is no pile list to be delivered
#无符合下发需求的桩
\u65e0\u7b26\u5408\u4e0b\u53d1\u9700\u6c42\u7684\u6869=No piles meet the delivery requirements
#topCommId 不能为空
\u0074\u006f\u0070\u0043\u006f\u006d\u006d\u0049\u0064\u0020\u4e0d\u80fd\u4e3a\u7a7a=The topCommId cannot be empty
#VIN列表不能为空
\u0056\u0049\u004e\u5217\u8868\u4e0d\u80fd\u4e3a\u7a7a=The VIN list cannot be empty
#存在订单尚未结算，请处理后再进行操作
\u5b58\u5728\u8ba2\u5355\u5c1a\u672a\u7ed3\u7b97\uff0c\u8bf7\u5904\u7406\u540e\u518d\u8fdb\u884c\u64cd\u4f5c=There is an order that has not been settled, please process it before proceeding
#鉴权介质类型为空
\u9274\u6743\u4ecb\u8d28\u7c7b\u578b\u4e3a\u7a7a=The authentication medium type is null
#鉴权介质集合不能为空，
\u9274\u6743\u4ecb\u8d28\u96c6\u5408\u4e0d\u80fd\u4e3a\u7a7a\uff0c=The authentication media set cannot be empty
#commId不能为null
\u0063\u006f\u006d\u006d\u0049\u0064\u4e0d\u80fd\u4e3a\u006e\u0075\u006c\u006c=The commId cannot be null
#操作类型不能为空
\u64cd\u4f5c\u7c7b\u578b\u4e0d\u80fd\u4e3a\u7a7a=The operation type cannot be empty
#修改失败，VIN码已被使用
\u4fee\u6539\u5931\u8d25\uff0c\u0056\u0049\u004e\u7801\u5df2\u88ab\u4f7f\u7528=Description Failed to modify. The VIN code is already in use
#VIN码存在订单尚未结算，请处理后再进行操作
\u0056\u0049\u004e\u7801\u5b58\u5728\u8ba2\u5355\u5c1a\u672a\u7ed3\u7b97\uff0c\u8bf7\u5904\u7406\u540e\u518d\u8fdb\u884c\u64cd\u4f5c=The order with the VIN code has not been settled, please process it before proceeding
#VIN不能为空
\u0056\u0049\u004e\u4e0d\u80fd\u4e3a\u7a7a=The VIN cannot be empty
#--------------VIN相关end
#--------------订单相关start
#数据量超过10万条,无法导出
\u6570\u636e\u91cf\u8d85\u8fc7\u0031\u0030\u4e07\u6761\u002c\u65e0\u6cd5\u5bfc\u51fa=The amount of data exceeds 100,000 and cannot be exported
#请提供需要查询的充电订单号
\u8bf7\u63d0\u4f9b\u9700\u8981\u67e5\u8be2\u7684\u5145\u7535\u8ba2\u5355\u53f7=Please provide the charging order number to query
#请提供充电订单号
\u8bf7\u63d0\u4f9b\u5145\u7535\u8ba2\u5355\u53f7=Please provide the charging order number
#参数错误, 企业ID不能为空
\u53c2\u6570\u9519\u8bef\u002c\u0020\u4f01\u4e1a\u0049\u0044\u4e0d\u80fd\u4e3a\u7a7a=The parameter is incorrect. The enterprise ID cannot be empty
#要查询的订单信息不存在
\u8981\u67e5\u8be2\u7684\u8ba2\u5355\u4fe1\u606f\u4e0d\u5b58\u5728=The order information you want to query does not exist
#获取物理卡号失败，卡号不能为空
\u83b7\u53d6\u7269\u7406\u5361\u53f7\u5931\u8d25\uff0c\u5361\u53f7\u4e0d\u80fd\u4e3a\u7a7a=Failed to obtain the physical card number. The card number cannot be empty
#获取卡信息失败
\u83b7\u53d6\u5361\u4fe1\u606f\u5931\u8d25=Failed to obtain the card information.
#无法获取到该订单信息
\u65e0\u6cd5\u83b7\u53d6\u5230\u8be5\u8ba2\u5355\u4fe1\u606f=The order information could not be retrieved
#查询订单不能为空
\u67e5\u8be2\u8ba2\u5355\u4e0d\u80fd\u4e3a\u7a7a=The query order cannot be empty
#参数错误,缺少参数topCommId
\u53c2\u6570\u9519\u8bef\u002c\u7f3a\u5c11\u53c2\u6570\u0074\u006f\u0070\u0043\u006f\u006d\u006d\u0049\u0064=Parameter Description Parameter topCommId is missing
#扣款账户类型不能为空
\u6263\u6b3e\u8d26\u6237\u7c7b\u578b\u4e0d\u80fd\u4e3a\u7a7a=The debit account type cannot be empty
#订单编号不能为空
\u8ba2\u5355\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The order number cannot be empty
#找不到订单，请确认DB中是否存在订单信息。
\u627e\u4e0d\u5230\u8ba2\u5355\uff0c\u8bf7\u786e\u8ba4\u0044\u0042\u4e2d\u662f\u5426\u5b58\u5728\u8ba2\u5355\u4fe1\u606f\u3002=The order cannot be found, please confirm that the order information exists in the DB.
#无分时计费模板信息: {0}
order.timeDivision.notFound=No time-sharing billing template information: {0}
#订单号不能为空
\u8ba2\u5355\u53f7\u4e0d\u80fd\u4e3a\u7a7a=The order number cannot be empty
#订单不存在
\u8ba2\u5355\u4e0d\u5b58\u5728=Order does not exist
#当前订单状态不识别
\u5f53\u524d\u8ba2\u5355\u72b6\u6001\u4e0d\u8bc6\u522b=The current order status is not recognized
#当前订单不是异常订单
\u5f53\u524d\u8ba2\u5355\u4e0d\u662f\u5f02\u5e38\u8ba2\u5355=The current order is not an exception order
#当前订单状态充电中,只能修改为充电中或待支付
\u5f53\u524d\u8ba2\u5355\u72b6\u6001\u5145\u7535\u4e2d\u002c\u53ea\u80fd\u4fee\u6539\u4e3a\u5145\u7535\u4e2d\u6216\u5f85\u652f\u4ed8=The current order status can only be changed to charging or to be paid
#当前订单状态为待支付,只能修改为待支付或已结算
\u5f53\u524d\u8ba2\u5355\u72b6\u6001\u4e3a\u5f85\u652f\u4ed8\u002c\u53ea\u80fd\u4fee\u6539\u4e3a\u5f85\u652f\u4ed8\u6216\u5df2\u7ed3\u7b97=The current order status is pending payment and can only be changed to Pending payment or settled
#当前订单已结算,请刷新页面
\u5f53\u524d\u8ba2\u5355\u5df2\u7ed3\u7b97\u002c\u8bf7\u5237\u65b0\u9875\u9762=The current order has been settled, please refresh the page
#当前订单状态为其它,只能修改为待支付
\u5f53\u524d\u8ba2\u5355\u72b6\u6001\u4e3a\u5176\u5b83\u002c\u53ea\u80fd\u4fee\u6539\u4e3a\u5f85\u652f\u4ed8=The current order status is other and can only be changed to pending payment
#充电电量输入错误
\u5145\u7535\u7535\u91cf\u8f93\u5165\u9519\u8bef=Incorrect charging amount input
#原始电费输入错误
\u539f\u59cb\u7535\u8d39\u8f93\u5165\u9519\u8bef=Incorrect original electricity fee input
#电费输入错误
\u7535\u8d39\u8f93\u5165\u9519\u8bef=Incorrect electricity fee input
#原始服务费输入错误
\u539f\u59cb\u670d\u52a1\u8d39\u8f93\u5165\u9519\u8bef=Incorrect original service fee input
#服务费输入错误
\u670d\u52a1\u8d39\u8f93\u5165\u9519\u8bef=Incorrect service fee input
#余额不足
\u4f59\u989d\u4e0d\u8db3=Insufficient balance
#该订单支付账户无效
\u8be5\u8ba2\u5355\u652f\u4ed8\u8d26\u6237\u65e0\u6548=The order payment account is invalid
#请给账户充值
\u8bf7\u7ed9\u8d26\u6237\u5145\u503c=Please top up your account
#订单金额超出账户余额
\u8ba2\u5355\u91d1\u989d\u8d85\u51fa\u8d26\u6237\u4f59\u989d=The order amount exceeds the account balance
#默认扣款类型错误
\u9ed8\u8ba4\u6263\u6b3e\u7c7b\u578b\u9519\u8bef=The default deduction type is incorrect
#修改出现异常
\u4fee\u6539\u51fa\u73b0\u5f02\u5e38=Modification exception
#--------------订单相关end
#--------------角色相关start
#操作失败,角色名称已存在
\u64cd\u4f5c\u5931\u8d25\u002c\u89d2\u8272\u540d\u79f0\u5df2\u5b58\u5728=The operation failed. The role name already exists
#--------------角色相关end