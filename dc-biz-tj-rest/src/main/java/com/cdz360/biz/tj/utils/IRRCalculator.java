package com.cdz360.biz.tj.utils;

public class IRRCalculator {
    // 计算净现值（NPV）
    private static double calculateNPV(double[] cashFlows, double rate) {
        double npv = 0.0;
        for (int t = 0; t < cashFlows.length; t++) {
            npv += cashFlows[t] / Math.pow(1 + rate, t);
        }
        return npv;
    }

    // 计算净现值的一阶导数（NPV 的导数）
    private static double calculateNPVDerivative(double[] cashFlows, double rate) {
        double npvDerivative = 0.0;
        for (int t = 1; t < cashFlows.length; t++) {
            npvDerivative -= t * cashFlows[t] / Math.pow(1 + rate, t + 1);
        }
        return npvDerivative;
    }

    // 使用牛顿法计算 IRR
    public static double calculateIRR(double[] cashFlows, double initialGuess, double tolerance, int maxIterations) {
        double rate = initialGuess;
        double npv;
        double npvDerivative;
        int iteration = 0;

        do {
            npv = calculateNPV(cashFlows, rate);
            npvDerivative = calculateNPVDerivative(cashFlows, rate);
            rate = rate - npv / npvDerivative;
            iteration++;
        } while (Math.abs(npv) > tolerance && iteration < maxIterations);

        return rate;
    }

    public static void main(String[] args) {
        double[] cashFlows = {-237.3537,13.0524,19.0944,26.168,33.5325,41.1998,47.0628,52.1719,-0.2444,57.941};
        double initialGuess = 0.1; // 初始猜测值
        double tolerance = 1e-6; // 收敛容差
        int maxIterations = 10000; // 最大迭代次数

        double irr = calculateIRR(cashFlows, initialGuess, tolerance, maxIterations);
        System.out.println("IRR: " + irr);
    }
}
