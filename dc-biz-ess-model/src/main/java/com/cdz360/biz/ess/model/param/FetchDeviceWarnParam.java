package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.cdz360.base.model.base.param.TimeFilter;
import com.cdz360.base.model.es.type.WarnDeviceType;
import com.cdz360.base.model.es.type.WarnSubDeviceType;
import com.cdz360.base.model.iot.type.EssEquipType;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.site.type.AlarmStatusEnum;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "获取设备告警列表参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class FetchDeviceWarnParam extends BaseListParam {

    @Schema(description = "发生告警主体设备归属类型", example = "充电桩/户用储能ESS/商户储能ESS")
    private WarnDeviceType deviceType;

    @Schema(description = "告警编号")
    @JsonInclude(Include.NON_EMPTY)
    private String seqNo;

//    @Schema(description = "告警编号(模糊查询)")
//    @JsonInclude(Include.NON_EMPTY)
//    private String seqNoLike;

    @Schema(description = "告警对象编号或名称")
    @JsonInclude(Include.NON_EMPTY)
    private String warnTargetNoOrNameLike;

    @Schema(description = "告警类型")
    @JsonInclude(Include.NON_EMPTY)
    private String warnType;

    @Schema(description = "告警代码")
    @JsonInclude(Include.NON_NULL)
    private Long warnCode;

    @Schema(description = "告警代码列表")
    @JsonInclude(Include.NON_NULL)
    private List<Long> warnCodeList;

//    @Schema(description = "告警代码(模糊查询)")
//    @JsonInclude(Include.NON_EMPTY)
//    private String warnCodeLike;

    @Schema(description = "子设备类型(告警目标设备)")
    private WarnSubDeviceType subDeviceType;

    @Schema(description = "子设备类型(告警目标设备)")
    private EssEquipType equipType;

    @Schema(description = "子设备告警对象编号或名称(告警目标设备)")
    private String subTargetNoOrNameLike;

    @Schema(description = "告警名称")
    @JsonInclude(Include.NON_EMPTY)
    private String warnNameLike;

    @Schema(description = "告警代码/名称(支持模糊)")
    private String warnCodeOrNameLike;

    @Schema(description = "告警状态: 未结束/自动结束/手动结束")
    @JsonInclude(Include.NON_EMPTY)
    private List<AlarmStatusEnum> warnStatusList;

    @Schema(description = "所属国家地区代码(关联用户)",
        externalDocs = @ExternalDocumentation(
            description = "iso-3166 Country Codes",
            url = "https://www.iso.org/obp/ui/#search"))
    @JsonInclude(Include.NON_EMPTY)
    private List<String> countryCodeList;

    @Schema(description = "发生时间")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter startTime;

    @Schema(description = "结束时间")
    @JsonInclude(Include.NON_NULL)
    private TimeFilter stopTime;

    @Schema(description = "补充说明")
    @JsonInclude(Include.NON_EMPTY)
    private String warnNoteLike;

    @Schema(description = "场站ID列表")
    @JsonInclude(Include.NON_NULL)
    private List<String> siteIdList;

    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
