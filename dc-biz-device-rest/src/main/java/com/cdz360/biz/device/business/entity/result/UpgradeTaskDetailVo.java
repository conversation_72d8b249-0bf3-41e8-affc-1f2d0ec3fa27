package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.business.constant.UpdateTaskStatusEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Classname UpdateTaskDetailVo
 * @Description TODO
 * @Date 9/11/2019 2:10 PM
 * @Created by Rafael
 */
@Data
@ApiModel("桩升级详情")
public class UpgradeTaskDetailVo {
    private Long id;
    private Long taskId;
    private String evseId;
    private String evseName;
    @ApiModelProperty(value = "PC01版本号, 格式为: 硬件版本号-软件版本号-定制号")
    private String pc01Ver;
    @ApiModelProperty(value = "PC02版本号, 格式为: 硬件版本号-软件版本号-定制号")
    private String pc02Ver;
    @ApiModelProperty(value = "PC03版本号, 格式为: 硬件版本号-软件版本号-定制号")
    private String pc03Ver;
    private UpdateTaskStatusEnum status;
    private String failReason;
    private Date updateTime;
}