package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "用户token")
@Data
@Accessors(chain = true)
public class UserLoginTokenVo {

    @Schema(description = "用户ID")
    @JsonInclude(Include.NON_NULL)
    private Long id;

    @Schema(description = "登录成功token值", required = true)
    @JsonInclude(Include.NON_EMPTY)
    private String token;

}
