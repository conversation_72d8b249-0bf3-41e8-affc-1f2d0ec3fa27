package com.cdz360.biz.model.cus.corp.po;

import com.cdz360.base.model.base.type.InvoicingMode;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.corp.type.CorpType;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 企业信息(集团客户)
 */
@Data
@Accessors(chain = true)
public class CorpPo {

    @Schema(description = "企业ID", example = "123")
    private Long id;

    /**
     * 集团商户ID
     */
    @Schema(description = "集团商户ID", example = "123")
    private Long topCommId;
    /**
     * 商户Id
     */
    @Schema(description = "商户Id", example = "123")
    private Long commId;
    private String commName;

    @Schema(description = "用户UID", example = "123")
    private Long uid;
    /**
     * 企业名称
     */
    @Schema(description = "企业名称", example = "XX公司")
    private String corpName;

    /**
     * 联系人名称
     */
    @Schema(description = "联系人名称", example = "张三")
    private String contactName;

    /**
     * 手机号
     */
    @Schema(description = "手机号", example = "13012345678")
    private String phone;

    /**
     * 企业类型
     */
    @Schema(description = "企业类型")
    private CorpType type;

    /**
     * 电子邮箱
     */
    @Schema(description = "电子邮箱", example = "<EMAIL>")
    private String email;

    /**
     * 省
     */
    @Schema(description = "省", example = "123456")
    private String province;

    /**
     * 市
     */
    @Schema(description = "市", example = "123456")
    private String city;

    /**
     * 区
     */
    @Schema(description = "区", example = "123456")
    private String district;

    /**
     * 地址
     */
    @Schema(description = "地址", example = "xx路123号")
    private String address;

    /**
     * 组织机构代码
     */
    private String organizationImage;

    /**
     * 营业执照
     */
    @Schema(description = "营业执照")
    private String businessImage;


    /**
     * 续费提醒金额 ( 为null时表示不开启续费提醒 )
     */
    @Schema(description = "续费提醒金额 ( 为null时表示不开启续费提醒 )")
    private BigDecimal renewReminderAmount;

    /**
     * 是否已发送提醒邮件
     */
    @Schema(description = "是否已发送提醒邮件")
    private Boolean isSendReminderEmail;

    /**
     * 账号
     */
    @Schema(description = "登陆账号", example = "abcd1234")
    private String account;

    /** 密码 */
    /**
     * 密码(以字母开头，长度在6~18之间，只能包含字符、数字和下划线)
     */
    private String password;


    private Boolean enable;


    @Schema(description = "是否全额开票,true-是，false-否")
    private Boolean fullInvoicing;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;
    /**
     * 绑定账户ID
     */
    private Long sysUid;

    private String blocUserName;

    @Schema(description = "企业结算方式: UNKNOWN(0)-未知,BALANCE(1)-账户余额扣减(先付费),GUARANTEE(2)-担保消费结算,"
        +
        "POSTPAID(3)-预消费结算(后付费),PARTNER(4)-外部平台结算", format = "java.lang.Integer")
    private SettlementType settlementType;

    @Schema(description = "企业客户开票方式: 充值预开票(PRE_PAY)、充电后开票(POST_CHARGER)、后付费账单开票(POST_SETTLEMENT)")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private InvoicingMode invoiceWay;

    private List<String> gids;

    /**
     * 企客摘要
     */
    private String digest;

    @Schema(description = "创建人ID（非必填）")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long creatorId;

    @Schema(description = "创建人名称（非必填）")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String creatorName;

}
