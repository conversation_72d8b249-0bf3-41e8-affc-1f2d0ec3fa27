package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyHighVoltageRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveySupportingFacilitiesOtherRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveySupportingFacilitiesRoMapper;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveySupportingFacilitiesRoDs {


    @Autowired

    private TjSurveySupportingFacilitiesRoMapper tjSurveySupportingFacilitiesRoMapper;


    public TjSurveySupportingFacilitiesPo getById(Long id) {

        return this.tjSurveySupportingFacilitiesRoMapper.getById(id);

    }

    public TjSurveySupportingFacilitiesPo findTjSurveySupportingFacilitiesBySurveyNo(String surveyNo) {
        return this.tjSurveySupportingFacilitiesRoMapper.findTjSurveySupportingFacilitiesBySurveyNo(surveyNo);
    }

}

