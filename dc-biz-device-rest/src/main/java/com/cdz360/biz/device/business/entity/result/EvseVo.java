package com.cdz360.biz.device.business.entity.result;

import com.cdz360.base.model.base.type.EvseProtocolType;
import com.cdz360.base.model.base.type.EvseStatus;
import com.cdz360.base.model.base.type.SupplyType;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class EvseVo implements Serializable {

    private String gwno;
    private String evseId;
    private String name;
    private Integer power; // 额定功率
    private EvseStatus evseStatus = EvseStatus.UNKNOWN;
    private SupplyType supply = SupplyType.UNKNOWN;
    private Integer plugNum;
    /**
     * 场站ID
     */
    private String siteId;

    /**
     * 桩协议版本
     */
    private Integer protocolVer;
    /**
     * 桩固件(软件)版本
     */
    private String firmwareVer;

    /**
     * PC01版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc01Ver;
    /**
     * PC02版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc02Ver;
    /**
     * PC03版本号, 格式为: 硬件版本号-软件版本号-定制号
     */
    private String pc03Ver;

    private EvseProtocolType protocol;//桩协议类型 DC/CCTIA
    /**
     * 电桩型号, 如: G4-001
     */
    private String modelName;

    private String model = "TN-QCZ02-G04";

    private String upgradeStatus = "FAIL";

    /**
     * 电压
     */
    private Integer voltage;

    /**
     * 电流
     */
    private Integer current;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    @ApiModelProperty("是否开启debug")
    private Boolean debugTag;
}
