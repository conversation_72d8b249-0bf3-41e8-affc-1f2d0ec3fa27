package com.cdz360.biz.model.cus.post.param;

import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.vo.BaseObject;
import com.cdz360.base.utils.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "变更用户评论标签")
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CusPostChangeTagParam extends BaseObject {

    @Schema(description = "用户评论记录ID列表", required = true)
    @NotEmpty(message = "用户评论ID列表不能为空")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<Long> idList;

    @Schema(description = "用户评论记录充电订单号列表 操作日志使用", required = true)
    @NotEmpty(message = "用户评论充电订单号列表不能为空")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> orderNoList;

    @Schema(description = "标签列表", required = true)
    @NotNull(message = "标签列表不能为空")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> tags;

    @Schema(description = "操作人ID. sys_user.id", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long opUid;

    @Schema(description = "操作人姓名", hidden = true)
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String opName;

    public static void check(CusPostChangeTagParam param) {
        if (CollectionUtils.isEmpty(param.getIdList())) {
            throw new DcArgumentException("用户评论ID列表不能为空");
        }

        if (CollectionUtils.isEmpty(param.getTags())) {
            throw new DcArgumentException("请填入标签值");
        }
    }
}
