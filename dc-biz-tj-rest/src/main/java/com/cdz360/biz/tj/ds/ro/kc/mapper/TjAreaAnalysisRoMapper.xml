<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaAnalysisRoMapper">

  <resultMap id="RESULT_TJ_AREA_ANALYSIS_PO"
    type="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="aid" jdbcType="BIGINT" property="aid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="radius" jdbcType="BIGINT" property="radius"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <resultMap id="RESULT_WITH_POINT"
    type="com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="aid" jdbcType="BIGINT" property="aid"/>
    <result column="name" jdbcType="VARCHAR" property="name"/>
    <result column="radius" property="radius"/>
    <result column="locationText" property="location"
      typeHandler="com.cdz360.biz.ds.trading.PointTypeHandler"/>
    <association property="tjArea" column="aid"
      javaType="com.cdz360.biz.model.tj.kc.po.TjAreaPo"
      select="com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaRoMapper.getByAid"/>
    <collection property="analysisPointList" column="{analysisId=id}"
      ofType="com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo"
      select="com.cdz360.biz.tj.ds.ro.kc.mapper.TjAreaAnalysisPointRoMapper.selectByAnalysisId"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_AREA_ANALYSIS_PO">
    select *, ST_AsText(`location`) locationText from t_tj_area_analysis where id = #{id}
  </select>

  <sql id="FIND_ALL_SQL">
    <where>
      <if test="null != enable">
        taa.enable = #{enable}
      </if>
      <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
        <foreach collection="gidList" index="index" item="item" open=" AND ta.gid in ("
          separator="," close=")">
          #{item}
        </foreach>
      </if>
    </where>
  </sql>

  <select id="findTjAnalysis"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam"
    resultMap="RESULT_TJ_AREA_ANALYSIS_PO">
    select *, ST_AsText(`location`) locationText from t_tj_area_analysis taa
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      left join t_tj_area ta on ta.aid = taa.aid
    </if>
    <include refid="FIND_ALL_SQL"/>
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( sorts )">
      <foreach item="sort" collection="sorts"
        open="order by" separator="," close=" ">
        ${sort.columnsString} ${sort.order}
      </foreach>
    </if>
    <choose>
      <when test="start != null and size != null">
        limit #{start},#{size}
      </when>
      <otherwise>
        limit #{size}
      </otherwise>
    </choose>
  </select>

  <select id="countTjAnalysis"
    parameterType="com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam"
    resultType="long">
    select count(id) from t_tj_area_analysis taa
    <if test="@com.cdz360.base.utils.CollectionUtils@isNotEmpty( gidList )">
      left join t_tj_area ta on ta.aid = taa.aid
    </if>
    <include refid="FIND_ALL_SQL"/>
  </select>

  <select id="getWithPointById"
    resultMap="RESULT_WITH_POINT">
    select *, ST_AsText(`location`) locationText from t_tj_area_analysis
    where id = #{id}
  </select>

</mapper>

