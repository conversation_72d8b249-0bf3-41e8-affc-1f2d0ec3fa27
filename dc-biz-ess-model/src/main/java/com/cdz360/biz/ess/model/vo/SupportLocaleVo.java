package com.cdz360.biz.ess.model.vo;

import io.swagger.v3.oas.annotations.ExternalDocumentation;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.Locale;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "支持语言")
@Data
@Accessors(chain = true)
public class SupportLocaleVo {

    @Schema(description = "BCP 47 语言标签(后续用于语言区分)", required = true,
        externalDocs = @ExternalDocumentation(
            description = "BCP 47 Language Tags",
            url = "https://www.rfc-editor.org/info/bcp47"))
    private String language;

    @Schema(description = "支持语言显示(根据用户当前语言显示)", required = true)
    private String display;

    public static SupportLocaleVo builder(Locale target, Locale locale) {
        return new SupportLocaleVo()
            .setLanguage(target.getLanguage())
            .setDisplay(target.getDisplayLanguage(locale));
    }
}
