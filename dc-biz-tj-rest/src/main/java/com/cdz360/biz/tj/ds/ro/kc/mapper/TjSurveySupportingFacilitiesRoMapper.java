package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjSurveySupportingFacilitiesRoMapper {

    TjSurveySupportingFacilitiesPo getById(@Param("id") Long id);

    TjSurveySupportingFacilitiesPo findTjSurveySupportingFacilitiesBySurveyNo(@Param("surveyNo") String surveyNo);

}

