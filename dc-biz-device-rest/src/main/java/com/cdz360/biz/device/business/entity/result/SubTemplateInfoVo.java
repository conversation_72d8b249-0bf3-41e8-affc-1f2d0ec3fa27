package com.cdz360.biz.device.business.entity.result;

import com.cdz360.biz.device.business.constant.TariffTagEnum;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 计费子模板信息
 *
 * <AUTHOR>
 * @date Create on 2018/12/24 11:52
 */
@Data
public class SubTemplateInfoVo implements Serializable {

    /**
     * 开始时间**单位：分钟**
     */
    private Integer startTime;
    /**
     * 结束时间**单位：分钟**
     */
    private Integer stopTime;
    //时段编号
    private Integer num;
    /**
     * 分子-价格**单位：元**
     */
    private BigDecimal price;
    /**
     * 分母-单位**分钟或度数**
     */
    private Integer scale;
    /**
     * 服务费价格-分子**单位：分**
     */
    private BigDecimal servicePrice;
    /**
     * 服务费单位-分母**分钟或度数**
     */
    private Integer serviceScale;
    /**
     * 尖峰平谷标签名称{@link TariffTagEnum}
     */
    private String tariffTagName;
    /**
     * 备注
     */
    private String remark;
    /**
     * 尖峰平谷标签
     */
    private String tariffTag;

    /**
     * 当前时间段标识
     */
    private boolean currentFlag;
}
