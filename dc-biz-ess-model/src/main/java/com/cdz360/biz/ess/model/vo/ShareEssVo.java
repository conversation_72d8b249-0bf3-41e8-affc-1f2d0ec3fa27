package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "设备分享信息")
@Data
@Accessors(chain = true)
public class ShareEssVo {

    @Schema(description = "分享码")
    @JsonInclude(Include.NON_EMPTY)
    private String shareCode;

}
