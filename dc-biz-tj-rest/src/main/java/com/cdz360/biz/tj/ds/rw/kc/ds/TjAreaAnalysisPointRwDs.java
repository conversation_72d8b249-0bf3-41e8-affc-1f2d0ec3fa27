package com.cdz360.biz.tj.ds.rw.kc.ds;

import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.biz.tj.ds.rw.kc.mapper.TjAreaAnalysisPointRwMapper;
import com.cdz360.biz.model.tj.kc.param.UpdateAnalysisPointStatusParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPointPo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class TjAreaAnalysisPointRwDs {

    @Autowired
    private TjAreaAnalysisPointRwMapper tjAreaAnalysisPointRwMapper;

    public int batchInsertTjAreaAnalysisPoint(List<TjAreaAnalysisPointPo> analysisPointPoList) {
        if (CollectionUtils.isEmpty(analysisPointPoList)) {
            return 0;
        }

        // 考虑数量巨大，分批更新
        int start = 0;
        int size = 200;

        int cnt = analysisPointPoList.size() / size + 1;
        do {
            int i = tjAreaAnalysisPointRwMapper.batchInsertTjAreaAnalysisPoint(
                analysisPointPoList.stream().skip(start).limit(size).collect(Collectors.toList()));

            start += i;
            --cnt;
        } while (cnt > 0);

        return start;
    }

    public boolean insertTjAreaAnalysisPoint(TjAreaAnalysisPointPo tjAreaAnalysisPointPo) {
        return this.tjAreaAnalysisPointRwMapper.insertTjAreaAnalysisPoint(tjAreaAnalysisPointPo)
            > 0;
    }

    public int deleteByAnalysisId(Long analysisId) {
        return this.tjAreaAnalysisPointRwMapper.deleteByAnalysisId(analysisId);
    }

    public long updateTjAnalysisPointStatus(UpdateAnalysisPointStatusParam updateParam) {
        return this.tjAreaAnalysisPointRwMapper.updateTjAnalysisPointStatus(updateParam);
    }
}

