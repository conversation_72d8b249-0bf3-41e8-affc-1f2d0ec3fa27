package com.cdz360.biz.ess.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateDeserializer;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateSerializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class EssEquipDailyElecBiDto {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateDeserializer.class)
    @JsonSerialize(using = LocalDateSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Schema(title = "日期", description = "数据对应的日期")
    @ApiModelProperty(value = "日期")
    private LocalDate date;


    private List<Kv> inVals = new ArrayList<>();
    private List<Kv> outVals = new ArrayList<>();


    @ApiModelProperty(value = "今日正向电量， 单位kwh")
    private BigDecimal todayInKwh;

    @ApiModelProperty(value = "今日正向金额")
    private BigDecimal todayInFee;


    @ApiModelProperty(value = "总正向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalInKwh;

    @ApiModelProperty(value = "今日反向电量， 单位kwh")
    private BigDecimal todayOutKwh;

    @ApiModelProperty(value = "今日反向金额")
    private BigDecimal todayOutFee;


    @ApiModelProperty(value = "总反向电量，为时段开始时的数据, 单位kwh")
    private BigDecimal totalOutKwh;

    @Data
    @Accessors(chain = true)
    @AllArgsConstructor
    public static class Kv {

        private String k;

        private BigDecimal kwh;
        private BigDecimal fee;
    }
}
