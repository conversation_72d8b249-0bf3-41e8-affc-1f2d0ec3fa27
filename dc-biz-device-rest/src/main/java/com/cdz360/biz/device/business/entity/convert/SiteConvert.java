package com.cdz360.biz.device.business.entity.convert;

import com.cdz360.biz.device.business.constant.BizType;
import com.cdz360.biz.device.business.entity.po.Site;
import com.cdz360.biz.device.business.entity.result.SiteDetailInfoVo;
import com.cdz360.biz.device.business.entity.result.SiteSimpleInfoVo;
import com.cdz360.biz.device.business.util.PositionUtil;

/**
 * <AUTHOR>
 * @since 2018/7/27
 */
public class SiteConvert {


    /**
     * @param site
     * @return
     */
    public static SiteDetailInfoVo convertToSiteVo(Site site) {
        if (site == null) {
            return null;
        }
        SiteDetailInfoVo vo = new SiteDetailInfoVo();
        // 站点ID
        vo.setId(site.getId());
        // 站点编号
        vo.setIdNo(site.getIdNo());
        // 站点名称
        vo.setSiteName(site.getName());
        // 场站支持充电桩电流形式
        vo.setSupplyType(site.getSupplyType());
        // 经度
        vo.setLongitude(site.getLongitude());
        // 纬度
        vo.setLatitude(site.getLatitude());
        //处理微信小程序需要的经纬度
        GpsConvert gpsConvert = PositionUtil.gps84_To_Gcj02(site.getLatitude().doubleValue(),
                site.getLongitude().doubleValue());
        if (null != gpsConvert) {
            vo.setLatitudeWX(gpsConvert.getWgLat());
            vo.setLongitudeWX(gpsConvert.getWgLon());
        }
        // 站点地址
        vo.setAddress(site.getAddress());
        // 省份编码
        vo.setProvince(Integer.valueOf(site.getProvince()));
        // 城市编码
        vo.setCity(Integer.valueOf(site.getCity()));
        // 区域编码
        vo.setArea(Integer.valueOf(site.getArea()));
        // 服务号码
        vo.setPhone(site.getPhone());
        // 站点状态{@link com.chargerlink.device.business.constant.SiteStatusEnum}
        vo.setStatus(site.getStatus());
        // 运营商ID
        vo.setOperateId(site.getOperateId());
        // 运营商名称
        vo.setOperateName(site.getOperateName());
        // 物业ID或代理商账号ID
        vo.setMerchantId(site.getMerchantId());
        // 联系人
        vo.setContacts(site.getContacts());
        // 联系人号码
        vo.setContactsPhone(site.getContactsPhone());
        // 备注
        vo.setRemark(site.getRemark());
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        vo.setType(site.getType());
        // 工作日服务时间
        vo.setServiceWorkdayTime(site.getServiceWorkdayTime());
        // 节假日服务时间
        vo.setServiceHolidayTime(site.getServiceHolidayTime());
        // 停车是否收费 **0-未知 1-收费 2-免费**
        vo.setPark(site.getPark());
        // 停车费: 单位元
        vo.setParkFee(site.getParkFee());// TODO: 2019/8/31 数据库字段类型待调整，暂时还原为String 
//        if (StringUtils.isNotBlank(site.getParkFee())) {
//            // 考虑小数
//            if (site.getParkFee().contains(".")) {
//                vo.setParkFee((int) (Double.valueOf(site.getParkFee()) * 100));
//            } else {
//                vo.setParkFee(Integer.valueOf(site.getParkFee()) * 100);
//            }
//        } else {
//            vo.setParkFee(0);
//        }
        // 是否需要预约 **0-未知 1-需要预约 2-不需要预约**
        vo.setAppoint(site.getAppoint());
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        vo.setScope(site.getScope());
        // 上线时间
        vo.setOnlineDate(site.getOnlineDate());
        // 最后更新时间
        vo.setUpdateTime(site.getUpdateTime());
        // 创建时间
        vo.setCreateTime(site.getCreateTime());
        // 站点图片
        vo.setImages(site.getImages());
        // 支持品牌
        vo.setBrandIds(site.getBrandIds());
        // 支付方式
        vo.setPayMod(site.getAppPay());
        // 收费方式
        vo.setFeeDescription(site.getFeeDescription());
        // 发票提供方
        vo.setInvoiceDesc(site.getInvoiceDesc());
        //收费范围 最小
        vo.setFeeMin(site.getFeeMin());
        //收费范围 最大
        vo.setFeeMax(site.getFeeMax());
        //站点默认绑定计费模板Id
        vo.setTemplateId(site.getTemplateId());
        //站点默认绑定计费模板名称
        vo.setTemplateName(site.getTemplateName());
        vo.setBizType(null == site.getBizType() ? BizType.UNKNOWN : BizType.valueOf(site.getBizType()));
        vo.setBizName(site.getBizName());
        return vo;
    }

    /**
     * @param site
     * @return
     */
    public static SiteSimpleInfoVo convertToSiteSimpleInfoVo(Site site) {
        if (site == null) {
            return null;
        }
        SiteSimpleInfoVo vo = new SiteSimpleInfoVo();
        // 站点id
        vo.setSiteId(site.getId());
        // 站点名称
        vo.setSiteName(site.getName());
        //站点自编号
        vo.setSiteNo(site.getSiteNo());
        //冻结金额
        vo.setFrozenAmount(site.getFrozenAmount());
        // 站点地址
        vo.setAddress(site.getAddress());
        // 省份编码
        vo.setProvince(Integer.valueOf(site.getProvince()));
        // 城市编码
        vo.setCity(Integer.valueOf(site.getCity()));
        // 站点状态 {@link com.chargerlink.device.business.constant.SiteStatusEnum}
        vo.setStatus(site.getStatus());
        //经纬度
        vo.setLatitude(site.getLatitude());
        vo.setLongitude(site.getLongitude());
        vo.setTopCommId(site.getTopCommId());
        // 运营商ID
        vo.setOperateId(site.getOperateId());
        // 运营商名称
        vo.setOperateName(site.getOperateName());
        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
        vo.setType(site.getType());
        // 工作日服务时间
        vo.setServiceWorkdayTime(site.getServiceWorkdayTime());
        // 节假日服务时间
        vo.setServiceHolidayTime(site.getServiceHolidayTime());
        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
        vo.setScope(site.getScope());
        // 上线时间
        vo.setOnlineDate(site.getOnlineDate());
        //收费说明
        vo.setFeeDescription(site.getFeeDescription());
        //收费范围 最小
        vo.setFeeMin(site.getFeeMin());
        //收费范围 最大
        vo.setFeeMax(site.getFeeMax());
        //站点手机号
        vo.setContactsPhone(site.getContactsPhone());
        //计费模板
        vo.setTemplateId(site.getTemplateId());
        vo.setTemplateName(site.getTemplateName());
        vo.setBizType(site.getBizType());
        vo.setBizName(site.getBizName());
        vo.setDefaultPayType(site.getDefaultPayType());
        vo.setPayAccountId(site.getPayAccountId());
        return vo;
    }

//    /**
//     * @param site
//     * @return
//     */
//    public static SiteElasticsearch convertToSiteElasticsearch(Site site) {
//        SiteElasticsearch entity = new SiteElasticsearch();
//        // 站点ID
//        entity.setSiteId(site.getId());
//        // 站点名称
//        entity.setSiteName(site.getName());
//        // 站点地址
//        entity.setAddress(site.getAddress());
//        // 省份编码
//        entity.setProvince(site.getProvince());
//        // 城市编码
//        entity.setCity(site.getCity());
//        // 运营商ID
//        entity.setOperateId(site.getOperateId());
//        // 运营商名称
//        entity.setOperateName(site.getOperateName());
//        // 站点类型**0-未知 1-公共 2-公交 3-物流 4-混合**
//        entity.setType(site.getType());
//        // 工作日服务时间
//        entity.setServiceWorkdayTime(site.getServiceWorkdayTime());
//        // 节假日服务时间
//        entity.setServiceHolidayTime(site.getServiceHolidayTime());
//        // 使用范围**0-未知 1-对外开放 2-内部使用 3-特定人群**
//        entity.setScope(site.getScope());
//        // 停车是否收费 **0-未知 1-收费 2-免费**
//        entity.setPark(site.getPark());
//        // 停车费
//        entity.setParkFee(site.getParkFee());
//        // 站点图片
//        entity.setImages(site.getImages());
//        // 支持品牌
//        entity.setBrandIds(site.getBrandIds());
//        // 站点坐标
//        entity.setLocation(site.getLatitude().doubleValue(), site.getLongitude().doubleValue());
//        // 站点状态
//        entity.setStatus(site.getStatus());
//        //最小值
//        entity.setFeeMin(site.getFeeMin());
//        //最大值
//        entity.setFeeMax(site.getFeeMax());
//        entity.setArea(site.getArea());
//        return entity;
//    }
}
