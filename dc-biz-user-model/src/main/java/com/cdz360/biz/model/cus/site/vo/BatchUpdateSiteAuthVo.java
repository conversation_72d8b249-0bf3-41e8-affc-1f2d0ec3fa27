package com.cdz360.biz.model.cus.site.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;

@Data
public class BatchUpdateSiteAuthVo {
    /**
     * {@link com.cdz360.biz.model.cus.site.type.AuthMediaType}
     */
    @Schema(description = "鉴权介质类型 卡片= 1, VIN码= 2")
    private Integer authMediaType;

    private Long commId;

    @Schema(description = "鉴权介质集合 authMediaType= 1时, 传逻辑卡号(cardNo) authMediaType= 2时, 传VIN码")
    private List<String> accountList;

    @Schema(description = "物理卡号，用于记录操作日志")
    private List<String> cardChipNoList;

    private List<String> siteIdList;
    @Schema(description = "VIN本地鉴权场站id列表")
    private List<String> vinAuthSiteIdList;

    // 兼容充值卡，否0，是1
    private Integer deposit;

}
