package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "分体机类型")
@Getter
public enum SplitFlagType implements DcEnum {


    UNKNOWN(999, "未知"),

    SPLIT_NO(0, "非分体机"),
    SPLIT_SINGLE(1, "分体机单枪"),
    SPLIT_DOUBLE(2, "分体机双枪"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    SplitFlagType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static SplitFlagType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (SplitFlagType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return SplitFlagType.UNKNOWN;
    }

}
