package com.cdz360.biz.device.business.client;

import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.biz.device.business.entity.param.ModifyEvseCfgParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = DcConstants.KEY_FEIGN_IOT_WORKER, fallbackFactory = HystrixEvseCfgClientFactory.class)
public interface EvseCfgClient {

    @Deprecated
    @PostMapping(value = "/iot/biz/evse/modifyEvseCfgX")
        // TODO: 临时方案
    BaseResponse modifyEvseCfg(@RequestParam(value = "cfgParam") String cfgEvseParam);


    /**
     * 修改/下发桩配置
     */
    @PostMapping(value = "/iot/biz/evse/modifyEvseCfgV2")
    BaseResponse modifyEvseCfgV2(@RequestBody ModifyEvseCfgParam param);
}
