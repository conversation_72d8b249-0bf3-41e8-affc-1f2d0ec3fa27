package com.cdz360.biz.auth.rest;

import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.auth.model.vo.TCommercial;
import com.cdz360.biz.auth.service.AuthorityService;
import com.cdz360.biz.auth.service.TCommercialService;
import com.cdz360.biz.model.merchant.dto.CommercialDto;
import com.cdz360.biz.model.merchant.dto.CommercialTreeNode;
import com.cdz360.biz.model.merchant.param.AddCommercialParam;
import com.cdz360.biz.model.merchant.param.ConvertCommPayParam;
import com.cdz360.biz.model.merchant.param.ListCommercialParam;
import com.cdz360.biz.model.merchant.param.UpdateCommercialParam;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import java.util.List;
import java.util.UUID;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/api/comm")
@Tag(name = "商户信息", description = "商户")
public class CommercialController {


    @Autowired
    private TCommercialService tCommercialService;
    @Autowired
    private TCommercialInterface httpCommercialService;
    @Autowired
    private AuthorityService authorityService;


    @GetMapping("/getCommercial")
    @Operation(summary = "获取商户信息")
    public ObjectResponse<TCommercial> getCommercial(HttpServletRequest request,
                                                     @RequestParam Long commId) {
        log.debug("commId = {}", commId);
        var comm = tCommercialService.selectById(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/findSimpleVoById")
    @Operation(summary = "获取商户信息")
    public ObjectResponse<CommercialSimpleVo> findSimpleVoById(@RequestParam("commId") Long commId) {
        log.debug("findSimpleVoById. commId = {}", commId);
        var comm = tCommercialService.findSimpleVoById(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/getCommPlatInfo")
    @Operation(summary = "获取商户logo等信息，不存在则获取上级")
    public ObjectResponse<TCommercial> getCommPlatInfo(HttpServletRequest request,
                                                     @RequestParam Long commId) {
        log.debug("commId = {}", commId);
        var comm = tCommercialService.getCommPlatInfo(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/convertCommCorpDeposit")
    @Operation(summary = "切换企业在线充值")
    public ObjectResponse<TCommercial> convertCommCorpDeposit(
            HttpServletRequest request,
            @Parameter(name = "商户ID", required = true) @RequestParam(required = false) Long commId) {
        log.debug("commId = {}", commId);
        TCommercial comm = tCommercialService.convertCommCorpDeposit(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/convertCommScore")
    @Operation(summary = "切换商户会员等级开关")
    public BaseResponse convertCommScore(
            HttpServletRequest request,
            @Parameter(name = "商户ID", required = true) @RequestParam(value = "commId") Long commId,
            @Parameter(name = "商户等级开关", required = true) @RequestParam("enableUseScore") Boolean enableUseScore) {
        log.debug("commId = {},enableUseScore", commId,enableUseScore);
        TCommercial comm = tCommercialService.convertCommScore(commId,enableUseScore);
        return RestUtils.success();
    }

    @GetMapping("/convertCommCorpRefund")
    @Operation(summary = "切换企业在线退款")
    public ObjectResponse<TCommercial> convertCommCorpRefund(
            HttpServletRequest request,
            @Parameter(name = "商户ID", required = true) @RequestParam(required = false) Long commId) {
        log.debug("commId = {}", commId);
        TCommercial comm = tCommercialService.convertCommCorpRefund(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/convertCommRefund")
    @Operation(summary = "切换会员在线退款")
    public ObjectResponse<TCommercial> convertCommRefund(
            HttpServletRequest request,
            @Parameter(name = "商户ID", required = true) @RequestParam(required = false) Long commId) {
        log.debug("commId = {}", commId);
        TCommercial comm = tCommercialService.convertCommRefund(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/convertEnableOnlinePay")
    @Operation(summary = "切换商户会员在线充值")
    public ObjectResponse<TCommercial> convertEnableOnlinePay(
            HttpServletRequest request,
            @Parameter(name = "商户ID", required = true) @RequestParam(required = false) Long commId) {
        log.debug("commId = {}", commId);
        TCommercial comm = tCommercialService.convertEnableOnlinePay(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @PostMapping("/convertCommPayAndRefund")
    @Operation(summary = "切换商户在线充值，在线退款")
    public ObjectResponse<TCommercial> convertCommPayAndRefund(
            @RequestBody ConvertCommPayParam param) {
        log.debug("commId = {}", JsonUtils.toJsonString(param));
        TCommercial comm = tCommercialService.convertCommPayAndRefund(param);
        return RestUtils.buildObjectResponse(comm);
    }

    @GetMapping("/getCommTree")
    @Operation(summary = "获取商户信息列表")
    public ObjectResponse<CommercialTreeNode> getCommTree(HttpServletRequest request,
                                                          @RequestParam(required = false) Long commId) {
        log.debug("commId = {}", commId);
        CommercialTreeNode comm = tCommercialService.getCommercialTreeById(commId);
        return RestUtils.buildObjectResponse(comm);
    }

    @PostMapping("/updateRefundStatus")
    @Operation(summary = "更新商户以及子商户在线退款状态")
    public BaseResponse updateRefundStatus(@RequestBody AddCommercialParam param) {
        log.info("commId = {}", param.getId());
        int ret = this.tCommercialService.updateRefundStatus(param.getId(),param.getRefundStatus());
        return RestUtils.buildObjectResponse(ret);
    }

    /**
     * @param commId 传 null 用于更新所有的商户
     * @return
     */
    @PostMapping("/updateIdChain")
    @Operation(summary = "更新商户的idChain字段")
    public BaseResponse updateIdChain(@RequestParam(required = false) Long commId) {

        String traceId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("更新商户的 idChain. traceId = {}", traceId);
        this.tCommercialService.updateIdChains(commId, null, traceId);
        return RestUtils.success();
    }

    @PostMapping("/addComm")
    @Operation(summary = "新增商户, 新增成功会返回商户ID")
    public ObjectResponse<Long> addComm(@RequestBody AddCommercialParam param) {
        log.info("param = {}", param);
        Long ret = this.tCommercialService.addComm(param);
        return RestUtils.buildObjectResponse(ret);
    }

    @PostMapping("/commercial/addComm")
    @Operation(summary = "海外版, 新增商户, 新增成功会返回商户ID")
    public ObjectResponse<Long> addEssComm(@RequestBody AddCommercialParam param) {
        log.info("param = {}", param);
        Long ret = this.tCommercialService.addEssComm(param);
        return RestUtils.buildObjectResponse(ret);
    }

    @PostMapping("/updateCommInfo")
    @Operation(summary = "更新商户信息")
    public BaseResponse updateCommInfo(@RequestBody UpdateCommercialParam param) {
        String traceId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("traceId = {}, param = {}", traceId, param);
        Pair<Boolean, String> treeChanged = this.tCommercialService.updateCommInfo(param);
        if (treeChanged.getFirst()) {
            this.tCommercialService.updateIdChains(param.getId(), treeChanged.getSecond(), traceId);
        }
        return RestUtils.success();
    }

    @PostMapping("/commercial/updateCommInfo")
    @Operation(summary = "更新商户信息")
    public BaseResponse updateEssCommInfo(@RequestBody UpdateCommercialParam param) {
        String traceId = UUID.randomUUID().toString().replaceAll("-", "");
        log.info("traceId = {}, param = {}", traceId, param);
        Pair<Boolean, String> treeChanged = this.tCommercialService.updateEssCommInfo(param);
        if (treeChanged.getFirst()) {
            this.tCommercialService.updateIdChains(param.getId(), treeChanged.getSecond(), traceId);
        }
        return RestUtils.success();
    }

    /**
     * 获取下属（含自己）商户ID列表
     *
     * @param commIdChain
     * @return 下属（含自己）商户ID列表
     */
    @GetMapping("/getSubCommIdList")
    @Operation(summary = "更新商户信息")
    public ListResponse<Long> getSubCommIdList(@RequestParam String commIdChain) {
        log.info("commIdChain = {}", commIdChain);
        var list = this.tCommercialService.getSubCommIdList(commIdChain);
        return RestUtils.buildListResponse(list);
    }


    @PostMapping("/getCommList")
    @Operation(summary = "获取商户信息列表")
    public ListResponse<CommercialDto> getCommList(@RequestBody ListCommercialParam param) {
        log.debug("param = {}", param);
        return RestUtils.buildListResponse(this.tCommercialService.getCommercialList(param));
    }

    /**
     * 根据商户号merchants修改logo
     * @return
     */
    @PostMapping("/updateLogoByMerchants")
    @Operation(summary = "根据商户号修改logo")
    public BaseResponse updateLogoByMerchants(@RequestParam("merchants") String merchants,
                                              @RequestParam("logo") String logo,
                                              @RequestParam("platformName") String platformName) {
        this.tCommercialService.updateLogoByMerchants(merchants,logo,platformName);
        return RestUtils.success();
    }

    @Operation(summary = "商户编辑互联站点支持的账户类型")
    @GetMapping(value = "/editHlhtSitePayType")
    public BaseResponse editHlhtSitePayType(@RequestParam(value = "commId") Long commId,
                                            @RequestParam(value = "payTypeList") List<String> payTypeList) {
        log.info("editHlhtSitePayType commId = {}, payTypeList = {}", commId, payTypeList);
        return this.tCommercialService.editHlhtSitePayType(commId, payTypeList);
    }

    @Operation(summary = "获取商户以及子商户的ID集合")
    @GetMapping(value = "/getCommIdList")
    public ListResponse<Long> getCommIdList(@RequestParam(value = "commId") Long commId) {
        log.info(" commId = {}", commId);
        List<Long> commIdList =  this.tCommercialService.getCommIdList(commId);
        return RestUtils.buildListResponse(commIdList);
    }

}
