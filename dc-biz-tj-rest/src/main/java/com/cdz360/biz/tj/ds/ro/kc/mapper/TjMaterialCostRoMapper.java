package com.cdz360.biz.tj.ds.ro.kc.mapper;

import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjMaterialCostPo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjMaterialCostRoMapper {

    TjMaterialCostPo getById(@Param("id") Long id);

    List<TjMaterialCostVo> findTjMaterialCost(ListTjMaterialCostParam param);

    long countTjMaterialCost(ListTjMaterialCostParam param);

}

