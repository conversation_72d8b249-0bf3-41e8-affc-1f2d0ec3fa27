package com.cdz360.biz.ant.feign;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.ant.domain.BlocUser;
import com.cdz360.biz.ant.domain.ListResponseEvseList;
import com.cdz360.biz.ant.domain.RelOperator;
import com.cdz360.biz.ant.domain.User;
import com.cdz360.biz.ant.domain.request.CardRequest;
import com.cdz360.biz.ant.domain.vo.CardListdetailVO;
import com.cdz360.biz.ant.domain.vo.GroupUserListVo;
import com.cdz360.biz.ant.domain.vo.OperatorInfoVo;
import com.cdz360.biz.ant.domain.vo.UserAndDynamicAndCardsVo;
import com.cdz360.biz.ant.domain.vo.UserGroupVo;
import com.cdz360.biz.ant.domain.vo.UserSampleVo;
import com.cdz360.biz.ant.domain.vo.UserVo;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationCheckParam;
import com.cdz360.biz.ds.cus.ro.balance.param.BalanceApplicationParam;
import com.cdz360.biz.model.bi.dashboard.CommPropertyCountBiVo;
import com.cdz360.biz.model.bi.param.ExcelPosition;
import com.cdz360.biz.model.commercialUser.param.UserRegisterParam;
import com.cdz360.biz.model.common.po.CustomerAttractPo;
import com.cdz360.biz.model.cus.SiteAuthVin.po.SiteAuthVinPo;
import com.cdz360.biz.model.cus.SiteAuthVin.vo.SiteListVo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationCheckPo;
import com.cdz360.biz.model.cus.balance.po.BalanceApplicationPo;
import com.cdz360.biz.model.cus.balance.vo.BalanceApplicationVo;
import com.cdz360.biz.model.cus.corp.dto.CorpInvoiceInfoDto;
import com.cdz360.biz.model.cus.corp.po.CorpOrgLoginVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.vo.CorpOrgVO;
import com.cdz360.biz.model.cus.param.CustomerAttractListParam;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.vo.BatchUpdateSiteAuthVo;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.cus.soc.param.QueryStrategyParam;
import com.cdz360.biz.model.cus.soc.param.SocStrategyDict;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyCreditCusVo;
import com.cdz360.biz.model.cus.soc.vo.UserSocStrategyVinVo;
import com.cdz360.biz.model.cus.user.dto.UserAndBalanceAndTokenVo;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.param.AddUserParam;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.cus.wallet.param.ListRefundOrderParam;
import com.cdz360.biz.model.cus.wallet.po.RefundOrderPo;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.ChangeInfo;
import com.chargerlinkcar.framework.common.domain.PointLog;
import com.chargerlinkcar.framework.common.domain.UserCommRef;
import com.chargerlinkcar.framework.common.domain.invoice.dto.UpdateIdDTO;
import com.chargerlinkcar.framework.common.domain.invoice.vo.CorpInvoiceInfoVo;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.CorpCreditAccountEx;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthVinParam;
import com.chargerlinkcar.framework.common.domain.vo.UserPropVO;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.fasterxml.jackson.databind.JsonNode;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class AntUserClientHystrixFactory implements FallbackFactory<AntUserFeignClient> {

    @Override
    public AntUserFeignClient create(Throwable throwable) {
        log.error("【服务熔断】。Service = {},Message = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getMessage());
        log.error("【服务熔断】。Service = {},StackTrace = {}", DcConstants.KEY_FEIGN_DC_BIZ_USER,
            throwable.getStackTrace());

        return new AntUserFeignClient() {
            @Override
            public ListResponse<WhiteCardCfgVo> getWhiteCardCfgVoByCardNos(CardsParam cardsParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse insertBatchRelOperator(RelOperator relOperator) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<CorpOrgVO> getOrgById(Long corpId, Long id, Integer _index,
                Integer _size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpOrgPo> getCorpOrgByIdAndLevel(Long corpId, Integer orgLevel) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CorpOrgLoginVo> getOrgByAccount(String account) {
                return RestUtils.serverBusy4ObjectResponse();
            }
//            @Override
//            public BaseResponse addOrUpdateCorpOrg(CorpOrgPo corpOrgPo) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<CorpOrgVO> getOrgTree(Long corpId, Integer _index, Integer _size) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<CorpOrgVO> getOrgList(Long corpId, Integer _index, Integer _size) {
//                return RestUtils.serverBusy4ListResponse();
//            }
//
//            @Override
//            public ListResponse<CorpOrgVO> getOrgByLevel(Long corpId, Integer level) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ListResponse<OperatorInfoVo> queryAllOperators() {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ListResponse<UserAndDynamicVo> queryUsers(long topCommId, //Integer _index, Integer _size,
//                                                             Integer userParams, String keyWord, Long sourceId, Integer stats, List<Long> commIdList,
//                                                             Long start, Integer size) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ObjectResponse<UserAndDynamicAndCardsVo> findUserAndDynamicByCommIdAndUid(
                Long uid, Long topCommId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<UserGroupVo> queryUserGroupList(Integer _index, Integer _size,
                String groupName, List<Long> commIdList) {
                return RestUtils.serverBusy4ListResponse();
            }


            @Override
            public ObjectResponse<GroupUserListVo> queryUserGroupById(Long userGroupId,
                List<Long> commIdList) {
                return null;
            }

            @Override
            public ListResponse<UserSampleVo> queryNegationUsers(List<Long> commIdList) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse updateUserGroup(Long commId, Long groupId, String groupName,
                String groupDescrition, List<Long> userIds) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse addUserGroup(Long commId, String groupName, String groupDescrition,
                List<Long> userIds) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<CardListdetailVO> queryCardsByPage(String token, int pageNum,
                int pageSize, CardSearchParam cardSearchParam) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CardListdetailVO> queryOnlineCardsByPage(String token, int pageNum,
                int pageSize, String cardStatus,
                String beginTime, String endTime, String cardNo, Long cardType,
                Long commId, Long userId, Long topCommId, String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CardListdetailVO> queryOnlineCardsByPageOnCorp(
                CardRequest cardRequest) {
                return null;
            }

            @Override
            public ObjectResponse<ExcelPosition> exportOnlineCardsByCorp(CardRequest cardRequest) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CardListdetailVO> queryUrgencyCardsByPage(String token, int pageNum,
                int pageSize, CardRequest cardRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(String token,
                int pageNum, int pageSize, CardRequest cardRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<ExcelPosition> exportOnlineCardList(String token,
                CardSearchParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse urgencyCardsDetail(String token, Long cardId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponseEvseList urgencyCardsDetailEvseList(String token, Long cardId,
                String evse, Integer page, Integer rows) {
                ListResponseEvseList listResponse = new ListResponseEvseList();
                listResponse.setError("系统繁忙,请稍后重试!");
                listResponse.setStatus(5000);
                return listResponse;
            }

            @Override
            public ObjectResponse<Card> queryCardById(String id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateCardStatus(String cardNo, String cardStatus) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse addCard(Card card) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse updateCard(Card card) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Integer> batchGrantCard(List<Card> cards) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public BaseResponse rechargeAuthCard(List<Long> commIdList, String operatorId, String cardNo, long chargeMoney) {
//                return RestUtils.serverBusy();
//            }

            @Override
            public ObjectResponse<UserPropVO> findByPhone(String phone, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }


            @Override
            public BaseResponse userToBlack(Long uid, Long topCommId, String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse userUnBlack(Long uid, Long topCommId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<UserVo> findInfoByUid(Long uid, Long topCommId,
                String commIdChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<UserVo> findInfoByUid(long uid) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<UserVo> findUserByPhone(List<Long> commIdList, String phone) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<User> setBasicInfo(User user) {

                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<AuthMediaResult> authByBalanceIdAndPayType(long topCommId, long siteCommId, long cusId, Long balanceId, Integer defaultPayType, boolean realTimeFlag, BigDecimal frozenAmount, Integer initAmount) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<RBlocUser> findByCondition(RBlocUser rBlocUser) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse modifyByCondition(RBlocUser rBlocUser) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<UserPropVO> findByEmail(String email, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse insertRBlocUser(RBlocUser rBlocUser) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<Long> findRBlocUserByBlocUserId(Long blocUserId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse updateRBlocUser(RBlocUser rBlocUser) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse deleteRBlocUserById(Long rBlocUserId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<RBlocUser> findRBlocUserById(Long rBlocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<RBlocUserVo> findRBlocUserVoById(Long rBlocUserId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse deleteBatchRBlocUser(List<Long> rBlocUserIds) {
                return RestUtils.serverBusy();
            }


            @Override
            public BaseResponse openMultiOrder(Long uid, boolean isMulti, Long topCommId) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<RBlocUser> selectRBlocUserByPhone(RBlocUser rBlocUser) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<RBlocUser> selectRBlocUserByStartWithPhone(Long blocUserId,
                String phone) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BlocUserDto> selectSubBlocUserByCommIdChain(String commIdChain,
                Boolean includedHlhtCorp) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<BlocUserDto> selectSubBlocUserByTokenOnOperate() {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<BlocUserDto> findById(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<BlocUserDto> blocUserFindByCondition(BlocUser blocUser) {
                return RestUtils.serverBusy4ListResponse();
            }

            //            @Override
//            public ObjectResponse<BlocUserDto> getByRBlocUserId(Long rBlocUserId) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }
//
//            @Override
//            public JSONObject getkhtjgl(List<Long> commIdList) {
//                return JSONObject.parseObject(RestUtils.serverBusy4ObjectResponse().toJsonString());
//            }
//
//            @Override
//            public JSONObject getkhzzzs(List<Long> commIdList) {
//                return JSONObject.parseObject(RestUtils.serverBusy4ObjectResponse().toJsonString());
//            }
//
//            @Override
//            public JSONObject getkhtjzzt(List<Long> commIdList) {
//                return JSONObject.parseObject(RestUtils.serverBusy4ObjectResponse().toJsonString());
//            }

            @Override
            public ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> queryRBlocUser(
                Integer _num, Integer _size,
                String keyWord, Long blocUserId,
                String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<CardVo> queryAllCardList(String token, String userId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<VinDto> selectAllVinList(String token, Long userId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<MoveCorpSiteAuthList> getMoveCorpVINByCorpId(Long corpId,
                Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse getCorpInfoByProduct(Long productId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<ChangeInfo> getChangeInfo(String siteId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> queryChangeCommIdBySiteId(String siteId, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<RBlocUserVo> getAuthAccountByUserId(Long uid, String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<User> setDefaultAccount(Long uid, Long accountId,
                Integer defaultPayType) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CardMgnVo> queryAllCardsByPage(String token, int pageNum,
                int pageSize, String cardStatus, String cardType, Long commId, String beginTime,
                String endTime, String keyWord) {
                return RestUtils.serverBusy4ListResponse();
            }

//            @Override
//            public ObjectResponse<Card> queryCardByCardNo(String cardNo) {
//
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ObjectResponse<Card> queryCardByCardChipNo(String cardChipNo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<List<String>> sendWhiteCard(String cardChipNo, String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<List<String>> abandonWhiteCard(String cardChipNo, String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<List<String>> resetWhiteCardPwd(String cardChipNo, String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Map<String, Object>> sendBatchWhiteCards(String token) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<WhiteCardDto> queryWhiteCardDtoBySiteList(
                WhiteCardRequest whiteCardRequest) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public BaseResponse deleteCardsById(List<Long> ids) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse resetCardsByIds(List<Long> ids) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<JsonNode> parseExcel(List<List<Object>> list) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<JsonNode> parseCorpCardExcel(List<List<Object>> list,
                Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<JsonNode> parseCorpVinExcel(List<List<String>> list,
                Long corpId, Long corpTopCommId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> batchAddCard(List<Card> cards) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> siteAuthCard(SiteAuthCardParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }
//
//            @Override
//            public ListResponse<InvoicedUserVo> queryUserList(String keyword, Integer status, Integer index, Integer size) {
//                return RestUtils.serverBusy4ListResponse();
//            }

            @Override
            public ObjectResponse saveInvoicedUserAutoAmount(Long userId, long invoicedAmount,
                Integer monthDay, Integer auto) {

                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ListResponse<BalanceInfoVo> queryBalanceByType(Integer _index, Integer _size, String phone, Long subCommId,
//                                                                  Integer status, String balanceType, List<Long> commIdList) {
//                return RestUtils.serverBusy4ListResponse();
//            }

//            @Override
//            public ObjectResponse<BalanceInfoVo> findBalanceDetail(Long id) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<CommCusRef> queryCommCusRefs(Integer _index, Integer _size,
                String userPhone, Boolean enable,
                Long commId, Long userId, String cusName, String commIdChain) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<PointLog> selectBlocPointLogsByOrderNo(String orderNo) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<RefundOrderPo> listRefundOrder(ListRefundOrderParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Long> addUser(AddUserParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse addUserCommRef(UserCommRef userCommRef) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Boolean> findUserCommRef(Long uid, Long commId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommCusRef> merFindById(Long id) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<com.chargerlinkcar.framework.common.domain.vo.RBlocUser> getRBlocUserList(
                Integer _num, Integer _size, Integer userParams, String keyWord, Long corpId,
                Long corpOrgId, String corpOrgIds) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Long> upserdelRBlocUser(CorpCreditAccountEx corpCreditAccountEx) {
                return RestUtils.serverBusy4ObjectResponse();
            }

//            @Override
//            public ObjectResponse<CommCusRef> findByCommIdAndPhone(Long commId, String phone) {
//                return RestUtils.serverBusy4ObjectResponse();
//            }

            @Override
            public ListResponse<CommCusRef> findByCondition(CommCusRef ref) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CommPropertyCountBiVo> getCommBi(Long commId, String idChain) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Long> batchUpdate(BatchUpdateSiteAuthVo vo) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse setCouponAutoDeduct(Long userId, Boolean autoDeduct) {
                return RestUtils.serverBusy();
            }

            @Override
            public ObjectResponse<Integer> getUserCount(String idChain, Long topCommId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CustomerAttractPo> queryCusAttractList(
                CustomerAttractListParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<CorpInvoiceInfoVo> getCorpInvoiceInfo(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> saveCorpInvoiceInfo(CorpInvoiceInfoDto dto) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> disableByCorpId(Long corpId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<CommCusRef> findByCommIdAndPhone(Long commId, String phone) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> updateCorpInvoiceInfoProTempId(
                List<UpdateIdDTO> updateIdDTOList) {
                log.error(
                    "更新企业开票设置熔断: updateCorpInvoiceInfoProTempId(需要比对数据), param = {}",
                    JsonUtils.toJsonString(updateIdDTOList));
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse updateCorpStrategy(SocStrategyDict param) {
                return RestUtils.serverBusy();
            }

            @Override
            public BaseResponse deleteCorpStrategy(Long id) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<SocStrategyDict> queryStrategy(QueryStrategyParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserSocStrategyCreditCusVo> queryCorpStrategyCreditCus(
                QueryStrategyParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<UserSocStrategyVinVo> queryCorpStrategyVin(
                QueryStrategyParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> addCorpStrategyCreditCus(
                List<QueryStrategyParam> params) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> removeCorpStrategyCreditCus(
                List<QueryStrategyParam> params) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Integer> addCorpStrategyVin(List<QueryStrategyParam> params) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public BaseResponse createCorpSocStrategy(SocStrategyDict param) {
                return RestUtils.serverBusy();
            }

            @Override
            public ListResponse<BalanceApplicationVo> searchBalanceApplication(
                BalanceApplicationParam po) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<BalanceApplicationPo> addBalanceApplication(
                BalanceApplicationPo po) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> checkBalanceApplication(
                BalanceApplicationCheckParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ObjectResponse<Boolean> batchCheckReviewBalanceApplication(
                BalanceApplicationCheckParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<BalanceApplicationCheckPo> getCheckList(
                BalanceApplicationCheckParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<SiteAuthVinLogPo> getVinListByEvse(String evseId) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @GetMapping("/api/vin/getVinListBySiteId")
            @Override
            public ListResponse<SiteAuthVinPo> getVinListBySiteId(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteListVo> getSiteListByVin(String vin, Long start, Long size) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SitePo> getCommonSiteList(VinParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<Integer> siteAuthVin(SiteAuthVinParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<SitePo> getCardCommonSiteList(CardSearchParam param) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ListResponse<SiteAuthCardPo> getCardListBySiteId(String siteId) {
                return RestUtils.serverBusy4ListResponse();
            }

            @Override
            public ObjectResponse<UserAndBalanceAndTokenVo> registerByEmail(
                UserRegisterParam param) {
                return RestUtils.serverBusy4ObjectResponse();
            }

            @Override
            public ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(CardRequest param) {
                return RestUtils.serverBusy4ListResponse();
            }
        };
    }
}
