package com.cdz360.biz.ess.model.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "电网信息")
@Data
@Accessors(chain = true)
public class GridInfoVo {

    @Schema(description = "电网电压A相 V 0x1000")
    @JsonProperty("gva")
    private BigDecimal gridVoltageA;

    @Schema(description = "电网电压B相 V")
    @JsonProperty("gvb")
    private BigDecimal gridVoltageB;

    @Schema(description = "电网电压C相 V")
    @JsonProperty("gvc")
    private BigDecimal gridVoltageC;

    @Schema(description = "电网电压AB线 V")
    @JsonProperty("gvlab")
    private BigDecimal gridVoltageLineAB;

    @Schema(description = "电网电压BC线 V")
    @JsonProperty("gvlbc")
    private BigDecimal gridVoltageLineBC;

    @Schema(description = "电网电压CA线 V 0x100A")
    @JsonProperty("gvlca")
    private BigDecimal gridVoltageLineCA;

    @Schema(description = "电网A相THDU %")
    @JsonProperty("gta")
    private BigDecimal gridThduA;

    @Schema(description = "电网B相THDU %")
    @JsonProperty("gtb")
    private BigDecimal gridThduB;

    @Schema(description = "电网C相THDU %")
    @JsonProperty("gtc")
    private BigDecimal gridThduC;

    @Schema(description = "电网电压频率 Hz")
    @JsonProperty("gvf")
    private BigDecimal gridVoltageFrequency;

    @Schema(description = "电网A相电流有效值 A")
    @JsonProperty("gcea")
    private BigDecimal gridCurrentEffectiveA;

    @Schema(description = "电网B相电流有效值 A")
    @JsonProperty("gceb")
    private BigDecimal gridCurrentEffectiveB;

    @Schema(description = "电网C相电流有效值 A")
    @JsonProperty("gcec")
    private BigDecimal gridCurrentEffectiveC;

    @Schema(description = "电网N相电流有效值 A")
    @JsonProperty("gcen")
    private BigDecimal gridCurrentEffectiveN;

    @Schema(description = "电网A相电流THD %")
    @JsonProperty("gcta")
    private BigDecimal gridCurrentThdA;

    @Schema(description = "电网B相电流THD %")
    @JsonProperty("gctb")
    private BigDecimal gridCurrentThdB;

    @Schema(description = "电网C相电流THD % 0x101E")
    @JsonProperty("gctc")
    private BigDecimal gridCurrentThdC;

    @Schema(description = "电网A相电流峰值比 %")
    @JsonProperty("gcpra")
    private BigDecimal gridCurrentPeakRatioA;

    @Schema(description = "电网B相电流峰值比 %")
    @JsonProperty("gcprb")
    private BigDecimal gridCurrentPeakRatioB;

    @Schema(description = "电网C相电流峰值比 %")
    @JsonProperty("gcprc")
    private BigDecimal gridCurrentPeakRatioC;

    @Schema(description = "电网A相视在功率 kVA")
    @JsonProperty("gappa")
    private BigDecimal gridApparentPowerA;

    @Schema(description = "电网B相视在功率 kVA")
    @JsonProperty("gappb")
    private BigDecimal gridApparentPowerB;

    @Schema(description = "电网C相视在功率 kVA")
    @JsonProperty("gappc")
    private BigDecimal gridApparentPowerC;

    @Schema(description = "电网A相有功功率 kW 0x102E")
    @JsonProperty("gacpa")
    private BigDecimal gridActivePowerA;

    @Schema(description = "电网B相有功功率 kW")
    @JsonProperty("gacpb")
    private BigDecimal gridActivePowerB;

    @Schema(description = "电网C相有功功率 kW")
    @JsonProperty("gacpc")
    private BigDecimal gridActivePowerC;

    @Schema(description = "电网A相无功功率 kVar")
    @JsonProperty("grpa")
    private BigDecimal gridReactivePowerA;

    @Schema(description = "电网B相无功功率 kVar")
    @JsonProperty("grpb")
    private BigDecimal gridReactivePowerB;

    @Schema(description = "电网C相无功功率 kVar")
    @JsonProperty("grpc")
    private BigDecimal gridReactivePowerC;

    @Schema(description = "电网A相基波功率因数")
    @JsonProperty("gfpfa")
    private BigDecimal gridFundamentalPowerFactorA;

    @Schema(description = "电网B相基波功率因数")
    @JsonProperty("gfpfb")
    private BigDecimal gridFundamentalPowerFactorB;

    @Schema(description = "电网C相基波功率因数")
    @JsonProperty("gfpfc")
    private BigDecimal gridFundamentalPowerFactorC;

    @Schema(description = "电网A相功率因数")
    @JsonProperty("gpfa")
    private BigDecimal gridPowerFactorA;

    @Schema(description = "电网B相功率因数")
    @JsonProperty("gpfb")
    private BigDecimal gridPowerFactorB;

    @Schema(description = "电网C相功率因数 0x1044")
    @JsonProperty("gpfc")
    private BigDecimal gridPowerFactorC;
}
