package com.cdz360.biz.device.business.entity.param;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "'附近'查找参数")
public class GeoNearParam extends GpsPointVo {

    @ApiModelProperty(value = "距离,单位'米'")
    private Long distance;
}
