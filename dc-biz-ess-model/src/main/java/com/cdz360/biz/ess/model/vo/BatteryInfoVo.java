package com.cdz360.biz.ess.model.vo;

import com.cdz360.base.model.es.vo.BatData;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "电池信息")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class BatteryInfoVo extends BatData {

    @Schema(description = "电池容量,单位: kW·h", example = "11.22")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private BigDecimal capacity;
}
