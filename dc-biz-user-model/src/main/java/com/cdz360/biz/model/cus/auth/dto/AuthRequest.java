package com.cdz360.biz.model.cus.auth.dto;

import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class AuthRequest {
    @Schema(description = "场站ID")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String siteId;

    private List<String> siteGids;

    private EvseVo evseCache;

    private long balanceId;
    private long userId;
    private BigDecimal curFrozenAmount;
    private BigDecimal initAmount;

    /**
     * @deprecated 使用 payType 替换
     */
    @Deprecated
    private Integer defaultPayType;

    @Schema(description = "支付渠道")
    @JsonInclude(Include.NON_NULL)
    private PayAccountType payType;

    private Long commId;

    @Schema(description = "是否已校验充电场站与企业场站组的关系（true：是；false or null：否）")
    private Boolean siteAndCorpGidsVerified;


    @Override
    public String toString() {
        return JsonUtils.toJsonString(this);
    }
}
