package com.cdz360.biz.tj.ds.rw.kc.mapper;

import com.cdz360.biz.model.tj.kc.po.TjCompetitorPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface TjCompetitorRwMapper {

    TjCompetitorPo getById(@Param("id") Long id, @Param("lock") boolean lock);

    int insertTjCompetitor(TjCompetitorPo tjCompetitorPo);

    int updateTjCompetitor(TjCompetitorPo tjCompetitorPo);

    int disableCompetitor(@Param("competitorId") Long competitorId);
}

