package com.cdz360.biz.model.tj.kc.type;

import com.cdz360.base.model.base.type.DcEnum;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;

@Schema(description = "道闸类型")
@Getter
public enum BrandImageType implements DcEnum {


    UNKNOWN(999, "未知"),

    FLAGSHIP_STORE(1, "品牌形象RVI旗舰店"),
    ORDINARY_STATION(2, "品牌形象RVI普通站"),
    BRAND_NO(3, "不需要"),
    ;
    @JsonValue
    private final int code;
    private final String desc;

    BrandImageType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return this.code;
    }

    @JsonCreator
    public static BrandImageType valueOf(Object codeIn) {
        int code = 0;
        if (codeIn instanceof Integer) {
            code = (Integer) codeIn;
        } else if (codeIn instanceof Long) {
            code = ((Long) codeIn).intValue();
        } else if (codeIn instanceof String) {
            code = Integer.parseInt((String) codeIn);
        }

        for (BrandImageType flag : values()) {
            if (flag.code == code) {
                return flag;
            }
        }
        return BrandImageType.UNKNOWN;
    }

}
