package com.cdz360.biz.cus.service.impl;


import com.cdz360.base.model.base.constants.DcConstants;
import com.cdz360.base.model.base.dto.BaseResponse;
import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcArgumentException;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.model.base.exception.DcTokenException;
import com.cdz360.base.model.base.type.PayAccountType;
import com.cdz360.base.model.charge.type.SettlementType;
import com.cdz360.base.model.iot.vo.EvseVo;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.DecimalUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.base.utils.NumberUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.biz.cus.client.AuthCenterFeignClient;
import com.cdz360.biz.cus.client.BizBiFeignClient;
import com.cdz360.biz.cus.client.BoxSettingFeignClient;
import com.cdz360.biz.cus.client.BsBoxSettingFeignClient;
import com.cdz360.biz.cus.client.TradingFeignClient;
import com.cdz360.biz.cus.constant.CardConstants;
import com.cdz360.biz.cus.constant.UserConstants;
import com.cdz360.biz.cus.domain.ListResponseEvseList;
import com.cdz360.biz.cus.domain.UrgencyCardDetail;
import com.cdz360.biz.cus.domain.UrgencyCardEvse;
import com.cdz360.biz.cus.domain.User;
import com.cdz360.biz.cus.domain.request.CardRequest;
import com.cdz360.biz.cus.domain.vo.Card4ManagerVo;
import com.cdz360.biz.cus.domain.vo.CardListdetailVO;
import com.cdz360.biz.cus.domain.vo.CardListdetailVOParam;
import com.cdz360.biz.cus.domain.vo.CardMgnListVo;
import com.cdz360.biz.cus.domain.vo.SemiAuthVo;
import com.cdz360.biz.cus.repository.BlocUserMapper;
import com.cdz360.biz.cus.repository.CardMapper;
import com.cdz360.biz.cus.repository.RBlocUserMapper;
import com.cdz360.biz.cus.repository.UserMapper;
import com.cdz360.biz.cus.repository.VinMapper;
import com.cdz360.biz.cus.service.CommercialService;
import com.cdz360.biz.cus.service.CorpBizService;
import com.cdz360.biz.cus.service.ICardService;
import com.cdz360.biz.cus.service.MerchantService;
import com.cdz360.biz.cus.service.UserBizService;
import com.cdz360.biz.ds.cus.ro.basic.ds.UserRoDs;
import com.cdz360.biz.ds.cus.ro.card.ds.CardAmountSyncRoDs;
import com.cdz360.biz.ds.cus.ro.comm.ds.CommCusRefRoDs;
import com.cdz360.biz.ds.cus.ro.comm.ds.TRCommercialRoDs;
import com.cdz360.biz.ds.cus.ro.mechant.ds.CommercialRoDs;
import com.cdz360.biz.ds.cus.ro.site.ds.SiteAuthRoDs;
import com.cdz360.biz.ds.cus.ro.site.ds.SiteRoDs;
import com.cdz360.biz.ds.cus.ro.siteAuthCard.ds.SiteAuthCardRoDs;
import com.cdz360.biz.ds.cus.rw.card.ds.CardAmountSyncRwDs;
import com.cdz360.biz.ds.cus.rw.corp.ds.CorpRwDs;
import com.cdz360.biz.ds.cus.rw.site.ds.SiteAuthDs;
import com.cdz360.biz.ds.cus.rw.siteAuthCard.ds.SiteAuthCardLogRwDs;
import com.cdz360.biz.ds.cus.rw.siteAuthCard.ds.SiteAuthCardRwDs;
import com.cdz360.biz.model.card.po.SiteAuthCardLogPo;
import com.cdz360.biz.model.common.constant.DcBizConstants;
import com.cdz360.biz.model.common.constant.ExcelCheckResult;
import com.cdz360.biz.model.cus.card.param.CardAmountSyncParam;
import com.cdz360.biz.model.cus.card.po.CardAmountSyncPo;
import com.cdz360.biz.model.cus.card.vo.CardAmountSyncVo;
import com.cdz360.biz.model.cus.corp.po.CorpOrgPo;
import com.cdz360.biz.model.cus.corp.po.CorpPo;
import com.cdz360.biz.model.cus.corp.type.LimitCycle;
import com.cdz360.biz.model.cus.corp.vo.CorpVo;
import com.cdz360.biz.model.cus.site.po.SiteAuthPo;
import com.cdz360.biz.model.cus.site.po.SitePo;
import com.cdz360.biz.model.cus.site.type.AuthMediaType;
import com.cdz360.biz.model.cus.site.vo.MoveCorpSiteAuthList;
import com.cdz360.biz.model.cus.site.vo.SiteAuthMoveCorpVo;
import com.cdz360.biz.model.cus.site.vo.SiteAuthVo;
import com.cdz360.biz.model.cus.siteAuthCard.po.SiteAuthCardPo;
import com.cdz360.biz.model.cus.siteAuthCard.vo.SiteAuthCardVo;
import com.cdz360.biz.model.cus.user.dto.WhiteCard;
import com.cdz360.biz.model.cus.user.dto.WhiteCardDto;
import com.cdz360.biz.model.cus.user.dto.WhiteCardEvse;
import com.cdz360.biz.model.cus.user.param.WhiteCardRequest;
import com.cdz360.biz.model.cus.user.po.UserPo;
import com.cdz360.biz.model.iot.param.ListEvseParam;
import com.cdz360.biz.model.iot.param.ModifyEvseCfgParam;
import com.cdz360.biz.model.iot.vo.EvseModelVo;
import com.cdz360.biz.model.iot.vo.LocalCard;
import com.cdz360.biz.model.iot.vo.WhiteCardCfgVo;
import com.cdz360.biz.model.merchant.vo.CommercialSimpleVo;
import com.cdz360.biz.model.order.type.OrderPayType;
import com.cdz360.biz.model.site.type.SiteStatus;
import com.cdz360.biz.model.trading.iot.param.ListWhiteCardEvseParam;
import com.cdz360.biz.model.trading.iot.po.WhiteCardEvsePo;
import com.cdz360.biz.model.trading.site.param.ListSiteParam;
import com.cdz360.biz.model.trading.site.vo.CorpOrderCountVo;
import com.cdz360.biz.model.trading.site.vo.SiteCardCount;
import com.cdz360.biz.model.trading.site.vo.SiteVo;
import com.cdz360.data.cache.RedisIotReadService;
import com.chargerlinkcar.core.domain.Commercial;
import com.chargerlinkcar.framework.common.constant.AccountType;
import com.chargerlinkcar.framework.common.constant.ResultConstant;
import com.chargerlinkcar.framework.common.constant.SendStatus;
import com.chargerlinkcar.framework.common.domain.BlocUserDto;
import com.chargerlinkcar.framework.common.domain.OldPageParam;
import com.chargerlinkcar.framework.common.domain.OrderCountParam;
import com.chargerlinkcar.framework.common.domain.param.AddEssCardParam;
import com.chargerlinkcar.framework.common.domain.param.CardSearchParam;
import com.chargerlinkcar.framework.common.domain.param.CardsParam;
import com.chargerlinkcar.framework.common.domain.param.MerchantBalanceParam;
import com.chargerlinkcar.framework.common.domain.type.CardStatus;
import com.chargerlinkcar.framework.common.domain.type.CardType;
import com.chargerlinkcar.framework.common.domain.type.WhiteCardEvseStatus;
import com.chargerlinkcar.framework.common.domain.vo.AccountInfoVo;
import com.chargerlinkcar.framework.common.domain.vo.Card;
import com.chargerlinkcar.framework.common.domain.vo.CardDetailVO;
import com.chargerlinkcar.framework.common.domain.vo.CardMgnVo;
import com.chargerlinkcar.framework.common.domain.vo.CardVo;
import com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo;
import com.chargerlinkcar.framework.common.domain.vo.CommCusRef;
import com.chargerlinkcar.framework.common.domain.vo.MoveCorpCardList;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUser;
import com.chargerlinkcar.framework.common.domain.vo.RBlocUserVo;
import com.chargerlinkcar.framework.common.domain.vo.SiteAuthCardParam;
import com.chargerlinkcar.framework.common.domain.vo.UserVo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import com.chargerlinkcar.framework.common.feign.CardDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.IotBizClient;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmFeignClient;
import com.chargerlinkcar.framework.common.feign.SiteDataCoreFeignClient;
import com.chargerlinkcar.framework.common.feign.UserFeignClient;
import com.chargerlinkcar.framework.common.utils.AssertUtil;
import com.chargerlinkcar.framework.common.utils.DcAssertUtil;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import com.chargerlinkcar.framework.common.utils.RegularExpressionUtil;
import com.chargerlinkcar.framework.common.utils.UUIDUtils;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.EnumSet;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class CardServiceImpl implements ICardService {

    private static final String PART_FILE_NAME = "part";
    private static final List<PayAccountType> CARD_SYNC_SUPPORT_PAY_TYPE = List.of(
        PayAccountType.COMMERCIAL,
        PayAccountType.PERSONAL,
        PayAccountType.CREDIT);
    @Autowired
    private CardMapper cardMapper;
    @Autowired
    private VinMapper vinMapper;
    @Autowired
    private RBlocUserMapper rBlocUserMapper;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private CommercialService commercialService;
    @Autowired
    private TradingFeignClient tradingFeignClient;
    @Autowired
    private BizBiFeignClient bizBiFeignClient;
    @Autowired
    private BoxSettingFeignClient boxSettingFeignClient;
    //    @Autowired
//    private WhiteCardEvseFeignClient whiteCardEvseFeignClient;
    //    @Autowired
//    private SiteFeignClient siteFeignClient;
    @Autowired
    private BsBoxSettingFeignClient bsBoxSettingFeignClient;
    //    @Autowired
//    private BsBoxFeignClient bsBoxFeignClient;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private BlocUserMapper blocUserMapper;
    @Autowired
    private CorpRwDs corpRwDs;
    @Autowired
    private RedisIotReadService redisIotReadService;
    @Autowired
    private CommercialRoDs commercialRoDs;
    @Autowired
    private UserBizService userService;

    // @Autowired
    // private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private CorpOrgServiceImpl corpOrgService;
    @Autowired
    private CardDataCoreFeignClient cardDataCoreFeignClient;
    @Autowired
    private UserFeignClient userFeignClient;
    @Autowired
    private SiteAuthDs siteAuthDs;
    @Autowired
    private SiteAuthRoDs siteAuthRoDs;
    @Autowired
    private CommCusRefRoDs commCusRefRoDs;
    @Autowired
    private MerchantBalanceServiceImpl merchantBalanceService;
    @Autowired
    private SiteDataCoreFeignClient siteDataCoreFeignClient;
    @Autowired
    private AuthCenterFeignClient authCenterFeignClient;
    @Autowired
    private SiteRoDs siteRoDs;
    @Autowired
    private CardAmountSyncRwDs cardAmountSyncRwDs;
    @Autowired
    private CardAmountSyncRoDs cardAmountSyncRoDs;
    @Autowired
    private ICardService cardService;
    @Autowired
    private AuthFacadeImpl authFacade;
    @Autowired
    private TRCommercialRoDs trCommercialRoDs;
    @Autowired
    private UserRoDs userRoDs;
    @Autowired
    private CorpBizService corpBizService;
    @Autowired
    private SiteAuthCardRoDs siteAuthCardRoDs;
    @Autowired
    private SiteAuthCardRwDs siteAuthCardRwds;
    @Autowired
    private IotDeviceMgmFeignClient iotDeviceMgmFeignClient;
    @Autowired
    private SiteAuthCardLogRwDs siteAuthCardLogRwDs;
    @Autowired
    private IotBizClient iotBizClient;

    public CardServiceImpl() {
    }

//    public static void main(String[] args) {
//        List<String> stringList = Arrays.asList("013311696960", "013162806620", "013162806680");
//        String evseNosStr = "";
//        Long sendBoxSuccess = stringList.stream().filter(wce -> evseNosStr.contains(wce)).count();
//        System.out.println(sendBoxSuccess);
//    }

    /**
     * 拆分list
     *
     * @param targe
     * @param size  按size长度拆分
     * @return
     */
    public static List<List<Card>> createList(List<Card> targe, int size) {
        List<List<Card>> listArr = new ArrayList<>();
        //获取被拆分的数组个数
        int arrSize = targe.size() % size == 0 ? targe.size() / size : targe.size() / size + 1;
        for (int i = 0; i < arrSize; i++) {
            List<Card> sub = new ArrayList<>();
            //把指定索引数据放入到list中
            for (int j = i * size; j <= size * (i + 1) - 1; j++) {
                if (j <= targe.size() - 1) {
                    //得到拆分后的集合
                    sub.add(targe.get(j));
                }
            }
            listArr.add(sub);
        }
        return listArr;
    }

    /**
     * 根据条件查询鉴权卡分页数据
     *
     * @param token
     * @param page             分页
     * @param keyword          查询关键字: 物理卡号/卡片名称
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号 commId 所选商户
     * @return
     */
    @Override
    public ListResponse<CardListdetailVO> queryCardsByPage(String token,
        OldPageParam page,
        String keyword, CardListdetailVO cardListdetailVO,
        Long topCommId,
        String commIdChain) {

        log.info("卡片查询。cardListdetailVO: {}", JsonUtils.toJsonString(cardListdetailVO));

        Map<String, Object> params = new HashMap<>(16);
        params.put("keyWord", cardListdetailVO.getCardChipNo());
        Long commId = cardListdetailVO.getCommId();
        //Long subCommId = null;
        if (commId != null && commId > 0) {
            params.put("commId", commId);
            //subCommId = commId;
        } else {
            //params.put("commIds", commIds);
            //subCommId = commIds.stream().min(Long::compareTo).orElse(0L);
            params.put("commIdChain", commIdChain);
        }

        params.put("topCommId", topCommId);

        params.put("begintime", cardListdetailVO.getBeginTime());
        params.put("endtime", cardListdetailVO.getEndTime());
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        params.put("isPackage", cardListdetailVO.getIsPackage());
        params.put("stationName", cardListdetailVO.getStationName());
        params.put("cardNoList", cardListdetailVO.getCardNoList());
        params.put("deposit", cardListdetailVO.getDeposit());

        // -------- start Nathan
        // 解决问题: 充电管理平台 -> 卡片管理 -> 在线卡/离线卡列表数据一样
        // 传参 isPackage 意义: [3 -- 离线卡; 4 -- 鉴权卡]
        // 使用 cardType 字段区分在线卡/离线卡: [1 -- 紧急充电卡; 0 -- 鉴权卡; 2 -- 待定]

        params.put("cardType", cardListdetailVO.getIsPackage() == 3 ?
            Long.valueOf(CardType.EMERGENCY.getCode()) : Long.valueOf(CardType.ONLINE.getCode()));
        // ------- end Nathan

        //卡片管理添加三代离线卡
        if (cardListdetailVO.getIsPackage().equals(4)) {
            if (cardListdetailVO.getCardType() != null) {
                params.put("cardTypeList", List.of(cardListdetailVO.getCardType()));
            } else {
                params.put("cardTypeList",
                    List.of(CardType.ONLINE.getCode(), CardType.OFFLINE_CARD.getCode()));
            }
        }

        // 查询关键字
        params.put("keyWord", keyword);
        params.put("corpName", cardListdetailVO.getCorpName());
        params.put("carNo", cardListdetailVO.getCarNo());
        Page<Object> pageResult = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true,
            false, null);

        List<CardListdetailVO> cardList = cardMapper.queryCards(params);
        log.info("查询卡片信息。params: {}, result: {}", params, JsonUtils.toJsonString(cardList));

        //根据user id获取userName
        //以下代码根据card的user id，调接口获取user name的map:<userid,username>
        //在之后的循环中设置到每个VO节点中
        //TODO 可以优化为表链接
        List<Long> findList = cardList.stream().map(CardListdetailVO::getUserId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<UserVo> userVos = null;
        if (!findList.isEmpty()) {
            userVos = userMapper.queryUsersByIds(findList);
        }
        Map<Long, UserVo> userMap;
        if (userVos != null && !userVos.isEmpty()) {
            userMap = userVos.stream().collect(Collectors.toMap(UserVo::getId, e -> e));
        } else {
            userMap = new HashMap<>();
        }

        if (CollectionUtils.isNotEmpty(cardList)) {

            Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
                AuthMediaType.CARD,
                cardList.stream().map(CardListdetailVO::getCardNo).collect(Collectors.toList()),
                topCommId,
                commIdChain);
            Map<String, List<String>> stationMap = optionalMap.orElse(null);

//            List<CardListdetailVO> tempVo = cardMapper.getUsableStationByCardIdList(cardList.stream().map(
//                    CardListdetailVO::getCardId).collect(Collectors.toList()), commIdChain);
//            Map<Long, Integer> stationCountMap = tempVo.stream().collect(Collectors.toMap(CardListdetailVO::getCardId, CardListdetailVO::getUsableStationCount));

            //获取card本地鉴权绑定的场站列表
            List<SiteAuthCardVo> siteAuthCardVoList = siteAuthCardRoDs.getListByCard(
                cardList.stream().map(CardListdetailVO::getCardNo).collect(Collectors.toList()),
                null, commIdChain);
            Map<String, List<SiteAuthCardVo>> siteMap = siteAuthCardVoList.stream()
                .collect(Collectors.groupingBy(SiteAuthCardVo::getCardNo));

            cardList.forEach(e -> {
                e.setUsableStationCount(0);
//                Integer usableStationCount = stationCountMap.get(e.getCardId());
//                e.setUsableStationCount(usableStationCount == null ? 0 : usableStationCount);

                if (stationMap != null && stationMap.get(e.getCardNo()) != null) {
                    e.setStationList(stationMap.get(e.getCardNo()));
                    e.setUsableStationCount(stationMap.get(e.getCardNo()).size());
                }

                // 补充到返回信息里
                if (siteMap != null && siteMap.get(e.getCardNo()) != null) {
                    e.setIsLocalAuth(!siteMap.get(e.getCardNo()).isEmpty());
                    e.setLocalAuthSiteAmount(siteMap.get(e.getCardNo()).size());
                    List<SiteAuthCardVo> siteList = siteMap.get(e.getCardNo());
                    e.setLocalAuthSiteList(siteList);
                }
            });
        }

        //循环获取商户名
        for (CardListdetailVO card : cardList) {

            ObjectResponse<Commercial> commercial = commercialService.getCommercialByCommId(
                card.getCommId());

            if (commercial != null && commercial.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                Commercial data = commercial.getData();
                card.setCommName(data.getCommName());
            } else {
                log.info("根据商户获取商户数据失败。cardId: {}, commId: {}, result: {}",
                    card.getCardId(), card.getCommId(), commercial);
            }

            //设定该Card的username
            UserVo userVo = userMap.get(card.getUserId());
            if (userVo != null && userVo.getUsername() != null) {
                card.setUserName(userVo.getUsername());
            }
            if (card.getCorpId() != null && card.getCorpId() > 0) {
                card.setDebitAccountName("企业账户-" + card.getCorpName());
            } else if (userVo != null && userVo.getDebitAccountName() != null) {
                card.setDebitAccountName(userVo.getDebitAccountName());
            }

            card.setIsEdit(true);
            if (commIdChain != null) {
                String[] strArr = commIdChain.split(",");
                int length = strArr.length;
                if (Arrays.asList(strArr).contains(card.getCommId().toString()) && !strArr[length
                    - 1].equals(card.getCommId().toString())) {
                    card.setIsEdit(false);
                }
            }
        }

        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        log.info("{}查询卡片返回结果是：{}", commId, JsonUtils.toJsonString(res));

        return res;
    }

    /**
     * 根据条件查询在线卡分页数据
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    @Override
    public ListResponse<CardListdetailVO> queryOnlineCardsByPage(String token,
        OldPageParam page,
        CardListdetailVO cardListdetailVO,
        Long topCommId,
        String commIdChain) {

        log.info("卡片查询条件{}---{}", JsonUtils.toJsonString(cardListdetailVO),
            cardListdetailVO.getCardStatus() == null);

        // 查询在线封装查询条件数据
        Map<String, Object> params = new HashMap<>(16);
        params.put("keyWord", cardListdetailVO.getCardChipNo());

        params.put("topCommId", topCommId);
        //}
        params.put("commIdChain", commIdChain);
        params.put("userId", cardListdetailVO.getUserId());
        params.put("begintime", cardListdetailVO.getBeginTime());
        if (cardListdetailVO.getEndTime() == null) {
            params.put("endtime", null);
        } else {
            String[] temp = cardListdetailVO.getEndTime().split(":");
            if (temp.length == 3) {
                params.put("endtime", cardListdetailVO.getEndTime());
            } else {
                params.put("endtime", cardListdetailVO.getEndTime().concat(" 23:59:59"));
            }
        }
        //卡片列表添加三代离线卡
        if (cardListdetailVO.getCardType() != null) {
            params.put("cardType", cardListdetailVO.getCardType());
        }
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        Page<Object> pageResult = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true,
            false, null);

//        指定场站数量不包括已删除的场站
        List<CardListdetailVO> cardList = cardMapper.queryOnlineCardsByPage(params);

        List<Long> findList = cardList.stream().map(CardListdetailVO::getUserId)
            .filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<UserVo> userVos = null;
        if (!findList.isEmpty()) {
            userVos = userMapper.queryUsersByIds(findList);
        }
        Map<Long, UserVo> userMap;
        if (userVos != null && !userVos.isEmpty()) {
            userMap = userVos.stream().collect(Collectors.toMap(UserVo::getId, e -> e));
        } else {
            userMap = new HashMap<>();
        }

        if (CollectionUtils.isNotEmpty(cardList)) {
            Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
                AuthMediaType.CARD,
                cardList.stream().map(CardListdetailVO::getCardNo).collect(Collectors.toList()),
                topCommId,
                commIdChain);
            Map<String, List<String>> stationMap = optionalMap.orElse(null);

            //获取card本地鉴权绑定的场站列表
            List<SiteAuthCardVo> siteAuthCardVoList = siteAuthCardRoDs.getListByCard(
                cardList.stream().map(CardListdetailVO::getCardNo).collect(Collectors.toList()),
                null, commIdChain);
            Map<String, List<SiteAuthCardVo>> siteMap = siteAuthCardVoList.stream()
                .collect(Collectors.groupingBy(SiteAuthCardVo::getCardNo));

            cardList.forEach(e -> {
                e.setUsableStationCount(0);

                if (stationMap != null && stationMap.get(e.getCardNo()) != null) {
                    e.setStationList(stationMap.get(e.getCardNo()));
                    e.setUsableStationCount(stationMap.get(e.getCardNo()).size());
                }
                // 补充到返回信息里
                if (siteMap != null && siteMap.get(e.getCardNo()) != null) {
                    e.setIsLocalAuth(!siteMap.get(e.getCardNo()).isEmpty());
                    e.setLocalAuthSiteAmount(siteMap.get(e.getCardNo()).size());
                    List<SiteAuthCardVo> siteList = siteMap.get(e.getCardNo());
                    e.setLocalAuthSiteList(siteList);
                }
            });
        }

        //循环获取商户名
        for (CardListdetailVO card : cardList) {

            // 通过商户Id获取商户名称
            ObjectResponse<Commercial> commercial = commercialService.getCommercialByCommId(
                card.getCommId());

            if (commercial != null && commercial.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                Commercial data = commercial.getData();
                card.setCommName(data.getCommName());
            } else {
                log.info("卡片{}根据商户{}获取商户数据失败结果是{}", card.getCardId(),
                    card.getCommId(), JsonUtils.toJsonString(commercial));
            }

            //设定该Card的username
            UserVo userVo = userMap.get(card.getUserId());
            if (card.getCorpId() != null && card.getCorpId() > 0) {
                card.setDebitAccountName("企业账户-" + card.getCorpName());
            } else if (userVo != null && userVo.getDebitAccountName() != null) {
                card.setDebitAccountName(userVo.getDebitAccountName());
            }

            card.setIsEdit(true);
            if (commIdChain != null) {
                String[] strArr = commIdChain.split(",");
                int length = strArr.length;

                if (Arrays.asList(strArr).contains(card.getCommId().toString()) && !strArr[length
                    - 1].equals(card.getCommId().toString())) {

                    card.setIsEdit(false);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(cardList)) {
            /*Feign 获得卡号对应一年内的订单总数(订单创建时间)*/
            List<String> cardNoList = cardList.stream().map(CardListdetailVO::getCardNo)
                .collect(Collectors.toList());
            CardsParam cardsParam = new CardsParam();
            cardsParam.setCardNos(cardNoList);
            //cardsParam.setCommIds(commIds);// 查询时需加上商户条件
            cardsParam.setCommIdChain(commIdChain);
            cardsParam.setUserId(cardListdetailVO.getUserId());
            ListResponse<ChargerOrderVo> chargerOrderJson = tradingFeignClient.selectByConditionAndOneYear(
                cardsParam);
            FeignResponseValidate.check(chargerOrderJson);

            chargerOrderJson.getData().forEach(e -> {
                cardList.forEach(d -> {
                    if (e.getCardNo().equals(d.getCardNo())) {
                        d.setOrderNum(e.getOrderNum());
                    }
                });
            });
        }

        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        return res;
    }

    @Override
    public ObjectResponse<CardListdetailVO> queryOnlineCard(CardListdetailVO cardListdetailVO) {

        log.info("卡片查询条件{}---{}", JsonUtils.toJsonString(cardListdetailVO),
            cardListdetailVO.getCardStatus() == null);
        Map<String, Object> params = new HashMap<>(16);
        params.put("keyWord", cardListdetailVO.getCardChipNo());
        params.put("userId", cardListdetailVO.getUserId());
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        List<CardListdetailVO> cardList = cardMapper.queryOnlineCardsByPage(params);
        if (CollectionUtils.isNotEmpty(cardList)) {
            return new ObjectResponse<>(cardList.get(0));
        }
        return new ObjectResponse<>();
    }

    @Override
    public ListResponse<CardListdetailVO> queryOnlineCardsByPageOnCorp(CardRequest cardRequest) {
        log.info("卡片查询条件{}", JsonUtils.toJsonString(cardRequest));
        Page<Object> pageResult = PageHelper.startPage(cardRequest.getPage(), cardRequest.getRows(),
            true, false, null);
        IotAssert.isNotNull(cardRequest.getCorpId(), "无法获取用户信息");
//        指定场站数量不包括已删除的场站
        List<CardListdetailVO> cardList = cardMapper.queryOnlineCardsByPageOnCorp(cardRequest);

        if (CollectionUtils.isNotEmpty(cardList)) {
            String commIdChain = CollectionUtils.isNotEmpty(cardRequest.getGids()) ? null
                : cardRequest.getCommIdChain();
            Optional<Map<String, List<String>>> optionalMap = siteAuthRoDs.findStationList(
                AuthMediaType.CARD,
                cardList.stream().map(CardListdetailVO::getCardNo).collect(Collectors.toList()),
                cardRequest.getTopCommId(),
                commIdChain);
            Map<String, List<String>> stationMap = optionalMap.orElse(null);

//            List<CardListdetailVO> tempVo = cardMapper.getUsableStationByCardIdList(
//                    cardList.stream().map(CardListdetailVO::getCardId).collect(Collectors.toList()), cardRequest.getCommIdChain());
//            Map<Long, Integer> stationCountMap = tempVo.stream().collect(Collectors.toMap(CardListdetailVO::getCardId, CardListdetailVO::getUsableStationCount));

            cardList.forEach(e -> {
                e.setUsableStationCount(0);
//                Integer usableStationCount = stationCountMap.get(e.getCardId());
//                e.setUsableStationCount(usableStationCount == null ? 0 : usableStationCount);

                if (stationMap != null && stationMap.get(e.getCardNo()) != null) {
                    e.setStationList(stationMap.get(e.getCardNo()));
                    e.setUsableStationCount(stationMap.get(e.getCardNo()).size());
                }
            });
        }

        //循环获取商户名
        for (CardListdetailVO card : cardList) {
/*          企业平台无此需求
           // 通过商户Id获取商户名称
            ObjectResponse<Commercial> commercial = merchantFeignClient.getCommercialByCommId(card.getCommId());
            if (commercial != null && commercial.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                Commercial data = commercial.getData();
                card.setCommName(data.getCommName());
            } else {
                log.info("卡片{}根据商户{}获取商户数据失败结果是{}", card.getCardId(), card.getCommId(), JsonUtils.toJsonString(commercial));
            }
*/
            RBlocUserVo rBlocUserVo = cardMapper.getCorpOrgName(card.getCorpId(), card.getUserId(),
                card.getMobile());
            if (rBlocUserVo != null) {
                card.setCorpOrgId(rBlocUserVo.getCorpOrgId());
                card.setCorpOrgName(rBlocUserVo.getCorpOrgName());
            }
        }
        /*      企业平台无此需求
         *//*Feign 获得卡号对应一年内的订单总数(订单创建时间)*//*
        List<String> cardChipNoList = cardList.stream().map(e -> e.getCardChipNo()).collect(Collectors.toList());
        CardsParam cardsParam = new CardsParam();
        cardsParam.setCardChipNolist(cardChipNoList);
//        cardsParam.setCommIds(commIds);// 查询时需加上商户条件
        ListResponse<ChargerOrderVo> chargerOrderJson = tradingFeignClient.selectByConditionAndOneYear(cardsParam);
        FeignResponseValidate.check(chargerOrderJson);
        chargerOrderJson.getData().stream().forEach(e -> {
            cardList.stream().forEach(d -> {
                if (e.getCardChipNo().equals(d.getCardChipNo())) {
                    d.setOrderNum(e.getOrderNum());
                }
            });
        });
*/
        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        return res;
    }

    /**
     * 根据条件查询紧急充电卡分页数据 用于充电管理平台
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    @Override
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPage(String token,
        OldPageParam page,
        CardListdetailVO cardListdetailVO,
        Long topCommId,
        Long commId,
        String commIdChain) {

        log.info("卡片查询条件{}---{}", JsonUtils.toJsonString(cardListdetailVO),
            cardListdetailVO.getCardStatus() == null);

        // 查询在线封装查询条件数据
        Map<String, Object> params = new HashMap<>(16);
        //params.put("commIds", commIds);
        params.put("commIdChain", commIdChain);
        params.put("commId", cardListdetailVO.getCommId());

        if (null != topCommId) {
            params.put("topCommId", topCommId);
        }

        params.put("begintime", cardListdetailVO.getBeginTime());
        if (cardListdetailVO.getEndTime() == null) {
            params.put("endtime", null);
        } else {
            String[] temp = cardListdetailVO.getEndTime().split(":");
            if (temp.length == 3) {
                params.put("endtime", cardListdetailVO.getEndTime());
            } else {
                params.put("endtime", cardListdetailVO.getEndTime().concat(" 23:59:59"));
            }
        }
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        params.put("stationName", cardListdetailVO.getStationName());
        params.put("corpId", cardListdetailVO.getCorpId());
        params.put("queryType", cardListdetailVO.getQueryType());
        params.put("queryStr", cardListdetailVO.getQueryStr());
        params.put("cardNo", cardListdetailVO.getCardNo());
        params.put("stations", cardListdetailVO.getStations());
        Page<Object> pageResult = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true,
            false, null);
        log.info("查询参数。param = {}", params);
        List<CardListdetailVO> cardList = cardMapper.queryUrgencyCardsByPage(params);
//        cardList.stream().forEach(e -> {
//            if (e.getMobile().startsWith("2")) {
//                e.setMobile("无");
//                e.setUserName("无");
//            }
//        });
        log.info("cardList.size：{}", cardList.size());
        //循环获取商户名
        for (CardListdetailVO card : cardList) {
            if (card.getMobile().startsWith(("2"))) {
                card.setMobile("无");
                card.setUserName("无");
            }

            // 通过商户Id获取商户名称
            ObjectResponse<Commercial> commercial = commercialService.getCommercialByCommId(
                card.getCommId());

            if (commercial != null && commercial.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                Commercial data = commercial.getData();
                card.setCommName(data.getCommName());
            } else {
                log.info("卡片{}根据商户{}获取商户数据失败结果是{}", card.getCardId(),
                    card.getCommId(), JsonUtils.toJsonString(commercial));
            }

            if (com.cdz360.base.utils.StringUtils.isNotBlank(card.getStations())) {
                //紧急卡配置充电站点充电桩总数
                List<EvseVo> evseVos = redisIotReadService.listEvseBySiteId(card.getStations());
                card.setSendBoxTotal(CollectionUtils.isNotEmpty(evseVos) ? evseVos.size() : 0);
                //紧急卡已成功下发的桩数
                ListWhiteCardEvseParam whiteCardEvse = new ListWhiteCardEvseParam();
                List<Integer> list = new ArrayList<>();
                list.add(Integer.parseInt(SendStatus.ISSUE_SUCCESS.getCode()));
                list.add(Integer.parseInt(SendStatus.ABANDON_SUCCESS.getCode()));
                whiteCardEvse.setSendStatusList(list);
                whiteCardEvse.setWhiteCardNo(card.getCardChipNo());
                log.debug("param = {}", JsonUtils.toJsonString(whiteCardEvse));
                ListResponse<WhiteCardEvsePo> res2 = cardDataCoreFeignClient.getWhiteCardEvseList(
                    whiteCardEvse);
                FeignResponseValidate.check(res2);
                //ListResponse<WhiteCardEvse> res2 = whiteCardEvseFeignClient.queryByCondition(whiteCardEvse);
//                if (res2 == null || res2.getStatus() != DcConstants.KEY_RES_CODE_SUCCESS) {
//                    throw new DcServiceException("获取下发成功桩数失败");
//                }
                if (CollectionUtils.isEmpty(evseVos) || CollectionUtils.isEmpty(res2.getData())) {
                    card.setSendBoxSuccess(0);
                } else {
                    String evseNosStr = evseVos.stream().map(evseVo -> evseVo.getEvseNo())
                        .collect(Collectors.joining(","));
                    Long sendSuccCount = res2.getData().stream()
                        .filter(wce -> evseNosStr.contains(wce.getEvseId())).count();
                    sendSuccCount = Math.min(sendSuccCount, evseVos.size());
                    card.setSendBoxSuccess(sendSuccCount.intValue());
                }
            } else {
                card.setSendBoxTotal(0);
                card.setSendBoxSuccess(0);
            }

            //下级不允许编辑上级
            card.setIsEdit(true);
            if (commIdChain != null) {
                String[] strArr = commIdChain.split(",");
                int length = strArr.length;
                if (Arrays.asList(strArr).contains(card.getCommId().toString()) && !strArr[length
                    - 1].equals(card.getCommId().toString())) {
                    card.setIsEdit(false);
                }
            }
        }

        /*Feign 获得卡号对应一年内的订单总数(订单创建时间)*/
        List<String> cardNoList = cardList.stream().map(CardListdetailVO::getCardNo)
            .collect(Collectors.toList());
        CardsParam cardsParam = new CardsParam();
        cardsParam.setCardNos(cardNoList);
        //cardsParam.setCommIds(commIds);// 查询时需加上商户条件
        cardsParam.setCommIdChain(commIdChain);
        ListResponse<ChargerOrderVo> chargerOrderJson = tradingFeignClient.selectByConditionAndOneYear(
            cardsParam);
        FeignResponseValidate.check(chargerOrderJson);
        //List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo> chargerOrderVoList = JSONArray.parseArray(chargerOrderJson.getJSONArray("data").toJSONString(), ChargerOrderVo.class);
        chargerOrderJson.getData().forEach(e -> {
            cardList.forEach(d -> {
                if (e.getCardNo().equals(d.getCardNo())) {
                    d.setOrderNum(e.getOrderNum());
                }
            });
        });

        //        PaginationEntity<CardListdetailVO> paginationEntity = new PaginationEntity<CardListdetailVO>(cardList, pageResult.getTotal());
        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        //        log.info("世事洞明皆学问{}查询卡片返回结果是：{}",commId,JsonUtils.toJsonString(paginationEntity));
        //        log.info("人情练达即文章{}查询卡片查询结果是：{}",commId,JsonUtils.toJsonString(cardList));
        return res;
    }

    /**
     * 根据条件查询紧急充电卡分页数据 用于运营支撑平台
     *
     * @param token
     * @param page             分页
     * @param cardListdetailVO 查询条件：beginTime 查询开始时间 endTime 查询结束时间 cardChipNo 物理卡号/卡名称 cardStatus
     *                         卡状态
     * @return
     */
    @Override
    public ListResponse<CardListdetailVO> queryUrgencyCardsByPageOnOperate(String token,
        OldPageParam page,
        CardListdetailVOParam cardListdetailVO) {

        log.info("卡片查询条件{}---{}", JsonUtils.toJsonString(cardListdetailVO),
            cardListdetailVO.getCardStatus() == null);

        // 查询在线封装查询条件数据
        Map<String, Object> params = new HashMap<>(16);
        //        params.put("commIds", commIds);
        params.put("commId", cardListdetailVO.getCommId());

        // 集团商户
        params.put("topCommId", null);
        if (null != cardListdetailVO.getCommId()) {
            Long subCommId = cardListdetailVO.getCommId();
            Long topCommId = this.commercialRoDs.getTopCommId(subCommId);
            //CommercialSimpleVo topCommercial = this.commercialRoDs.getTopCommercial(subCommId, 10);
            if (null != topCommId) {
                params.put("topCommId", topCommId);
            }
        }

        params.put("begintime", cardListdetailVO.getBeginTime());
        if (cardListdetailVO.getEndTime() == null) {
            params.put("endtime", null);
        } else {
            String[] temp = cardListdetailVO.getEndTime().split(":");
            if (temp.length == 3) {
                params.put("endtime", cardListdetailVO.getEndTime());
            } else {
                params.put("endtime", cardListdetailVO.getEndTime().concat(" 23:59:59"));
            }
        }
        params.put("cardStatus", cardListdetailVO.getCardStatus());
        params.put("stationName", cardListdetailVO.getStationName());
        params.put("corpId", cardListdetailVO.getCorpId());
        params.put("queryType", cardListdetailVO.getQueryType());
        params.put("queryStr", cardListdetailVO.getQueryStr());
        params.put("cardNo", cardListdetailVO.getCardNo());
        params.put("stations", cardListdetailVO.getStations());
        params.put("excludeCardStatusList", cardListdetailVO.getExcludeCardStatusList());
        Page<Object> pageResult = PageHelper.startPage(page.getPageNum(), page.getPageSize(), true,
            false, null);
        log.info("查询参数。param = {}", params);
        List<CardListdetailVO> cardList = cardMapper.queryUrgencyCardsByPage(params);
        cardList.stream().forEach(e -> {
            if (e.getMobile().startsWith("2")) {
                e.setMobile("无");
                e.setUserName("无");
            }
        });
        log.info("cardList：{}", JsonUtils.toJsonString(cardList));
        if ((CollectionUtils.isEmpty(cardList))) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        // 所有商户Id列表
        List<Long> commIdList = cardList.stream()
            .map(CardListdetailVO::getCommId).distinct()
            .collect(Collectors.toList());

        // 获取商户的名称
        // FIXME: 这样传参是有问题，后续需要调整
        ListResponse<Commercial> commercials = commercialService.getCommercialsByCommIds(token,
            null, commIdList);
        final Map<Long, String> commId2CommName =
            commercials == null || commercials.getData() == null ?
                new HashMap<>() : commercials.getData().stream()
                .collect(Collectors.toMap(Commercial::getId, Commercial::getCommName));

        Set<String> siteIdSet = new HashSet<>();
        for (var card : cardList) {
            siteIdSet.add(card.getSiteId());
        }
        List<String> siteIdList = new ArrayList<>();
        siteIdList.addAll(siteIdSet);
        ListSiteParam siteParam = new ListSiteParam();
        siteParam.setSiteIdList(siteIdList);
        ListResponse<SiteVo> siteRes = siteDataCoreFeignClient.getSiteVoList(siteParam);
        Map<String, SiteVo> siteMap = siteRes.getData().stream()
            .collect(Collectors.toMap(SiteVo::getId, o -> o));
        log.info("siteIds = {}, siteMap = {}", siteIdList, siteMap);
        //循环获取商户名
        cardList.parallelStream().forEach(card -> {

            // 商户名称赋值
            card.setCommName(commId2CommName.getOrDefault(card.getCommId(), null));

            if (card.getSiteId() != null && !card.getSiteId().equals("")) {
                //紧急卡配置充电站点充电桩总数
                SiteVo site = siteMap.get(card.getSiteId());
                if (site != null) {
                    card.setSendBoxTotal(site.getAcEvseNum() + site.getDcEvseNum());
                }
//                ListResponse<String> res1 = bsBoxFeignClient.getBoxCodeListBySiteId(card.getStations());
//                FeignResponseValidate.check(res1);
//                Integer sendBoxTotal = res1.getData().size();
//                card.setSendBoxTotal(sendBoxTotal);
                //紧急卡已成功下发的桩数
                ListWhiteCardEvseParam whiteCardEvse = new ListWhiteCardEvseParam();
                List<Integer> list = new ArrayList<>();
                list.add(Integer.parseInt(SendStatus.ISSUE_SUCCESS.getCode()));
                list.add(Integer.parseInt(SendStatus.ABANDON_SUCCESS.getCode()));
                whiteCardEvse.setSendStatusList(list);
                whiteCardEvse.setWhiteCardNo(card.getCardChipNo());
                ListResponse<WhiteCardEvsePo> res2 = cardDataCoreFeignClient.getWhiteCardEvseList(
                    whiteCardEvse);
                FeignResponseValidate.check(res2);
                //ListResponse<WhiteCardEvse> res2 = whiteCardEvseFeignClient.queryByCondition(whiteCardEvse);
                //FeignResponseValidate.check(res2);
                Integer sendBoxSuccess = res2.getData().size();
                card.setSendBoxSuccess(sendBoxSuccess);
            } else {
                card.setSendBoxTotal(0);
                card.setSendBoxSuccess(0);
            }
        });

        /*Feign 获得卡号对应一年内的订单总数*/
        List<String> cardNoList = cardList.stream().map(CardListdetailVO::getCardNo)
            .collect(Collectors.toList());
        CardsParam cardsParam = new CardsParam();
        cardsParam.setCardNos(cardNoList);
        cardsParam.setCommIds(null);// 查询无需加上商户条件
        ListResponse<ChargerOrderVo> chargerOrderJson = tradingFeignClient.selectByConditionAndOneYear(
            cardsParam);
        FeignResponseValidate.check(chargerOrderJson);
        //List<com.chargerlinkcar.framework.common.domain.vo.ChargerOrderVo> chargerOrderVoList = JSONArray.parseArray(chargerOrderJson.getJSONArray("data").toJSONString(), ChargerOrderVo.class);
        chargerOrderJson.getData().forEach(e -> {
            cardList.forEach(d -> {
                if (e.getCardNo().equals(d.getCardNo())) {
                    d.setOrderNum(e.getOrderNum());
                }
            });
        });

        //        PaginationEntity<CardListdetailVO> paginationEntity = new PaginationEntity<CardListdetailVO>(cardList, pageResult.getTotal());
        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        //        log.info("世事洞明皆学问{}查询卡片返回结果是：{}",commId,JsonUtils.toJsonString(paginationEntity));
        //        log.info("人情练达即文章{}查询卡片查询结果是：{}",commId,JsonUtils.toJsonString(cardList));
        return res;
    }

    /**
     * 根据(紧急)卡cardId获取相关场站的信息
     *
     * @param cardId
     * @return
     */
    @Override
    public ObjectResponse<UrgencyCardDetail> urgencyCardsDetail(Long cardId) {

        Card card = cardMapper.queryCardWithUserById(cardId);
        DcAssertUtil.isNotNull(card, "找不到离线卡,卡号:" + cardId);
        DcAssertUtil.notEmpty(card.getStations(), "离线卡相关的场站不能为空");

        List<String> stationsName = this.cardMapper.getSiteNameListByCardId(cardId);

        ObjectResponse<Commercial> commRes = commercialService.getCommercialByCommId(
            card.getCommId());
        FeignResponseValidate.check(commRes);
        Commercial commercial = commRes.getData();

        UrgencyCardDetail urgencyCardDetail = new UrgencyCardDetail();
        urgencyCardDetail.setStationName(StringUtils.join(stationsName, ", "));//站点名称

        ObjectResponse<Commercial> topCommRes = commercialService.getCommercialByCommId(
            commercial.getTopCommId());
        FeignResponseValidate.check(topCommRes);
        String corpName = topCommRes.getData().getCommName();

        //获取企业客户
        String businessCustomerName = "无";
        if (card.getCorpId() != null && card.getCorpId() > 0) {
            BlocUserDto dto = blocUserMapper.findById(card.getCorpId());
            if (dto != null) {
                businessCustomerName = dto.getBlocUserName();
            }
        }

        urgencyCardDetail.setCommName(commercial.getCommName());//归属商户
        urgencyCardDetail.setCorpName(corpName);//集团商户
        urgencyCardDetail.setBusinessCustomerName(businessCustomerName);//企业客户
        urgencyCardDetail.setCardChipNo(card.getCardChipNo());//物理卡号（卡号）
        if (card.getMobile().startsWith("2")) {
            urgencyCardDetail.setCustomerName("无");//客户姓名
            urgencyCardDetail.setCustomerPhone("无");//客户手机号
        } else {
            urgencyCardDetail.setCustomerName(card.getUserName());//客户姓名
            urgencyCardDetail.setCustomerPhone(card.getMobile());//客户手机号
        }

        return new ObjectResponse<>(urgencyCardDetail);
    }

    /**
     * 根据离线卡cardId获取相关桩的信息
     *
     * @param token
     * @param cardId
     * @return
     */
    @Override
    public ListResponseEvseList<UrgencyCardEvse> urgencyCardsDetailEvseList(String token,
        Long cardId, String evse, Integer page, Integer rows) {
        log.info("查找离线卡：token: {}, cardId: {}, page: {}, rows: {}", token, cardId, page, rows);
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }

        Card card = cardMapper.queryCardById(cardId);
        DcAssertUtil.isNotNull(card, "找不到离线卡,卡号:" + cardId);
        DcAssertUtil.notEmpty(card.getStations(), "离线卡相关的场站不能为空");

        log.info("查找场站下的离线卡：card: {}", JsonUtils.toJsonString(card));
        ListResponseEvseList<UrgencyCardEvse> ret = bsBoxSettingFeignClient.urgencyCardsDetailEvseList(
            card.getStations(), card.getCardChipNo(), evse, page, rows);
        FeignResponseValidate.check(ret);

        ret.setCurrentCardStatus(card.getStatus());
        return ret;
    }

    /**
     * 查询商户及子商户卡列表信息
     *
     * @param token
     * @return
     */
    @Override
    public ListResponse<CardVo> queryAllCardList(String token, String userId) {
        if (StringUtils.isBlank(token)) {
            throw new DcTokenException("token无效，请先登录");
        }
        List<Long> ret = merchantService.getCommIdListByToken(token);
        //FeignResponseValidate.check(ret);
        List<Long> commIds = ret;

        Map<String, Object> params = new HashMap<>();
        params.put("commIds", commIds);
        params.put("userId", userId);
        List<CardVo> cardList = cardMapper.queryAllCardList(params);
        return new ListResponse<>(cardList, (long) cardList.size());
    }

    /**
     * 初始化 用户卡片
     *
     * @param commericalId 商户id
     * @param uid          用户id
     * @param mobile       用户手机号
     * @return
     */
    @Override
    public Long initUserCard(Long commericalId, Long uid, String mobile) {
        //cardService.insertVirtualCard(CommId.intValue(), userdate.getPhone(),userdate.getId());
        if (commericalId == null || uid == null) {
            return null;
        }
        Card card = new Card();
        card.setCardChannel("3");
        card.setCardType(CardConstants.VIRTUAL_CARD);
        String cardNo = this.getCardNo("0");
        card.setCardNo(cardNo);
        card.setCardStatus(CardConstants.CARD_STATUS_ACTIVATED);
        card.setCardActivationDate(new Date());
        card.setCardCreateDate(new Date());
        card.setCardActivationDate(new Date());

        card.setMobile(mobile);
        card.setUserId(uid);
        card.setCommId(commericalId);

        Long insertCardStatus = cardMapper.insertSelective(card);

        // 跟新场站使用范围
        this.updateSiteAuth(card);

        return insertCardStatus;
    }

    /**
     * 更新场站使用范围
     *
     * @param card
     */
    private void updateSiteAuth(Card card) {
        String stations = card.getStations();
        if (null == stations) {
            return;
        }

        List<String> siteIdList = List.of(stations.split(","));

        // 获取顶级商户Id
        Long topCommId = this.commercialRoDs.getTopCommId(card.getCommId());
        //CommercialSimpleVo topCommercial = this.commercialRoDs.getTopCommercial(card.getCommId(), 10);
        if (null == topCommId) {
            log.error("该商户顶级商户不存在: card = {}", card);
            return;
        }

        // 查找原来的数据，对比更新数据，如果不存在则disable
        List<SiteAuthVo> oldSiteAuthList = this.siteAuthDs.findByTypeAndAccountAndTopCommId(
            AuthMediaType.CARD, card.getCardNo(), topCommId, null, null, null);

        List<String> disableList = new ArrayList<>();
        oldSiteAuthList.forEach(o -> {
            if (!siteIdList.contains(o.getSiteId())) {
                disableList.add(o.getSiteId());
            }
        });

        if (siteIdList.isEmpty()) {
            this.siteAuthDs.disableAll(AuthMediaType.CARD, card.getCardNo(), null, topCommId);
        } else {
            this.siteAuthDs.insertOrUpdate(siteIdList.stream().map(siteId ->
                new SiteAuthPo()
                    .setTopCommId(topCommId)
                    .setAccount(card.getCardNo())
                    .setSiteId(siteId)
                    .setType(AuthMediaType.CARD)
                    .setEnable(true)
            ).collect(Collectors.toList()));

            if (CollectionUtils.isNotEmpty(disableList)) {
                this.siteAuthDs.insertOrUpdate(disableList.stream().map(siteId ->
                    new SiteAuthPo()
                        .setTopCommId(topCommId)
                        .setAccount(card.getCardNo())
                        .setSiteId(siteId)
                        .setType(AuthMediaType.CARD)
                        .setEnable(false)
                ).collect(Collectors.toList()));
            }
        }

        // 开始处理本地鉴权
        List<String> localAuthList;
        if (card.getIsLocalAuth() == null || Boolean.FALSE.equals(card.getIsLocalAuth())
            || card.getLocalAuthSiteList() == null) {
            localAuthList = new ArrayList<>();
        } else {
            localAuthList = card.getLocalAuthSiteList();
        }
        // 查找原来的数据，对比更新数据，如果不存在则disable
        List<SiteAuthCardVo> siteAuthCardVoList = this.siteAuthCardRoDs.findByCardNoAndCommId(
            card.getCardNo(), card.getCommId());

        List<String> disableSiteAuthCardList = new ArrayList<>();
        siteAuthCardVoList.forEach(o -> {
            if (!localAuthList.contains(
                o.getSiteId())) {
                disableSiteAuthCardList.add(o.getSiteId());
            }
        });

        if (!Boolean.TRUE.equals(card.getIsLocalAuth()) || CollectionUtils.isEmpty(localAuthList)) {
            this.siteAuthCardRwds.disableAll(card.getCardNo(), null, card.getCommId());
        } else {
            List<SiteAuthCardPo> siteAuthCardPos = localAuthList.stream().map(siteId ->
                new SiteAuthCardPo()
                    .setCommId(card.getCommId())
                    .setCardNo(card.getCardNo())
                    .setCardChipNo(card.getCardChipNo())
                    .setSiteId(siteId)
                    .setEnable(true)
            ).collect(Collectors.toList());

            this.siteAuthCardRwds.insertOrUpdate(siteAuthCardPos);

            if (CollectionUtils.isNotEmpty(disableSiteAuthCardList)) {
                this.siteAuthCardRwds.insertOrUpdate(disableSiteAuthCardList.stream().map(siteId ->
                    new SiteAuthCardPo()
                        .setCommId(card.getCommId())
                        .setCardNo(card.getCardNo())
                        .setCardChipNo(card.getCardChipNo())
                        .setSiteId(siteId)
                        .setEnable(false)
                ).collect(Collectors.toList()));
            }
        }

        if (CollectionUtils.isNotEmpty(disableSiteAuthCardList)) {
            // 根据原先的记录下发到受影响的场站
            log.info("原先的CARD鉴权siteId列表: {}，从各个场站剔除目标CARD: {}，开始下发",
                disableSiteAuthCardList.size(), card.getCardNo());
            SiteAuthCardParam param = new SiteAuthCardParam();
            param.setCardNoList(List.of(card.getCardNo()))
                .setSiteIdList(disableSiteAuthCardList);
            this.siteAuthCard(param);
        }
    }

    /**
     * 查询用户卡片信息
     *
     * @param commericalId 商户id
     * @param userId       用户id
     * @return
     */
    @Override
    public Card findByCommIdAndUserId(Long commericalId, Long userId) {
        Card card = new Card();
        if (commericalId == null || userId == null) {
            log.info("客户—根据手机号查询信息—参数有误");
            return null;
        }
        card.setCommId(commericalId);
        card.setUserId(userId);
        return cardMapper.findByCommIdAndUserId(commericalId, userId);
    }

    /**
     * 查询用户卡片列表
     *
     * @param userId 用户id
     * @return
     */
    @Override
    public List<Card> findListByUserId(Long userId) {
        return cardMapper.findListByUserId(userId);
    }

    /**
     * 查询最后卡片的卡号
     *
     * @return
     */
    @Override
    public Long findlastCardNo() {
        return cardMapper.findlastCardNo();
    }

    @Override
    public String getCardNo(String type) {
        String cardNo = null;
        Long count = 0L;
        try {
            count = this.findlastCardNo() + 1;
        } catch (Exception e) {
            log.error("卡片查询序号出错" + e.getMessage(), e);
            count = 1L;
        }
        String cardNorule = "CD" + type + new SimpleDateFormat("yyMMdd").format(new Date());
        // modify by zhiqiang.guo 解决卡id 超过8位后 StringIndexOutOfBoundsException
        String noSuffix = String.format("%08d", count);
        // String no = "0000000";
        /**
         * no.substring(0, no.length() - (count +"").trim().length()) + count;
         */
        cardNo = cardNorule + noSuffix;
        return cardNo;
    }

    /**
     * 根据卡号更新卡状态
     *
     * @param cardNo     卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     *                   卡有效标志1有效0无效
     * @return
     */
    @Override
    public BaseResponse updateCardStatus(String cardNo, String cardStatus) {
        int i = 0;
        // 将卡置为无效
        if ("0".equals(cardStatus)) {
            i = cardMapper.updateCardInvalid(cardNo);
        } else {//更新卡状态
            i = cardMapper.updateCardStatus(cardNo, cardStatus);
        }
        log.info("根据卡号{}更新卡状态{}", cardNo, i);
        if (i > 0) {
            return new BaseResponse();
        } else {
            throw new DcServiceException("操作失败");
        }
    }

    /**
     * 根据卡物理号更新卡状态
     *
     * @param cardChipNo 卡号
     * @param cardStatus 卡状态（10000未激活，10001已激活，10002卡锁定(已挂失)，10005已失效(黑名单)，10006已过期，20000离线卡已删除，20001离线卡正常）
     *                   卡有效标志1有效0无效
     * @return
     */
    @Override
    public ObjectResponse updateCardStatusByChipNo(String cardChipNo, String cardStatus) {
        log.info(">> 根据物理卡号变更卡片状态: cardChipNo={}, status={}", cardChipNo, cardStatus);
        int i = cardMapper.updateCardStatusByCardChipNo(cardChipNo, cardStatus);
        log.info("<< 变更结果: result={}", i);
        if (i > 0) {
            return new ObjectResponse<>(i);
        } else {
            throw new DcServiceException("变更卡片状态失败");
        }
    }

    /**
     * 添加卡
     *
     * @param card
     * @return
     */
    @Override
    public BaseResponse addCard(Card card) {
        // 查询卡号是否存在
        Card queryCard = cardMapper.queryCardByCardNo(card.getCardNo());

        if (queryCard != null && StringUtils.isNotBlank(queryCard.getCardNo())) {
            if (queryCard.getYxBz().equals("1")) {
                throw new DcServiceException("卡片已存在");
            }
            //先删除卡片，再添加
            cardMapper.deleteCardByCardNo(card.getCardNo());

            // disable 使用范围信息
            this.siteAuthDs.disableAll(AuthMediaType.CARD, card.getCardNo(), null, null);
        }
        //插入数据库
        long i = cardMapper.insertSelective(card);

        if (i > 0) {
            // 跟新场站使用范围
            this.updateSiteAuth(card);

            return new BaseResponse();
        } else {
            throw new DcServiceException("卡添加失败");
        }
    }

    /**
     * 批量添加卡
     *
     * @param cards
     * @return
     */
    @Override
    @Transactional
    public ObjectResponse<Integer> batchAddCard(List<Card> cards) {
        log.info(">> 批量添加卡片: list.size()={}", cards.size());

        Map<String, Object> params = new HashMap<>();
        List<CardMgnVo> cList = cardMapper.queryMgmCards(params);
        HashSet<String> cardChipNo = new HashSet<>();
        HashSet<String> cardNo = new HashSet<>();
        cList.forEach(card -> {
            cardChipNo.add(card.getCardChipNo());
            cardNo.add(card.getCardNo());
        });

        // 单张卡片时，显示错误提示使用
        EnumSet<CardValidateType> validType = EnumSet.noneOf(CardValidateType.class);

        // 过滤无效数据
        List<Card> valids = cards.stream()
            .filter(card -> {

                EnumSet<CardValidateType> isNew = isNew(card.getCardChipNo(), card.getCardNo(),
                    cardChipNo, cardNo);
                if (validType.size() == 0) {
                    validType.addAll(isNew);
                }

//                    if (isNew) {
                if (CollectionUtils.isEmpty(isNew)) {
                    // 状态未激活
                    if (card.getCardStatus() == null) {
                        card.setCardStatus(CardStatus.INACTIVE.getCode());
                        card.setCardType(CardType.UNDISTRIBUTED.getCode());
                    }

                    // 创建时间和更新时间
                    card.setCardCreateDate(new Date());
                    card.setCardUpdateDate(new Date());
                }
                return CollectionUtils.isEmpty(isNew);
            })
            .collect(Collectors.toList());
        log.info("有效卡片数量: {}", valids.size());

        if (cards.size() == 1 && valids.size() == 0 && CollectionUtils.isNotEmpty(validType)) {
            // 单张卡片时，提示详细错误原因
            String cardText = validType.stream()
                .map(CardValidateType::getDesc)
                .filter(Objects::nonNull)
                .collect(Collectors.joining("、"));
            throw new DcServiceException(cardText + "已存在");
        }

        if (valids.size() == 0) {
            log.info("<< 不存在有效卡片, 数据都已存在: list={}", cards);
            throw new DcServiceException("不存在有效卡片, 数据都已存在");
        }

        int cnt = 0;
        List<List<Card>> resList = createList(valids,
            1000);//将list按长度1W拆分，拆分后再多次调用mybatis；暂时上限1W条，按1000条拆分
        for (List<Card> list : resList) {
            cnt += cardMapper.insertBatch(list);

            // 更新场站使用范围
            list.forEach(this::updateSiteAuth);
        }
        //        int cnt = cardMapper.insertBatch(valids);

        log.info("<< 批量添加完成. 新添加卡片数量: {}", cnt);
        return new ObjectResponse<>(cnt);
    }

    /**
     * 企业平台批量添加卡
     *
     * @param cards
     * @return
     */
    @Override
    @Transactional
    public ObjectResponse<Integer> batchGrantCard(List<Card> cards) {
        log.info(">> 批量添加卡片: list.size()={}", cards.size());

        Map<String, Object> params = new HashMap<>();
        List<CardMgnVo> cList = cardMapper.queryMgmCards(params);
        Map<String, String> map = cList.stream()
            .filter(e -> e.getCardChipNo() != null && e.getCardStatus() != null).
            collect(Collectors.toMap(CardMgnVo::getCardChipNo, CardMgnVo::getCardStatus));
        Map<String, String> cardNomap = cList.stream()
            .filter(e -> e.getCardChipNo() != null && e.getCardNo() != null).
            collect(Collectors.toMap(CardMgnVo::getCardChipNo, CardMgnVo::getCardNo));

        List<String> phoneList = cards.stream().map(Card::getMobile).collect(Collectors.toList());
        List<RBlocUser> rBlocUsers = rBlocUserMapper.findByPhoneListAndBlocUserId(phoneList,
            cards.get(0).getCorpId());
        Map<String, Long> corpIdMap = rBlocUsers.stream()
            .filter(e -> e.getPhone() != null && e.getCorpOrgId() != null)
            .collect(Collectors.toMap(RBlocUser::getPhone, RBlocUser::getCorpOrgId));
        Map<String, Long> userIdMap = rBlocUsers.stream()
            .filter(e -> e.getPhone() != null && e.getUserId() != null)
            .collect(Collectors.toMap(RBlocUser::getPhone, RBlocUser::getUserId));

        // 过滤无效数据(针对物理卡号和手机号进行简单校验)
        List<Card> valids = cards.stream()
            .filter(card -> {
                //物理卡号提前需已在系统中导入，且未曾激活
                String originalCardStatus = map.get(card.getCardChipNo());
                Long corpOrgId = corpIdMap.get(card.getMobile());
                Long userId = userIdMap.get(card.getMobile());
                String cardNo = cardNomap.get(card.getCardChipNo());
                if (originalCardStatus == null ||
                    !originalCardStatus.equals(CardStatus.INACTIVE.getCode())) {
                    return false;
                }
                //需确保对应的手机号已在人员管理中绑定了相应的组织名称
                if (corpOrgId == null || userId == null || cardNo == null) {
                    return false;
                }
                card.setUserId(userId);
                card.setCardNo(cardNo);// 后面修改时需要作为条件
                return true;
            }).collect(Collectors.toList());
        log.info("有效卡片数量: {}", valids.size());

        if (valids.size() == 0) {
            log.info("<< 不存在有效卡片, 数据都已存在: list={}", cards);
            throw new DcServiceException("不存在有效卡片, 请重新上传文件");
        }

        int cnt = 0;
        log.debug("valids: {}", JsonUtils.toJsonString(valids));
        List<List<Card>> resList = createList(valids,
            1000);//将list按长度1W拆分，拆分后再多次调用mybatis；暂时上限1W条，按1000条拆分
        log.debug("resList: {}", JsonUtils.toJsonString(resList));
        for (List<Card> list : resList) {
            for (Card card : list) {
                cnt += cardMapper.updateByCardNoSelective(card);

                // 跟新场站使用范围
                this.updateSiteAuth(card);
            }
        }
        log.info("<< 批量添加完成. 新添加卡片数量: {}", cnt);
        return new ObjectResponse<>(cnt);
    }

    /**
     * 更新卡
     *
     * @param card
     * @return
     */
    @Override
    public BaseResponse updateCard(Card card) {
        if (null == card.getLocale()) {
            card.paramCheck();
        } else {
            card.essParamCheck();
        }

//        log.info(">> 处理卡片信息更新: card={}", card);
        //查询卡号是否存在
        Card queryCard = cardMapper.queryCardByCardNo(card.getCardNo());

        if (queryCard == null || StringUtils.isBlank(queryCard.getCardNo())) {
            log.info("<< 卡号不存在，更新失败，cardNo={}", card.getCardNo());
            throw new DcServiceException("卡号不存在");
        }

        if (NumberUtils.equals(card.getDeposit(), 1) && queryCard.getCorpId() != null) {
            log.info("兼容充值卡设置离线必要字段: {} -> {}", queryCard.getCorpId(),
                queryCard.getCorpId());
            card.setCorpId(queryCard.getCorpId());
        }

        // 企业账号木账号不允许绑卡
        if (queryCard.getUserId() != null) {
            UserPo cusInfo = userRoDs.getCusById(queryCard.getUserId());
            if (cusInfo != null && StringUtils.isNotEmpty(cusInfo.getPhone()) && cusInfo.getPhone()
                .startsWith("B")) {
                throw new DcServiceException("企业母账号不能绑定卡");
            }
        }

        //存入数据库
        long i = cardMapper.updateByCardNoSelective(card);

        // 商户会员的紧急卡，创建商户会员
        if (card.getCardType() != null &&
            card.getCardType() == CardType.EMERGENCY.getCode() &&
            card.getCorpId() == null &&
            !commCusRefRoDs.checkCommCusRefExists(card.getCommId(), card.getUserId())) {
            MerchantBalanceParam balanceParam = new MerchantBalanceParam();
            balanceParam.setTopCommId(commercialRoDs.getTopCommId(card.getCommId()));
            balanceParam.setSubCommId(card.getCommId());
            balanceParam.setPhone(card.getMobile());
            balanceParam.setAmount(BigDecimal.ZERO);
            balanceParam.setBalanceType(AccountType.COMMERCIAL.getCode());
            balanceParam.setRemark("商户会员绑定紧急卡新增账户");
            merchantBalanceService.insertMerchantBalance(balanceParam);
        }

        if (i > 0) {
            if (card.getCommId() == null) {
                queryCard = cardMapper.queryCardByCardNo(card.getCardNo());
                card.setCommId(queryCard.getCommId());
            }
            if (card.getCardChipNo() == null) {
                card.setCardChipNo(queryCard.getCardChipNo());
            }
            // 跟新场站使用范围
            this.updateSiteAuth(card);

            log.info("<< 更新到数据库返回成功, mapper 处理返回 num={}", i);
            return new BaseResponse();
        } else {
            log.info("<< 更新到数据库返回失败, mapper 处理返回 num={}", i);
            throw new DcServiceException("卡信息变更失败");
        }
    }

    /**
     * 根据卡号获取卡信息
     *
     * @param cardNo
     * @return
     */
    @Override
    public Card getCardByCardNo(String cardNo, Long topCommId) {
        Card card = cardMapper.getCardByCardNo(cardNo, topCommId);
        if (card != null) {
            if (card.getCorpId() != null && card.getCorpId() > 0) {
                CorpPo corpPo = corpRwDs.getCorp(card.getCorpId(), false);
                card.setCorpSettlementType(corpPo.getSettlementType());
            }
            if (CollectionUtils.isNotEmpty(card.getSiteList())) {
                card.setStations(CollectionUtils.join(card.getSiteList(), ","));
            }
        }
        return card;
    }

    /**
     * 根据卡号获取卡信息
     *
     * @param cardNo
     * @return
     */
    @Override
    public Card getCardByCardNoX(String cardNo, boolean cardExactMatch, Long topCommId,
        String siteId) {
        Card card = cardMapper.getCardByCardNoX(cardNo, cardExactMatch, topCommId, siteId);
        if (card != null) {
            if (card.getCorpId() != null && card.getCorpId() > 0) {
                CorpPo corpPo = corpRwDs.getCorp(card.getCorpId(), false);
                card.setCorpSettlementType(corpPo.getSettlementType());
            }
            if (CollectionUtils.isNotEmpty(card.getSiteList())) {
                card.setStations(CollectionUtils.join(card.getSiteList(), ","));
            }
        }
        return card;
    }

    @Override
    public List<Card> getCardByCorIdAndUserId(Long corpId, Long userId) {
        return cardMapper.getCardByCorIdAndUserId(corpId, userId);
    }

    /**
     * 根据Chip卡号查询卡
     *
     * @param cardChipNo
     * @return
     */
    @Override
    public Card queryCardByCardChipNo(String cardChipNo) {
        return cardMapper.queryCardByCardChipNo(cardChipNo);
    }

    /**
     * 根据卡号查询卡,跟getCardByCardNo相比，不做前面0匹配的相似查询
     *
     * @param cardNo 卡号
     * @return
     */
    public Card queryCardByCardNo(String cardNo) {
        return cardMapper.queryCardByCardNo(cardNo);
    }

    @Override
    public AccountInfoVo getAccountByCardNo(String cardNo, Long topCommId) {
        log.info("getAccountByCardNo cardNo: {} topCommId: {}", cardNo, topCommId);
        AccountInfoVo res = new AccountInfoVo();
        Card card = cardMapper.getCardByCardNo(cardNo, topCommId);
        IotAssert.isNotNull(card, "获取卡账户信息失败");
        if (CardType.EMERGENCY.getCode() == card.getCardType()) {
            // 紧急卡
            if (card.getCorpId() != null && card.getCorpId() > 0) {
                //集团账户扣钱
                RBlocUser req = new RBlocUser();
                req.setPhone(card.getMobile())
                    .setBlocUserId(card.getCorpId())
                    .setUserId(card.getUserId())
                    .setStart(0L)
                    .setSize(1);
                List<RBlocUser> rBlocUsers = rBlocUserMapper.findByCondition(req);
                IotAssert.isTrue(rBlocUsers.size() == 1, "获取卡账户信息失败");
                res.setPayAccountId(rBlocUsers.get(0).getId());
                res.setEnable(rBlocUsers.get(0).getStatus());
                res.setDefaultPayType(String.valueOf(PayAccountType.CREDIT.getCode()));
            } else { // 紧急卡仅可绑定: 商户会员/企业客户，下面处理商户会员的逻辑
                CommCusRef req = new CommCusRef();
                req.setStart(0L);
                req.setSize(1);
                req.setUserId(card.getUserId());
                req.setCommId(card.getCommId());
                ListResponse<CommCusRef> commCusRefListResponse = commCusRefRoDs.findByCondition(
                    req);
                IotAssert.isTrue(
                    commCusRefListResponse != null && commCusRefListResponse.getData() != null
                        && commCusRefListResponse.getData().size() == 1, "获取卡账户信息失败");
                res.setEnable(commCusRefListResponse.getData().get(0).getEnable());
                // 商户会员: 卡绑定时选择的商户为支付账号(2020-09-04)
                res.setPayAccountId(card.getCommId());
                res.setDefaultPayType(String.valueOf(PayAccountType.COMMERCIAL.getCode()));
            }
        } else if (CardType.OFFLINE_CARD.getCode() == card.getCardType() ||
            NumberUtils.equals(card.getDeposit(), 1)) { // 兼容卡沿用离线卡处理逻辑，兼容卡须绑定授信，所以corpId不为空
            // 离线卡
            if (card.getCorpId() != null && card.getCorpId() > 0
                && card.getRBlocUserId() != null && card.getRBlocUserId() > 0) {
                res.setDefaultPayType(String.valueOf(OrderPayType.BLOC.getCode()))
                    .setPayAccountId(card.getRBlocUserId())
                    .setEnable(1) // TODO: 2021/4/21 离线卡暂不判断扣款账户是否有效
                ;
            } else {
                UserVo userVo = userMapper.findUserInfoByUid(card.getUserId(), topCommId);
                res.setDefaultPayType(String.valueOf(userVo.getDefaultPayType()))
                    .setPayAccountId(userVo.getBalanceId())
                    .setEnable(1) // TODO: 2021/4/21 离线卡暂不判断扣款账户是否有效
                ;
            }
        } else {
            // nothing to do
        }
        return res;
    }

    @Override
    public List<WhiteCardCfgVo> queryCardByCardNoList(List<String> cardNo) {
        if (CollectionUtils.isEmpty(cardNo)) {
            return Collections.emptyList();
        }
        return cardMapper.queryCardByCardNoList(cardNo);
    }


    /**
     * 更新卡余额
     *
     * @param card
     * @return
     */
    @Override
    public int updateCardBalance(Card card) {
        log.info("更新卡余额");
        int count = cardMapper.updateCardBalance(card.getCardNo(), card.getCardDenomination());
        return count;
    }

    /**
     * 根据ID查询卡
     *
     * @param id 卡id
     * @return
     */
    @Override
    public ObjectResponse<Card> queryCardById(Long id) {
        Card card = cardMapper.queryCardById(id);

        return new ObjectResponse<>(card);
    }

    /**
     * 根据条件查询卡(得到集团客户名下的卡) 可继续补充条件
     *
     * @return
     */
    @Override
    public ListResponse<Card> queryCardByCondition(CardRequest cardRequest) {
        List<Card> card = cardMapper.queryCardByCondition(cardRequest);
        return new ListResponse<>(card);
    }

    /**
     * 校验卡片有效性
     *
     * @param physicalNo 物理卡号
     * @param logicNo    逻辑卡号
     * @return 返回卡片创建失败原因
     */
    private EnumSet<CardValidateType> isNew(String physicalNo, String logicNo,
        HashSet<String> cardChipNo, HashSet<String> cardNo) {
        //        log.info(">> 判断卡片信息有效性: physicalNo={}, logicNo={}", physicalNo, logicNo);
//        if (StringUtils.isEmpty(physicalNo) || StringUtils.isEmpty(logicNo)) {
//            //            log.info("<< 卡片有效性校验结果: {}, 物理卡号为空或逻辑卡号为空", false);
//            return false;
//        }
        EnumSet<CardValidateType> cardValidateTypes = EnumSet.noneOf(CardValidateType.class);
        if (StringUtils.isBlank(physicalNo)) {
            cardValidateTypes.add(CardValidateType.PhysicsBlank);
        }
        if (StringUtils.isBlank(logicNo)) {
            cardValidateTypes.add(CardValidateType.LogicalBlank);
        }

        if (CollectionUtils.isNotEmpty(cardValidateTypes)) {
            return cardValidateTypes;
        }

//        boolean result = cardChipNo.add(physicalNo) && cardNo.add(logicNo);
        if (!cardChipNo.add(physicalNo)) {
            cardValidateTypes.add(CardValidateType.PhysicsExist);
        }
        if (!cardNo.add(logicNo)) {
            cardValidateTypes.add(CardValidateType.LogicalExist);
        }
        //        log.info("<< 卡片有效性校验结果: {}, 查询数据结果", result);
        return cardValidateTypes;
    }

    private ExcelCheckResult checkCardFormat(String physicalNo, String logicNo, String cardKey) {

        // 有效性检测
        if (StringUtils.isEmpty(physicalNo) || StringUtils.isEmpty(logicNo) || StringUtils.isEmpty(
            cardKey)) {
            //            log.info("<< 物理卡号/逻辑卡号/密钥为空, physicalNo={}, logicNo={}, cardKey={}", physicalNo, logicNo, cardKey);
            return ExcelCheckResult.FORMAT_INVALID;
        }

        // 默认物理卡号和逻辑卡号需要全为数字
        if (!RegularExpressionUtil.englishNumberAll(physicalNo, null, null)
            || !RegularExpressionUtil.englishNumberAll(logicNo, null, null)) {
            //            log.info("<< 物理卡号或逻辑卡号不全为数字, physicalNo={}, logicNo={}", physicalNo, logicNo);
            return ExcelCheckResult.FORMAT_INVALID;
        }

        /*
        if (logicNo.startsWith("0")) {
            return ExcelCheckResult.FORMAT_INVALID;
        }
        */

        if (physicalNo.length() > 30 || logicNo.length() > 20 || cardKey.length() > 30) {
            return ExcelCheckResult.FORMAT_INVALID;
        }

        //        log.info("<< 卡片有效性校验结果: 可作为新卡片");
        return ExcelCheckResult.NEW_CARD;
    }

    private ExcelCheckResult checkCorpCardFormat(String physicalNo, String cardName, String carNo,
        String lineNum, String carNum, String phone, String carDepart) {

        // 有效性检测
        if (StringUtils.isEmpty(physicalNo) || StringUtils.isEmpty(phone)) {
            return ExcelCheckResult.CORP_CARD_FORMAT_INVALID;
        }

        // 物理卡号规则1-20位数字
        if (!RegularExpressionUtil.digitAll(physicalNo) || physicalNo.length() > 20) {
            return ExcelCheckResult.PHYSICAL_FORMAT_INVALID;
        }

        // 卡名称要求输入在20个汉字之内，只支持字母、数字、汉字
        if (StringUtils.isNotEmpty(cardName)) {
            if (!RegularExpressionUtil.chineseEnglishNumberAll(cardName)
                || cardName.length() > 20) {
                return ExcelCheckResult.CARD_NAME_FORMAT_INVALID;
            }
        }

        // 车牌号标准输入7或者8位车牌号，不允许存在空格
        if (StringUtils.isNotEmpty(carNo)) {
            if (!RegularExpressionUtil.isProductDemandCarNo(carNo) || !(carNo.length() == 7
                || carNo.length() == 8)) {
                return ExcelCheckResult.CAR_NO_FORMAT_INVALID;
            }
        }

        // 线路要求输入20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(lineNum)) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(lineNum, 1, 20)) {
                return ExcelCheckResult.LINE_NUM_FORMAT_INVALID;
            }
        }

        // 车队名称要求输入20个字符，只支持字母、数字、汉字、常规字符
        if (StringUtils.isNotEmpty(carDepart)) {
            if (!RegularExpressionUtil.chineseEnglishNumberCharacterAll(carDepart, 1, 20)) {
                return ExcelCheckResult.CAR_DEPART_FORMAT_INVALID;
            }
        }

        // 车辆自编号要求输入1~17位数字、字母组成
        if (StringUtils.isNotEmpty(carNum)) {
            if (!RegularExpressionUtil.englishNumberAll(carNum, 1, 17)) {
                return ExcelCheckResult.CAR_NUM_FORMAT_INVALID;
            }
        }
        return ExcelCheckResult.BIND_CARD;
    }

    /**
     * 解析 excel 文件
     *
     * @param list
     * @return
     */
    @Override
    public ObjectResponse parseExcel(List<List<Object>> list) {
        log.info(">> 解析 excel: list.size={}", list.size());
        try {
            List<Card4ManagerVo> cardList = new ArrayList<>(); //excel导入的card集合
            List<Card4ManagerVo> valid = new ArrayList<>(); // 有效
            List<Card4ManagerVo> invalid = new ArrayList<>(); // 无效

            list.forEach(card -> {
                Card4ManagerVo vo = new Card4ManagerVo();
                vo.setCardChipNo(
                    null == card.get(0) ? null : card.get(0).toString().trim()); // 物理卡号
                vo.setCardNo(
                    null == card.get(1) ? null : card.get(1).toString().trim());     // 逻辑卡号
                vo.setCardKey(
                    null == card.get(2) ? null : card.get(2).toString().trim());    // 卡读写密码

                ExcelCheckResult checkResult = checkCardFormat(vo.getCardChipNo(), vo.getCardNo(),
                    vo.getCardKey());
                if (checkResult.getCode() != ExcelCheckResult.NEW_CARD.getCode()) {
                    vo.setDetail(checkResult.getDesc());
                    invalid.add(vo);
                } else {
                    valid.add(vo);
                }
                cardList.add(vo);
            });
            HashSet<String> cardChipNo = new HashSet<>();
            HashSet<String> cardNo = new HashSet<>();
            HashSet<String> cardChipNoLst = new HashSet<>();
            HashSet<String> cardNoLst = new HashSet<>();
            CardMgnListVo cardMgnListVo = checkCardInExcel(cardChipNo, cardNo, valid, invalid);
            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
            cardMgnListVo.getInvalid().forEach(card4ManagerVo -> {
                cardChipNoLst.add(card4ManagerVo.getCardChipNo());
                cardNoLst.add(card4ManagerVo.getCardNo());
            });
            cardMgnListVo = checkCardInExcel(cardChipNoLst, cardNoLst, cardMgnListVo.getValid(),
                invalid);

            cardMgnListVo = checkCardInDatebase(cardMgnListVo.getValid(), invalid);
            List<Card4ManagerVo> validLst = cardMgnListVo.getValid();
            List<Card4ManagerVo> invalidLst = cardMgnListVo.getInvalid();
            log.info("<< 解析结果: 有效记录数={}, 无效记录数={}", validLst.size(),
                invalidLst.size());

            return new ObjectResponse<>(new HashMap<String, List<Card4ManagerVo>>() {{
                put("valid", validLst);
                put("invalid", invalidLst.size() > 100 ? invalidLst.subList(0, 100) : invalidLst);
            }});
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }
    }

    @Override
    public ObjectResponse parseCorpCardExcel(List<List<Object>> list, Long corpId) {
        log.info(">> 解析企业平台Card excel文件");
        try {
            List<Card4ManagerVo> cardList = new ArrayList<>(); //excel导入的card集合
            List<Card4ManagerVo> valid = new ArrayList<>(); // 有效
            List<Card4ManagerVo> invalid = new ArrayList<>(); // 无效

            list.forEach(card -> {

                Card4ManagerVo vo = new Card4ManagerVo();
                vo.setCardChipNo(
                    null == card.get(0) ? null : card.get(0).toString().trim()); // 物理卡号
                vo.setCardName(
                    null == card.get(1) ? null : card.get(1).toString().trim());     // 卡名称
                vo.setCarNo(null == card.get(2) ? null : card.get(2).toString().trim());    // 车牌号
                vo.setCarDepart(
                    null == card.get(3) ? null : card.get(3).toString().trim());    //车队名称
                vo.setLineNum(null == card.get(4) ? null : card.get(4).toString().trim());    // 线路
                vo.setCarNum(
                    null == card.get(5) ? null : card.get(5).toString().trim());    // 车辆自编号
                vo.setMobile(null == card.get(6) ? null : card.get(6).toString().trim());    // 手机号

                ExcelCheckResult checkResult = checkCorpCardFormat(vo.getCardChipNo(),
                    vo.getCardName(), vo.getCarNo(), vo.getLineNum(), vo.getCarNum(),
                    vo.getMobile(), vo.getCarDepart());

                if (checkResult.getCode() != ExcelCheckResult.BIND_CARD.getCode()) {
                    vo.setDetail(checkResult.getDesc());
                    invalid.add(vo);
                } else {

                    valid.add(vo);
                }
                cardList.add(vo);
            });
            HashSet<String> cardChipNo = new HashSet<>();
            HashSet<String> cardChipNoLst = new HashSet<>();
            CardMgnListVo cardMgnListVo = checkCorpCardInExcel(cardChipNo, valid, invalid);
            log.info("cardMgnListVo: {}", JsonUtils.toJsonString(cardMgnListVo));
            //相同数据第一次add的时候是能插入HashSet的，需将这部分数据也标记为重复数据
            cardMgnListVo.getInvalid().forEach(card4ManagerVo -> {
                cardChipNoLst.add(card4ManagerVo.getCardChipNo());
            });
            cardMgnListVo = checkCorpCardInExcel(cardChipNoLst, cardMgnListVo.getValid(), invalid);
            log.info("cardMgnListVo: {}", JsonUtils.toJsonString(cardMgnListVo));
            cardMgnListVo = checkCorpCardInDatebase(cardMgnListVo.getValid(), invalid, corpId);
            log.info("cardMgnListVo: {}", JsonUtils.toJsonString(cardMgnListVo));
            List<Card4ManagerVo> validLst = cardMgnListVo.getValid();
            List<Card4ManagerVo> invalidLst = cardMgnListVo.getInvalid();
            log.info("<< 解析结果: 有效记录数={}, 无效记录数={}", validLst.size(),
                invalidLst.size());

            return new ObjectResponse<>(new HashMap<String, List<Card4ManagerVo>>() {{
                put("valid", validLst);
                put("invalid", invalidLst);
            }});
        } catch (DcServiceException e) {
            throw new DcServiceException(e.getMessage());
        } catch (Exception e) {
            log.warn("<< 解析excel文件异常. message = {}", e.getMessage(), e);
            throw new DcServiceException("解析excel文件异常，请求确认文件内容.");
        }
    }

    /**
     * 校验数据是否重复
     *
     * @param cardList 需要判断是否重复的数据
     * @param invalid  无效的数据
     * @return
     */
    public CardMgnListVo checkCardInDatebase(List<Card4ManagerVo> cardList,
        List<Card4ManagerVo> invalid) {

        Map<String, Object> params = new HashMap<>();
        List<CardMgnVo> cList = cardMapper.queryMgmCards(params);
        HashSet<String> cardChipNo = new HashSet<>();
        HashSet<String> cardNo = new HashSet<>();
        cList.forEach(card -> {
            cardChipNo.add(card.getCardChipNo());
            cardNo.add(card.getCardNo());
        });
        CardMgnListVo cardMgnListVo = new CardMgnListVo();
        List<Card4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        cardList.forEach(card -> {
            ExcelCheckResult checkResult = null;
            if (cardChipNo.add(card.getCardChipNo())) {
                checkResult = ExcelCheckResult.NEW_CARD;
            } else {
                checkResult = ExcelCheckResult.PHYSICAL_EXIST;
            }
            if (checkResult.getCode() == ExcelCheckResult.NEW_CARD.getCode()) {
                if (cardNo.add(card.getCardNo())) {
                    checkResult = ExcelCheckResult.NEW_CARD;
                } else {
                    checkResult = ExcelCheckResult.LOGIC_EXIST;
                }
            }
            card.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.NEW_CARD.getCode()) {
                invalid.add(card);
            } else {
                valid.add(card);
            }
        });
        cardMgnListVo.setValid(valid);
        cardMgnListVo.setInvalid(invalid);
        return cardMgnListVo;
    }

    /**
     * 校验数据
     *
     * @param cardList 需要判断的数据
     * @param invalid  无效的数据
     * @return
     */
    public CardMgnListVo checkCorpCardInDatebase(List<Card4ManagerVo> cardList,
        List<Card4ManagerVo> invalid, Long corpId) {
        CardMgnListVo cardMgnListVo = new CardMgnListVo();
        List<Card4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        if (CollectionUtils.isEmpty(cardList)) {
            cardMgnListVo.setValid(valid);
            cardMgnListVo.setInvalid(invalid);
            return cardMgnListVo;
        }
        Map<String, Object> params = new HashMap<>();
        List<CardMgnVo> cList = cardMapper.queryMgmCards(params);
        Map<String, String> map = cList.stream()
            .filter(e -> StringUtils.isNotEmpty(e.getCardStatus()))
            .collect(Collectors.toMap(CardMgnVo::getCardChipNo, CardMgnVo::getCardStatus));

        List<String> phoneList = cardList.stream().map(Card4ManagerVo::getMobile)
            .collect(Collectors.toList());
        List<RBlocUser> rBlocUsers = rBlocUserMapper.findByPhoneListAndBlocUserId(phoneList,
            corpId);
        Map<String, Long> stringLongMap = rBlocUsers.stream()
            .collect(Collectors.toMap(RBlocUser::getPhone, RBlocUser::getCorpOrgId));

        // 物理卡号需提前已在系统中导入，且未曾激活
        // 需确保对应的手机号已在人员管理中绑定了相应的组织名称
        cardList.forEach(card -> {
            ExcelCheckResult checkResult = null;
            String cardStatus = map.get(card.getCardChipNo());
            Long corpOrgId = stringLongMap.get(card.getMobile());
            if (cardStatus == null) {
                checkResult = ExcelCheckResult.PHYSICAL_NOT_EXIST;
            } else if (!cardStatus.equals(CardStatus.INACTIVE.getCode())) {
                checkResult = ExcelCheckResult.PHYSICAL_NOT_INACTIVE;
            } else if (!(corpOrgId != null && corpOrgId > 0)) {
                checkResult = ExcelCheckResult.PHONE_NOT_BIND_ORG;
            } else {
                checkResult = ExcelCheckResult.BIND_CARD;
            }
            card.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.BIND_CARD.getCode()) {
                invalid.add(card);
            } else {
                valid.add(card);
            }
        });
        cardMgnListVo.setValid(valid);
        cardMgnListVo.setInvalid(invalid);
        return cardMgnListVo;
    }

    /**
     * 校验数据是否重复
     *
     * @param cardChipNoLst 物理卡号的样本数据
     * @param cardNoLst     逻辑卡号的样本的数据
     * @param cardList      需要判断是否重复的数据
     * @param invalid       无效的数据
     * @return
     */
    public CardMgnListVo checkCardInExcel(HashSet<String> cardChipNoLst, HashSet<String> cardNoLst,
        List<Card4ManagerVo> cardList, List<Card4ManagerVo> invalid) {
        CardMgnListVo cardMgnListVo = new CardMgnListVo();
        List<Card4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        cardList.forEach(card -> {
            ExcelCheckResult checkResult = null;
            if (cardChipNoLst.add(card.getCardChipNo())) {
                checkResult = ExcelCheckResult.NEW_CARD;
            } else {
                checkResult = ExcelCheckResult.EXCEL_PHYSICAL_EXIST;
            }
            if (checkResult.getCode() == ExcelCheckResult.NEW_CARD.getCode()) {
                if (cardNoLst.add(card.getCardNo())) {
                    checkResult = ExcelCheckResult.NEW_CARD;
                } else {
                    checkResult = ExcelCheckResult.EXCEL_LOGIC_EXIST;
                }
            }
            card.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.NEW_CARD.getCode()) {
                invalid.add(card);
            } else {
                valid.add(card);
            }
        });
        cardMgnListVo.setValid(valid);
        cardMgnListVo.setInvalid(invalid);
        return cardMgnListVo;
    }

    /**
     * 校验数据是否重复
     *
     * @param cardChipNoLst 物理卡号的样本数据
     * @param cardList      需要判断是否重复的数据
     * @param invalid       无效的数据
     * @return
     */
    public CardMgnListVo checkCorpCardInExcel(HashSet<String> cardChipNoLst,
        List<Card4ManagerVo> cardList, List<Card4ManagerVo> invalid) {
        CardMgnListVo cardMgnListVo = new CardMgnListVo();
        List<Card4ManagerVo> valid = new ArrayList<>(); // 新的有效数据集合

        cardList.forEach(card -> {
            ExcelCheckResult checkResult = null;
            if (cardChipNoLst.add(card.getCardChipNo())) {
                checkResult = ExcelCheckResult.BIND_CARD;
            } else {
                checkResult = ExcelCheckResult.EXCEL_PHYSICAL_EXIST;
            }
            card.setDetail(checkResult.getDesc());
            if (checkResult.getCode() != ExcelCheckResult.BIND_CARD.getCode()) {
                invalid.add(card);
            } else {
                valid.add(card);
            }
        });
        cardMgnListVo.setValid(valid);
        cardMgnListVo.setInvalid(invalid);
        return cardMgnListVo;
    }

    /**
     * 根据逻辑卡号查询物理卡号
     *
     * @param map
     * @return
     */
    @Override
    public ObjectResponse selectShipCardNoByCardNos(Map map) {
        List<Card> cardDtos = cardMapper.selectShipCardNoByCardNos(map);
        Map<String, String> resultMap = new HashMap<>();
        if (cardDtos != null && cardDtos.size() > 0) {
            cardDtos.stream().forEach(cardDto -> {
                resultMap.put(cardDto.getCardNo(), cardDto.getCardChipNo());
            });
        }
        return new ObjectResponse<>(resultMap);
    }

    @Override
    public ObjectResponse getCardChipNoByCardNo(String cardNo) {
        String cardChipNoByCardNo = cardMapper.getCardChipNoByCardNo(cardNo);
        return new ObjectResponse<>(cardChipNoByCardNo);
    }

    /**
     * 查询站点下的紧急充电卡 备注：isAbandon 是否弃用 isResetPass 是否重置密码
     *
     * @return
     */
    public List<WhiteCardDto> queryWhiteCardDtoMapList(WhiteCardRequest whiteCardRequest) {
        // 获取紧急充电卡对应站点ID
        String cardChipNo = whiteCardRequest.getCardChipNo();
        Boolean isAbandon = whiteCardRequest.getIsAbandon();
        Boolean isResetPass = whiteCardRequest.getIsResetPass();

        Card card = null;
        if (StringUtils.isNotBlank(cardChipNo)) {
            card = cardMapper.queryCardByCardChipNo(cardChipNo);
            if (card == null || StringUtils.isBlank(card.getStations())) {
                throw new DcServiceException("紧急充电卡记录不存在或未绑定场站");
            }
            if (whiteCardRequest.getIsAbandon()
                && CardStatus.DELETED.getCode().equals(card.getCardStatus())) {
                throw new DcServiceException("紧急充电卡记录已弃用，请检查");
            }
        }

        // 将下列状态的卡排除在外
        List<String> excludeCardStatusList = Arrays.asList(CardStatus.INACTIVE.getCode(),
            CardStatus.LOCK.getCode(),
            CardStatus.FAILURE.getCode(), CardStatus.EXPIRED.getCode(),
            CardStatus.DELETED.getCode());
        // 场站下的紧急充电卡列表
        List<WhiteCardDto> whiteCardDtos = cardMapper.queryWhiteCardDtoMapList(
            null,
            card == null ? null : card.getStations(),
            null, null, null, excludeCardStatusList);
        whiteCardDtos.forEach(wcd -> {
            List<WhiteCard> whiteCardList = wcd.getWhiteCardList();
            List<WhiteCardEvse> whiteCardEvses = new ArrayList<>();
            if (whiteCardList != null && whiteCardList.size() > 0) {
                whiteCardList.forEach(wc -> {
                    WhiteCardEvse whiteCardEvse = new WhiteCardEvse();
                    if (!isAbandon) {
                        String passwd = UUIDUtils.getRandom(true, 6);
                        if (whiteCardRequest.getIsResetPass()) {
                            // 重置密码
                            if (wc.getCardChipNo().equals(cardChipNo)) {
                                wc.setPasscode(passwd);
                            }
                        } else if (StringUtils.isBlank(wc.getPasscode())) {
                            // 有密码下发无密码生成6位密码下发
                            wc.setPasscode(passwd);
                        }
                        // 回写下发密码
                        cardMapper.updateActivationCode(wc);

                        // @Nathan 这里不需要更新场站使用范围信息
                    }
                    // 回写卡状态下发中
                    cardMapper.updateCardStatus(wc.getCardNumber(), CardStatus.ISSUE.getCode());
                    whiteCardEvse.setWhiteCardNo(wc.getCardChipNo());
                    whiteCardEvse.setCardNo(wc.getCardNumber());
                    // 紧急充电卡 6:弃用中 3:下发中
                    whiteCardEvse.setSendStatus(isAbandon && wc.getCardChipNo().equals(cardChipNo) ?
                        WhiteCardEvseStatus.ABANDON_PROESS.getCode()
                        : WhiteCardEvseStatus.SEND_PROESS.getCode());
                    // 拟下发密码 下发成功后会将其回写到卡密码字段
                    whiteCardEvse.setPassWordTmp(wc.getPasscode());
                    whiteCardEvses.add(whiteCardEvse);
                });
                wcd.setWhiteCardEvses(whiteCardEvses);

                // 剔除弃用的紧急充电卡
                if (isAbandon) {
                    List<WhiteCard> wclist = whiteCardList.stream()
                        .filter(wc -> !wc.getCardChipNo().equals(cardChipNo))
                        .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(wclist)) {
                        // 生成弃用的卡号
                        String abandonCardNumber = "0".concat(UUIDUtils.getRandom(true, 9));
                        int abandonCardCount = cardMapper.queryCountByCardNo(abandonCardNumber);
                        while (abandonCardCount != 0) {
                            // 卡号存在 重新生成
                            abandonCardNumber = "0".concat(UUIDUtils.getRandom(true, 9));
                            abandonCardCount = cardMapper.queryCountByCardNo(abandonCardNumber);
                        }
                        WhiteCard wc = new WhiteCard();
                        wc.setPasscode("000000");
                        wc.setCardChipNo("00000000");
                        wc.setCardNumber(abandonCardNumber);
                        wclist.add(wc);
                    }
                    wcd.setWhiteCardList(wclist);
                }
            }
        });
        return whiteCardDtos;
    }

    /**
     * 查询站点下的紧急充电卡 用于多场站下多卡弃用查询DTO时，条件必须为(cardChipNoList必传，siteList必传,isAbandon必传为true)
     * 用于单场站下发查询DTO时，条件为(cardChipNoList不传，site必传,isAbandon为false) 不支持重置密码 不支持单卡弃用
     *
     * @return
     */
    @Override
    public List<WhiteCardDto> queryWhiteCardDtoBySiteList(WhiteCardRequest whiteCardRequest) {
        DcAssertUtil.isNotNull(whiteCardRequest, "参数为空");
        //将下列状态的卡排除在外
        List<String> excludeCardStatusList = Arrays.asList(CardStatus.INACTIVE.getCode(),
            CardStatus.LOCK.getCode(),
            CardStatus.FAILURE.getCode(), CardStatus.EXPIRED.getCode(),
            CardStatus.DELETED.getCode());

        //无需commIds,cardChipNo
        //site可为null，siteList可为null
        // device中调用这个接口
        List<WhiteCardDto> whiteCardDtos = cardMapper.queryWhiteCardDtoMapList(
            null,
            whiteCardRequest.getSite(),
            whiteCardRequest.getSiteList(),
            null, null, excludeCardStatusList);
        whiteCardDtos.forEach(wcd -> {
            List<WhiteCard> whiteCardList = wcd.getWhiteCardList();
            List<WhiteCardEvse> whiteCardEvses = new ArrayList<>();

            if (whiteCardList != null && whiteCardList.size() > 0) {
                whiteCardList.forEach(wc -> {
                    WhiteCardEvse whiteCardEvse = new WhiteCardEvse();
                    if (whiteCardRequest.getCardChipNoList() == null
                        || whiteCardRequest.getCardChipNoList().isEmpty()) {
                        whiteCardEvse.setSendStatus(3);
                    } else {
                        whiteCardRequest.getCardChipNoList().stream().forEach(c -> {
                            // 紧急充电卡 6:弃用中 3:下发中
                            whiteCardEvse.setSendStatus(whiteCardRequest.getIsAbandon()
                                && wc.getCardChipNo().equals(c) ? 6 : 3);
                        });
                    }

                    whiteCardEvse.setWhiteCardNo(wc.getCardChipNo());
                    whiteCardEvse.setCardNo(wc.getCardNumber());
                    // 拟下发密码 下发成功后会将其回写到卡密码字段
                    whiteCardEvse.setPassWordTmp(wc.getPasscode());
                    whiteCardEvses.add(whiteCardEvse);
                });
                wcd.setWhiteCardEvses(whiteCardEvses);
                // 剔除弃用的紧急充电卡
                List<WhiteCard> wclist = null;
                if (whiteCardRequest.getCardChipNoList() == null
                    || whiteCardRequest.getCardChipNoList().isEmpty()) {
                    wclist = whiteCardList;
                } else {
                    wclist = whiteCardList.stream().filter(wc ->
                        !(whiteCardRequest.getCardChipNoList().contains(wc.getCardChipNo()))
                    ).collect(Collectors.toList());
                }
                if (wclist.isEmpty()) {
                    // 生成弃用的卡号
                    String abandonCardNumber = "0".concat(UUIDUtils.getRandom(true, 9));
                    int abandonCardCount = cardMapper.queryCountByCardNo(abandonCardNumber);
                    while (abandonCardCount != 0) {
                        // 卡号存在 重新生成
                        abandonCardNumber = "0".concat(UUIDUtils.getRandom(true, 9));
                        abandonCardCount = cardMapper.queryCountByCardNo(abandonCardNumber);
                    }
                    WhiteCard wc = new WhiteCard();
                    wc.setPasscode("000000");
                    wc.setCardNumber(abandonCardNumber);
                    wclist.add(wc);
                }
                wcd.setWhiteCardList(wclist);
            }
        });
        return whiteCardDtos;
    }

    /**
     * 根据卡片id（用,分割），更新紧急充电卡状态
     *
     * @param cardNos
     * @param status
     */
    @Override
    public int updBatchCardStatusByNos(List<String> cardNos, int status) {
        log.info("更新紧急充电卡下发状态, {}, {}", cardNos, status);
        AssertUtil.isFalse(CollectionUtils.isEmpty(cardNos), "卡片NO不能为空");
        // 更新紧急充电卡状态
        int count = cardMapper.updateUrgencyCard(cardNos, status + "", 1, null);
        log.info("更新了{}张卡片。", count);
        return count;
    }

    /**
     * 根据卡片id（用,分割），更新卡片状态 0x00为下发成功，其他为失败
     *
     * @param cardIds
     * @param status
     */
    @Override
    public int updateUrgencyCardStatus(String evseNo, String cardIds, int status) {
        log.info("尝试更新紧急充电卡下发状态, {}, {}", cardIds, status);
        AssertUtil.isFalse(StringUtils.isBlank(cardIds), "卡片id不能为空");

        List<String> cardList = Arrays.asList(cardIds.split(","));
        List<String> cardIdList = cardList.stream().filter(card -> !card.contains(":"))
            .collect(Collectors.toList());
        List<String> abandonCardIdList = cardList.stream().filter(card -> card.contains(":"))
            .map(card -> card.split(":")[0]).collect(Collectors.toList());

        CardStatus cardStatus = (status == 1 ? CardStatus.ISSUE_SUCCESS /*下发成功*/
            : CardStatus.ISSUE_FAIL /*下发失败*/);

        if (cardStatus == CardStatus.ISSUE_SUCCESS) {
            // 下发成功，将移动t_r_white_card_evse该条记录的passwordTmp到password
            log.info("下发成功,开始更新紧急充电卡-桩关系表");
            boxSettingFeignClient.updateStatusOnEvseIdAndStatus(evseNo,
                BoxSettingFeignClient.STATUS_DELTA_OK);
        } else if (cardStatus == CardStatus.ISSUE_FAIL) {
            log.info("下发失败,开始更新紧急充电卡-桩关系表");
            boxSettingFeignClient.updateStatusOnEvseIdAndStatus(evseNo,
                BoxSettingFeignClient.STATUS_DELTA_FAIL);
        }
        int ret = 0;
        if (!cardIdList.isEmpty()) {
            //当前紧急卡状态处于【下发成功】时，不响应任何更新
            int count = cardMapper.updateUrgencyCard(cardIdList, cardStatus.getCode(), 1,
                CardStatus.ISSUE_SUCCESS.getCode());
            ret = ret + count;
        }
        if (!CollectionUtils.isEmpty(abandonCardIdList)) {
            //当前紧急卡状态处于【删除】时，不响应任何更新
            int count = cardMapper.updateUrgencyCard(abandonCardIdList,
                CardStatus.DELETED.getCode(), 1, CardStatus.DELETED.getCode());
            ret = ret + count;
        }
        log.info("更新了{}张卡片。cardIdList = {},abandonCardIdList = {}", ret, cardIdList,
            abandonCardIdList);
        return ret;
    }

    /**
     * 多场站下多卡弃用，条件必须为(cardChipNoList必传，siteList必传,isAbandon为true) 不支持重置密码 不支持单卡弃用
     *
     * @param whiteCardRequest
     * @return
     */
    @Override
    public BaseResponse sendBatchBySiteList(WhiteCardRequest whiteCardRequest) {
        List<WhiteCardDto> whiteCardDtoList = this.queryWhiteCardDtoBySiteList(whiteCardRequest);
        if (CollectionUtils.isEmpty(whiteCardDtoList)) {
            log.info("紧急充电卡记录不存在, 不需要下发");
            return RestUtils.success();
        }

        ObjectResponse<Map<String, Object>> objectResponse = bsBoxSettingFeignClient.sendBatchWhiteCards(
            whiteCardDtoList);
        FeignResponseValidate.check(objectResponse);
        return new BaseResponse();
    }

    @Override
    public List<String> queryCardNoListByBlocUserId(Long blocUserId) {
        return cardMapper.queryCardNoListByBlocUserId(blocUserId);
    }

    @Override
    public List<Card> selectBlocUserNameByCardNos(List<String> cardNos) {
        return cardMapper.selectBlocUserNameByCardNos(cardNos);
    }

    /**
     * 根据条件批量查询场站下的有效紧急卡数量
     *
     * @param siteIdList
     * @return
     */
    @Override
    public ListResponse<SiteCardCount> findUrgencyCardNumCount(List<String> siteIdList) {
        if (CollectionUtils.isEmpty(siteIdList)) {
            return RestUtils.buildListResponse(new ArrayList<>());
        }

        CardRequest request = new CardRequest();
        request.setCardType((long) CardType.EMERGENCY.getCode());
        List<String> excludeCardStatusList = new ArrayList<>();
        excludeCardStatusList.add(CardStatus.INACTIVE.getCode());
        excludeCardStatusList.add(CardStatus.LOCK.getCode());
        excludeCardStatusList.add(CardStatus.FAILURE.getCode());
        excludeCardStatusList.add(CardStatus.EXPIRED.getCode());
        excludeCardStatusList.add(CardStatus.DELETED.getCode());
        request.setExcludeCardStatusList(excludeCardStatusList);
        request.setSiteIdList(siteIdList);
        List<SiteCardCount> siteVoList = cardMapper.findUrgencyCardNumCount(request);
        return new ListResponse<>(siteVoList);
    }

    @Override
    public List<CardDetailVO> queryOnlineCardsByUser(OldPageParam page, Long userId,
        List<String> status, Long commId) {
        log.info(">> 获取用户充电卡列表: page={}, userId={}, status={}", page, userId, status);
        if (page.getPageNum() <= 0) {
            log.info("<< 分页索引值必须从1开始");
            throw new DcArgumentException("分页索引值必须从1开始");
        }

        UserVo userVo = userService.findInfoByUid(userId, null);
        if (!Integer.valueOf(1).equals(userVo.getStats()) || !userVo.getStatus()
            .equals(UserConstants.USER_STATUS_NORMAL)) {
            log.info("<< 此账号已不可用。userId={}, stats={}, status={}", userVo.getId(),
                userVo.getStats(), userVo.getStatus());
            return new ArrayList<>();
        }
        // 获取子商户
        List<Long> commIds = commercialRoDs.listSubCommercialIds2(commId);
        List<CardDetailVO> result = cardMapper.findByUserIdAndStatus(
            (page.getPageNum() - 1) * page.getPageSize(), page.getPageSize(), userId, status,
            commIds);

        // 查询结果
        if (result.size() == 0) {
            log.info("<< 暂无数据");
            return new ArrayList<>();
        }

        log.info("<< 获取用户充电卡列表结果: result size={}", result.size());
        return result;
    }

    /**
     * 根据逻辑卡号获取适用的场站
     *
     * @param cardNo 逻辑卡号
     * @return
     */
    @Override
    public List<String> getStationsOfCard(String cardNo, Integer type, String idChain) {
        log.info(">> 获取卡片的适用场站: cardNo={}", cardNo);
//        String stations = cardMapper.getStationsByCardNo(cardNo);
        List<SiteAuthVo> poList = this.siteAuthDs.findByTypeAndAccountAndTopCommId(
            type == null ? AuthMediaType.CARD : type,
            cardNo,
            null,
            type == null ? List.of(SiteStatus.ONLINE, SiteStatus.UNAVAILABLE)
                : List.of(SiteStatus.ONLINE, SiteStatus.UNAVAILABLE, SiteStatus.OPENING),
            Boolean.TRUE,
            idChain);
        List<String> siteIdList = null;
        if (type != null) {
            siteIdList = poList.stream().map(SiteAuthVo::getSiteName).collect(Collectors.toList());
            //超过10个场站截取保留
            if (siteIdList != null && siteIdList.size() > 10) {
                siteIdList.subList(0, 10);
            }
        } else {
            siteIdList = poList.stream().map(SiteAuthVo::getSiteId).collect(Collectors.toList());
        }

        log.info("<< result={}", siteIdList);
        return siteIdList;
    }

    @Override
    public List<String> getEmergencyCardBySiteId(String siteId, Long commId) {
        log.info(">> 获取卡片的适用场站: siteId={},commId={}", siteId, commId);

        CommercialSimpleVo commercialInfo = commercialRoDs.getCommerial(commId);
        if (commercialInfo == null) {
            throw new DcServiceException("商户信息不存在");
        }
        //获取场站绑定的紧急充电卡列表
        List<Card> cardList = cardMapper.getEmergencyCardBySiteId(siteId);
        List<String> cardNoList = new ArrayList<>();

        if (CollectionUtils.isNotEmpty(cardList)) {
            List<Card> cardLists = cardList.stream()
                .filter(e -> !commercialInfo.getIdChain().contains(e.getIdChain()))
                .collect(Collectors.toList());
            log.info("cardLists.size={}", cardLists.size());
            if (CollectionUtils.isNotEmpty(cardLists)) {
                cardNoList = cardLists.stream().map(e -> e.getCardChipNo())
                    .collect(Collectors.toList());
                return cardNoList;
            }
        }
        return cardNoList;
    }

    /**
     * 紧急卡修改后的新商户，商户不包含原先紧急卡的站点列表
     *
     * @param corpId
     * @param commId
     * @return
     */
    public MoveCorpCardList getEmergencyCardByCorpId(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        BlocUserDto byId = blocUserMapper.findById(corpId);
        IotAssert.isNotNull(byId, "找不到该企业");

        CommercialSimpleVo commerial = commercialRoDs.getCommerial(byId.getCommId());
        IotAssert.isNotNull(commerial, "找不到企业对应商户");

        Map<String, Object> params = new HashMap<>(16);
        //params.put("commIds", commIds);
        params.put("cardStatusList",
            List.of(CardStatus.ISSUE.getCode(), CardStatus.ISSUE_SUCCESS.getCode()));
        params.put("corpId", corpId);
        params.put("commIdChain", commerial.getIdChain());
        List<CardListdetailVO> cardList = cardMapper.queryUrgencyCardsByPage(params);

//        List<String> ret = List.of();
        MoveCorpCardList ret = new MoveCorpCardList();
        if (CollectionUtils.isNotEmpty(cardList)) {

            CommercialSimpleVo newCommercial = commercialRoDs.getCommerial(commId);
            IotAssert.isNotNull(newCommercial, "找不到商户: " + commId);

            List<SitePo> siteBySiteIdList = siteRoDs.getSiteBySiteIdList(cardList.stream()
                .map(CardListdetailVO::getSiteId)
                .distinct()
                .collect(Collectors.toList()));

            if (CollectionUtils.isNotEmpty(siteBySiteIdList)) {
                List<String> excludeSiteList = siteBySiteIdList.stream()
                    .filter(e -> e.getIdChain().indexOf(newCommercial.getIdChain()) != 0)
                    .map(SitePo::getId)
                    .collect(Collectors.toList());
                log.info("修改后的新商户不包含此站点： {}", JsonUtils.toJsonString(excludeSiteList));

                cardList.stream().forEach(e -> {

                    if (excludeSiteList.contains(e.getSiteId())) {
                        // 在剔除站点内的卡，添加到移除列表中
                        ret.getRemoveList().add(e.getCardChipNo());
                    } else {
                        ret.getRemainList().add(e.getCardChipNo());
                    }

                });

                // 使2个列表元素唯一化
                ret.setRemoveList(
                    ret.getRemoveList().stream().distinct().collect(Collectors.toList()));
                ret.setRemainList(
                    ret.getRemainList().stream().distinct().collect(Collectors.toList()));

                // 通过remove列表，矫正remain列表
                ret.setRemainList(
                    ret.getRemainList()
                        .stream()
                        .filter(e -> !ret.getRemoveList().contains(e))
                        .collect(Collectors.toList())
                );
            }
        }

        return ret;
    }

    public MoveCorpSiteAuthList getMoveCorpCardAuthByCorpId(Long corpId, Long commId) {

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        BlocUserDto byId = blocUserMapper.findById(corpId);
        IotAssert.isNotNull(byId, "找不到该企业");

        MoveCorpSiteAuthList ret = new MoveCorpSiteAuthList();

        List<SiteAuthMoveCorpVo> cardAuthList = siteAuthRoDs.getMoveCorpCardByCorpId(corpId);
        if (CollectionUtils.isNotEmpty(cardAuthList)) {
            CommercialSimpleVo commercial = commercialRoDs.getCommerial(commId);
            IotAssert.isNotNull(commercial, "找不到对应商户: " + commId);

            List<SiteAuthMoveCorpVo> removeList = cardAuthList.stream()
                .filter(e -> e.getIdChain().indexOf(commercial.getIdChain()) != 0)
                .collect(Collectors.toList());
            ret.setRemoveList(removeList);

            List<SiteAuthMoveCorpVo> remainList = cardAuthList.stream()
                .filter(e -> e.getIdChain().indexOf(commercial.getIdChain()) == 0)
                .collect(Collectors.toList());
            ret.setRemainList(remainList);

            // 位于 remove 列表的相同元素需要从 remain 中剔除
            Set<String> removeSet = ret.getRemoveList().stream().map(SiteAuthMoveCorpVo::getAccount)
                .collect(Collectors.toSet());
            if (CollectionUtils.isNotEmpty(removeSet) && CollectionUtils.isNotEmpty(
                ret.getRemainList())) {
                List<SiteAuthMoveCorpVo> collect = ret.getRemainList()
                    .stream()
                    .filter(e -> !removeSet.contains(e.getAccount()))
                    .collect(Collectors.toList());
                ret.setRemainList(collect);
            }
        }

        return ret;
    }

    public Long moveCorpCardAuthByCorpId(Long corpId, Long commId) {

        log.info("修改card鉴权信息: corpId: {}, corpId: {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        BlocUserDto byId = blocUserMapper.findById(corpId);
        IotAssert.isNotNull(byId, "找不到该企业");

        CommercialSimpleVo commercial = commercialRoDs.getCommerial(commId);
        IotAssert.isNotBlank(commercial.getIdChain(), "找不到该企业idChain");

        IotAssert.isNotNull(commercial.getTopCommId(), "找不到该商户的顶级商户id");

        Long ret = siteAuthDs.moveCorpCardByCorpId(corpId, commercial.getTopCommId(),
            commercial.getIdChain());

        log.info("移除了card auth记录 {} 条", ret);

        return ret;
    }

    /**
     * 改t_card
     *
     * @param corpId
     * @param commId
     * @return
     */
    public Long moveCorpCardByCorpId(Long corpId, Long commId) {

        log.info("修改card所属商户: corpId: {}, corpId: {}", corpId, commId);

        IotAssert.isNotNull(corpId, "请传入企业id");
        IotAssert.isNotNull(commId, "请传入商户id");

        long ret = cardMapper.moveCorp(corpId, commId);

        log.info("修改了t_card记录 {} 条", ret);

        return ret;
    }

    @Override
    public Boolean ownCard(Long userId, String commIdChain) {
        return null != cardMapper.findOneByUserId(userId, commIdChain);
    }

    /**
     * 企业平台数据清洗 旧数据(在线卡，VIN码，授信客户)转移 自动创建一级组织 企业管理平台第一版（2020-02）上线时
     */
    @Override
    public void corpDataCleaning() {
        // 将旧的在线卡进行清洗，使其能在企业平台上展示
        List<Card> cardData = cardMapper.getCardCleanData();
        cardData.forEach(e -> {
            e.setCorpId(e.getBlocUserId());
            cardMapper.updateByCardNoSelective(e);
        });
        // 将旧的VIN码进行清洗，使其能在企业平台上展示
        List<VinDto> vinData = vinMapper.getVinCleanData();
        vinData.forEach(e -> {
            VinParam vinParam = new VinParam();
            vinParam.setId(e.getId());
            vinParam.setCorpId(e.getBlocUserId());
            vinMapper.update(vinParam);
        });
        // 卡/VIN码清洗完后，若用户的默认扣款账户为企业账户，则改为基本账户
        List<User> userData = userMapper.getUserCleanData();
        userData.forEach(e -> {
            e.setDefaultPayType(OrderPayType.PERSON.getCode());
            e.setBalanceId(e.getCommId());
            userService.setBasicInfo(e);
        });
        // 企业客户默认一级组织初始化清洗
        List<BlocUserDto> blocUserDtoData = blocUserMapper.getBlocUserCleanData();
        blocUserDtoData.forEach(e -> {
            CorpOrgPo corpOrgPo = new CorpOrgPo();
            corpOrgPo.setCorpId(e.getId())
                .setOrgName(e.getBlocUserName())
                .setOrgLevel(1)
                .setAccount(e.getAccount());
            corpOrgService.addOrUpdateCorpOrg(corpOrgPo);
        });
        // 旧授信客户(t_r_bloc_user)中corpOrgId默认属于一级组织清洗
        List<RBlocUser> rBlocUserData = blocUserMapper.getCorpOrgCleanData();
        Map<Long, Long> orgMap = rBlocUserData.stream().
            collect(Collectors.toMap(RBlocUser::getBlocUserId, RBlocUser::getCorpOrgId));
        List<RBlocUser> rBlocUserData2 = rBlocUserMapper.getRBlocUserCleanData();
        rBlocUserData2.forEach(e -> {
            Long corpOrgId = orgMap.get(e.getBlocUserId());
            if (corpOrgId != null && corpOrgId > 0) {
                e.setCorpOrgId(corpOrgId);
                rBlocUserMapper.modifyByCondition(e);
            }
        });
    }

    @Override
    public void syncCardSiteAuth() {
        log.info(">> card start");
        long start = 0;
        int size = 100;

        List<Card> cardList;
        do {
            cardList = this.cardMapper.findAll(start, size);
            start = start + cardList.size();

            cardList.forEach(this::updateSiteAuth);
            log.info("to start = {}", start);
        } while (CollectionUtils.isNotEmpty(cardList));
        log.info("<< card end");
    }

    @Override
    public Mono<ObjectResponse<Boolean>> syncAmount(String cardNo,
        BigDecimal amount,
        Integer result,
        Long topCommId,
        Long userId) {

        return this.getCardAmount(cardNo, topCommId, true)
            .map(e -> {
                CardAmountSyncPo cardAmountSyncPo = new CardAmountSyncPo();
                cardAmountSyncPo.setCardNo(cardNo)
                    .setAmount(amount)
                    .setAvailable(e.getData())
                    .setOpUserId(userId);
                if (e.getData().compareTo(amount) == 0) {
                    cardAmountSyncPo.setResult(0);
                } else {
                    cardAmountSyncPo.setResult(1);
                }
                IotAssert.isTrue(cardAmountSyncRwDs.insertCardAmountSync(cardAmountSyncPo),
                    "同步失败");
                return cardAmountSyncPo;
            })
            .map(e -> {
                ObjectResponse<Boolean> ret = new ObjectResponse<>(e.getResult() == 0);
                if (e.getResult() != 0) {
                    ret.setError("同步时金额异常")
                        .setStatus(DcConstants.KEY_RES_CODE_SERVICE_ERROR);
                }
                return ret;
            });

    }

    @Override
    public Mono<ListResponse<CardAmountSyncVo>> getSyncCardAmount(CardAmountSyncParam param) {
        Long count = cardAmountSyncRoDs.getCount(param);
        if (count > 0) {
            return Mono.just(new ListResponse<>(cardAmountSyncRoDs.getList(param), count));
        } else {
            return Mono.just(new ListResponse<>(List.of(), count));
        }
    }

    @Override
    public Mono<ObjectResponse<BigDecimal>> getCardAmount(String cardNo, Long topCommId,
        Boolean cardMaker) {

        Card card = cardService.getCardByCardNo(cardNo, topCommId);
        IotAssert.isNotNull(card, "找不到指定的卡片信息");
        IotAssert.isNotNull(card.getUserId(), "卡片对应用户为空");

        if (cardMaker) {
//            IotAssert.isNull(card.getCorpId(), "该卡片是企业授信卡片，无法同步余额。");
            IotAssert.isTrue(NumberUtils.equals(card.getDeposit(), 1),
                "卡片当前未设置兼容充值，请先设置后再尝试");

            UserPo userPo = userRoDs.getCusById(card.getUserId());
            IotAssert.isNotNull(userPo, "卡片对应用户不存在");

            IotAssert.isTrue(CARD_SYNC_SUPPORT_PAY_TYPE.contains(
                    PayAccountType.valueOf(userPo.getDefaultPayType())),
                "卡片对应默认支付类型不支持同步操作");

            // 判断多张卡
            IotAssert.isTrue(card.getUserId() != null || card.getCorpId() != null,
                "卡片扣款信息不存在");
            List<Card> cardByCorIdAndUserId = cardService.getCardByCorIdAndUserId(card.getCorpId(),
                card.getUserId());
            if (cardByCorIdAndUserId.size() > 1) {
                log.error("扣款帐户存在多张卡片: {}",
                    cardByCorIdAndUserId.stream()
                        .map(Card::getCardNo)
                        .collect(Collectors.toList()));
                IotAssert.isTrue(false, "帐户存在多张卡");
            }

            // 判断多个VIN
            List<Long> corpIdList = new ArrayList<>();
            if (card.getCorpId() != null) {
                corpIdList.add(card.getCorpId());
            }
            List<VinDto> cardsByUserIdAndCorpIds = vinMapper.getCardsByUserIdAndCorpIds(
                card.getUserId(), corpIdList);
            if (cardsByUserIdAndCorpIds.size() >= 1) {
                log.error("扣款帐户存在其他VIN: {}",
                    cardsByUserIdAndCorpIds.stream()
                        .map(VinDto::getVin)
                        .collect(Collectors.toList()));
                IotAssert.isTrue(false, "帐户存在多张卡或VIN");
            }

            // 企业授信
            if (card.getCorpId() != null) {
                log.info("userId: {}, corpId: {}", card.getUserId(), card.getCorpId());
                List<RBlocUserVo> rBlocUserVOByUserId =
                    rBlocUserMapper.findRBlocUserVOByUserId(card.getUserId(), card.getCorpId(),
                        null);
                IotAssert.isTrue(CollectionUtils.isNotEmpty(rBlocUserVOByUserId),
                    "找不到对应的企业授信");
                log.info("ids: {}", rBlocUserVOByUserId.stream().map(RBlocUserVo::getId)
                    .collect(Collectors.toList()));
                IotAssert.isTrue(rBlocUserVOByUserId.size() == 1, "存在多个企业授信");
                RBlocUserVo rBlocUserVo = rBlocUserVOByUserId.get(0);

                IotAssert.isNotNull(rBlocUserVo, "企业授信不存在, userId: " + userPo.getId());
                IotAssert.isTrue(LimitCycle.NONCYCLE.equals(rBlocUserVo.getLimitCycle()),
                    "企业授信限额周期必须是无周期");

                CorpVo corpVo = corpBizService.getCorpVo(card.getCorpId());
                // 返回可用余额
                OrderCountParam param = new OrderCountParam();
                param.setCorpId(card.getCorpId())
                    .setPayAccountIdList(List.of(rBlocUserVo.getId()));
                ListResponse<CorpOrderCountVo> corpOrderCountVoListResponse = bizBiFeignClient.corpOrderCount(
                    param);
                FeignResponseValidate.check(corpOrderCountVoListResponse);

                List<CorpOrderCountVo> biCorp = corpOrderCountVoListResponse.getData();
                log.info("获取授信统计成功: {}", biCorp);
                CorpOrderCountVo corpOrderCountVo = biCorp.get(0);
                BigDecimal paidFee = BigDecimal.ZERO;
                if (corpOrderCountVo != null && corpOrderCountVo.getPaidFee() != null) {
                    paidFee = corpOrderCountVo.getPaidFee();
                }
                // 后付费处理
                if (SettlementType.POSTPAID.equals(corpVo.getSettlementType())) {
                    // 使用授信账户的可用余额
                    if (CollectionUtils.isNotEmpty(biCorp) && biCorp.size() == 1) {
                        // 无周期的授信同步到卡片的金额 = 限额 - 总充电金额 - 冻结金额
                        return Mono.just(new ObjectResponse<>(
                            rBlocUserVo.getLimitMoney()
                                .subtract(paidFee)
                                .subtract(rBlocUserVo.getFrozenAmount())
                        ));
                    } else {
                        log.warn("获取授信统计出错: {}", biCorp);
                        IotAssert.isTrue(false, "获取授信统计出错");
                    }
                }

                // 预付费处理
//                if(LimitCycle.NONCYCLE.equals(rBlocUserVo.getLimitCycle())) {
                // 无周期，可用额度 = 限额 - 已使用 - 冻结
                BigDecimal ret = rBlocUserVo.getLimitMoney()
                    .subtract(paidFee)
                    .subtract(corpVo.getFrozenAmount());
//                } else {
//                    ret.setAvailableAmount(e.getAvailableMoney() == null ? BigDecimal.ZERO : e.getAvailableMoney());
//                }

                if (corpVo.getAvailableAmount() == null) {
                    return Mono.just(new ObjectResponse<>(BigDecimal.ZERO));
                } else if (DecimalUtils.gt(ret, corpVo.getAvailableAmount())) {
                    return Mono.just(new ObjectResponse<>(corpVo.getAvailableAmount()));
                } else {
                    return Mono.just(new ObjectResponse<>(ret));
                }
            }
        }

        Long userId = card.getUserId();
        UserVo user = userService.findInfoByUid(userId, topCommId);

//        ListCommercialParam param = new ListCommercialParam();
//        param.setSize(999);
//        List<TRCommercialPo> trCommercialPos = trCommercialRoDs.listCommercial(param);
//        List<String> allCommId = trCommercialPos.stream()
//            .map(TRCommercialPo::getId)
//            .filter(Objects::nonNull)
//            .map(Object::toString)
//            .collect(Collectors.toList());
//        String allCommIdStr = StringUtils.join(allCommId, ",");

        CommercialSimpleVo siteComm = new CommercialSimpleVo();
        siteComm.setTopCommId(topCommId)
            .setIdChain(null); // 表示所有商户

        ListResponse<String> response = null;
        if (null != card.getCorpId() && card.getCorpId() > 0) {
            response = authCenterFeignClient.getGidsById(card.getCorpId());
            FeignResponseValidate.checkIgnoreData(response);
        }

        SemiAuthVo semiAuthVo = new SemiAuthVo();
        semiAuthVo.setUser(user)
            .setRealTimeFlag(false)
            .setCorpId(card.getCorpId())
            .setCorpBalancId(card.getRBlocUserId())
            .setSiteFrozenAmount(BigDecimal.ZERO)
            .setEvseCache(null)
            .setSiteComm(siteComm);
//            .setSiteEcny(false)

        if (null != response) {
            semiAuthVo.setSiteGids(response.getData());
        } else {
            semiAuthVo.setSiteGids(List.of());
        }

        return authFacade.semiAuth(semiAuthVo)
            .map(e -> e.getCardMakerBalance() == null ? e.getBalance() : e.getCardMakerBalance())
            .doOnError(e -> log.error("出现异常，此时返回可用金额0元，原因：{}", e.getMessage()))
            .onErrorReturn(BigDecimal.ZERO)
            .map(RestUtils::buildObjectResponse);
    }

    public long batchUpdateCardDeposit(List<String> cardNo, Integer deposit) {
        return cardMapper.batchUpdateCardDeposit(cardNo, deposit);
    }

    @Override
    public Integer siteAuthCard(SiteAuthCardParam param) {
        // 获取场站配置组
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getSiteIdList()),
            "请传入需要下发的目标场站列表");
        List<SiteAuthCardPo> bySiteIds = siteAuthCardRoDs.getBySiteIds(param.getSiteIdList());
//        IotAssert.isTrue(CollectionUtils.isNotEmpty(bySiteIds), "目标场站未配置需要下发的卡片列表");
        Map<String, List<SiteAuthCardPo>> siteCardMap;
        if (CollectionUtils.isEmpty(bySiteIds)) {
            siteCardMap = new HashMap<>();
            param.getSiteIdList().forEach(siteId -> {
                siteCardMap.put(siteId, new ArrayList<>());
            });
        } else {
            siteCardMap = bySiteIds.stream()
                .collect(Collectors.groupingBy(SiteAuthCardPo::getSiteId));
        }

        // 获取待下发的桩列表
        long page = 0L;
        boolean loop = true;
        List<EvseModelVo> evseRepos = new ArrayList<>();
        while (loop) {
            ListEvseParam listEvseParam = new ListEvseParam();
            listEvseParam.setSiteIdList(param.getSiteIdList())
                .setSize(999)
                .setStart(page);
            ListResponse<EvseModelVo> evseModelVoList = iotDeviceMgmFeignClient.getEvseModelVoList(
                listEvseParam);
            FeignResponseValidate.check(evseModelVoList);
            if (CollectionUtils.isEmpty(evseModelVoList.getData())
                || evseModelVoList.getData().size() < 999) {
                loop = false;
            }

            if (CollectionUtils.isNotEmpty(param.getEvseNoList())) {
                // 仅加入关注的桩
                evseRepos.addAll(evseModelVoList.getData()
                    .stream()
                    .filter(e -> param.getEvseNoList().contains(e.getEvseId()))
                    .collect(Collectors.toList()));
            } else {
                evseRepos.addAll(evseModelVoList.getData());
            }
            page++;
        }
        IotAssert.isTrue(CollectionUtils.isNotEmpty(evseRepos), "待下发的桩列表为空");

        List<SiteAuthCardLogPo> authCardLogPos = evseRepos.stream()
            .map(e -> {
                List<SiteAuthCardPo> siteAuthCardPos = siteCardMap.get(e.getSiteId());
                if (StringUtils.isBlank(e.getSiteId())) {
                    return null;
                } else {
                    SiteAuthCardLogPo ret = new SiteAuthCardLogPo();
                    List<LocalCard> cardNoList = new ArrayList<>();
                    if (CollectionUtils.isNotEmpty(siteAuthCardPos)) {
                        cardNoList = siteAuthCardPos.stream()
                            .map(siteAuthCardPo -> {
                                LocalCard localCard = new LocalCard();
                                localCard.setCardNumber(siteAuthCardPo.getCardNo())
                                    .setVisibleNumber(siteAuthCardPo.getCardChipNo());
                                return localCard;
                            }).toList();
                    }
                    return ret.setCardNos(cardNoList)
                        .setSiteId(e.getSiteId())
                        .setEvseId(e.getEvseId())
                        .setStatus(this.computeAuthCardLogStatus(e));
                }
            })
            .filter(Objects::nonNull)
            .collect(Collectors.toList());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(authCardLogPos), "无需要下发的桩列表");

        // 下发记录处理
        log.info("新增卡片本地鉴权下发记录: {}", siteAuthCardLogRwDs.batchInsert(authCardLogPos));

        List<SiteAuthCardLogPo> sSendCardAuthEvseList = authCardLogPos.stream()
            .filter(e -> e.getStatus() == DcBizConstants.EVSE_LOCAL_CARD_AUTH_INIT)
            .collect(Collectors.toList());
        IotAssert.isTrue(CollectionUtils.isNotEmpty(sSendCardAuthEvseList), "无符合下发需求的桩");

        ModifyEvseCfgParam cfgParam = new ModifyEvseCfgParam();
        cfgParam.setSiteAuthCardList(sSendCardAuthEvseList);
        cfgParam.setEvseNoList(sSendCardAuthEvseList.stream()
            .map(SiteAuthCardLogPo::getEvseId)
            .collect(Collectors.toList()));
        BaseResponse baseResponse = this.iotBizClient.modifyEvseCfgV2(cfgParam);
        log.info("下发桩关联的场站配置信息完成");
        if (null == baseResponse || baseResponse.getStatus() != 0) {
            log.warn("下发本地card鉴权iot返回异常");
            int ret = siteAuthCardLogRwDs.batchSetStatus(sSendCardAuthEvseList.stream()
                .map(SiteAuthCardLogPo::getId)
                .collect(Collectors.toList()), DcBizConstants.EVSE_LOCAL_CARD_AUTH_FAIL);
            log.info("修改 {} 条记录为失败", ret);
        } else {
            log.warn("下发本地card鉴权iot返回正常: {}", baseResponse);
            int ret = siteAuthCardLogRwDs.batchSetStatus(sSendCardAuthEvseList.stream()
                .map(SiteAuthCardLogPo::getId)
                .collect(Collectors.toList()), DcBizConstants.EVSE_LOCAL_CARD_AUTH_SEND);
            log.info("修改 {} 条记录为下发中", ret);
        }
        return 0;
    }

    @Override
    public ListResponse<SitePo> getCommonSiteList(CardSearchParam param) {
        if (CollectionUtils.isEmpty(param.getCardNoList()) || param.getCommId() == null) {
            throw new DcArgumentException("请求参数不完整");
        }
        //获取绑定的场站
        List<SiteAuthCardVo> list = siteAuthCardRoDs.getListByCard(param.getCardNoList(),
            param.getCommId(), null);
        List<String> commonList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            Map<String, List<SiteAuthCardVo>> map = list.stream()
                .collect(Collectors.groupingBy(SiteAuthCardVo::getCardNo));
            for (String key : map.keySet()) {
                List<String> siteIdList = map.get(key).stream().map(SiteAuthCardVo::getSiteId)
                    .collect(Collectors.toList());
                if (CollectionUtils.isEmpty(commonList)) {
                    commonList.addAll(siteIdList);
                    continue;
                }
                commonList.retainAll(siteIdList);
            }
        }
        if (CollectionUtils.isNotEmpty(commonList)) {
            List<SitePo> sitePoList = siteRoDs.getSiteBySiteIdList(commonList);
            return RestUtils.buildListResponse(sitePoList);
        }
        return RestUtils.buildListResponse(null);
    }

    @Override
    public List<SiteAuthCardPo> getCardListBySiteId(String siteId) {
        return siteAuthCardRoDs.getBySiteIds(List.of(siteId));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addEssCard(AddEssCardParam param) {
        IotAssert.isNotBlank(param.getCardChipNo(), "卡面编号不能为空");
        IotAssert.isNotBlank(param.getCardNo(), "鉴权卡号不能为空");
        // 卡面编号和鉴权卡号校验
        IotAssert.isTrue(RegularExpressionUtil.englishNumberAll(param.getCardChipNo(), 1, 30)
            , "卡面编号最多30位，仅支持大小写英文字母和数字");
        IotAssert.isTrue(RegularExpressionUtil.englishNumberAll(param.getCardNo(), 1, 16)
            , "鉴权卡号最多16位，仅支持大小写英文字母和数字");
        if (StringUtils.isNotBlank(param.getCardName())) {
            IotAssert.isTrue(
                RegularExpressionUtil.chineseEnglishNumberAll(param.getCardName())
                    && param.getCardName().length() <= 20,
                "卡名称最多20位，仅支持字母、数字、汉字");
        }
        Card card = cardMapper.checkCard(param.getCardNo(), param.getCardChipNo());
        if (card != null) {
            IotAssert.isTrue(!card.getCardChipNo().equals(param.getCardChipNo()), "卡面编号已存在");
            IotAssert.isTrue(!card.getCardNo().equals(param.getCardNo()), "鉴权卡号已存在");
        }
        UserPo cusInfo = userRoDs.getCusById(param.getUserId());
        if (cusInfo == null || !NumberUtils.gtZero(cusInfo.getCommId())) {
            throw new DcServiceException("用户所属商户信息异常");
        }
        if (StringUtils.isNotEmpty(cusInfo.getPhone()) && cusInfo.getPhone()
            .startsWith("B")) {
            throw new DcServiceException("企业母账号不能绑定卡");
        }

        card = new Card();
        card.setCardChipNo(param.getCardChipNo());
        card.setCardNo(param.getCardNo());
        card.setCardName(param.getCardName());
        // 先设置为未激活，下发代码去锁定
        card.setCardStatus(CardStatus.INACTIVE.getCode());
        // 海外版的鉴权卡就是管理平台的在线卡，状态先设置为2未分配，下发代码去设置成0在线卡
        card.setCardType(CardType.UNDISTRIBUTED.getCode());
        card.setCardCreateDate(new Date());
        card.setCardUpdateDate(new Date());
        card.setYxBz("1");
        card.setCardKey("");

        Long insertResult = cardMapper.insertSelective(card);
        if (insertResult == null || insertResult < 1L) {
            throw new DcServiceException("添加失败");
        }

        card.setUserId(param.getUserId());
        card.setCommId(cusInfo.getCommId());
        card.setCardStatus(CardStatus.ACTIVE.getCode());//新增卡成功后, 卡的状态为'已激活
        card.setCardType(CardType.ONLINE.getCode());//新增卡成功后, 卡的类型为在线卡
        card.setCardActivationDate(new Date());//新增卡成功后, 当前时间为卡片激活时间
        card.setCardUpdateDate(new Date());//更新修改时间

        BaseResponse jsonResult = this.updateCard(card);
        log.info("海外平台新增鉴权卡片，并且发卡给客户，调用服务完成结果是{}", jsonResult);
    }

    @Override
    public ListResponse<CardListdetailVO> queryEssOnlineCardsByPage(CardRequest param) {
        Page<Object> pageResult = PageHelper.startPage(param.getPage(), param.getRows(),
            true, false, null);
        // commId用不到,同一商户下的用户都能看到同一commId的卡,用到的是commIdChain
        param.setCardType(Long.valueOf(CardType.ONLINE.getCode()));
        List<CardListdetailVO> cardList = cardMapper.queryEssOnlineCardsByPage(param);

        if (CollectionUtils.isEmpty(cardList)) {
            return RestUtils.buildListResponse(new ArrayList<>(), pageResult.getTotal());
        }

        // 补充可用场站相关信息以及本地鉴权可用站点信息
        List<String> cardNoList = Optional.of(cardList)
            .orElse(Collections.emptyList())
            .stream()
            .map(CardListdetailVO::getCardNo)
            .toList();
        if (CollectionUtils.isEmpty(cardNoList)) {
            return RestUtils.buildListResponse(new ArrayList<>(), pageResult.getTotal());
        }

        Optional<Map<String, List<String>>> siteAuthMapOptional = siteAuthRoDs.findStationList(1,
            cardNoList, param.getTopCommId(), param.getCommIdChain());
        Map<String, List<String>> siteAuthMap = siteAuthMapOptional.orElse(new HashMap<>());

        List<SiteAuthCardVo> siteAuthCardVoList = siteAuthCardRoDs.getListByCard(cardNoList,
            param.getCommId(), param.getCommIdChain());
        Map<String, List<SiteAuthCardVo>> siteAuthCardMap =
            CollectionUtils.isEmpty(siteAuthCardVoList)
                ? Collections.emptyMap()
                : siteAuthCardVoList.stream()
                    .collect(Collectors.groupingBy(SiteAuthCardVo::getCardNo));

        cardList.forEach(card -> {
            card.setUsableStationCount(
                siteAuthMap.getOrDefault(card.getCardNo(), new ArrayList<>()).size());
            card.setStationList(siteAuthMap.getOrDefault(card.getCardNo(), new ArrayList<>()));
            card.setLocalAuthSiteAmount(
                siteAuthCardMap.getOrDefault(card.getCardNo(), new ArrayList<>()).size());
            card.setLocalAuthSiteList(
                siteAuthCardMap.getOrDefault(card.getCardNo(), new ArrayList<>()));
        });

        //循环获取商户名
        for (CardListdetailVO card : cardList) {

            // 通过商户Id获取商户名称
            ObjectResponse<Commercial> commercial = commercialService.getCommercialByCommId(
                card.getCommId());

            if (commercial != null && commercial.getStatus() == ResultConstant.RES_SUCCESS_CODE) {
                Commercial data = commercial.getData();
                card.setCommName(data.getCommName());
            } else {
                log.info("卡片{}根据商户{}获取商户数据失败结果是{}", card.getCardId(),
                    card.getCommId(), JsonUtils.toJsonString(commercial));
            }

            card.setIsEdit(true);
            if (param.getCommIdChain() != null) {
                String[] strArr = param.getCommIdChain().split(",");
                int length = strArr.length;

                if (Arrays.asList(strArr).contains(card.getCommId().toString()) && !strArr[length
                    - 1].equals(card.getCommId().toString())) {

                    card.setIsEdit(false);
                }
            }
        }
        ListResponse<CardListdetailVO> res = new ListResponse<>(cardList, pageResult.getTotal());
        return res;
    }

    private int computeAuthCardLogStatus(EvseModelVo vo) {
        List<Integer> protocolList = List.of(DcBizConstants.PROTOCOL_VERSION_370,
            DcBizConstants.PROTOCOL_VERSION_9160, DcBizConstants.PROTOCOL_VERSION_9201);
        if (!protocolList.contains(vo.getProtocolVer()) ||
            CollectionUtils.isEmpty(vo.getFlags()) ||
            !vo.getFlags().contains(DcBizConstants.EVSE_LOCAL_CARD_AUTH_FLAG)) {
            return DcBizConstants.EVSE_LOCAL_CARD_AUTH_NA;
        } else {
            return DcBizConstants.EVSE_LOCAL_CARD_AUTH_INIT;
        }
    }

    enum CardValidateType {
        PhysicsExist("物理卡号"),// 存在
        LogicalExist("逻辑卡号"),// 存在
        PhysicsBlank(null),// 为空
        LogicalBlank(null);// 为空

        private String desc;

        CardValidateType(String desc) {
            this.desc = desc;
        }

        public String getDesc() {
            return this.desc;
        }
    }
}
