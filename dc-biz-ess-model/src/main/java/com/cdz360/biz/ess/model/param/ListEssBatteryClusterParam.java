package com.cdz360.biz.ess.model.param;

import com.cdz360.base.model.base.param.BaseListParam;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "查询电池簇参数")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ListEssBatteryClusterParam extends BaseListParam {

    public ListEssBatteryClusterParam() {
    }

    public ListEssBatteryClusterParam(int size) {
        this.setSize(size);
    }

    @Schema(description = "场站ID列表")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private List<String> siteIdList;

    @Schema(description = "ESS平台分配的设备编号")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private String essDno;

    @Schema(description = "电池堆设备名称")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private String stackEquipName;

    @Schema(description = "电池堆设备ID ESS下唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long stackEquipId;

    @Schema(description = "电池簇号")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long clusterNo;

    @Schema(description = "电池簇设备ID ESS下唯一")
    @JsonInclude(JsonInclude.Include.NON_NULL)
    private Long clusterEquipId;
}
