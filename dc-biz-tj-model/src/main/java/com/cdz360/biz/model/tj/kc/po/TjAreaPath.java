package com.cdz360.biz.model.tj.kc.po;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "投建区域路径信息")
@Data
@Accessors(chain = true)
public class TjAreaPath {

    @Schema(description = "圆表示中心点[[1,2]];矩形表示对角线[[1,2],[3,4]];其他表示点集合[[],[],...,[]]")
    private List<List<BigDecimal>> paths;
}
