<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.rw.kc.mapper.TjDepreciationRwMapper">

  <resultMap id="RESULT_TJ_DEPRECIATION_PO" type="com.cdz360.biz.model.tj.kc.po.TjDepreciationPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="ageLimit" jdbcType="INTEGER" property="ageLimit"/>
    <result column="residualValue" jdbcType="DECIMAL" property="residualValue"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_DEPRECIATION_PO">
    select * from t_tj_depreciation where id = #{id}
    <if test="lock == true">
      for update
    </if>
  </select>

  <update id="updateTjDepreciation" parameterType="com.cdz360.biz.model.tj.kc.po.TjDepreciationPo">
    update t_tj_depreciation set
    <if test="ageLimit != null">
      ageLimit = #{ageLimit},
    </if>
    <if test="residualValue != null">
      residualValue = #{residualValue},
    </if>
    updateTime = now()
    where id = #{id}
  </update>

</mapper>

