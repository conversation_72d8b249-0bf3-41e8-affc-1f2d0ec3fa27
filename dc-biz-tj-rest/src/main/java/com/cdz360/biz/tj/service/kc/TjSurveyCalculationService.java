package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ObjectResponse;
import com.cdz360.base.model.base.exception.DcServiceException;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.JsonUtils;
import com.cdz360.biz.model.tj.kc.param.ListTjDailyChargingDurationParam;
import com.cdz360.biz.model.tj.kc.param.ListTjMaterialCostParam;
import com.cdz360.biz.model.tj.kc.po.TjCashOutflowPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationCoefficientPo;
import com.cdz360.biz.model.tj.kc.po.TjDailyChargingDurationPo;
import com.cdz360.biz.model.tj.kc.po.TjDepreciationPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyHighVoltagePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesOtherPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo;
import com.cdz360.biz.model.tj.kc.type.BarrierGateType;
import com.cdz360.biz.model.tj.kc.type.BranchBoxType;
import com.cdz360.biz.model.tj.kc.type.BrandImageType;
import com.cdz360.biz.model.tj.kc.type.CalculationMethod;
import com.cdz360.biz.model.tj.kc.type.CalculationResult;
import com.cdz360.biz.model.tj.kc.type.CarParkType;
import com.cdz360.biz.model.tj.kc.type.CashOutflowType;
import com.cdz360.biz.model.tj.kc.type.LightingType;
import com.cdz360.biz.model.tj.kc.type.MaterialCostChildType;
import com.cdz360.biz.model.tj.kc.type.MaterialCostGrandchildType;
import com.cdz360.biz.model.tj.kc.type.MaterialCostPower;
import com.cdz360.biz.model.tj.kc.type.MaterialCostType;
import com.cdz360.biz.model.tj.kc.type.SplitFlagType;
import com.cdz360.biz.model.tj.kc.vo.TjInitCashVo;
import com.cdz360.biz.model.tj.kc.vo.TjMaterialCostVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationInfoVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyCalculationResultVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaPileVo;
import com.cdz360.biz.model.tj.kc.vo.TjSurveyChargeAreaVo;
import com.cdz360.biz.model.tj.kc.vo.TjYearCashVo;
import com.cdz360.biz.model.trading.iot.po.EvseModelPo;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjCashOutflowRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationCoefficientRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDailyChargingDurationRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjDepreciationRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjMaterialCostRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyChargeAreaPileRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyChargeAreaRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyHighVoltageRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyOperationExpensesRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyOperationIncomeRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveyRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveySupportingFacilitiesOtherRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjSurveySupportingFacilitiesRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyChargeAreaPileRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyChargeAreaRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyHighVoltageRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyOperationExpensesRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveyOperationIncomeRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveySupportingFacilitiesOtherRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjSurveySupportingFacilitiesRwDs;
import com.cdz360.biz.tj.utils.EquationSolver;
import com.cdz360.biz.tj.utils.IRRCalculator;
import com.chargerlinkcar.framework.common.feign.IotDeviceMgmEvseModelFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjSurveyCalculationService {

    @Autowired
    private TjSurveyChargeAreaRoDs tjSurveyChargeAreaRoDs;

    @Autowired
    private TjSurveyChargeAreaRwDs tjSurveyChargeAreaRwDs;

    @Autowired
    private TjSurveyChargeAreaPileRoDs tjSurveyChargeAreaPileRoDs;

    @Autowired
    private TjSurveyChargeAreaPileRwDs tjSurveyChargeAreaPileRwDs;

    @Autowired
    private TjSurveySupportingFacilitiesRoDs tjSurveySupportingFacilitiesRoDs;

    @Autowired
    private TjSurveySupportingFacilitiesRwDs tjSurveySupportingFacilitiesRwDs;

    @Autowired
    private TjSurveySupportingFacilitiesOtherRoDs tjSurveySupportingFacilitiesOtherRoDs;

    @Autowired
    private TjSurveySupportingFacilitiesOtherRwDs tjSurveySupportingFacilitiesOtherRwDs;

    @Autowired
    private TjSurveyHighVoltageRoDs tjSurveyHighVoltageRoDs;

    @Autowired
    private TjSurveyHighVoltageRwDs tjSurveyHighVoltageRwDs;

    @Autowired
    private TjSurveyOperationIncomeRoDs tjSurveyOperationIncomeRoDs;

    @Autowired
    private TjSurveyOperationIncomeRwDs tjSurveyOperationIncomeRwDs;

    @Autowired
    private TjSurveyOperationExpensesRoDs tjSurveyOperationExpensesRoDs;

    @Autowired
    private TjSurveyOperationExpensesRwDs tjSurveyOperationExpensesRwDs;

    @Autowired
    private TjSurveyRoDs tjSurveyRoDs;

    @Autowired
    private TjDailyChargingDurationRoDs tjDailyChargingDurationRoDs;

    @Autowired
    private TjDailyChargingDurationCoefficientRoDs tjDailyChargingDurationCoefficientRoDsDs;

    @Autowired
    private TjCashOutflowRoDs tjCashOutflowRoDs;

    @Autowired
    private TjMaterialCostRoDs tjMaterialCostRoDs;

    @Autowired
    private TjDepreciationRoDs tjDepreciationRoDs;

    @Autowired
    private IotDeviceMgmEvseModelFeignClient iotDeviceMgmEvseModelFeignClient;

    private Integer scale = 8;


    public Mono<List<TjSurveyChargeAreaVo>> findTjSurveyChargeAreaBySurveyNo(String surveyNo) {
        List<TjSurveyChargeAreaPo> tjSurveyChargeAreaPoList = tjSurveyChargeAreaRoDs.findTjSurveyChargeAreaBySurveyNo(
            surveyNo);
        List<TjSurveyChargeAreaVo> tjSurveyChargeAreaVoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(tjSurveyChargeAreaPoList)) {
            tjSurveyChargeAreaPoList.forEach(e -> {
                TjSurveyChargeAreaVo tjSurveyChargeAreaVo = tjSurveyChargeAreaPoToVo(e);
                List<TjSurveyChargeAreaPilePo> tjSurveyChargeAreaPilePoList = tjSurveyChargeAreaPileRoDs.findTjSurveyChargeAreaPileByAreaId(
                    e.getId());
                tjSurveyChargeAreaVo.setTjSurveyChargeAreaPilePoList(tjSurveyChargeAreaPilePoList);
                tjSurveyChargeAreaVoList.add(tjSurveyChargeAreaVo);
            });
        }
        return Mono.just(tjSurveyChargeAreaVoList);
    }

    @Transactional
    public Mono<TjSurveyChargeAreaVo> saveTjSurveyChargeArea(
        TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
        TjSurveyChargeAreaPo tjSurveyChargeAreaPo = tjSurveyChargeAreaVoToPo(
            tjSurveyChargeAreaVo);
        tjSurveyChargeAreaRwDs.insertTjSurveyChargeArea(tjSurveyChargeAreaPo);
        List<TjSurveyChargeAreaPilePo> tjSurveyChargeAreaPilePoList = tjSurveyChargeAreaVo.getTjSurveyChargeAreaPilePoList();
        if (CollectionUtils.isNotEmpty(tjSurveyChargeAreaPilePoList)) {
            tjSurveyChargeAreaPilePoList.forEach(e -> {
                e.setAreaId(tjSurveyChargeAreaPo.getId());
                tjSurveyChargeAreaPileRwDs.insertTjSurveyChargeAreaPile(e);
            });
        }
        return Mono.just(tjSurveyChargeAreaVo);
    }

    public Mono<TjSurveySupportingFacilitiesPo> findTjSurveySupportingFacilitiesBySurveyNo(
        String surveyNo) {
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo = tjSurveySupportingFacilitiesRoDs.findTjSurveySupportingFacilitiesBySurveyNo(
            surveyNo);
        if (tjSurveySupportingFacilitiesPo == null) {
            throw new DcServiceException("未找到配套设施数据");
        }
        return Mono.just(tjSurveySupportingFacilitiesPo);
    }

    public Mono<TjSurveySupportingFacilitiesPo> saveTjSurveySupportingFacilities(
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo) {
        tjSurveySupportingFacilitiesRwDs.insertTjSurveySupportingFacilities(
            tjSurveySupportingFacilitiesPo);
        return Mono.just(tjSurveySupportingFacilitiesPo);
    }

    public Mono<List<TjSurveySupportingFacilitiesOtherPo>> findTjSurveySupportingFacilitiesOtherBySurveyNo(
        String surveyNo) {
        return Mono.just(
            tjSurveySupportingFacilitiesOtherRoDs.findTjSurveySupportingFacilitiesOtherBySurveyNo(
                surveyNo));
    }

    public Mono<TjSurveySupportingFacilitiesOtherPo> saveTjSurveySupportingFacilitiesOther(
        TjSurveySupportingFacilitiesOtherPo tjSurveySupportingFacilitiesOtherPo) {
        tjSurveySupportingFacilitiesOtherRwDs.insertTjSurveySupportingFacilitiesOther(
            tjSurveySupportingFacilitiesOtherPo);
        return Mono.just(tjSurveySupportingFacilitiesOtherPo);
    }

    public Mono<List<TjSurveyHighVoltagePo>> findTjSurveyHighVoltageBySurveyNo(String surveyNo) {
        return Mono.just(tjSurveyHighVoltageRoDs.findTjSurveyHighVoltageBySurveyNo(surveyNo));
    }

    public Mono<TjSurveyHighVoltagePo> saveTjSurveyHighVoltage(
        TjSurveyHighVoltagePo tjSurveyHighVoltagePo) {
        tjSurveyHighVoltageRwDs.insertTjSurveyHighVoltage(tjSurveyHighVoltagePo);
        return Mono.just(tjSurveyHighVoltagePo);
    }


    public Mono<TjSurveyOperationIncomePo> findTjSurveyOperationIncomeBySurveyNo(String surveyNo) {
        TjSurveyOperationIncomePo tjSurveyOperationIncomePo = tjSurveyOperationIncomeRoDs.findTjSurveyOperationIncomeBySurveyNo(
            surveyNo);
        if (tjSurveyOperationIncomePo == null) {
            throw new DcServiceException("未查询到运营收入信息");
        }
        return Mono.just(tjSurveyOperationIncomePo);
    }

    public Mono<TjSurveyOperationIncomePo> saveTjSurveyOperationIncome(
        TjSurveyOperationIncomePo tjSurveyOperationIncomePo) {
        tjSurveyOperationIncomeRwDs.insertTjSurveyOperationIncome(tjSurveyOperationIncomePo);
        return Mono.just(tjSurveyOperationIncomePo);
    }


    public Mono<TjSurveyOperationExpensesPo> findTjSurveyOperationExpensesBySurveyNo(
        String surveyNo) {
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo = tjSurveyOperationExpensesRoDs.findTjSurveyOperationExpensesBySurveyNo(
            surveyNo);
        if (tjSurveyOperationExpensesPo == null) {
            throw new DcServiceException("未查询到运营支出信息");
        }
        return Mono.just(tjSurveyOperationExpensesPo);
    }

    public Mono<TjSurveyOperationExpensesPo> saveTjSurveyOperationExpenses(
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        tjSurveyOperationExpensesRwDs.insertTjSurveyOperationExpenses(
            tjSurveyOperationExpensesPo);
        return Mono.just(tjSurveyOperationExpensesPo);
    }

    @Transactional
    public Mono<TjSurveyCalculationInfoVo> saveTjSurveyCalculationInfoVo(
        TjSurveyCalculationInfoVo tjSurveyCalculationInfoVo) {
        String surveyNo = tjSurveyCalculationInfoVo.getSurveyNo();
        List<TjSurveyChargeAreaPo> tjSurveyChargeAreaPoList = tjSurveyChargeAreaRoDs.findTjSurveyChargeAreaBySurveyNo(
            surveyNo);
        if (CollectionUtils.isNotEmpty(tjSurveyChargeAreaPoList)) {
            tjSurveyChargeAreaPoList.forEach(e -> {
                tjSurveyChargeAreaPileRwDs.disableTjSurveyChargeAreaPileBySurveyNo(surveyNo);
            });
        }
        tjSurveyChargeAreaRwDs.disableTjSurveyChargeAreaBySurveyNo(surveyNo);
        tjSurveySupportingFacilitiesRwDs.disableTjSurveySupportingFacilitiesBySurveyNo(surveyNo);
        tjSurveySupportingFacilitiesOtherRwDs.disableTjSurveySupportingFacilitiesOtherBySurveyNo(
            surveyNo);
        tjSurveyHighVoltageRwDs.disableTjSurveyHighVoltageBySurveyNo(surveyNo);
        tjSurveyOperationExpensesRwDs.disableTjSurveyOperationExpensesBySurveyNo(surveyNo);
        tjSurveyOperationIncomeRwDs.disableTjSurveyOperationIncome(surveyNo);

        if (CollectionUtils.isNotEmpty(tjSurveyCalculationInfoVo.getTjSurveyChargeAreaVoList())) {
            tjSurveyCalculationInfoVo.getTjSurveyChargeAreaVoList().forEach(e -> {
                e.setSurveyNo(surveyNo);
                saveTjSurveyChargeArea(e);
            });
        }
        tjSurveyCalculationInfoVo.getTjSurveySupportingFacilitiesPo().setSurveyNo(surveyNo);
        saveTjSurveySupportingFacilities(
            tjSurveyCalculationInfoVo.getTjSurveySupportingFacilitiesPo());
        if (CollectionUtils.isNotEmpty(
            tjSurveyCalculationInfoVo.getTjSurveySupportingFacilitiesOtherPoList())) {
            tjSurveyCalculationInfoVo.getTjSurveySupportingFacilitiesOtherPoList().forEach(e -> {
                e.setSurveyNo(surveyNo);
                saveTjSurveySupportingFacilitiesOther(e);
            });
        }
        if (CollectionUtils.isNotEmpty(tjSurveyCalculationInfoVo.getTjSurveyHighVoltagePoList())) {
            tjSurveyCalculationInfoVo.getTjSurveyHighVoltagePoList().forEach(e -> {
                e.setSurveyNo(surveyNo);
                saveTjSurveyHighVoltage(e);
            });
        }
        tjSurveyCalculationInfoVo.getTjSurveyOperationIncomePo().setSurveyNo(surveyNo);
        saveTjSurveyOperationIncome(tjSurveyCalculationInfoVo.getTjSurveyOperationIncomePo());
        tjSurveyCalculationInfoVo.getTjSurveyOperationExpensesPo().setSurveyNo(surveyNo);
        saveTjSurveyOperationExpenses(tjSurveyCalculationInfoVo.getTjSurveyOperationExpensesPo());
        return Mono.just(tjSurveyCalculationInfoVo);
    }

    private TjSurveyChargeAreaVo tjSurveyChargeAreaPoToVo(
        TjSurveyChargeAreaPo tjSurveyChargeAreaPo) {
        TjSurveyChargeAreaVo tjSurveyChargeAreaVo = new TjSurveyChargeAreaVo();
        tjSurveyChargeAreaVo.setId(tjSurveyChargeAreaPo.getId())
            .setSurveyNo(tjSurveyChargeAreaPo.getSurveyNo())
            .setTransformerToChargeCable(tjSurveyChargeAreaPo.getTransformerToChargeCable())
            .setTransformerToChargeCableDistance(
                tjSurveyChargeAreaPo.getTransformerToChargeCableDistance())
            .setTransformerToChargeCableType(tjSurveyChargeAreaPo.getTransformerToChargeCableType())
            .setChargeCableDistance(tjSurveyChargeAreaPo.getChargeCableDistance())
            .setMasterToTerminalDistance(tjSurveyChargeAreaPo.getMasterToTerminalDistance())
            .setChargeCableType(tjSurveyChargeAreaPo.getChargeCableType())
            .setGalvanizedCableTrayDistance(tjSurveyChargeAreaPo.getGalvanizedCableTrayDistance())
            .setOrdinarySoilExcavationDistance(
                tjSurveyChargeAreaPo.getOrdinarySoilExcavationDistance())
            .setPedestrianWalkwayExcavationDistance(
                tjSurveyChargeAreaPo.getPedestrianWalkwayExcavationDistance())
            .setConcreteExcavationDistance(tjSurveyChargeAreaPo.getConcreteExcavationDistance())
            .setEvseBaseNeed(tjSurveyChargeAreaPo.getEvseBaseNeed())
            .setEnable(tjSurveyChargeAreaPo.getEnable())
            .setCreateTime(tjSurveyChargeAreaPo.getCreateTime())
            .setUpdateTime(tjSurveyChargeAreaPo.getUpdateTime());
        return tjSurveyChargeAreaVo;
    }

    private TjSurveyChargeAreaPo tjSurveyChargeAreaVoToPo(
        TjSurveyChargeAreaVo tjSurveyChargeAreaVo) {
        TjSurveyChargeAreaPo tjSurveyChargeAreaPo = new TjSurveyChargeAreaPo();
        tjSurveyChargeAreaPo.setId(tjSurveyChargeAreaVo.getId())
            .setSurveyNo(tjSurveyChargeAreaVo.getSurveyNo())
            .setTransformerToChargeCable(tjSurveyChargeAreaVo.getTransformerToChargeCable())
            .setTransformerToChargeCableDistance(
                tjSurveyChargeAreaVo.getTransformerToChargeCableDistance())
            .setTransformerToChargeCableType(tjSurveyChargeAreaVo.getTransformerToChargeCableType())
            .setChargeCableDistance(tjSurveyChargeAreaVo.getChargeCableDistance())
            .setMasterToTerminalDistance(tjSurveyChargeAreaVo.getMasterToTerminalDistance())
            .setChargeCableType(tjSurveyChargeAreaVo.getChargeCableType())
            .setGalvanizedCableTrayDistance(tjSurveyChargeAreaVo.getGalvanizedCableTrayDistance())
            .setOrdinarySoilExcavationDistance(
                tjSurveyChargeAreaVo.getOrdinarySoilExcavationDistance())
            .setPedestrianWalkwayExcavationDistance(
                tjSurveyChargeAreaVo.getPedestrianWalkwayExcavationDistance())
            .setConcreteExcavationDistance(tjSurveyChargeAreaVo.getConcreteExcavationDistance())
            .setEvseBaseNeed(tjSurveyChargeAreaVo.getEvseBaseNeed())
            .setEnable(tjSurveyChargeAreaVo.getEnable())
            .setCreateTime(tjSurveyChargeAreaVo.getCreateTime())
            .setUpdateTime(tjSurveyChargeAreaVo.getUpdateTime());
        return tjSurveyChargeAreaPo;
    }


    public Mono<List<TjSurveyCalculationResultVo>> calculationResult(String surveyNo) {
        List<TjSurveyCalculationResultVo> tjSurveyCalculationResultVoList = new ArrayList<>();
        TjDailyChargingDurationCoefficientPo tjDailyChargingDurationCoefficientPo = tjDailyChargingDurationCoefficientRoDsDs.findTjDailyChargingDurationCoefficient();
        List<TjYearCashVo> optimisticYearCashFee = yearCashFee(surveyNo,
            tjDailyChargingDurationCoefficientPo.getOptimisticCoefficient());
        List<TjYearCashVo> normalYearCashFee = yearCashFee(surveyNo, BigDecimal.ONE);
        List<TjYearCashVo> pessimisticYearCashFee = yearCashFee(surveyNo,
            tjDailyChargingDurationCoefficientPo.getPessimisticCoefficient());
        TjSurveyCalculationResultVo optimisticCalculationResultVo = calculationResultVo(
            optimisticYearCashFee, CalculationMethod.METHOD_OPTIMISTIC.getDesc(), surveyNo);
        TjSurveyCalculationResultVo normalCalculationResultVo = calculationResultVo(
            normalYearCashFee, CalculationMethod.METHOD_NORMAL.getDesc(), surveyNo);
        TjSurveyCalculationResultVo pessimisticCalculationResultVo = calculationResultVo(
            pessimisticYearCashFee, CalculationMethod.METHOD_PESSIMISTIC.getDesc(), surveyNo);
        tjSurveyCalculationResultVoList.add(normalCalculationResultVo);
        tjSurveyCalculationResultVoList.add(optimisticCalculationResultVo);
        tjSurveyCalculationResultVoList.add(pessimisticCalculationResultVo);
        return Mono.just(tjSurveyCalculationResultVoList);
    }

    private TjSurveyCalculationResultVo calculationResultVo(List<TjYearCashVo> yearCashFee,
        String calculationMethod, String surveyNo) {
        TjSurveyCalculationResultVo calculationResultVo = new TjSurveyCalculationResultVo();
        calculationResultVo.setCalculationMethod(calculationMethod)
            .setCalculationResult(CalculationResult.RESULT_NO_PASS.getDesc());
        double[] cashFlows = new double[yearCashFee.size()];
        AtomicInteger i = new AtomicInteger();
        AtomicReference<BigDecimal> paybackPeriod = new AtomicReference<>(new BigDecimal("100"));
        yearCashFee.forEach(e -> {
            //本期经营活动现金流量 (税前运营收入+税前运营补贴收入+建站补贴)-(通道费+平台托管费+运维费+引流费用+电费支出+电损支出+服务费分成+场地租金+其他运营费用+管理费用+财务费用+增值税+企业所得税)
            //本期投资活动现金流量 -(高压投资额 + 低压投资额（铝芯线缆） + 设备投资额 + 设备更新投入 + 后期投入)
            BigDecimal expensesFee = e.getChannelFee().add(e.getPlatformFee()).add(e.getMaintFee())
                .add(e.getFlowFee()).add(e.getEleFee()).add(e.getEleLossFee())
                .add(e.getServiceSharingFee()).add(e.getRentFee()).add(e.getOtherOperationFee())
                .add(e.getManageFee()).add(e.getFinanceFee()).add(e.getVatFee()).add(e.getCitFee());
            BigDecimal operationCash = e.getOperationIncomeFee().add(e.getOperationSubsidyFee())
                .add(e.getStationSubsidyFee()).subtract(expensesFee);
            BigDecimal tzCash = BigDecimal.ZERO.subtract(
                e.getTzHighVoltageFee().add(e.getTzLowVoltageFee()).add(e.getTzEquipmentsFee())
                    .add(e.getTzUpdateEquipmentsFee()).add(e.getTzOtherFee()));
            BigDecimal cash = operationCash.add(tzCash);
            cashFlows[i.get()] = cash.divide(new BigDecimal("10000"), 4, RoundingMode.HALF_UP)
                .doubleValue();
            if (e.getTotalCashFee().compareTo(BigDecimal.ZERO) >= 0
                && cash.compareTo(BigDecimal.ZERO) > 0) {
                if (paybackPeriod.get().compareTo(new BigDecimal("100")) == 0) {
                    paybackPeriod.set(new BigDecimal(e.getYear().toString()).add(new BigDecimal(
                        String.valueOf(Math.abs(e.getTotalCashFee().doubleValue()))).divide(cash,
                        scale, RoundingMode.HALF_UP)));
                }
            }
            i.getAndIncrement();
        });
        double initialGuess = 0.1; // 初始猜测值
        double tolerance = 1e-6; // 收敛容差
        int maxIterations = 10000; // 最大迭代次数

        if (cashFlows[0] >= 0) {
            return calculationResultVo.setCalculationResult(
                CalculationResult.RESULT_PASS.getDesc());
        }
        double irr = IRRCalculator.calculateIRR(cashFlows, initialGuess, tolerance, maxIterations);
        double standardIrr = 0.05;
        double standardPaybackPeriod = 6;
        TjSurveyPo tjSurveyPo = tjSurveyRoDs.getByNo(surveyNo);
        // =IF(OR(当期累计现金流量<0,(本期经营活动现金流量+本期投资活动现金流量)<0),"无法回收",IF((本期经营活动现金流量+本期投资活动现金流量)=0,"无法回收",year+ABS(当期累计现金流量)/(本期经营活动现金流量+本期投资活动现金流量)))
        if (irr >= standardIrr && paybackPeriod.get().doubleValue() <= standardPaybackPeriod
            && tjSurveyPo.getContractTerm().compareTo(paybackPeriod.get()
            .add(new BigDecimal("0.5"))) >= 0) {
            calculationResultVo.setCalculationResult(CalculationResult.RESULT_PASS.getDesc());
        }

        return calculationResultVo;
    }

    /**
     * 每年现金流量数据信息 默认返回10年数据
     *
     * @return
     */
    private List<TjYearCashVo> yearCashFee(String surveyNo, BigDecimal coefficient) {
        TjSurveyOperationIncomePo tjSurveyOperationIncomePo = tjSurveyOperationIncomeRoDs.findTjSurveyOperationIncomeBySurveyNo(
            surveyNo);
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo = tjSurveyOperationExpensesRoDs.findTjSurveyOperationExpensesBySurveyNo(
            surveyNo);
        List<TjSurveyChargeAreaPo> tjSurveyChargeAreaPoList = tjSurveyChargeAreaRoDs.findTjSurveyChargeAreaBySurveyNo(
            surveyNo);
        List<TjSurveyChargeAreaPilePo> tjSurveyChargeAreaPilePoList = tjSurveyChargeAreaPileRoDs.findTjSurveyChargeAreaPileBySurveyNo(
            surveyNo);
        List<TjSurveyHighVoltagePo> tjSurveyHighVoltagePoList = tjSurveyHighVoltageRoDs.findTjSurveyHighVoltageBySurveyNo(
            surveyNo);
        List<TjSurveySupportingFacilitiesOtherPo> tjSurveySupportingFacilitiesOtherPoList = tjSurveySupportingFacilitiesOtherRoDs.findTjSurveySupportingFacilitiesOtherBySurveyNo(
            surveyNo);
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo = tjSurveySupportingFacilitiesRoDs.findTjSurveySupportingFacilitiesBySurveyNo(
            surveyNo);
        if (CollectionUtils.isEmpty(tjSurveyChargeAreaPoList) || CollectionUtils.isEmpty(
            tjSurveyChargeAreaPilePoList)) {
            throw new DcServiceException("充电区域信息不全，不能进行测算");
        }
        if (tjSurveySupportingFacilitiesPo == null) {
            throw new DcServiceException("辅助设施信息不全，不能进行测算");
        }
        if (tjSurveyOperationExpensesPo == null) {
            throw new DcServiceException("运营支出信息不全，不能进行测算");
        }
        if (tjSurveyOperationIncomePo == null) {
            throw new DcServiceException("运营收入信息不全，不能进行测算");
        }
        AtomicReference<Integer> stationPower = new AtomicReference<>(0);
        List<TjSurveyChargeAreaPileVo> tjSurveyChargeAreaPileVoList = new ArrayList<>();
        tjSurveyChargeAreaPilePoList.forEach(e -> {
            ObjectResponse<EvseModelPo> res = iotDeviceMgmEvseModelFeignClient.findById(
                e.getEvseModelId());
            FeignResponseValidate.check(res);
            TjSurveyChargeAreaPileVo tjSurveyChargeAreaPileVo = new TjSurveyChargeAreaPileVo();
            BeanUtils.copyProperties(e, tjSurveyChargeAreaPileVo);
            tjSurveyChargeAreaPileVo.setPower(res.getData().getPower());
            tjSurveyChargeAreaPileVo.setSplitFlag(res.getData().getSplitFlag());
            tjSurveyChargeAreaPileVoList.add(tjSurveyChargeAreaPileVo);
            stationPower.set(stationPower.get() + res.getData().getPower() * e.getEvseCount());
        });
        TjDepreciationPo tjDepreciationPo = tjDepreciationRoDs.findTjDepreciation();

        List<TjCashOutflowPo> tjCashOutflowPoList = tjCashOutflowRoDs.findAllTjCashOutflow();

        TjSurveyPo tjSurveyPo = tjSurveyRoDs.getByNo(surveyNo);
        ListTjDailyChargingDurationParam param = new ListTjDailyChargingDurationParam();
        param.setProvinceFullName(tjSurveyPo.getProvince())
            .setCityFullName(tjSurveyPo.getCity())
            .setAreaFullName(tjSurveyPo.getArea());
        List<TjDailyChargingDurationPo> tjDailyChargingDurationPoList1 = tjDailyChargingDurationRoDs.findTjDailyChargingDuration(
            param);
        TjDailyChargingDurationPo tjDailyChargingDurationPo;
        if (CollectionUtils.isEmpty(tjDailyChargingDurationPoList1)) {
            param.setAreaFullName(null);
            List<TjDailyChargingDurationPo> tjDailyChargingDurationPoList2 = tjDailyChargingDurationRoDs.findTjDailyChargingDuration(
                param);
            if (CollectionUtils.isEmpty(tjDailyChargingDurationPoList2)) {
                throw new DcServiceException("该区域未能获取到充电时长数据，不能进行测算");
            } else {
                tjDailyChargingDurationPo = tjDailyChargingDurationPoList2.get(0);
            }
        } else {
            tjDailyChargingDurationPo = tjDailyChargingDurationPoList1.get(0);
        }
        List<TjYearCashVo> tjYearCashVoList = new ArrayList<>();
        BigDecimal day = new BigDecimal("30"); //每月按30天计算
        BigDecimal baseServicePrice = tjSurveyOperationIncomePo.getFirstYearLastNinthMonthServicePrice();
        BigDecimal serviceMaxPrice = tjSurveyOperationIncomePo.getFirstYearLastNinthMonthServicePrice()
            .add(tjSurveyOperationIncomePo.getServiceIncreaseMaxPrice());

        BigDecimal totalBeforeCashFee = BigDecimal.ZERO;
        BigDecimal totalBeforeProfitBeforeTaxFee = BigDecimal.ZERO;
        BigDecimal totalBeforeCitFee = BigDecimal.ZERO;
        TjInitCashVo tjInitCashVo = initTzCash(tjSurveyPo, tjDepreciationPo,
            tjSurveyChargeAreaPileVoList, tjSurveyHighVoltagePoList, tjSurveySupportingFacilitiesPo,
            tjSurveySupportingFacilitiesOtherPoList, tjSurveyChargeAreaPoList, stationPower.get());
        for (int i = 0; i < Math.min(10, tjSurveyPo.getContractTerm().intValue()); i++) {
            TjYearCashVo tjYearCashVo = new TjYearCashVo();
            tjYearCashVo.setYear(i + 1);

            BigDecimal kwh = BigDecimal.ZERO;
            BigDecimal serviceFee = BigDecimal.ZERO;
            //计算日充电时长计算电量、服务费单价、服务费
            Triple<BigDecimal, BigDecimal, BigDecimal> triple = calculateKwhAndServicePriceAndServiceFee(
                i, kwh, serviceFee, day, baseServicePrice, serviceMaxPrice,
                tjDailyChargingDurationPo, tjSurveyPo, tjSurveyOperationIncomePo, coefficient,
                stationPower.get());
            kwh = triple.getLeft();
            baseServicePrice = triple.getMiddle();
            serviceFee = triple.getRight();

            //预计估算电量
            tjYearCashVo.setKwh(kwh);
            //建设投资计算
            tjYearCashVo = tjFee(i, tjYearCashVo, tjDepreciationPo, tjSurveyPo, tjInitCashVo);

            //建站补贴
            tjYearCashVo.setStationSubsidyFee(BigDecimal.ZERO);
            if (tjSurveyOperationIncomePo.getStationSubsidyYear() != null
                && tjSurveyOperationIncomePo.getStationSubsidyYear() == tjYearCashVo.getYear()) {
                tjYearCashVo.setStationSubsidyFee(
                    tjSurveyOperationIncomePo.getStationSubsidy() == null ? BigDecimal.ZERO
                        : tjSurveyOperationIncomePo.getStationSubsidy());
            }
            //税前运营补贴
            tjYearCashVo.setOperationSubsidyFee(
                kwh.multiply(tjSurveyOperationIncomePo.getOperationSubsidyPrice()));

            //税前运营收入（电费+服务费）
            BigDecimal eleFee = kwh.multiply(tjSurveyOperationIncomePo.getElecPrice());
            tjYearCashVo.setOperationIncomeFee(eleFee.add(serviceFee));

            //经营性现金流出 电费
            tjYearCashVo.setEleFee(eleFee);

            tjYearCashVo.setTotalBeforeCashFee(totalBeforeCashFee);
            tjYearCashVo.setTotalBeforeProfitBeforeTaxFee(totalBeforeProfitBeforeTaxFee);
            tjYearCashVo.setTotalBeforeCitFee(totalBeforeCitFee);

            //经营性现金流出
            tjYearCashVo = yearExpensesFee(tjYearCashVo, kwh, tjSurveyOperationExpensesPo,
                tjCashOutflowPoList, tjSurveyPo, tjInitCashVo.getTzEquipmentsFee());
            totalBeforeCashFee = tjYearCashVo.getTotalCashFee();
            totalBeforeProfitBeforeTaxFee = totalBeforeProfitBeforeTaxFee.add(
                tjYearCashVo.getProfitBeforeTaxFee());
            totalBeforeCitFee = totalBeforeCitFee.add(tjYearCashVo.getTotalBeforeCitFee());
            log.info("当前测算系数:{}, 当期资金信息动态: {}", coefficient,
                JsonUtils.toJsonString(tjYearCashVo));
            tjYearCashVoList.add(tjYearCashVo);
        }
        return tjYearCashVoList;
    }

    private TjInitCashVo initTzCash(TjSurveyPo tjSurveyPo, TjDepreciationPo tjDepreciationPo,
        List<TjSurveyChargeAreaPileVo> tjSurveyChargeAreaPileVoList,
        List<TjSurveyHighVoltagePo> tjSurveyHighVoltagePoList,
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo,
        List<TjSurveySupportingFacilitiesOtherPo> tjSurveySupportingFacilitiesOtherPoList,
        List<TjSurveyChargeAreaPo> tjSurveyChargeAreaPoList,
        Integer stationPower) {
        ListTjMaterialCostParam param = new ListTjMaterialCostParam();
        List<TjMaterialCostVo> tjMaterialCostVoList = tjMaterialCostRoDs.findTjMaterialCost(param);
        List<TjMaterialCostVo> evseCostVoList = tjMaterialCostVoList.stream()
            .filter(e -> e.getType() == MaterialCostType.EVSE.getCode())
            .collect(Collectors.toList());
        List<TjMaterialCostVo> boxTransformationCostVoList = tjMaterialCostVoList.stream()
            .filter(e -> e.getType() == MaterialCostType.BOX_TRANSFORMATION.getCode())
            .collect(Collectors.toList());
        List<TjMaterialCostVo> cableCostVoList = tjMaterialCostVoList.stream()
            .filter(e -> e.getType() == MaterialCostType.CABLE.getCode())
            .collect(Collectors.toList());
        List<TjMaterialCostVo> stationSupportingFacilitiesCostVoList = tjMaterialCostVoList.stream()
            .filter(e -> e.getType() == MaterialCostType.STATION_SUPPORTING_FACILITIES.getCode())
            .collect(Collectors.toList());
        List<TjMaterialCostVo> otherCostVoList = tjMaterialCostVoList.stream()
            .filter(e -> e.getType() == MaterialCostType.OTHER.getCode())
            .collect(Collectors.toList());

        //设备投资额
        Map<Long, TjMaterialCostVo> evseCostMap = evseCostVoList.stream()
            .collect(Collectors.toMap(TjMaterialCostVo::getEvseModelId, o -> o));
        TjInitCashVo tjInitCashVo = new TjInitCashVo();
        final BigDecimal[] tzEquipmentsFee = {BigDecimal.ZERO};
        final BigDecimal[] tzEquipmentsInstallFee = {BigDecimal.ZERO};
        Map<Long, BigDecimal> chargeAreaPileBaseFeeMap = new HashMap<>();
        tjSurveyChargeAreaPileVoList.forEach(e -> {
            tzEquipmentsFee[0] = tzEquipmentsFee[0].add(
                evseCostMap.get(e.getEvseModelId()).getPrice()
                    .multiply(new BigDecimal(e.getEvseCount().toString())));
            tzEquipmentsInstallFee[0] = tzEquipmentsInstallFee[0].add(
                evseCostMap.get(e.getEvseModelId()).getInstallationFee()
                    .multiply(new BigDecimal(e.getEvseCount().toString())));
            BigDecimal baseFee =
                chargeAreaPileBaseFeeMap.get(e.getAreaId()) == null ? BigDecimal.ZERO
                    : chargeAreaPileBaseFeeMap.get(e.getAreaId());
            baseFee = baseFee.add(evseCostMap.get(e.getEvseModelId()).getEvseBaseFee()
                .multiply(new BigDecimal(e.getEvseCount().toString())));
            chargeAreaPileBaseFeeMap.put(e.getAreaId(), baseFee);
        });

        //高压金额(箱变、箱变基础以及安装费用、电缆及安装包含管线及开挖或者顶管、柱开及安装、环网柜及安装)
        Map<Integer, TjMaterialCostVo> boxTransformationCostMap = boxTransformationCostVoList.stream()
            .collect(Collectors.toMap(TjMaterialCostVo::getCapacity, o -> o));
        Map<Integer, TjMaterialCostVo> otherCostMap = otherCostVoList.stream()
            .collect(Collectors.toMap(TjMaterialCostVo::getGrandchildType, o -> o));
        List<TjMaterialCostVo> highVoltageCableList = cableCostVoList.stream()
            .filter(e -> e.getChildType() == MaterialCostChildType.HIGH_VOLTAGE_CABLE.getCode())
            .collect(
                Collectors.toList());
        final BigDecimal[] tzHighVoltageFee = {BigDecimal.ZERO};
        final BigDecimal[] tzLowVoltageFee = {BigDecimal.ZERO};
        //设计费如何考虑 是直接根据高压是否有 考虑是否加上高压设计费 低压肯定有的 需要低压设计费? 暂时先按照这个逻辑实现
        if (CollectionUtils.isNotEmpty(tjSurveyHighVoltagePoList)) {
            TjMaterialCostVo highVoltageCable = null;
            if (CollectionUtils.isNotEmpty(highVoltageCableList)) {
                highVoltageCable = highVoltageCableList.get(0);
            }
            TjMaterialCostVo lowerColumnSwitch = otherCostMap.get(
                MaterialCostGrandchildType.LOWER_COLUMN_SWITCH.getCode());
            TjMaterialCostVo numberOfRingNetworkIntervals = otherCostMap.get(
                MaterialCostGrandchildType.NUMBER_OF_RING_NETWORK_INTERVALS.getCode());
            TjMaterialCostVo designHighVoltageProject = otherCostMap.get(
                MaterialCostGrandchildType.DESIGN_HIGH_VOLTAGE_PROJECT.getCode());
            if (highVoltageCable == null) {
                throw new DcServiceException("数据字典缺少高压电缆成本信息，无法进行测算");
            }
            if (lowerColumnSwitch == null) {
                throw new DcServiceException("数据字典缺少下柱开关成本信息，无法进行测算");
            }
            if (numberOfRingNetworkIntervals == null) {
                throw new DcServiceException("数据字典缺少环网间隔数成本信息，无法进行测算");
            }
            if (designHighVoltageProject == null) {
                throw new DcServiceException("数据字典缺少高压设计费成本信息，无法进行测算");
            }
            TjMaterialCostVo finalHighVoltageCable = highVoltageCable;
            tjSurveyHighVoltagePoList.forEach(e -> {
                TjMaterialCostVo boxTransformation = boxTransformationCostMap.get(e.getCapacity());
                tzHighVoltageFee[0] = tzHighVoltageFee[0].add(
                    boxTransformation.getPrice().add(boxTransformation.getInstallationFee()).add(
                        finalHighVoltageCable.getPrice()
                            .multiply(new BigDecimal(e.getCableDistance().toString()))).add(
                        lowerColumnSwitch.getPrice()
                            .multiply(new BigDecimal(e.getPoleTopSwitch().toString()))).add(
                        numberOfRingNetworkIntervals.getPrice()
                            .multiply(new BigDecimal(e.getRingMainUnit().toString()))));
            });
            //高压设计费 按站单价（低压成本）
            tzLowVoltageFee[0] = tzLowVoltageFee[0].add(designHighVoltageProject.getPrice());
            log.info("高压设计费：{}", designHighVoltageProject.getPrice());
        }
        //低压金额(低压电缆及安装含穿管及管材（铜芯电缆）、低压电缆及安装含穿管及管材（铝芯电缆）、桥架及安装、电缆直埋开挖、充电桩安装、场站配套设施、品牌形象RVI等、防占位需求、功能需求、设计费、其他)
//        tjSurveyChargeAreaPileVoList.stream().
        tjSurveyChargeAreaPoList.forEach(e -> {
            List<TjSurveyChargeAreaPileVo> tjSurveyChargeAreaPileVos = tjSurveyChargeAreaPileVoList.stream()
                .filter(f -> f.getAreaId() == e.getId()).collect(Collectors.toList());
            Integer areaPower = tjSurveyChargeAreaPileVos.stream()
                .mapToInt(TjSurveyChargeAreaPileVo::getTotalPower).reduce(Integer::sum).getAsInt();
            //充电站到电源点走线距离
            if (e.getTransformerToChargeCable() == BranchBoxType.BRANCH_BOX_USE.getCode()) {
                List<TjMaterialCostVo> tjMaterialCostVos = tjMaterialCostVoList.stream()
                    .filter(f -> f.getChildType() != null && f.getGrandchildType() != null
                        && f.getPower() != null && f.getChildType()
                        .equals(MaterialCostChildType.SITE_TO_POWER_SUPPLY_WIRING.getCode())
                        && f.getGrandchildType().equals(e.getTransformerToChargeCableType())
                        && f.getPower().equals(siteToPower(areaPower))).collect(
                        Collectors.toList());
                if (CollectionUtils.isEmpty(tjMaterialCostVos)) {
                    throw new DcServiceException("缺少充电站到电源点走线相关数据，无法进行测算");
                }
                tzLowVoltageFee[0] = tzLowVoltageFee[0].add(tjMaterialCostVos.get(0).getPrice()
                    .multiply(new BigDecimal(e.getTransformerToChargeCableDistance().toString())));
                log.info("充电站到电源点走线费用：{}", tjMaterialCostVos.get(0).getPrice()
                    .multiply(new BigDecimal(e.getTransformerToChargeCableDistance().toString())));
            }
            //充电区域单台直流充电桩电缆
            tjSurveyChargeAreaPileVos.forEach(f -> {
                if (f.getSplitFlag() != SplitFlagType.SPLIT_NO.getCode()) {
                    List<TjMaterialCostVo> tjMaterialCostVos = tjMaterialCostVoList.stream()
                        .filter(g -> g.getChildType() != null && g.getGrandchildType() != null
                            && g.getTerminal() != null && g.getChildType()
                            .equals(MaterialCostChildType.SINGLE_DC_CABLE.getCode())
                            && g.getGrandchildType().equals(e.getChargeCableType())
                            && g.getTerminal().equals(f.getSplitFlag())).collect(
                            Collectors.toList());
                    if (CollectionUtils.isEmpty(tjMaterialCostVos)) {
                        throw new DcServiceException("缺少充电区域充电桩相关数据，无法进行测算");
                    }
                    tzLowVoltageFee[0] = tzLowVoltageFee[0].add(tjMaterialCostVos.get(0).getPrice()
                        .multiply(new BigDecimal(e.getMasterToTerminalDistance().toString())));
                    log.info("充电区域单台直流充电桩电缆（终端）费用：{}",
                        tjMaterialCostVos.get(0).getPrice()
                            .multiply(new BigDecimal(e.getMasterToTerminalDistance().toString())));
                }
                List<TjMaterialCostVo> tjMaterialCostVos;
                if (f.getPower() == MaterialCostPower.AC_7.getCode()) {
                    tjMaterialCostVos = tjMaterialCostVoList.stream()
                        .filter(g -> g.getChildType() != null && g.getGrandchildType() != null
                            && g.getPower() != null && g.getChildType()
                            .equals(MaterialCostChildType.SINGLE_AC_CABLE.getCode())
                            && g.getGrandchildType().equals(e.getChargeCableType())
                            && g.getPower().equals(f.getPower())).collect(
                            Collectors.toList());
                } else {
                    tjMaterialCostVos = tjMaterialCostVoList.stream()
                        .filter(g -> g.getChildType() != null && g.getGrandchildType() != null
                            && g.getPower() != null && g.getChildType()
                            .equals(MaterialCostChildType.SINGLE_DC_CABLE.getCode())
                            && g.getGrandchildType().equals(e.getChargeCableType())
                            && g.getPower().equals(f.getPower())).collect(
                            Collectors.toList());
                }
                if (CollectionUtils.isEmpty(tjMaterialCostVos)) {
                    throw new DcServiceException("缺少充电区域充电桩相关数据，无法进行测算");
                }
                tzLowVoltageFee[0] = tzLowVoltageFee[0].add(tjMaterialCostVos.get(0).getPrice()
                    .multiply(new BigDecimal(e.getChargeCableDistance().toString())));
                log.info("充电区域单台直流充电桩电缆费用：{}", tjMaterialCostVos.get(0).getPrice()
                    .multiply(new BigDecimal(e.getChargeCableDistance().toString())));
            });
        });
        //充电桩安装费用
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(tzEquipmentsInstallFee[0]);
        log.info("充电桩安装费用：{}", tzEquipmentsInstallFee[0]);
        //充电桩基础费用
        final BigDecimal[] tzEquipmentsBaseFee = {BigDecimal.ZERO};
        //变压器到充电区域距离
        final BigDecimal[] galvanizedCableTrayDistance = {BigDecimal.ZERO};
        final BigDecimal[] ordinarySoilExcavationDistance = {BigDecimal.ZERO};
        final BigDecimal[] pedestrianWalkwayExcavationDistance = {BigDecimal.ZERO};
        final BigDecimal[] concreteExcavationDistance = {BigDecimal.ZERO};
        tjSurveyChargeAreaPoList.forEach(e -> {
            if (e.getEvseBaseNeed()) {
                tzEquipmentsBaseFee[0] = tzEquipmentsBaseFee[0].add(
                    chargeAreaPileBaseFeeMap.get(e.getId()));
            }
            galvanizedCableTrayDistance[0] = galvanizedCableTrayDistance[0].add(
                new BigDecimal(e.getGalvanizedCableTrayDistance() == null ? "0"
                    : e.getGalvanizedCableTrayDistance().toString()));
            ordinarySoilExcavationDistance[0] = ordinarySoilExcavationDistance[0].add(
                new BigDecimal(e.getOrdinarySoilExcavationDistance() == null ? "0"
                    : e.getOrdinarySoilExcavationDistance().toString()));
            pedestrianWalkwayExcavationDistance[0] = pedestrianWalkwayExcavationDistance[0].add(
                new BigDecimal(e.getPedestrianWalkwayExcavationDistance() == null ? "0"
                    : e.getPedestrianWalkwayExcavationDistance().toString()));
            concreteExcavationDistance[0] = concreteExcavationDistance[0].add(
                new BigDecimal(e.getConcreteExcavationDistance() == null ? "0"
                    : e.getConcreteExcavationDistance().toString()));
        });
        TjMaterialCostVo galvanizedCableTray = otherCostMap.get(
            MaterialCostGrandchildType.GALVANIZED_CABLE_TRAY_AND_INSTALLATION.getCode());
        TjMaterialCostVo ordinarySoilExcavation = otherCostMap.get(
            MaterialCostGrandchildType.ORDINARY_SOIL_EXCAVATION.getCode());
        TjMaterialCostVo pedestrianWalkwayExcavation = otherCostMap.get(
            MaterialCostGrandchildType.EXCAVATION_OF_PEDESTRIAN_WALKWAY.getCode());
        TjMaterialCostVo concreteExcavation = otherCostMap.get(
            MaterialCostGrandchildType.CONCRETE_EXCAVATION.getCode());
        if (galvanizedCableTray == null) {
            throw new DcServiceException("数据字典缺少镀锌桥架及安装成本信息，无法进行测算");
        }
        if (ordinarySoilExcavation == null) {
            throw new DcServiceException("数据字典缺少普通土开挖成本信息，无法进行测算");
        }
        if (pedestrianWalkwayExcavation == null) {
            throw new DcServiceException("数据字典缺少人行道开挖成本信息，无法进行测算");
        }
        if (concreteExcavation == null) {
            throw new DcServiceException("数据字典缺少混凝土开挖成本信息，无法进行测算");
        }
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(
            galvanizedCableTrayDistance[0].multiply(galvanizedCableTray.getPrice()));
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(
            ordinarySoilExcavationDistance[0].multiply(ordinarySoilExcavation.getPrice()));
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(pedestrianWalkwayExcavationDistance[0].multiply(
            pedestrianWalkwayExcavation.getPrice()));
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(
            concreteExcavationDistance[0].multiply(concreteExcavation.getPrice()));

        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(tzEquipmentsBaseFee[0]);
        log.info("镀锌桥架及安装费用：{}",
            galvanizedCableTrayDistance[0].multiply(galvanizedCableTray.getPrice()));
        log.info("普通土开挖费用：{}",
            ordinarySoilExcavationDistance[0].multiply(ordinarySoilExcavation.getPrice()));
        log.info("人行道开挖费用：{}", pedestrianWalkwayExcavationDistance[0].multiply(
            pedestrianWalkwayExcavation.getPrice()));
        log.info("混凝土开挖费用：{}",
            concreteExcavationDistance[0].multiply(concreteExcavation.getPrice()));
        log.info("充电桩基础费用：{}", tzEquipmentsBaseFee[0]);

        Map<Integer, TjMaterialCostVo> stationSupportingFacilitiesMap = stationSupportingFacilitiesCostVoList.stream()
            .collect(Collectors.toMap(TjMaterialCostVo::getChildType, o -> o));

        //辅助设施明细计算
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(
            supportingFacilitiesFee(stationSupportingFacilitiesMap, tjSurveySupportingFacilitiesPo,
                tjSurveyPo));

        //低压设计费 按站单价
        TjMaterialCostVo designLowVoltageProject = otherCostMap.get(
            MaterialCostGrandchildType.DESIGN_LOW_VOLTAGE_PROJECT.getCode());
        if (designLowVoltageProject == null) {
            throw new DcServiceException("数据字典缺少低压设计费成本信息，无法进行测算");
        }
        //其他（类型：施工围栏、垃圾清运、防火封堵、垃圾箱等）
        TjMaterialCostVo otherType = otherCostMap.get(
            MaterialCostGrandchildType.OTHER_TYPE.getCode());
        if (otherType == null) {
            throw new DcServiceException(
                "数据字典缺少其他（类型：施工围栏、垃圾清运、防火封堵、垃圾箱等）成本信息，无法进行测算");
        }
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(designLowVoltageProject.getPrice());
        log.info("低压设计费用：{}", designLowVoltageProject.getPrice());
        tzLowVoltageFee[0] = tzLowVoltageFee[0].add(otherType.getPrice());
        log.info("施工围挡、垃圾清运、防火封堵、垃圾箱等费用：{}", otherType.getPrice());
        //场站配套设施
        //其他设施
        if (CollectionUtils.isNotEmpty(tjSurveySupportingFacilitiesOtherPoList)) {
            tjSurveySupportingFacilitiesOtherPoList.forEach(e -> {
                tzLowVoltageFee[0] = tzLowVoltageFee[0].add(
                    e.getPrice().multiply(new BigDecimal(e.getFacilitiesCount().toString())));
                log.info("其他设施{} 费用：{}", e.getName(),
                    e.getPrice().multiply(new BigDecimal(e.getFacilitiesCount().toString())));
            });
        }

        tjInitCashVo.setTzEquipmentsFee(tzEquipmentsFee[0]);
        tjInitCashVo.setTzLowVoltageFee(tzLowVoltageFee[0]);
        tjInitCashVo.setTzHighVoltageFee(tzHighVoltageFee[0]);
        return tjInitCashVo;
    }

    private Integer siteToPower(Integer areaPower) {
        if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_3000.getCode()) {
            throw new DcServiceException("单个超出3000kW，无法进行测算");
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_1600.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_3000.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_1200.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_1600.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_1000.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_1200.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_900.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_1000.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_800.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_900.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_700.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_800.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_600.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_700.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_500.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_600.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_400.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_500.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_300.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_400.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_200.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_300.getCode();
        } else if (areaPower > MaterialCostPower.SITE_TO_POWER_SUPPLY_100.getCode()) {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_200.getCode();
        } else {
            return MaterialCostPower.SITE_TO_POWER_SUPPLY_100.getCode();
        }
    }

    private BigDecimal supportingFacilitiesFee(
        Map<Integer, TjMaterialCostVo> stationSupportingFacilitiesMap,
        TjSurveySupportingFacilitiesPo tjSurveySupportingFacilitiesPo, TjSurveyPo tjSurveyPo) {
        //车位类型（1：小车，2：大车）
        Integer carParkType = tjSurveySupportingFacilitiesPo.getCarParkType();
        BigDecimal parkNum = new BigDecimal(tjSurveyPo.getParkNum().toString());
        BigDecimal carGearFee = BigDecimal.ZERO; //车档费用
        BigDecimal intelligentLockFee = BigDecimal.ZERO; //智能地锁费用
        BigDecimal epoxyFlooringFee = BigDecimal.ZERO; //环氧地坪费用
        BigDecimal groundHardeningFee = BigDecimal.ZERO; //地面硬化费用
        BigDecimal fireProtectionSystemFee = BigDecimal.ZERO; //消防系统费用
        BigDecimal lightingFee = BigDecimal.ZERO; //照明费用
        BigDecimal monitoringFee = BigDecimal.ZERO; //监控费用
        BigDecimal canopyFee = BigDecimal.ZERO; //雨棚费用
        BigDecimal barrierGateFee = BigDecimal.ZERO; //道闸费用
        BigDecimal brandFee = BigDecimal.ZERO; //品牌形象费用
        //车档：根据勘察站录入的车位数量×大车/小车车挡单价；
        //环氧地坪：根据勘察站录入的车位数量×大车（4×12）/小车（2.5×5.5）×单价（80/平）；
        //地面硬化：根据勘察站录入的车位数量×大车（4×12）/小车（2.5×5.5）×单价（350/平）；
        //雨棚：根据勘察站录入的车位数量×大车（4×12）/小车（2.5×5.5）×单价
        BigDecimal area = BigDecimal.ZERO;
        if (carParkType == CarParkType.CAR_PARK_SMALL.getCode()) {
            area = parkNum.multiply(new BigDecimal("2.5").multiply(new BigDecimal("5.5")));
            if (tjSurveySupportingFacilitiesPo.getCarGearNeed()) {
                TjMaterialCostVo smallCarGear = stationSupportingFacilitiesMap.get(
                    MaterialCostChildType.SMALL_CAR_GEAR.getCode());
                if (smallCarGear == null) {
                    throw new DcServiceException("数据字典缺少小车车挡成本信息，无法进行测算");
                }
                carGearFee = parkNum.multiply(smallCarGear.getPrice());
            }
        } else if (carParkType == CarParkType.CAR_PARK_BIG.getCode()) {
            area = parkNum.multiply(new BigDecimal("4").multiply(new BigDecimal("12")));
            if (tjSurveySupportingFacilitiesPo.getCarGearNeed()) {
                TjMaterialCostVo bigCarGear = stationSupportingFacilitiesMap.get(
                    MaterialCostChildType.BIG_CAR_GEAR.getCode());
                if (bigCarGear == null) {
                    throw new DcServiceException("数据字典缺少大车车挡成本信息，无法进行测算");
                }
                carGearFee = parkNum.multiply(bigCarGear.getPrice());
            }
        }
        if (tjSurveySupportingFacilitiesPo.getEpoxyFlooringNeed()) {
            TjMaterialCostVo epoxyFlooring = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.EPOXY_FLOORING.getCode());
            if (epoxyFlooring == null) {
                throw new DcServiceException("数据字典缺少环氧地坪成本信息，无法进行测算");
            }
            epoxyFlooringFee = area.multiply(epoxyFlooring.getPrice());
        }
        if (tjSurveySupportingFacilitiesPo.getGroundHardeningNeed()) {
            TjMaterialCostVo groundHardening = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.GROUND_HARDENING.getCode());
            if (groundHardening == null) {
                throw new DcServiceException("数据字典缺少地面硬化成本信息，无法进行测算");
            }
            groundHardeningFee = area.multiply(groundHardening.getPrice());
        }
        if (tjSurveySupportingFacilitiesPo.getCanopyNeed()) {
            TjMaterialCostVo canopy = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.CANOPY.getCode());
            if (canopy == null) {
                throw new DcServiceException("数据字典缺少雨棚成本信息，无法进行测算");
            }
            canopyFee = parkNum.multiply(canopy.getPrice());
        }
        //智能地锁：根据勘察站录入的车位数量×智能地锁单价；
        if (tjSurveySupportingFacilitiesPo.getIntelligentLockNeed()) {
            TjMaterialCostVo intelligentLock = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.INTELLIGENT_LOCK.getCode());
            if (intelligentLock == null) {
                throw new DcServiceException("数据字典缺少智能地锁成本信息，无法进行测算");
            }
            intelligentLockFee = parkNum.multiply(intelligentLock.getPrice());
        }
        //消防系统：每8个车位一套消防，根据车位数量计算；
        if (tjSurveySupportingFacilitiesPo.getFireProtectionSystemNeed()) {
            TjMaterialCostVo fireProtectionSystem = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.FIRE_PROTECTION_SYSTEM.getCode());
            if (fireProtectionSystem == null) {
                throw new DcServiceException("数据字典缺少消防系统成本信息，无法进行测算");
            }
            fireProtectionSystemFee = parkNum.divide(new BigDecimal("8"), 0, RoundingMode.HALF_UP)
                .multiply(fireProtectionSystem.getPrice());
        }
        //照明：每4个车位一盏，根据车位数量计算；
        if (tjSurveySupportingFacilitiesPo.getLightingType()
            == LightingType.GROUND_LIGHTING.getCode()) {
            TjMaterialCostVo groundLighting = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.GROUND_LIGHTING.getCode());
            if (groundLighting == null) {
                throw new DcServiceException("数据字典缺少地上照明成本信息，无法进行测算");
            }
            lightingFee = parkNum.divide(new BigDecimal("4"), 0, RoundingMode.CEILING)
                .multiply(groundLighting.getPrice());
        } else if (tjSurveySupportingFacilitiesPo.getLightingType()
            == LightingType.UNDERGROUND_LIGHTING.getCode()) {
            TjMaterialCostVo undergroundLighting = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.UNDERGROUND_LIGHTING.getCode());
            if (undergroundLighting == null) {
                throw new DcServiceException("数据字典缺少地下照明成本信息，无法进行测算");
            }
            lightingFee = parkNum.divide(new BigDecimal("4"), 0, RoundingMode.CEILING)
                .multiply(undergroundLighting.getPrice());
        }
        //监控：按统一单价计算，每个场站统一算
        if (tjSurveySupportingFacilitiesPo.getMonitoringSystemNeed()) {
            TjMaterialCostVo cctvMonitoringSystem = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.CCTV_MONITORING_SYSTEM.getCode());
            if (cctvMonitoringSystem == null) {
                throw new DcServiceException("数据字典缺少监控成本信息，无法进行测算");
            }
            monitoringFee = cctvMonitoringSystem.getPrice();
        }
        //道闸：选需要（已有设备）只需要道闸对接费用；选需要（无设备）需要道闸对接费用+道闸设备费用
        if (tjSurveySupportingFacilitiesPo.getBarrierGateType()
            == BarrierGateType.BARRIER_GATE_SYSTEM.getCode()) {
            TjMaterialCostVo barrierGateSystem = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.BARRIER_GATE_SYSTEM.getCode());
            if (barrierGateSystem == null) {
                throw new DcServiceException("数据字典缺少道闸系统成本信息，无法进行测算");
            }
            barrierGateFee = barrierGateSystem.getPrice();
        } else if (tjSurveySupportingFacilitiesPo.getBarrierGateType()
            == BarrierGateType.BARRIER_GATE_SYSTEM_EQUIPMENT.getCode()) {
            TjMaterialCostVo barrierGateSystemEquipment = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.BARRIER_GATE_SYSTEM_EQUIPMENT.getCode());
            if (barrierGateSystemEquipment == null) {
                throw new DcServiceException("数据字典缺少道闸系统对接成本信息，无法进行测算");
            }
            barrierGateFee = barrierGateSystemEquipment.getPrice();
        }
        //品牌形象RVI：选普通站、旗舰站根据后端设置的不同价格计入成本，选不需要则不计入成本。（需确认是按车位还是按场站计算 先按场站计算）
        if (tjSurveySupportingFacilitiesPo.getBrandImageType()
            == BrandImageType.FLAGSHIP_STORE.getCode()) {
            TjMaterialCostVo flagshipStore = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.FLAGSHIP_STORE.getCode());
            if (flagshipStore == null) {
                throw new DcServiceException("数据字典缺少品牌形象RVI旗舰店成本信息，无法进行测算");
            }
            brandFee = flagshipStore.getPrice();
        } else if (tjSurveySupportingFacilitiesPo.getBrandImageType()
            == BrandImageType.ORDINARY_STATION.getCode()) {
            TjMaterialCostVo ordinaryStation = stationSupportingFacilitiesMap.get(
                MaterialCostChildType.ORDINARY_STATION.getCode());
            if (ordinaryStation == null) {
                throw new DcServiceException("数据字典缺少品牌形象RVI普通站成本信息，无法进行测算");
            }
            brandFee = ordinaryStation.getPrice();
        }
        log.info(
            "车档费用:{}, 智能地锁费用:{}, 环氧地坪费用:{}, 地面硬化费用:{}, 消防系统费用:{}, 照明费用:{}, 监控费用:{}, 雨棚费用:{}, 道闸费用:{}, 品牌形象费用:{}",
            carGearFee, intelligentLockFee, epoxyFlooringFee, groundHardeningFee,
            fireProtectionSystemFee, lightingFee, monitoringFee, canopyFee, barrierGateFee,
            brandFee);
        return carGearFee.add(intelligentLockFee).add(epoxyFlooringFee).add(groundHardeningFee)
            .add(fireProtectionSystemFee).add(lightingFee).add(monitoringFee).add(canopyFee)
            .add(barrierGateFee).add(brandFee);
    }

    private TjYearCashVo tjFee(int i, TjYearCashVo tjYearCashVo, TjDepreciationPo tjDepreciationPo,
        TjSurveyPo tjSurveyPo, TjInitCashVo tjInitCashVo) {

        BigDecimal ageLimit = new BigDecimal(tjDepreciationPo.getAgeLimit().toString());
        BigDecimal contractTerm = new BigDecimal(tjSurveyPo.getContractTerm().toString());
        BigDecimal residualValue = tjDepreciationPo.getResidualValue();
        BigDecimal tzHighVoltageFee = tjInitCashVo.getTzHighVoltageFee();
        BigDecimal tzLowVoltageFee = tjInitCashVo.getTzLowVoltageFee();
        BigDecimal tzEquipmentsFee = tjInitCashVo.getTzEquipmentsFee();
        BigDecimal multiplicand1 = BigDecimal.ONE.subtract(residualValue);
        BigDecimal dividend1 = new BigDecimal(
            String.valueOf(Math.max(1,
                ageLimit.divide(contractTerm, scale, RoundingMode.HALF_UP).doubleValue())));
        BigDecimal dividend2 = new BigDecimal(
            String.valueOf(Math.min(ageLimit.doubleValue(), contractTerm.doubleValue())));
        //n为更新投入次数 n = 向下取整(i/设备年限) m = 向上取整(i/设备年限)
        BigDecimal n = new BigDecimal(String.valueOf(i + 1)).divide(ageLimit, 0, RoundingMode.DOWN);
        BigDecimal m = new BigDecimal(String.valueOf(i + 1)).divide(ageLimit, 0,
            RoundingMode.CEILING);
        BigDecimal dividend3 = new BigDecimal(
            String.valueOf(Math.max(1,
                ageLimit.divide(contractTerm.subtract(n.multiply(ageLimit)), scale,
                    RoundingMode.HALF_UP).doubleValue())));
        //折旧（高低压）高压残值=高压投资额-高压投资额*(1-残值)/Max(1,设备年限/合同年限)、低压残值=低压投资额-低压投资额*(1-残值)/Max(1,设备年限/合同年限)
        //折旧（高低压) (高压投资额+低压投资额-高压残值-低压残值)/Min(设备年限,合同年限)/(1+0.09)
        BigDecimal tzHighVoltageResidualFee = tzHighVoltageFee.subtract(
                tzHighVoltageFee.multiply(multiplicand1))
            .divide(dividend1, scale, RoundingMode.HALF_UP);
        BigDecimal tzLowVoltageResidualFee = tzLowVoltageFee.subtract(
            tzLowVoltageFee.multiply(multiplicand1)).divide(dividend1, scale, RoundingMode.HALF_UP);
        BigDecimal tzEquipmentsResidualFee = tzEquipmentsFee.subtract(
            tzEquipmentsFee.multiply(multiplicand1)).divide(dividend3, scale, RoundingMode.HALF_UP);
        BigDecimal zjHighAndLowVoltageFee = tzHighVoltageFee.add(tzLowVoltageFee)
            .subtract(tzHighVoltageResidualFee.add(tzLowVoltageResidualFee))
            .divide(dividend2, scale, RoundingMode.HALF_UP)
            .divide(new BigDecimal("1.09"), scale, RoundingMode.HALF_UP);
        //折旧（设备更新投入）设备更新投入残值=设备更新投入-设备更新投入*(1-残值)/Max(1,设备年限/(合同年限-设备年限*n)) n为更新投入次数 n = 取下取整(i/设备年限)
        //折旧（设备更新投入） (设备更新投入额-设备更新投入残值)/Min(设备年限,合同年限)/(1+0.13)
        //折旧（设备）设备残值=设备投资额-设备投资额*(1-残值)/Max(1,设备年限/合同年限)
        //折旧（设备）(设备投资额-设备残值)/Min(设备年限,合同年限)/(1+0.13)
        BigDecimal zjEquipmentsFee = tzEquipmentsFee.subtract(tzEquipmentsResidualFee)
            .divide(dividend2, scale, RoundingMode.HALF_UP)
            .divide(new BigDecimal("1.13"), scale, RoundingMode.HALF_UP);
        Integer currentYear = i + 1;
        Boolean notUpdateEquipmentsFlag =
            n.compareTo(BigDecimal.ZERO) == 0 || m.compareTo(BigDecimal.ONE) == 0;

        tjYearCashVo.setTzHighVoltageFee(BigDecimal.ZERO)
            .setTzLowVoltageFee(BigDecimal.ZERO)
            .setTzEquipmentsFee(BigDecimal.ZERO)
            .setTzUpdateEquipmentsFee(BigDecimal.ZERO)
            .setTzOtherFee(BigDecimal.ZERO)
            .setZjHighAndLowVoltageFee(zjHighAndLowVoltageFee)
            .setZjEquipmentsFee(zjEquipmentsFee)
            .setZjUpdateEquipmentsFee(BigDecimal.ZERO)
            .setZjHighAndLowVoltageFee(
                currentYear <= ageLimit.intValue() ? zjHighAndLowVoltageFee : BigDecimal.ZERO);
        if (currentYear == 1) { //第一年
            tjYearCashVo.setTzHighVoltageFee(tzHighVoltageFee)
                .setTzLowVoltageFee(tzLowVoltageFee)
                .setTzEquipmentsFee(tzEquipmentsFee)
                .setZjHighAndLowVoltageFee(zjHighAndLowVoltageFee)
                .setZjEquipmentsFee(zjEquipmentsFee);
        } else if (currentYear == Math.min(10, tjSurveyPo.getContractTerm().intValue())) { //最后一年
            tjYearCashVo.setTzHighVoltageFee(BigDecimal.ZERO.subtract(tzHighVoltageResidualFee))
                .setTzLowVoltageFee(BigDecimal.ZERO.subtract(tzLowVoltageResidualFee))
                .setTzEquipmentsFee(notUpdateEquipmentsFlag && currentYear <= ageLimit.intValue()
                    ? BigDecimal.ZERO.subtract(tzEquipmentsResidualFee) : BigDecimal.ZERO)
                .setTzUpdateEquipmentsFee(
                    notUpdateEquipmentsFlag && currentYear <= ageLimit.intValue() ? BigDecimal.ZERO
                        : BigDecimal.ZERO.subtract(tzEquipmentsResidualFee))
                .setZjEquipmentsFee(
                    notUpdateEquipmentsFlag && currentYear <= ageLimit.intValue() ? zjEquipmentsFee
                        : BigDecimal.ZERO)
                .setZjUpdateEquipmentsFee(
                    notUpdateEquipmentsFlag && currentYear <= ageLimit.intValue() ? BigDecimal.ZERO
                        : zjEquipmentsFee);
        } else { //中间年份
            tjYearCashVo.setTzHighVoltageFee(BigDecimal.ZERO)
                .setTzLowVoltageFee(BigDecimal.ZERO)
                .setTzEquipmentsFee(notUpdateEquipmentsFlag && currentYear == ageLimit.intValue()
                    ? BigDecimal.ZERO.subtract(tzEquipmentsResidualFee) : BigDecimal.ZERO)
                .setTzUpdateEquipmentsFee(m.compareTo(n) == 0 ? tzEquipmentsFee : BigDecimal.ZERO)
                .setZjEquipmentsFee(
                    notUpdateEquipmentsFlag && currentYear == ageLimit.intValue() ? zjEquipmentsFee
                        : BigDecimal.ZERO);
            if (tjYearCashVo.getZjEquipmentsFee().compareTo(BigDecimal.ZERO) == 0) {
                tjYearCashVo.setZjUpdateEquipmentsFee(zjEquipmentsFee);
            }
        }
        return tjYearCashVo;
    }

    private BigDecimal checkFindServicePrice(BigDecimal servicePrice, BigDecimal serviceIncrease,
        BigDecimal serviceMaxPrice) {
        servicePrice = servicePrice.multiply(BigDecimal.ONE.add(serviceIncrease));
        if (servicePrice.compareTo(serviceMaxPrice) > 0) {
            servicePrice = serviceMaxPrice;
        }
        return servicePrice;
    }

    private Triple<BigDecimal, BigDecimal, BigDecimal> calculateKwhAndServicePriceAndServiceFee(
        int i, BigDecimal kwh, BigDecimal serviceFee, BigDecimal day, BigDecimal servicePrice,
        BigDecimal serviceMaxPrice, TjDailyChargingDurationPo tjDailyChargingDurationPo,
        TjSurveyPo tjSurveyPo, TjSurveyOperationIncomePo tjSurveyOperationIncomePo,
        BigDecimal coefficient, Integer stationPower) {
        if (i + 1 == 1) {
            BigDecimal firstYearThirdMonthKwh = tjDailyChargingDurationPo.getFirstYearThirdMonth()
                .multiply(day.multiply(new BigDecimal("3")))
                .multiply(new BigDecimal(stationPower.toString()));
            BigDecimal firstYearLastNinthMonthKwh = tjDailyChargingDurationPo.getFirstYearLastNinthMonth()
                .multiply(day.multiply(new BigDecimal("9")))
                .multiply(new BigDecimal(stationPower.toString()));
            kwh = kwh.add(firstYearThirdMonthKwh).add(firstYearLastNinthMonthKwh);
            serviceFee = tjSurveyOperationIncomePo.getFirstYearThirdMonthServicePrice()
                .multiply(firstYearThirdMonthKwh);
            serviceFee = serviceFee.add(servicePrice.multiply(firstYearLastNinthMonthKwh));
        }
        if (i + 1 == 2) {
            kwh = kwh.add(tjDailyChargingDurationPo.getSecondYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 3) {
            kwh = kwh.add(tjDailyChargingDurationPo.getThirdYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 4) {
            kwh = kwh.add(tjDailyChargingDurationPo.getFourthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 5) {
            kwh = kwh.add(tjDailyChargingDurationPo.getFifthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 6) {
            kwh = kwh.add(tjDailyChargingDurationPo.getSixthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 7) {
            kwh = kwh.add(tjDailyChargingDurationPo.getSeventhYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 8) {
            kwh = kwh.add(tjDailyChargingDurationPo.getEighthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 9) {
            kwh = kwh.add(tjDailyChargingDurationPo.getNinthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        if (i + 1 == 10) {
            kwh = kwh.add(tjDailyChargingDurationPo.getTenthYear()
                .multiply(day.multiply(new BigDecimal("12"))).multiply(tjSurveyPo.getPower()));
            servicePrice = checkFindServicePrice(servicePrice,
                tjSurveyOperationIncomePo.getServiceIncrease(), serviceMaxPrice);
            serviceFee = servicePrice.add(kwh);
        }
        return Triple.of(coefficient.multiply(kwh), servicePrice, coefficient.multiply(serviceFee));
    }

    /**
     * 每年支出费用 默认返回10年数据
     * <p>
     * 通道费（6‰） 平台托管费 运维费 引流费用 电费支出 电损支出（7%） 服务费分成 场地租金 其他运营费用 管理费用（0） 财务费用（3.95%） 增值税 企业所得税
     *
     * @return
     */
    private TjYearCashVo yearExpensesFee(TjYearCashVo tjYearCashVo, BigDecimal kwh,
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo,
        List<TjCashOutflowPo> tjCashOutflowPoList,
        TjSurveyPo tjSurveyPo, BigDecimal tzEquipmentsFee) {

        Map<Integer, TjCashOutflowPo> tjCashOutflowPoMap = tjCashOutflowPoList.stream().filter(
                e -> e.getType() != CashOutflowType.CASH_MAINT.getCode()
                    && e.getType() != CashOutflowType.CASH_FLOW.getCode())
            .collect(Collectors.toMap(TjCashOutflowPo::getType, o -> o));

        //现金流出 通道费
        BigDecimal channelRate = tjCashOutflowPoMap.get(CashOutflowType.CASH_CHANNEL.getCode())
            .getCommonRate();
        tjYearCashVo.setChannelFee(tjYearCashVo.getOperationIncomeFee().multiply(channelRate));
        //现金流出 平台托管费
        TjCashOutflowPo tjCashOutflowPo = tjCashOutflowPoMap.get(
            CashOutflowType.CASH_PLATFORM.getCode());
        tjYearCashVo.setPlatformFee(platformFee(tjYearCashVo, tjCashOutflowPo));

        //现金流出 运维费
        List<TjCashOutflowPo> maintTjCashOutflowPoList = tjCashOutflowPoList.stream()
            .filter(e -> e.getType() == CashOutflowType.CASH_MAINT.getCode()).collect(
                Collectors.toList());
        tjYearCashVo.setMaintFee(
            maintFee(tjYearCashVo, maintTjCashOutflowPoList, tzEquipmentsFee, tjSurveyPo));
        //现金流出 引流费用
        List<TjCashOutflowPo> flowTjCashOutflowPoList = tjCashOutflowPoList.stream()
            .filter(e -> e.getType() == CashOutflowType.CASH_FLOW.getCode()).collect(
                Collectors.toList());
        tjYearCashVo.setFlowFee(
            flowFee(tjYearCashVo, tjSurveyOperationExpensesPo, flowTjCashOutflowPoList,
                tjSurveyPo));
        //现金流出 电费支出
        tjYearCashVo.setEleFee(tjYearCashVo.getEleFee());
        //现金流出 电损支出
        BigDecimal eleLossRate = tjCashOutflowPoMap.get(CashOutflowType.CASH_ELE_LOSS.getCode())
            .getCommonRate();
        tjYearCashVo.setEleLossFee(tjYearCashVo.getEleFee().multiply(eleLossRate));
        //现金流出 服务费分成
        BigDecimal serviceFee = tjYearCashVo.getOperationIncomeFee()
            .subtract(tjYearCashVo.getEleFee());
        BigDecimal serviceSharingFee =
            tjSurveyOperationExpensesPo.getServiceFeeExcludeAttract() ? serviceFee.subtract(
                    tjYearCashVo.getEleLossFee()).subtract(tjYearCashVo.getFlowFee())
                .multiply(tjSurveyOperationExpensesPo.getServiceFeeSharing())
                : serviceFee.subtract(tjYearCashVo.getEleLossFee())
                    .multiply(tjSurveyOperationExpensesPo.getServiceFeeSharing());
        tjYearCashVo.setServiceSharingFee(serviceSharingFee);
        //现金流出 场地租金
        tjYearCashVo.setRentFee(rentFee(tjYearCashVo, tjSurveyOperationExpensesPo));
        //现金流出 其他运营费用
        tjYearCashVo.setOtherOperationFee(
            tjSurveyOperationExpensesPo.getOtherFee() == null ? BigDecimal.ZERO
                : tjSurveyOperationExpensesPo.getOtherFee());
        //现金流出 管理费用
        BigDecimal manageRate = tjCashOutflowPoMap.get(CashOutflowType.CASH_MANAGE.getCode())
            .getCommonRate();
        tjYearCashVo.setManageFee(manageRate);
        //现金流出 增值税
        tjYearCashVo.setVatFee(vatFee(tjYearCashVo));
        //现金流出 财务费用、企业所得税跟累计现金流量有关联 需要解方程式计算现金流量后才可以算财务费用、企业所得税
        BigDecimal financeRate = tjCashOutflowPoMap.get(CashOutflowType.CASH_FINANCE.getCode())
            .getCommonRate();
        double totalCashFeeDouble = EquationSolver.calculateEquation(tjYearCashVo, financeRate);
        BigDecimal totalCashFee = new BigDecimal(String.format("%.6f", totalCashFeeDouble));
        //累计现金流量
        tjYearCashVo.setTotalCashFee(totalCashFee);
        //现金流出 财务费用
        tjYearCashVo.setFinanceFee(new BigDecimal(String.valueOf(Math.max(0,
            BigDecimal.ZERO.subtract(financeRate).multiply(totalCashFee).doubleValue()))));
        //现金流出 企业所得税跟税前利润总额
        Pair<BigDecimal, BigDecimal> pair = citFeeAndProfitBeforeTaxFee(tjYearCashVo);
        tjYearCashVo.setCitFee(pair.getLeft());
        tjYearCashVo.setProfitBeforeTaxFee(pair.getRight());
        return tjYearCashVo;
    }

    /**
     * 计算平台费用托管费用
     *
     * @param tjYearCashVo
     * @param tjCashOutflowPo
     * @return
     */
    private BigDecimal platformFee(TjYearCashVo tjYearCashVo, TjCashOutflowPo tjCashOutflowPo) {
        if (tjYearCashVo.getYear() == 1) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getFirstYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 2) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getSecondYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 3) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getThirdYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 4) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getFourthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 5) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getFifthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 6) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getSixthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 7) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getSeventhYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 8) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getEighthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 9) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getNinthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        if (tjYearCashVo.getYear() == 10) {
            tjYearCashVo.setPlatformFee(
                tjCashOutflowPo.getTenthYearRate().multiply(tjYearCashVo.getKwh()));
        }
        return tjYearCashVo.getPlatformFee();
    }

    /**
     * 计算租金费用
     *
     * @param tjYearCashVo
     * @param tjSurveyOperationExpensesPo
     * @return
     */
    private BigDecimal rentFee(TjYearCashVo tjYearCashVo,
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo) {
        if (tjSurveyOperationExpensesPo.getFixedRent()) {
            return tjSurveyOperationExpensesPo.getRentFee();
        }
        BigDecimal riseCount = (new BigDecimal(tjYearCashVo.getYear().toString())).divide(
            tjSurveyOperationExpensesPo.getEveryFewYears(), RoundingMode.UP);
        return tjSurveyOperationExpensesPo.getRentFee().multiply(
            tjSurveyOperationExpensesPo.getIncrease().add(BigDecimal.ZERO)
                .pow(riseCount.intValue()));
    }

    /**
     * 运维费用计算
     *
     * @param tjYearCashVo
     * @param maintTjCashOutflowPoList
     * @param tzEquipmentsFee
     * @param tjSurveyPo
     * @return
     */
    private BigDecimal maintFee(TjYearCashVo tjYearCashVo,
        List<TjCashOutflowPo> maintTjCashOutflowPoList,
        BigDecimal tzEquipmentsFee, TjSurveyPo tjSurveyPo) {
        Map<String, TjCashOutflowPo> map = maintTjCashOutflowPoList.stream()
            .collect(Collectors.toMap(TjCashOutflowPo::getStationArea, o -> o));
        TjCashOutflowPo tjCashOutflowPo = map.get(tjSurveyPo.getStationArea());
        BigDecimal maintFee = BigDecimal.ZERO;
        if (tjYearCashVo.getYear() == 1) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getFirstYearRate());
        }
        if (tjYearCashVo.getYear() == 2) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getSecondYearRate());
        }
        if (tjYearCashVo.getYear() == 3) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getThirdYearRate());
        }
        if (tjYearCashVo.getYear() == 4) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getFourthYearRate());
        }
        if (tjYearCashVo.getYear() == 5) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getFifthYearRate());
        }
        if (tjYearCashVo.getYear() == 6) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getSixthYearRate());
        }
        if (tjYearCashVo.getYear() == 7) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getSeventhYearRate());
        }
        if (tjYearCashVo.getYear() == 8) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getEighthYearRate());
        }
        if (tjYearCashVo.getYear() == 9) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getNinthYearRate());
        }
        if (tjYearCashVo.getYear() == 10) {
            maintFee = tzEquipmentsFee.multiply(tjCashOutflowPo.getTenthYearRate());
        }
        return maintFee;
    }


    /**
     * 引流费用计算
     *
     * @param tjYearCashVo
     * @param tjSurveyOperationExpensesPo
     * @param flowTjCashOutflowPoList
     * @param tjSurveyPo
     * @return
     */
    private BigDecimal flowFee(TjYearCashVo tjYearCashVo,
        TjSurveyOperationExpensesPo tjSurveyOperationExpensesPo,
        List<TjCashOutflowPo> flowTjCashOutflowPoList, TjSurveyPo tjSurveyPo) {

//        if (tjSurveyOperationExpensesPo.getServiceFeeExcludeAttract()) {
//            return BigDecimal.ZERO;
//        }
        String detailArea =
            tjSurveyPo.getProvince() + "," + tjSurveyPo.getCity() + "," + tjSurveyPo.getArea();
        String detailCity = tjSurveyPo.getProvince() + "," + tjSurveyPo.getCity();
        Map<String, TjCashOutflowPo> map = flowTjCashOutflowPoList.stream()
            .collect(Collectors.toMap(TjCashOutflowPo::getChannelAreaName, o -> o));
        BigDecimal rate =
            map.get(detailArea) == null ? (map.get(detailCity) == null ? map.get("其他")
                .getCommonRate() : map.get(detailCity).getCommonRate())
                : map.get(detailArea).getCommonRate();
        return tjYearCashVo.getOperationIncomeFee().subtract(tjYearCashVo.getEleFee())
            .multiply(rate);
    }


    /**
     * 增值税（销项税-进项税-固定资产进项税）当小于0时候为0 销项税（(税前运营收入+税前运营补贴收入)/1.13*13%）
     * 进项税（6%通道费+13%平台托管费+13%运维费+13%引流费用+13%电费支出+13%电损支出+13%服务费分成+9%场地租金） A/(1+B)*B A为费用B为比例
     * 固定资产税（(高压投资额+低压投资额)/1.09*9%+(设备投资额+设备更新投入+后期投入)/1.13*13%）
     *
     * @return
     */
    private BigDecimal vatFee(TjYearCashVo tjYearCashVo) {
        BigDecimal rate13 = new BigDecimal("0.13");
        BigDecimal rate9 = new BigDecimal("0.09");
        BigDecimal rate6 = new BigDecimal("0.06");
        //销项税
        BigDecimal outputTax = tjYearCashVo.getOperationIncomeFee()
            .add(tjYearCashVo.getOperationSubsidyFee())
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        //进项税
        BigDecimal inputTaxChannel = tjYearCashVo.getChannelFee()
            .divide(BigDecimal.ONE.add(rate6), scale, RoundingMode.HALF_UP)
            .multiply(rate6);
        BigDecimal inputTaxPlatform = tjYearCashVo.getPlatformFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxMaint = tjYearCashVo.getMaintFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxFlow = tjYearCashVo.getFlowFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxEle = tjYearCashVo.getEleFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxEleLoss = tjYearCashVo.getEleLossFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxServiceSharing = tjYearCashVo.getServiceSharingFee()
            .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
            .multiply(rate13);
        BigDecimal inputTaxRent = tjYearCashVo.getRentFee()
            .divide(BigDecimal.ONE.add(rate9), scale, RoundingMode.HALF_UP)
            .multiply(rate9);
        BigDecimal inputTax = inputTaxChannel.add(inputTaxPlatform).add(inputTaxMaint)
            .add(inputTaxFlow).add(inputTaxEle).add(inputTaxEleLoss).add(inputTaxServiceSharing)
            .add(inputTaxRent);
        //固定资产税
        BigDecimal fixedAssetsTax = tjYearCashVo.getTzHighVoltageFee()
            .add(tjYearCashVo.getTzLowVoltageFee())
            .divide(BigDecimal.ONE.add(rate9), scale, RoundingMode.HALF_UP)
            .multiply(rate9).add(
                tjYearCashVo.getTzEquipmentsFee().add(tjYearCashVo.getTzUpdateEquipmentsFee())
                    .add(tjYearCashVo.getTzOtherFee())
                    .divide(BigDecimal.ONE.add(rate13), scale, RoundingMode.HALF_UP)
                    .multiply(rate13));
        BigDecimal vatFee = outputTax.subtract(inputTax).subtract(fixedAssetsTax)
            .max(BigDecimal.ZERO);
        return vatFee;
    }

    /**
     * 企业所得税（税前利润总额*15%）当小于0时候为0
     * 企业所得税（不含财务费用）（流入合计-折旧合计-通道费-平台托管费-运维费-引流费用-电费支出-电损支出-服务费分成-场地租金-其他运营费用-管理费用-增值税）
     * 税前利润总额（流入合计-折旧合计-通道费-平台托管费-运维费-引流费用-电费支出-电损支出-服务费分成-场地租金-其他运营费用-管理费用-财务费用-增值税）
     *
     * @return
     */
    private Pair<BigDecimal, BigDecimal> citFeeAndProfitBeforeTaxFee(TjYearCashVo tjYearCashVo) {
        //本期经营活动现金流量
        //本期流入现金(税前运营收入+税前运营补贴收入+建站补贴)
        BigDecimal currentPeriodIncomeCash = tjYearCashVo.getOperationIncomeFee()
            .add(tjYearCashVo.getOperationSubsidyFee())
            .add(tjYearCashVo.getStationSubsidyFee());
        //折旧费用 折旧（高低压)+折旧（设备）+折旧（设备更新投入）
        BigDecimal zjCash = tjYearCashVo.getZjHighAndLowVoltageFee()
            .add(tjYearCashVo.getZjEquipmentsFee())
            .add(tjYearCashVo.getZjUpdateEquipmentsFee());
        //本期流出现金(通道费+平台托管费+运维费+引流费用+电费支出+电损支出+服务费分成+场地租金+其他运营费用+管理费用+增值税+财务费用)
        BigDecimal currentPeriodExpensesCash = tjYearCashVo.getChannelFee()
            .add(tjYearCashVo.getPlatformFee()).add(tjYearCashVo.getMaintFee())
            .add(tjYearCashVo.getFlowFee()).add(tjYearCashVo.getEleFee())
            .add(tjYearCashVo.getEleLossFee()).add(tjYearCashVo.getServiceSharingFee())
            .add(tjYearCashVo.getRentFee()).add(tjYearCashVo.getOtherOperationFee())
            .add(tjYearCashVo.getManageFee()).add(tjYearCashVo.getVatFee())
            .add(tjYearCashVo.getFinanceFee());
        //税前利润总额
        BigDecimal profitBeforeTaxCash = currentPeriodIncomeCash.subtract(zjCash)
            .subtract(currentPeriodExpensesCash);
        //企业所得税税率
        BigDecimal citRate = new BigDecimal("0.15");

        BigDecimal citFee = profitBeforeTaxCash.compareTo(BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO
            : (tjYearCashVo.getTotalBeforeProfitBeforeTaxFee().add(profitBeforeTaxCash)).compareTo(
                BigDecimal.ZERO) <= 0 ? BigDecimal.ZERO
                : tjYearCashVo.getTotalBeforeProfitBeforeTaxFee()
                    .add(profitBeforeTaxCash).multiply(citRate)
                    .subtract(tjYearCashVo.getTotalBeforeCitFee());
        return Pair.of(citFee, profitBeforeTaxCash);
    }

}