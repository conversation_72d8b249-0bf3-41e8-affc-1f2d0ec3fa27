<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.device.business.mapper.SiteDefultSettingMapper">

    <!--通用查询映射结果-->
    <resultMap id="BaseResultMap" type="com.cdz360.biz.device.business.entity.result.SiteDefultSettingVO">
        <id column="id" property="id"/>
        <result column="siteId" property="siteId"/>
        <result column="chargeId" property="chargeId"/>
        <result column="adminPassword" property="adminPassword"/>
        <result column="level2Password" property="level2Password"/>
        <result column="dayVolume" property="dayVolume"/>
        <result column="nightVolume" property="nightVolume"/>
        <result column="url" property="url"/>
        <result column="isQueryChargeRecord" property="isQueryChargeRecord"/>
        <result column="isTimedCharge" property="isTimedCharge"/>
        <result column="isNoCardCharge" property="isNoCardCharge"/>
        <result column="isScanCharge" property="isScanCharge"/>
        <result column="isVinCharge" property="isVinCharge"/>

        <result column="isCardCharge" property="isCardCharge"/>
        <result column="isQuotaEleCharge" property="isQuotaEleCharge"/>
        <result column="isQuotaMoneyCharge" property="isQuotaMoneyCharge"/>
        <result column="isQuotaTimeCharge" property="isQuotaTimeCharge"/>
        <result column="internationalAgreement" property="internationalAgreement"/>
        <result column="isAutoStopCharge" property="isAutoStopCharge"/>
        <result column="avgOrTurnCharge" property="avgOrTurnCharge"/>
        <result column="isCombineCharge" property="isCombineCharge"/>
        <result column="heating" property="heating"/>
        <result column="heatingVoltage" property="heatingVoltage"/>
        <result column="batteryCheck" property="batteryCheck"/>
        <result column="securityCheck" property="securityCheck"/>
        <result column="constantCharge" property="constantCharge"/>
        <result column="vinDiscover" property="vinDiscover"/>
        <result column="orderPrivacySetting" property="orderPrivacySetting"/>
        <result column="accountDisplayType" property="accountDisplayType"/>
        <result column="createTime" property="createTime"/>
        <result column="updateTime" property="updateTime"/>
        <result column="updateByUserid" property="updateByUserid"/>
    </resultMap>

    <sql id="Base_Column_List">
      id,
      siteId,
      chargeId,
      adminPassword,
      level2Password,
      dayVolume,
      nightVolume,
      url,
      isQueryChargeRecord,
      isTimedCharge,
      isNoCardCharge,
      isScanCharge,
      isVinCharge,
      isCardCharge,
      isQuotaEleCharge,
      isQuotaMoneyCharge,
      isQuotaTimeCharge,
      internationalAgreement,
      isAutoStopCharge,
      avgOrTurnCharge,
      isCombineCharge,
      heating,
      heatingVoltage,
      batteryCheck,
      securityCheck,
      constantCharge,
      vinDiscover,
      orderPrivacySetting,
      accountDisplayType,
      createTime,
      updateTime,
      updateByUserid
    </sql>

    <select id="selectSiteDefultSettingList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from t_site_defult_setting sds
        <where>
            <if test="siteId != null and siteId != ''">
                and sds.siteId = #{siteId}
            </if>
        </where>
    </select>

    <select id="selectByBoxOutFactoryCode" resultMap="BaseResultMap">
        SELECT s.* from bs_box b
        INNER JOIN t_site_defult_setting s
        on b.station_code=s.siteId
        where b.box_out_factory_code=#{boxOutFactoryCode};
    </select>

    <update id="updateSiteDefaultPriceScheme">
        update t_site_defult_setting set chargeId=#{priceSchemeId}
        where siteId=#{siteId}
    </update>
</mapper>