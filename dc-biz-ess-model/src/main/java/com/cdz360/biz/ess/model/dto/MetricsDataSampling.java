package com.cdz360.biz.ess.model.dto;

import com.cdz360.base.model.bi.vo.SamplingMinuteDataVo;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Schema(description = "采集点指标数据")
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class MetricsDataSampling extends SamplingMinuteDataVo {

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @JsonDeserialize(using = LocalDateTimeDeserializer.class)
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(title = "时间", description = "数据对应的时间")
    private LocalDateTime time;

    public MetricsDataSampling() {
    }

    public MetricsDataSampling(LocalDateTime time) {
        this.time = time;
    }

    @Override
    public int getMinute() {
        if (null == time) {
            return 0;
        }
        return time.getHour() * 60 + time.getMinute();
    }
}
