package com.cdz360.biz.model.tj.kc.vo;

import com.cdz360.biz.model.tj.kc.po.TjSurveyChargeAreaPilePo;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonInclude.Include;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "TjSurveyChargeAreaPileVo")
@Data
@Accessors(chain = true)
public class TjSurveyChargeAreaPileVo extends TjSurveyChargeAreaPilePo {

    @Schema(description = "功率")
    @JsonInclude(Include.NON_NULL)
    private Integer power;

    @Schema(description = "0：非分体机，1：250A终端单枪，2：250A终端双枪")
    @JsonInclude(Include.NON_NULL)
    private Integer splitFlag;

    public Integer getTotalPower() {
        return power * getEvseCount();
    }

}
