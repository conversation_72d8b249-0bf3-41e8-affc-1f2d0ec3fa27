package com.cdz360.biz.tj.service.kc;

import com.cdz360.base.model.base.dto.ListResponse;
import com.cdz360.base.utils.CollectionUtils;
import com.cdz360.base.utils.RestUtils;
import com.cdz360.base.utils.StringUtils;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaAnalysisRoDs;
import com.cdz360.biz.tj.ds.ro.kc.ds.TjAreaRoDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaAnalysisPointRwDs;
import com.cdz360.biz.tj.ds.rw.kc.ds.TjAreaAnalysisRwDs;
import com.cdz360.biz.model.gaode.StaticMapGaoDe;
import com.cdz360.biz.model.tj.kc.param.ListTjAreaAnalysisParam;
import com.cdz360.biz.model.tj.kc.po.TjAreaAnalysisPo;
import com.cdz360.biz.model.tj.kc.po.TjAreaPo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisVo;
import com.cdz360.biz.model.tj.kc.vo.TjAreaAnalysisWithPointVo;
import com.cdz360.biz.utils.feign.hlht.OpenHlhtFeignClient;
import com.chargerlinkcar.framework.common.utils.FeignResponseValidate;
import com.chargerlinkcar.framework.common.utils.IotAssert;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class TjAnalysisService {

    @Autowired
    private OpenHlhtFeignClient openHlhtFeignClient;

    @Autowired
    private TjAreaRoDs tjAreaRoDs;

    @Autowired
    private TjAreaAnalysisPointRwDs tjAreaAnalysisPointRwDs;

    @Autowired
    private TjAreaAnalysisRoDs tjAreaAnalysisRoDs;

    @Autowired
    private TjAreaAnalysisRwDs tjAreaAnalysisRwDs;

    public Mono<ListResponse<TjAreaAnalysisVo>> findTjAnalysis(ListTjAreaAnalysisParam param) {
        if (null == param.getSize()) {
            param.setSize(10);
        }

        return Mono.just(param)
            .map(tjAreaAnalysisRoDs::findTjAnalysis)
            .map(list -> list.stream().map(x -> new TjAreaAnalysisVo()
                    .setId(x.getId())
                    .setAid(x.getAid())
                    .setName(x.getName())
                    .setRadius(x.getRadius())
                    .setLocation(x.getLocation())
                    .setStaticmap(x.getStaticmap()))
                .collect(Collectors.toList()))
            .map(list -> RestUtils.buildListResponse(
                list, Boolean.TRUE.equals(param.getTotal()) ?
                    tjAreaAnalysisRoDs.countTjAnalysis(param) : 0L));
    }

    public Mono<TjAreaAnalysisWithPointVo> getTjAnalysisById(Long analysisId) {
        IotAssert.isNotNull(analysisId, "投建分析唯一ID不能为空");
        TjAreaAnalysisWithPointVo result = tjAreaAnalysisRoDs.getWithPointById(analysisId);
        IotAssert.isNotNull(result, "投建分析唯一ID无效");
        return Mono.just(result);
    }

    @Transactional
    public Mono<TjAreaAnalysisWithPointVo> saveAnalysis(TjAreaAnalysisWithPointVo param) {
        IotAssert.isNotNull(param.getAid(), "投建区域ID不能为空");
        IotAssert.isNotNull(param.getRadius(), "投建分析半径不能为空");
        IotAssert.isNotBlank(param.getLocation(), "投建分析中心点不能为空");

        TjAreaPo tjArea = tjAreaRoDs.getByAid(param.getAid());
        IotAssert.isNotNull(tjArea, "投建区域ID无效");

        if (StringUtils.isBlank(param.getName())) {
            param.setName(tjArea.getName() + tjArea.getRadius() + "km投建分析");
        }

        Mono<TjAreaAnalysisWithPointVo> mono = null != param.getId() ?
            this.editAnalysis(param) : this.addAnalysis(param);
        return mono.doOnNext(newAnalysis -> tjAreaAnalysisPointRwDs.batchInsertTjAreaAnalysisPoint(
            param.getAnalysisPointList()
                .stream().peek(x -> x.setAnalysisId(newAnalysis.getId()))
                .collect(Collectors.toList())));
    }

    private Mono<TjAreaAnalysisWithPointVo> addAnalysis(TjAreaAnalysisWithPointVo param) {
        IotAssert.isTrue(CollectionUtils.isNotEmpty(param.getAnalysisPointList()),
            "投建分析划分点缺失");
        TjAreaAnalysisPo newAnalysis = new TjAreaAnalysisPo()
            .setName(param.getName())
            .setRadius(param.getRadius())
            .setLocation(param.getLocation())
            .setAid(param.getAid());
        Mono<TjAreaAnalysisPo> mono = Mono.just(newAnalysis);
        if (StringUtils.isNotBlank(param.getLocation())) {
            mono = mono.zipWith(openHlhtFeignClient.gaodeStaticMap(param.getLocation())
                    .map(FeignResponseValidate::checkReturn)
                    .map(StaticMapGaoDe::getBytes))
                .map(tuple -> newAnalysis.setStaticmap(tuple.getT2()));
        }

        return mono.doOnNext(tjAreaAnalysisRwDs::insertTjAreaAnalysis)
            .doOnNext(x -> param.setId(x.getId())
                .setStaticmap(x.getStaticmap()))
            .map(x -> param);
    }

    private Mono<TjAreaAnalysisWithPointVo> editAnalysis(TjAreaAnalysisWithPointVo param) {
        TjAreaAnalysisPo analysis = tjAreaAnalysisRoDs.getById(param.getId());
        IotAssert.isNotNull(analysis, "投建分析ID无效");

        analysis.setName(param.getName())
            .setRadius(param.getRadius())
            .setLocation(param.getLocation())
            .setAid(param.getAid());

        Mono<TjAreaAnalysisPo> mono = Mono.just(analysis);
        if (!param.getLocation().equals(analysis.getLocation())) {
            mono = mono.zipWith(openHlhtFeignClient.gaodeStaticMap(param.getLocation())
                    .map(FeignResponseValidate::checkReturn)
                    .map(StaticMapGaoDe::getBytes))
                .map(tuple -> analysis.setStaticmap(tuple.getT2()));
        }

        return mono.doOnNext(newAnalysis -> {
            tjAreaAnalysisRwDs.updateTjAreaAnalysis(newAnalysis.setId(param.getId()));

            // 删除旧分析点
            if (CollectionUtils.isNotEmpty(param.getAnalysisPointList())) {
                tjAreaAnalysisPointRwDs.deleteByAnalysisId(param.getAid());
            }
        }).map(x -> param);
    }

    public Mono<TjAreaAnalysisVo> disableTjAreaAnalysis(Long analysisId) {
        val area = tjAreaAnalysisRoDs.getById(analysisId);
        IotAssert.isNotNull(area, "投建分析区域唯一ID无效");

        if (Boolean.TRUE.equals(area.getEnable())) {
            boolean b = tjAreaAnalysisRwDs.disableTjAreaAnalysis(analysisId);
            log.debug("删除投建分析区域: {}", b);
        }

        TjAreaAnalysisVo result = new TjAreaAnalysisVo();
        BeanUtils.copyProperties(area, result);
        return Mono.just(result);
    }
}
