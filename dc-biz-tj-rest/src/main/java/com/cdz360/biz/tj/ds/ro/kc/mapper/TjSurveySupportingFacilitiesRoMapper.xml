<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveySupportingFacilitiesRoMapper">

  <resultMap id="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_PO" type="com.cdz360.biz.model.tj.kc.po.TjSurveySupportingFacilitiesPo">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="surveyNo" jdbcType="VARCHAR" property="surveyNo"/>
    <result column="carParkType" jdbcType="INTEGER" property="carParkType"/>
    <result column="carGearNeed" jdbcType="BOOLEAN" property="carGearNeed"/>
    <result column="intelligentLockNeed" jdbcType="BOOLEAN" property="intelligentLockNeed"/>
    <result column="epoxyFlooringNeed" jdbcType="BOOLEAN" property="epoxyFlooringNeed"/>
    <result column="groundHardeningNeed" jdbcType="BOOLEAN" property="groundHardeningNeed"/>
    <result column="fireProtectionSystemNeed" jdbcType="BOOLEAN" property="fireProtectionSystemNeed"/>
    <result column="lightingType" jdbcType="INTEGER" property="lightingType"/>
    <result column="monitoringSystemNeed" jdbcType="BOOLEAN" property="monitoringSystemNeed"/>
    <result column="canopyNeed" jdbcType="BOOLEAN" property="canopyNeed"/>
    <result column="barrierGateType" jdbcType="INTEGER" property="barrierGateType"/>
    <result column="brandImageType" jdbcType="INTEGER" property="brandImageType"/>
    <result column="enable" jdbcType="BOOLEAN" property="enable"/>
    <result column="createTime" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="updateTime" jdbcType="TIMESTAMP" property="updateTime"/>
  </resultMap>

  <select id="getById"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_PO">
    select * from t_tj_survey_supporting_facilities where id = #{id}
  </select>

  <select id="findTjSurveySupportingFacilitiesBySurveyNo"
    resultMap="RESULT_TJ_SURVEY_SUPPORTING_FACILITIES_PO">
    select * from t_tj_survey_supporting_facilities where surveyNo = #{surveyNo} and enable=true
  </select>


</mapper>

