package com.cdz360.biz.tj.ds.ro.kc.ds;


import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationExpensesPo;
import com.cdz360.biz.model.tj.kc.po.TjSurveyOperationIncomePo;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationExpensesRoMapper;
import com.cdz360.biz.tj.ds.ro.kc.mapper.TjSurveyOperationIncomeRoMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Slf4j

@Service

public class TjSurveyOperationIncomeRoDs {


    @Autowired

    private TjSurveyOperationIncomeRoMapper tjSurveyOperationIncomeRoMapper;


    public TjSurveyOperationIncomePo getById(Long id) {

        return this.tjSurveyOperationIncomeRoMapper.getById(id);

    }

    public TjSurveyOperationIncomePo findTjSurveyOperationIncomeBySurveyNo(String surveyNo) {
        return this.tjSurveyOperationIncomeRoMapper.findTjSurveyOperationIncomeBySurveyNo(surveyNo);
    }

}

