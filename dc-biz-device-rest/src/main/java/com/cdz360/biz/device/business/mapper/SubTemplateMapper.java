package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.SubTemplate;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 计费模板**子模板** Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
@Mapper
public interface SubTemplateMapper extends BaseMapper<SubTemplate> {

    List<Long> findAllForClean();

    List<SubTemplate> findByTemplateId(@Param("templateId") Long templateId);

    int updateByCondition(SubTemplate subTemplate);
}
