package com.cdz360.biz.cus.repository;

import com.cdz360.biz.cus.domain.vo.VinSearchParamComm;
import com.cdz360.biz.model.cus.vin.param.VINCarNoParam;
import com.cdz360.biz.model.trading.bi.param.ListChargeOrderBiByVinParam;
import com.cdz360.biz.model.vin.po.SiteAuthVinLogPo;
import com.chargerlinkcar.framework.common.domain.vo.VinDto;
import com.chargerlinkcar.framework.common.domain.vo.VinDto2;
import com.chargerlinkcar.framework.common.domain.vo.VinParam;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * VinMapper
 *
 * @since 2019/5/15 14:12
 * <AUTHOR>
 */
@Mapper
public interface VinMapper {

    Integer insert(VinParam vinParam);

    Integer update(VinParam vinParam);

    Integer updateEnableBySiteId(@Param("vinCommId") List<Long> vinCommId,
        @Param("siteId") String siteId);

    Integer updateByVinAndCommAndCorpOnCorp(VinParam vinParam);

    Integer delete(@Param(value = "id") Long id, @Param(value = "modifyBy") Long modifyBy);

    List<VinDto> select(VinSearchParamComm vinSearchParam);

    List<VinDto> selectComm(VinSearchParamComm vinSearchParamComm);

    List<VinDto> selectVinOnCorp(VinParam vinParam);

    //获取在commid下的不属于userId的该vin个数
    List<VinDto> selectCountByCommIdSinUserId(@Param(value = "commId") Long commId,
        @Param(value = "userId") Long userId,
        @Param(value = "vin") String vin);

    //获取在commid下的该vin
    List<VinDto> selectVinListByCommId(@Param(value = "commId") Long commId,
        @Param(value = "vin") String vin);

    //获取在commid下的属于userId的该vin的实体列表
    List<VinDto> selectCountByCommIdConUserId(@Param(value = "commId") Long commId,
        @Param(value = "userId") Long userId,
        @Param(value = "vin") String vin);

    int insertBatch(@Param("vins") List<VinParam> vins);

    /**
     * 订单筛选-查询Vin码列表
     *
     * @param map
     * @return
     */
    List<VinDto> selectAllVinList(Map map);

    List<VinDto> getVinBySiteId(@Param(value = "siteId") String siteId);

    /**
     * 订单筛选-根据VINS查询卡号列表 *
     *
     * @param param
     * @return
     */
    List<VinDto> selectCarNoByVins(VINCarNoParam param);

    Long checkVinCount(@Param("vin") String vin, @Param("topCommId") Long topCommId);

    VinDto selectByVinWithAuth(@Param(value = "vin") String vin,
        @Param(value = "commId") Long commId);

    VinDto selectByVin(@Param(value = "vin") String vin,
        @Param(value = "commId") Long commId);

    VinDto getVinByUid(@Param(value = "topCommId") Long topCommId,
        @Param(value = "uid") Long uid,
        @Param(value = "vin") String vin);

    VinDto selectByCarNo(@Param(value = "carNo") String carNo,
        @Param(value = "userId") Long userId);

    VinDto selectByVinAndCorp(@Param(value = "vin") String vin,
        @Param(value = "corpId") Long corpId);


    /**
     * 根据用户Id和VIN状态获取VIN码列表
     *
     * @param offset 偏移量
     * @param limit  分页大小
     * @param userId 用户Id
     * @param status VIN状态
     * @return
     */
    List<VinDto> findByUserIdAndStatus(
        @Param("offset") Long offset,
        @Param("limit") Integer limit,
        @Param("userId") Long userId,
        @Param("isPrimary") Boolean isPrimary,
        @Param("status") int status);

    /**
     * 通过VIN码Id获取VIN码
     *
     * @param id
     * @return
     */
    VinDto getById(@Param("id") Long id);

    VinDto2 getVinDto2ById(Long vinId);

    /**
     * 通过VIN码Id列表获取VIN码
     *
     * @param idList
     * @return
     */
    List<VinDto> getByIdList(@Param("idList") List<Long> idList);

    /**
     * 查询用户是否存在VIN码
     *
     * @param userId
     * @param commId
     * @return
     */
    VinDto findOneByUserId(@Param("userId") Long userId, @Param("commId") Long commId);

    List<VinDto> getCardsByUserIdAndCorpIds(@Param("userId") Long userId,
        @Param("corpIds") List<Long> corpIds);

    /**
     * 得到需要被清洗的VIN码数据
     *
     * @return
     */
    List<VinDto> getVinCleanData();

    @Deprecated
    List<VinDto> findAll(@Param("start") Long start, @Param("size") Integer size);

    /**
     * 获取授信账户名下未删除的VIN数量
     *
     * @param rBlocUserId
     */
    int getCreditAccountVinCount(@Param("rBlocUserId") long rBlocUserId);

    /**
     * 原先授信账户的VIN转到新授信账户
     *
     * @param rBlocUserId
     * @param userId
     * @return
     */
    int swapCreditAccountVin(@Param("rBlocUserId") long rBlocUserId, @Param("userId") long userId);

    int moveCorp(@Param("corpId") Long corpId, @Param("commId") Long commId);

    List<VinDto> getCarNoListByUserIds(@Param("userIds") List<Long> userIds,
        @Param("topCommId") Long topCommId);

    List<SiteAuthVinLogPo> getSiteAuthVinTime(@Param("evseNoList") List<String> evseNoList);

    SiteAuthVinLogPo getVinListByEvse(@Param("evseId") String evseId);

    List<VinDto> getCarNoListByParam(VinParam vinParam);

    Integer setPrimary(@Param("vinId") List<Long> vinId, @Param("isPrimary") boolean isPrimary);

    List<VinDto> findVinList(@Param(value = "param") ListChargeOrderBiByVinParam param);
}