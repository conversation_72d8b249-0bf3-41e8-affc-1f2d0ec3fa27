package com.cdz360.biz.cus.balance;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.SmartLifecycle;
import org.springframework.stereotype.Component;

import java.util.Timer;
import java.util.TimerTask;

@Slf4j
@Component
public class CusBalanceLifecycle implements SmartLifecycle {


    private boolean isRunning = false;

    @Override
    public void start() {
        log.info("starting");
        isRunning = true;
    }


    @Override
    public void stop(Runnable callback) {
        log.info("stopping 01");
        new Timer().schedule(new TimerTask() {
            @Override
            public void run() {
                callback.run();
                isRunning = false;
                log.info("stopping 02");
            }
        }, 3000L);  // 3秒后退出
    }

    @Override
    public void stop() {
        // do nothing
    }

    @Override
    public boolean isRunning() {
        log.info("isRunning = {}", isRunning);
        return isRunning;
    }
}
