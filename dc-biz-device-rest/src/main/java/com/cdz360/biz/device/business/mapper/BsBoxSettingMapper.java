package com.cdz360.biz.device.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.cdz360.biz.device.business.entity.po.BsBoxSetting;
import com.cdz360.biz.device.business.entity.request.BoxSettingListRequest;
import com.cdz360.biz.device.business.entity.result.BsBoxSettingBInfoVo;
import com.cdz360.biz.device.business.entity.result.SettingProgressVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Classname BsBoxSettingQueryMapper
 * @Description 桩配置 Mapper 接口
 * @Date 2019/6/6 9:26
 * @Created by JLei
 * @Email <EMAIL>
 */
@Mapper
public interface BsBoxSettingMapper extends BaseMapper<BsBoxSetting>{

    /**
     * 查询
     * @param query
     * @return
     */
    List<BsBoxSettingBInfoVo> selectBoxSettingList(BoxSettingListRequest query);

    List<BsBoxSettingBInfoVo> selectBoxSettingListForUrgencyCard(BoxSettingListRequest query);

    BsBoxSettingBInfoVo selectBoxSettingById(Long id);

    List<BsBoxSettingBInfoVo> selectResultByCommIds(@Param("commIdChain") String commIdChain, @Param("status") List<Integer> status);

    List<BsBoxSettingBInfoVo> getPagedBoxSettingFailList(@Param("commIds") List<Long> commIds);

    /**
     * 根据下发状态进行Count
     *
     * @param stationCode     必传，站点编号
     * @param whiteCardStatus 可传，紧急充电卡下发状态1下发成功2失败3下发中
     * @return
     */
    Integer selectCountByStationCodeAndWhiteCardStatus(@Param("stationCode") String stationCode, @Param("status") Integer whiteCardStatus);

    /**
     * 获取下发进度数据
     * @param templateCode
     * @return
     */
    List<SettingProgressVo> getSettingProgress(@Param("templateCode") String templateCode);

    int deleteSettingByBoxOutFactoryCode(@Param("boxOutFactoryCode") String boxOutFactoryCode);

    /**
     * 插入一条记录，并关联查询t_template得到code填入templateCode中
     * @param bsBoxSetting
     * @return
     */
    int insertFillTemplateCode(@Param("bsBoxSetting") BsBoxSetting bsBoxSetting);

    /**
     * 更新一条记录，并关联查询t_template得到code填入templateCode中
     * @param bsBoxSetting
     * @return
     */
    int updateByIdFillTemplateCode(@Param("bsBoxSetting") BsBoxSetting bsBoxSetting);
}